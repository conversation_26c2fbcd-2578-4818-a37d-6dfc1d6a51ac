# Database Migration Guide

## Overview

This document provides comprehensive documentation for the Mobile App Automation database structure, covering both iOS and Android platforms. The database architecture follows a modular design with separate database files for different functional areas.

## Database Architecture

### Database Files Structure

Both iOS and Android platforms maintain identical database structures with the following five database files:

#### iOS Database Files
- **Location**: `/app/data/`
- **Files**:
  - `ios_globals.db` - Global settings and environment variables
  - `ios_test_suites.db` - Test suites and test cases data
  - `ios_settings.db` - Application settings and configurations
  - `ios_environments.db` - Environment definitions and configurations
  - `ios_execution_tracker.db` - Test execution tracking and results

#### Android Database Files
- **Location**: `/app_android/data/`
- **Files**:
  - `android_globals.db` - Global settings and environment variables
  - `android_test_suites.db` - Test suites and test cases data
  - `android_settings.db` - Application settings and configurations
  - `android_environments.db` - Environment definitions and configurations
  - `android_execution_tracker.db` - Test execution tracking and results

## Detailed Database Schemas

### 1. Globals Database (`*_globals.db`)

**Purpose**: Stores global configuration variables, environment settings, and system-wide parameters.

```sql
CREATE TABLE globals (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    global_id TEXT UNIQUE NOT NULL,
    platform TEXT NOT NULL CHECK (platform IN ('ios', 'android', 'shared')),
    category TEXT NOT NULL CHECK (category IN ('directory_path', 'app_config', 'execution_setting', 'device_config')),
    key_name TEXT NOT NULL,
    key_value TEXT NOT NULL,
    value_type TEXT CHECK (value_type IN ('string', 'integer', 'boolean', 'json', 'path')) DEFAULT 'string',
    description TEXT,
    is_readonly BOOLEAN DEFAULT 0,
    port_specific INTEGER DEFAULT NULL,
    expires_at TIMESTAMP DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(platform, category, key_name, port_specific)
);
```

**Usage Guidelines**:
- Store system-wide configuration variables
- Platform-specific or shared settings
- Port-specific configurations for multi-device setups
- Read-only system parameters

### 2. Test Suites Database (`*_test_suites.db`)

**Purpose**: Contains all test suite and test case definitions, including their JSON payloads and metadata.

#### Test Suites Table
```sql
CREATE TABLE test_suites (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    suite_id TEXT UNIQUE NOT NULL,
    platform TEXT NOT NULL CHECK (platform IN ('ios', 'android')),
    name TEXT NOT NULL,
    description TEXT,
    file_path TEXT NOT NULL,
    test_case_count INTEGER DEFAULT 0,
    step_count INTEGER DEFAULT 0,
    json_payload TEXT NOT NULL,
    status TEXT CHECK (status IN ('active', 'inactive', 'archived')) DEFAULT 'active',
    version INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT DEFAULT 'system',
    last_modified_by TEXT DEFAULT 'system'
);
```

#### Test Cases Table
```sql
CREATE TABLE test_cases (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    test_case_id TEXT UNIQUE NOT NULL,
    suite_id TEXT NOT NULL,
    platform TEXT NOT NULL CHECK (platform IN ('ios', 'android')),
    name TEXT NOT NULL,
    description TEXT,
    file_path TEXT NOT NULL,
    step_count INTEGER DEFAULT 0,
    json_payload TEXT NOT NULL,
    test_idx INTEGER NOT NULL,
    status TEXT CHECK (status IN ('active', 'inactive', 'archived')) DEFAULT 'active',
    version INTEGER DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_by TEXT DEFAULT 'system',
    last_modified_by TEXT DEFAULT 'system',
    FOREIGN KEY (suite_id) REFERENCES test_suites(suite_id) ON DELETE CASCADE
);
```

#### Test Case Backups Table
```sql
CREATE TABLE test_case_backups (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    test_case_id TEXT NOT NULL,
    json_data TEXT NOT NULL,
    backup_reason TEXT NOT NULL CHECK (backup_reason IN ('manual_save', 'auto_backup', 'pre_execution', 'migration')),
    session_id TEXT,
    created_by TEXT DEFAULT 'system',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (test_case_id) REFERENCES test_cases(test_case_id) ON DELETE CASCADE
);
```

#### Locators Repository Table
```sql
CREATE TABLE locators_repository (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    test_case_id TEXT NOT NULL,
    action_id TEXT NOT NULL,
    locator_type TEXT NOT NULL,
    locator_value TEXT NOT NULL,
    platform TEXT NOT NULL CHECK (platform IN ('ios', 'android')),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_used_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    usage_count INTEGER DEFAULT 1,
    FOREIGN KEY (test_case_id) REFERENCES test_cases(test_case_id) ON DELETE CASCADE,
    UNIQUE(test_case_id, action_id, locator_type, locator_value, platform)
);
```

**Usage Guidelines**:
- Store complete test suite and test case definitions
- Maintain version control and backup history
- Track locator usage for optimization
- Support hierarchical test organization

### 3. Settings Database (`*_settings.db`)

**Purpose**: Application-specific settings, user preferences, and configuration parameters.

```sql
CREATE TABLE settings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    platform TEXT NOT NULL CHECK (platform IN ('ios', 'android', 'shared')),
    category TEXT NOT NULL CHECK (category IN ('directory_path', 'app_config', 'execution_setting', 'device_config')),
    setting_key TEXT NOT NULL,
    setting_value TEXT NOT NULL,
    description TEXT,
    is_default BOOLEAN DEFAULT 0,
    port_specific INTEGER DEFAULT NULL,
    device_id TEXT DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(platform, category, setting_key, port_specific, device_id)
);
```

**Usage Guidelines**:
- Store user-configurable application settings
- Device-specific configurations
- Directory paths and file locations
- Execution parameters and preferences

### 4. Environments Database (`*_environments.db`)

**Purpose**: Environment definitions for different testing contexts (dev, staging, production).

```sql
CREATE TABLE environments (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    environment_id TEXT UNIQUE NOT NULL,
    platform TEXT NOT NULL CHECK (platform IN ('ios', 'android', 'shared')),
    name TEXT NOT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT 0,
    port_specific INTEGER DEFAULT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(platform, name, port_specific)
);
```

**Usage Guidelines**:
- Define testing environments (dev, staging, prod)
- Environment-specific configurations
- Active environment tracking
- Port-specific environment isolation

### 5. Execution Tracker Database (`*_execution_tracker.db`)

**Purpose**: Track test execution progress, results, and performance metrics.

```sql
CREATE TABLE execution_tracker (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    execution_id TEXT UNIQUE NOT NULL,
    platform TEXT NOT NULL CHECK (platform IN ('ios', 'android')),
    execution_type TEXT CHECK (execution_type IN ('suite', 'case', 'step')) NOT NULL,
    parent_execution_id TEXT DEFAULT NULL,
    suite_id TEXT,
    test_case_id TEXT DEFAULT NULL,
    step_idx INTEGER DEFAULT NULL,
    name TEXT NOT NULL,
    status TEXT CHECK (status IN ('pending', 'running', 'passed', 'failed', 'skipped', 'cancelled')) NOT NULL,
    progress_percentage REAL DEFAULT 0.0,
    total_items INTEGER DEFAULT 0,
    completed_items INTEGER DEFAULT 0,
    passed_items INTEGER DEFAULT 0,
    failed_items INTEGER DEFAULT 0,
    skipped_items INTEGER DEFAULT 0,
    retry_count INTEGER DEFAULT 0,
    max_retries INTEGER DEFAULT 0,
    start_time TIMESTAMP,
    end_time TIMESTAMP,
    duration_ms INTEGER DEFAULT 0,
    error_message TEXT,
    error_details TEXT,
    execution_context TEXT,
    port_number INTEGER,
    device_id TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

**Usage Guidelines**:
- Track real-time execution progress
- Store execution results and metrics
- Support hierarchical execution tracking
- Performance analysis and reporting

## Entity Relationships

### Primary Relationships

1. **Test Suites → Test Cases**: One-to-many relationship
   - `test_suites.suite_id` → `test_cases.suite_id`
   - Cascade delete: Removing a suite deletes all its test cases

2. **Test Cases → Test Case Backups**: One-to-many relationship
   - `test_cases.test_case_id` → `test_case_backups.test_case_id`
   - Maintains version history and backup records

3. **Test Cases → Locators Repository**: One-to-many relationship
   - `test_cases.test_case_id` → `locators_repository.test_case_id`
   - Tracks locator usage and optimization

4. **Execution Tracker Hierarchy**: Self-referencing relationship
   - `execution_tracker.execution_id` → `execution_tracker.parent_execution_id`
   - Supports nested execution tracking (suite → case → step)

### Cross-Database Relationships

- **Settings ↔ Globals**: Complementary configuration storage
- **Environments ↔ Execution Tracker**: Environment context for executions
- **Test Suites ↔ Execution Tracker**: Execution tracking for test artifacts

## Data Flow and Integration

### File System Integration

1. **Test Cases**: JSON files in `test_cases_ios/` or `test_cases_android/`
2. **Test Suites**: JSON files in `test_suites_ios/` or `test_suites_android/`
3. **Settings**: Configuration files and UI preferences
4. **Reports**: Generated in `reports_ios/` or `reports_android/`

### Synchronization Strategy

1. **Dual Storage**: Maintain both JSON files and database records
2. **Database as Source of Truth**: Database takes precedence for conflicts
3. **Automatic Sync**: UI changes trigger both file and database updates
4. **Backup Strategy**: Automatic backups before major operations

## Migration and Maintenance

### Database Migration Process

1. **Schema Updates**: Use ALTER TABLE statements for schema changes
2. **Data Migration**: Preserve existing data during structure changes
3. **Backup Strategy**: Create backups before migrations
4. **Rollback Plan**: Maintain rollback scripts for failed migrations

### Maintenance Tasks

1. **Regular Cleanup**: Remove old execution records and backups
2. **Index Optimization**: Monitor and optimize database indexes
3. **Data Validation**: Regular integrity checks and validation
4. **Performance Monitoring**: Track query performance and optimization

## Security and Access Control

### Data Protection

1. **File Permissions**: Restrict database file access
2. **Connection Security**: Use secure database connections
3. **Data Validation**: Validate all input data
4. **Audit Trail**: Track all data modifications

### Backup and Recovery

1. **Automated Backups**: Regular database backups
2. **Point-in-Time Recovery**: Restore to specific timestamps
3. **Cross-Platform Sync**: Maintain consistency across platforms
4. **Disaster Recovery**: Complete system restoration procedures

## Performance Optimization

### Indexing Strategy

1. **Primary Keys**: Automatic indexing on all primary keys
2. **Foreign Keys**: Indexes on all foreign key relationships
3. **Query Optimization**: Indexes on frequently queried columns
4. **Composite Indexes**: Multi-column indexes for complex queries

### Query Optimization

1. **Prepared Statements**: Use parameterized queries
2. **Connection Pooling**: Efficient database connection management
3. **Batch Operations**: Group related operations for efficiency
4. **Caching Strategy**: Cache frequently accessed data

## Troubleshooting

### Common Issues

1. **Database Locks**: Handle concurrent access properly
2. **Schema Mismatches**: Validate schema consistency
3. **Data Corruption**: Regular integrity checks
4. **Performance Issues**: Monitor and optimize queries

### Diagnostic Tools

1. **SQLite CLI**: Direct database inspection
2. **Schema Validation**: Automated schema checking
3. **Data Integrity**: Constraint validation
4. **Performance Profiling**: Query execution analysis

---

*Last Updated: August 30, 2024*
*Version: 2.0*
*Platform: iOS/Android Mobile App Automation*