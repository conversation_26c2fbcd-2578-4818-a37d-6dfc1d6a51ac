# Environment Variables for Mobile App Automation
# Copy this file to .env and fill in your API keys

# =============================================================================
# AI-Powered Test Healing Configuration
# =============================================================================

# Together AI Configuration (Recommended - Free Models Available)
# 1. Go to: https://api.together.xyz/
# 2. Sign up for free (no credit card required)
# 3. Copy your API key and replace the value below
TOGETHER_API_KEY=your_together_ai_api_key_here
TOGETHER_BASE_URL=https://api.together.xyz/v1

# IMPORTANT: Replace 'your_together_ai_api_key_here' with your actual API key!

# Alternative AI Providers (Optional)
# Uncomment and configure if you prefer other providers

# OpenAI Configuration
# OPENAI_API_KEY=your_openai_api_key_here

# Anthropic Configuration  
# ANTHROPIC_API_KEY=your_anthropic_api_key_here

# =============================================================================
# AI Model Configuration
# =============================================================================

# Together AI Free Models (Choose one)
# AI_MODEL_PROVIDER=together
# AI_MODEL_NAME=meta-llama/Llama-2-7b-chat-hf          # Llama 2 7B (Free)
# AI_MODEL_NAME=meta-llama/Llama-2-13b-chat-hf         # Llama 2 13B (Free)
# AI_MODEL_NAME=mistralai/Mistral-7B-Instruct-v0.1     # Mistral 7B (Free)
# AI_MODEL_NAME=togethercomputer/RedPajama-INCITE-7B-Chat  # RedPajama 7B (Free)

# =============================================================================
# Performance Optimization Settings
# =============================================================================

# Screenshot Optimization
SCREENSHOT_FREQUENCY_LIMIT=5.0          # Minimum seconds between screenshots
SCREENSHOT_MAX_PER_MINUTE=12            # Maximum screenshots per minute
DISABLE_SCREENSHOTS=false               # Set to true to disable all screenshots

# Health Check Optimization  
HEALTH_CHECK_INTERVAL=30.0              # Health check interval in seconds
HEALTH_CHECK_SMART_SUSPENSION=true      # Enable intelligent health check suspension

# AI Healing Settings
AI_HEALING_ENABLED=true                 # Enable AI-powered test healing
AI_HEALING_MAX_ATTEMPTS=3               # Maximum healing attempts per failure
AI_HEALING_CONFIDENCE_THRESHOLD=0.6     # Minimum confidence for healing suggestions

# =============================================================================
# Database Configuration
# =============================================================================

# Database settings (if using custom database)
# DATABASE_URL=sqlite:///automation.db
# DATABASE_HOST=localhost
# DATABASE_PORT=5432
# DATABASE_NAME=mobile_automation
# DATABASE_USER=automation_user
# DATABASE_PASSWORD=your_database_password

# =============================================================================
# Appium Configuration
# =============================================================================

# Default Appium settings
# APPIUM_HOST=127.0.0.1
# APPIUM_PORT=4723
# APPIUM_TIMEOUT=30

# Device Configuration
# ANDROID_DEVICE_ID=your_android_device_id
# IOS_DEVICE_ID=your_ios_device_id
# IOS_BUNDLE_ID=com.yourapp.bundleid

# =============================================================================
# Logging Configuration
# =============================================================================

# Log levels: DEBUG, INFO, WARNING, ERROR, CRITICAL
LOG_LEVEL=INFO
AI_HEALING_LOG_LEVEL=INFO
PERFORMANCE_LOG_LEVEL=INFO

# Log file paths
# LOG_FILE_PATH=logs/automation.log
# AI_HEALING_LOG_FILE=logs/ai_healing.log
# PERFORMANCE_LOG_FILE=logs/performance.log

# =============================================================================
# Development and Testing
# =============================================================================

# Development mode settings
DEBUG_MODE=false
ENABLE_PERFORMANCE_METRICS=true
ENABLE_AI_HEALING_METRICS=true

# Testing settings
TEST_MODE=false
MOCK_AI_RESPONSES=false
SKIP_AI_INITIALIZATION=false

# =============================================================================
# Security Settings
# =============================================================================

# Security settings for production
SECURE_MODE=false
ENCRYPT_LOGS=false
MASK_SENSITIVE_DATA=true

# =============================================================================
# Notes
# =============================================================================

# 1. Together AI offers free access to several open-source models
# 2. Get your free Together AI API key at: https://api.together.xyz/
# 3. Free models include Llama 2, Mistral, and other open-source options
# 4. No credit card required for free tier
# 5. Rate limits apply to free tier but are generous for automation testing

# 6. To use this file:
#    - Copy this file to .env (without .template extension)
#    - Fill in your TOGETHER_API_KEY
#    - Adjust other settings as needed
#    - The .env file will be automatically loaded by the application

# 7. Security:
#    - Never commit .env files to version control
#    - Keep your API keys secure
#    - Use different API keys for development and production
