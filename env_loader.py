
import os
from pathlib import Path

def load_environment_variables():
    """Load environment variables from .env file"""
    try:
        from dotenv import load_dotenv
        
        # Get the project root directory
        project_root = Path(__file__).parent.parent if hasattr(Path(__file__), 'parent') else Path.cwd()
        env_file = project_root / ".env"
        
        if env_file.exists():
            load_dotenv(env_file)
            print(f"✅ Loaded environment variables from {env_file}")
            
            # Verify TOGETHER_API_KEY is loaded
            api_key = os.getenv('TOGETHER_API_KEY')
            if api_key:
                print(f"✅ TOGETHER_API_KEY loaded: {api_key[:10]}...")
                return True
            else:
                print("❌ TOGETHER_API_KEY not found after loading .env")
                return False
        else:
            print(f"❌ .env file not found at {env_file}")
            return False
            
    except ImportError:
        print("❌ python-dotenv not available")
        return False
    except Exception as e:
        print(f"❌ Error loading environment: {e}")
        return False

# Auto-load when imported
if __name__ != "__main__":
    load_environment_variables()
