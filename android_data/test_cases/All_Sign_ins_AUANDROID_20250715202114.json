{"name": "All Sign ins_AU-ANDROID", "created": "2025-08-19 18:02:50", "device_id": "PJTCI7EMSSONYPU8", "actions": [{"cleanup": false, "display_depth": 0, "interval": 0.5, "loading_in_progress": false, "locator_type": "accessibility_id", "locator_value": "txtOnePassOnboardingSkipForNow", "method": "locator", "steps_loaded": true, "test_case_id": "Onboarding-Start-AU.json", "test_case_name": "Onboarding-Start-AU", "test_case_steps": [{"function_name": "clear_app", "package_name": "au.com.kmart", "timestamp": 1755074292641, "type": "androidFunctions"}, {"package_id": "au.com.kmart", "timestamp": 1755074261875, "type": "launchApp"}, {"interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnOnboardingLocationLaterButton", "method": "locator", "timeout": 10, "timestamp": 1755074478382, "type": "tap"}, {"interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnOnboardingLocationLaterButton", "method": "locator", "timeout": 10, "timestamp": 1755074501686, "type": "tap"}, {"interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnMayBeLater", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtOnePassOnboardingSkipForNow", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}], "test_case_steps_count": 6, "timeout": 10, "timestamp": *************, "type": "multiStep"}, {"action_id": "qA1ap4n1m4", "executionTime": "2572ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "nshEZeNkzs", "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "KmartSigninAUANDROID_20250709191752.json", "test_case_name": "Kmart-Signin-AU-ANDROID", "test_case_steps": [{"action_id": "HEBUNq0P6l", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "3963ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "AtwFJEKbDH", "enter": false, "function_name": "text", "text": "<EMAIL>", "timestamp": *************, "type": "text"}, {"action_id": "zxSSc6NS3A", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Continue to password\"]", "method": "locator", "timeout": 10, "timestamp": 1754699352537, "type": "tap"}, {"action_id": "TkyyzFSlcu", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"password\"]", "method": "locator", "timeout": 10, "timestamp": 1751405157652, "type": "tap"}, {"action_id": "K7yV3GGsgr", "enter": true, "executionTime": "3905ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "text": "Wonderbaby@6", "timeout": 10, "timestamp": 1745666199850, "type": "text"}, {"action_id": "QugrQS9qYH", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Sign in\"]", "method": "locator", "timeout": 10, "timestamp": 1754709806783, "type": "tap"}], "test_case_steps_count": 7, "timestamp": 1752575011605, "type": "multiStep"}, {"action_id": "EDHl0X27Wi", "executionTime": "5013ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtHomeGreetingText\"]", "timeout": 10, "timestamp": 1746068337374, "type": "waitTill"}, {"action_id": "F0gZF1jEnT", "executionTime": "280ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746068378268, "type": "tap"}, {"count": 2, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1755589121730, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7], "executionTime": "30133ms"}, {"action_id": "bGo3feCwBQ", "executionTime": "1355ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1746068479716, "type": "tap"}, {"action_id": "IvqPpScAJa", "executionTime": "354ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 3 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746068564144, "type": "tap"}, {"action_id": "WlISsMf9QA", "double_tap": false, "executionTime": "13712ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtLog in\"]", "method": "locator", "text_to_find": "Log", "timeout": 10, "timestamp": 1746068627313, "type": "tap"}, {"action_id": "ZGVncEc5o1", "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "KmartSigninAUANDROID_20250709191752.json", "test_case_name": "Kmart-Signin-AU-ANDROID", "test_case_steps": [{"action_id": "HEBUNq0P6l", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "3963ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "AtwFJEKbDH", "enter": false, "function_name": "text", "text": "<EMAIL>", "timestamp": *************, "type": "text"}, {"action_id": "zxSSc6NS3A", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Continue to password\"]", "method": "locator", "timeout": 10, "timestamp": 1754699352537, "type": "tap"}, {"action_id": "TkyyzFSlcu", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"password\"]", "method": "locator", "timeout": 10, "timestamp": 1751405157652, "type": "tap"}, {"action_id": "K7yV3GGsgr", "enter": true, "executionTime": "3905ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "text": "Wonderbaby@6", "timeout": 10, "timestamp": 1745666199850, "type": "text"}, {"action_id": "QugrQS9qYH", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Sign in\"]", "method": "locator", "timeout": 10, "timestamp": 1754709806783, "type": "tap"}], "test_case_steps_count": 7, "timestamp": 1752575453584, "type": "multiStep"}, {"action_id": "IvqPpScAJa", "executionTime": "1105ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 1 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1753570065556, "type": "tap"}, {"action_id": "EDHl0X27Wi", "executionTime": "327ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtHomeGreetingText\"]", "timeout": 10, "timestamp": 1752575505301, "type": "waitTill"}, {"action_id": "F0gZF1jEnT", "executionTime": "14154ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752575515566, "type": "tap"}, {"count": 2, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1755589101828, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7], "executionTime": "1877ms"}, {"action_id": "bGo3feCwBQ", "executionTime": "1491ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1752575580844, "type": "tap"}, {"action_id": "4PZC1vVWJW", "double_tap": false, "executionTime": "26890ms", "image_filename": "homepage-search-se.png", "method": "image", "text_to_find": "Find", "threshold": 0.7, "timeout": 30, "timestamp": 1746069108697, "type": "tapOnText"}, {"action_id": "aRgHcQcLDP", "enter": true, "executionTime": "2691ms", "function_name": "text", "text": "Uno card", "timestamp": 1746069162268, "type": "text"}, {"action_id": "FLVgc6jpIu", "function_name": "send_key_event", "key_event": "ENTER", "timestamp": 1754742098319, "type": "androidFunctions", "executionTime": "13041ms"}, {"action_id": "YC6bBrKQgq", "executionTime": "6015ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Filter\"]", "timeout": 30, "timestamp": 1746078698825, "type": "waitTill"}, {"action_id": "BTYxjEaZEk", "executionTime": "25712ms", "image_filename": "Unocard-product-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "((//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView[1])[1]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746069249053, "type": "tap"}, {"action_id": "6zUBxjSFym", "executionTime": "3282ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[contains(@text,\"SKU\")]", "timeout": 30, "timestamp": 1746069314833, "type": "waitTill"}, {"action_id": "mcscWdhpn2", "count": 4, "direction": "up", "duration": 600, "end_x": 50, "end_y": 30, "executionTime": "7787ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[contains(@text,\"Already a member\")]", "start_x": 50, "start_y": 70, "timestamp": 1751713871587, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "4PZC1vVWJW", "double_tap": false, "executionTime": "2130ms", "image_filename": "homepage-search-se.png", "method": "image", "text_to_find": "Sign", "threshold": 0.7, "timeout": 30, "timestamp": 1750327000121, "type": "tapOnText"}, {"action_id": "VK2oI6mXSB", "executionTime": "2612ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"email-input\"]", "timeout": 10, "timestamp": 1750999390597, "type": "waitTill"}, {"action_id": "50Z2jrodNd", "executionTime": "261ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"email-input\"]", "method": "locator", "timeout": 10, "timestamp": 1750995710781, "type": "tap"}, {"action_id": "wuIMlAwYVA", "enter": true, "executionTime": "2601ms", "function_name": "text", "text": "<EMAIL>", "timestamp": 1749383618709, "type": "text"}, {"action_id": "KGagCNh4k8", "function_name": "send_key_event", "key_event": "ENTER", "timestamp": 1754742169325, "type": "androidFunctions", "executionTime": "132ms"}, {"action_id": "F4PAASAS6q", "locator_type": "xpath", "locator_value": "//android.widget.CheckBox[@resource-id=\"recaptcha-anchor\"]", "timeout": 15, "timestamp": 1753571783037, "type": "tapIfLocatorExists", "executionTime": "15080ms"}, {"action_id": "SHaIduBnay", "executionTime": "818ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"password-input\"]", "method": "locator", "timeout": 10, "timestamp": 1750995734361, "type": "tap"}, {"action_id": "N2yjynioko", "enter": true, "executionTime": "2569ms", "function_name": "text", "text": "Wonderbaby@5", "timestamp": 1746073544849, "type": "text"}, {"action_id": "g6SvrkfA9z", "function_name": "send_key_event", "key_event": "ENTER", "timestamp": 1754742159308, "type": "androidFunctions", "executionTime": "142ms"}, {"action_id": "XcWXIMtv1E", "duration": 5, "executionTime": "5075ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Add to bag", "method": "locator", "time": 5, "timeout": 10, "timestamp": 1752204317021, "type": "wait"}, {"action_id": "F0gZF1jEnT", "executionTime": "11724ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752576463739, "type": "tap"}, {"count": 2, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1755589082725, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7], "executionTime": "1789ms"}, {"action_id": "bGo3feCwBQ", "executionTime": "2069ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "F0gZF1jEnT", "executionTime": "171ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "L6wTorOX8B", "executionTime": "16916ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtMoreAccountCtaSignIn\"]", "method": "locator", "timeout": 15, "timestamp": *************, "type": "tap"}, {"action_id": "kR0cfY8jim", "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "KmartSigninAUANDROID_20250709191752.json", "test_case_name": "Kmart-Signin-AU-ANDROID", "test_case_steps": [{"action_id": "HEBUNq0P6l", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "3963ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "AtwFJEKbDH", "enter": false, "function_name": "text", "text": "<EMAIL>", "timestamp": *************, "type": "text"}, {"action_id": "zxSSc6NS3A", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Continue to password\"]", "method": "locator", "timeout": 10, "timestamp": 1754699352537, "type": "tap"}, {"action_id": "TkyyzFSlcu", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"password\"]", "method": "locator", "timeout": 10, "timestamp": 1751405157652, "type": "tap"}, {"action_id": "K7yV3GGsgr", "enter": true, "executionTime": "3905ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "text": "Wonderbaby@6", "timeout": 10, "timestamp": 1745666199850, "type": "text"}, {"action_id": "QugrQS9qYH", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Sign in\"]", "method": "locator", "timeout": 10, "timestamp": 1754709806783, "type": "tap"}], "test_case_steps_count": 7, "timestamp": 1752576595578, "type": "multiStep"}, {"count": 2, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1755589037658, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7], "executionTime": "1809ms"}, {"action_id": "bGo3feCwBQ", "executionTime": "1520ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1752576822616, "type": "tap"}, {"action_id": "F0gZF1jEnT", "executionTime": "25265ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 1 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752576865176, "type": "tap"}, {"action_id": "4PZC1vVWJW", "double_tap": false, "executionTime": "3430ms", "image_filename": "homepage-search-se.png", "method": "image", "text_to_find": "Find", "threshold": 0.7, "timeout": 30, "timestamp": 1750324373627, "type": "tapOnText"}, {"action_id": "aRgHcQcLDP", "enter": true, "executionTime": "2639ms", "function_name": "text", "text": "Uno card", "timestamp": 1750324384312, "type": "text"}, {"action_id": "GMdwqXyeMs", "function_name": "send_key_event", "key_event": "ENTER", "timestamp": 1754742136750, "type": "androidFunctions", "executionTime": "159ms"}, {"action_id": "YC6bBrKQgq", "executionTime": "6075ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Filter\"]", "timeout": 30, "timestamp": 1750324411955, "type": "waitTill"}, {"action_id": "BTYxjEaZEk", "executionTime": "358ms", "image_filename": "Unocard-product-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "((//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView[1])[1]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1750324420234, "type": "tap"}, {"action_id": "K2w9XUGwnb", "count": 3, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "30867ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Add to bag\"]", "method": "locator", "start_x": 50, "start_y": 70, "timeout": 10, "timestamp": 1750324450331, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "S1cQQxksEj", "executionTime": "327ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Add to bag\"]", "method": "locator", "timeout": 10, "timestamp": 1752204287597, "type": "tap", "x": 0, "y": 0}, {"action_id": "XcWXIMtv1E", "duration": 5, "executionTime": "5069ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Add to bag", "method": "locator", "time": 5, "timeout": 10, "timestamp": 1752576632298, "type": "wait"}, {"action_id": "F0gZF1jEnT", "executionTime": "4755ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752577045368, "type": "tap"}, {"action_id": "V9ldRojdyD", "executionTime": "908ms", "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"Checkout\"]", "timeout": 10, "timestamp": 1752577318811, "type": "tapIfLocatorExists"}, {"action_id": "g8u66qfKkX", "executionTime": "36840ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@text=\"Delivery\"]", "timeout": 25, "timestamp": 1750324747501, "type": "waitTill"}, {"action_id": "ZZ1yenkiIl", "executionTime": "349ms", "image_filename": "delivery-tab-android.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@text=\"Delivery\"]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1752577669150, "type": "tap"}, {"count": 2, "direction": "up", "duration": 500, "end_x": 50, "end_y": 30, "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"Continue\")]", "method": "coordinates", "start_x": 50, "start_y": 70, "timestamp": 1755589374853, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7], "x": 0, "y": 0, "executionTime": "1573ms"}, {"action_id": "iWRZoQx4qd", "executionTime": "2197ms", "image_filename": "continue-to-details-android.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"Continue\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1755589455509, "type": "tap"}, {"action_id": "6PL8P3rT57", "executionTime": "1885ms", "text_to_find": "Sign", "timeout": 30, "timestamp": 1750325569588, "type": "tapOnText"}, {"action_id": "kR0cfY8jim", "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "KmartSigninAUANDROID_20250709191752.json", "test_case_name": "Kmart-Signin-AU-ANDROID", "test_case_steps": [{"action_id": "HEBUNq0P6l", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "3963ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "AtwFJEKbDH", "enter": false, "function_name": "text", "text": "<EMAIL>", "timestamp": *************, "type": "text"}, {"action_id": "zxSSc6NS3A", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Continue to password\"]", "method": "locator", "timeout": 10, "timestamp": 1754699352537, "type": "tap"}, {"action_id": "TkyyzFSlcu", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"password\"]", "method": "locator", "timeout": 10, "timestamp": 1751405157652, "type": "tap"}, {"action_id": "K7yV3GGsgr", "enter": true, "executionTime": "3905ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "text": "Wonderbaby@6", "timeout": 10, "timestamp": 1745666199850, "type": "text"}, {"action_id": "QugrQS9qYH", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Sign in\"]", "method": "locator", "timeout": 10, "timestamp": 1754709806783, "type": "tap"}], "test_case_steps_count": 7, "timestamp": 1752578490004, "type": "multiStep"}, {"action_id": "V9ldRojdyD", "executionTime": "3941ms", "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"Checkout\"]", "timeout": 10, "timestamp": 1752578543597, "type": "tapIfLocatorExists"}, {"action_id": "g8u66qfKkX", "executionTime": "10908ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@text=\"Delivery\"]", "timeout": 25, "timestamp": 1752578598049, "type": "waitTill"}, {"action_id": "ZZ1yenkiIl", "executionTime": "280ms", "image_filename": "delivery-tab-android.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@text=\"Delivery\"]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1752578607020, "type": "tap"}, {"action_id": "1NWfFsDiTQ", "executionTime": "26964ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"Remove\")]", "method": "locator", "timeout": 10, "timestamp": 1750326180016, "type": "tap"}, {"action_id": "CkfAScJNq8", "executionTime": "15199ms", "image_filename": "bag-close-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1750379336861, "type": "tap"}, {"action_id": "F0gZF1jEnT", "executionTime": "704ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752578783073, "type": "tap"}, {"count": 2, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1755589052267, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7], "executionTime": "1808ms"}, {"action_id": "bGo3feCwBQ", "executionTime": "32576ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1752578938505, "type": "tap"}, {"action_id": "F0gZF1jEnT", "executionTime": "226ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 1 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752579068838, "type": "tap"}], "labels": [], "created_at": "2025-08-19T18:02:50.093503", "modified_at": "2025-08-19T18:02:50.093503"}