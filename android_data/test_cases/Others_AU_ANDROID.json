{"name": "Others AU ANDROID", "created": "2025-08-24 08:17:34", "device_id": "PJTCI7EMSSONYPU8", "actions": [{"cleanup": false, "display_depth": 0, "loading_in_progress": false, "method": "coordinates", "steps_loaded": true, "test_case_id": "Onboarding-Start-AU.json", "test_case_name": "Onboarding-Start-AU", "test_case_steps": [{"function_name": "clear_app", "package_name": "au.com.kmart", "timestamp": 1755074292641, "type": "androidFunctions"}, {"package_id": "au.com.kmart", "timestamp": 1755074261875, "type": "launchApp"}, {"interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnOnboardingLocationLaterButton", "method": "locator", "timeout": 10, "timestamp": 1755074478382, "type": "tap"}, {"interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnOnboardingLocationLaterButton", "method": "locator", "timeout": 10, "timestamp": 1755074501686, "type": "tap"}, {"interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnMayBeLater", "method": "locator", "timeout": 10, "timestamp": 1755074524127, "type": "tap"}, {"interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtOnePassOnboardingSkipForNow", "method": "locator", "timeout": 10, "timestamp": 1755074573524, "type": "tap"}], "test_case_steps_count": 6, "timestamp": 1755598698868, "type": "multiStep", "x": 0, "y": 0}, {"action_id": "Dzn2Q7JTe0", "executionTime": "14965ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"btnBarcodeScanner\"]", "method": "locator", "timeout": 20, "timestamp": 1747038761695, "type": "tap"}, {"action_id": "RlDZFks4Lc", "executionTime": "190ms", "function_name": "alert_accept", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"While using the app\")]", "method": "locator", "timeout": 20, "timestamp": 1747038830836, "type": "tap"}, {"action_id": "F4NGh9HrLw", "executionTime": "151ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"Barcode Scanner\"]", "method": "locator", "timeout": 10, "timestamp": 1746830724911, "type": "exists"}, {"action_id": "RbNtEW6N9T", "executionTime": "160ms", "locator_type": "xpath", "locator_value": "//android.widget.ImageView[@content-desc=\"imgHelp\"]", "text_to_find": "Living", "timeout": 10, "timestamp": 1746830828429, "type": "exists"}, {"action_id": "xUbWFa8Ok2", "double_tap": false, "executionTime": "139ms", "locator_type": "xpath", "locator_value": "//android.view.View[contains(@content-desc,\"rectangle frame to view helpful product information\")]", "text_to_find": "Shop", "timeout": 10, "timestamp": 1746830873534, "type": "exists"}, {"action_id": "74XW7x54ad", "executionTime": "184ms", "image_filename": "env[device-back-img]", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[@content-desc=\"imgBackArrow\"]", "method": "locator", "threshold": 0.7, "timeout": 20, "timestamp": 1749457134837, "type": "tap"}, {"action_id": "F4NGh9HrLw", "executionTime": "281ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746833833911, "type": "tap"}, {"action_id": "eHLWiRoqqS", "count": 1, "direction": "up", "double_tap": false, "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "1080ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtTrack My Order\"]", "method": "locator", "start_x": 50, "start_y": 70, "text_to_find": "Track", "timeout": 10, "timestamp": 1746835476969, "type": "tap", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "OmKfD9iBjD", "double_tap": false, "executionTime": "4353ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"orderId\"]", "method": "locator", "text_to_find": "Order", "timeout": 10, "timestamp": 1746835134218, "type": "tap"}, {"action_id": "7YbjwQH1Jc", "enter": false, "executionTime": "3040ms", "text": "*********", "timestamp": 1747039240064, "type": "text"}, {"action_id": "aNN0yYFLEd", "executionTime": "650ms", "function_name": "text", "image_filename": "prodcut-share-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"email\"]", "method": "locator", "threshold": 0.7, "timeout": 30, "timestamp": 1746831868369, "type": "tap"}, {"action_id": "XJv08Gkucs", "enter": false, "executionTime": "4383ms", "text": "<EMAIL>", "timestamp": 1747039307941, "type": "text"}, {"action_id": "aNN0yYFLEd", "executionTime": "667ms", "function_name": "text", "image_filename": "prodcut-share-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Search for order\"]", "method": "locator", "threshold": 0.7, "timeout": 30, "timestamp": 1755599308679, "type": "tap"}, {"action_id": "83tV9A4NOn", "executionTime": "809ms", "locator_type": "xpath", "locator_value": "//android.view.View[@text=\"Order ********* is refunded\"]", "timeout": 10, "timestamp": *************, "type": "exists"}, {"action_id": "GEMv6goQtW", "executionTime": "12328ms", "image_filename": "android-app-back.png", "method": "image", "threshold": 0.7, "timeout": 30, "timestamp": *************, "type": "tap"}, {"action_id": "gekNSY5O2E", "double_tap": false, "executionTime": "2483ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtMoreAccountCtaSignIn\"]", "method": "locator", "text_to_find": "Sign", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "s0WyiD1w0B", "cleanup": false, "display_depth": 0, "executionTime": "1190ms", "function_name": "alert_accept", "image_filename": "banner-close-updated.png", "loading_in_progress": false, "method": "image", "steps_loaded": true, "test_case_id": "KmartSigninAUANDROID_20250709191752.json", "test_case_name": "Kmart-Signin-AU-ANDROID", "test_case_steps": [{"action_id": "HEBUNq0P6l", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "3963ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "AtwFJEKbDH", "enter": false, "function_name": "text", "text": "<EMAIL>", "timestamp": 1749384094911, "type": "text"}, {"action_id": "zxSSc6NS3A", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Continue to password\"]", "method": "locator", "timeout": 10, "timestamp": 1754699352537, "type": "tap"}, {"action_id": "TkyyzFSlcu", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"password\"]", "method": "locator", "timeout": 10, "timestamp": 1751405157652, "type": "tap"}, {"action_id": "K7yV3GGsgr", "enter": true, "executionTime": "3905ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "text": "Wonderbaby@6", "timeout": 10, "timestamp": 1745666199850, "type": "text"}, {"action_id": "QugrQS9qYH", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Sign in\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}], "test_case_steps_count": 7, "threshold": 0.7, "timeout": 30, "timestamp": *************, "type": "multiStep"}, {"action_id": "ShJSdXvmVL", "count": 3, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "3603ms", "interval": 1, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtMy OnePass Account\"]", "start_x": 50, "start_y": 70, "timeout": 10, "timestamp": *************, "type": "exists", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "pk2DLZFBmx", "executionTime": "271ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtMy OnePass Account\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "DhWa2PCBXE", "executionTime": "682ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtOnePassSubscritionBox\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "exists"}, {"action_id": "GEMv6goQtW", "executionTime": "4263ms", "image_filename": "android-app-back.png", "method": "image", "threshold": 0.7, "timeout": 30, "timestamp": *************, "type": "tap"}, {"interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtMy orders & receipts\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "inrxgdWzXr", "double_tap": false, "executionTime": "6717ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Learn more about <PERSON><PERSON>", "method": "locator", "text_to_find": "Store", "timeout": 30, "timestamp": 1747039844368, "type": "tapOnText"}, {"action_id": "P4b2BITpCf", "executionTime": "1511ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//android.widget.GridView/android.view.View/android.view.View[2])[1]", "method": "locator", "timeout": 10, "timestamp": 1746832583435, "type": "exists"}, {"action_id": "zdh8hKYC1a", "executionTime": "876ms", "image_filename": "deviceback-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//android.widget.GridView/android.view.View/android.view.View[2])[1]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746832601515, "type": "tap"}, {"action_id": "q6cKxgMAIn", "executionTime": "506ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.webkit.WebView[@text=\"Slyp Receipt\"]", "method": "locator", "timeout": 10, "timestamp": 1746832657816, "type": "exists"}, {"action_id": "XjclKOaCTh", "executionTime": "496ms", "image_filename": "keyboard_done_iphoneSE.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"<PERSON>\"]", "method": "locator", "package_id": "au.com.kmart", "threshold": 0.7, "timeout": 10, "timestamp": 1746832702071, "type": "terminateApp"}, {"action_id": "U48qCNydwd", "executionTime": "241ms", "package_id": "au.com.kmart", "timestamp": 1747042054721, "type": "launchApp"}, {"action_id": "UoH0wdtcLk", "double_tap": false, "executionTime": "4063ms", "image_filename": "homepage_editbtn-se.png", "method": "image", "text_to_find": "Edit", "threshold": 0.7, "timeout": 30, "timestamp": 1747042435305, "type": "tapOnText"}, {"action_id": "jmKjclMUWT", "executionTime": "16392ms", "text_to_find": "current", "timeout": 30, "timestamp": 1747042463732, "type": "tapOnText"}, {"action_id": "RlDZFks4Lc", "executionTime": "212ms", "function_name": "alert_accept", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"While using the app\")]", "method": "locator", "timeout": 20, "timestamp": 1755600384421, "type": "tap"}, {"executionTime": "1630ms", "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"btnSaveOrContinue\"]", "timeout": 10, "timestamp": 1755600439578, "type": "tapIfLocatorExists"}, {"action_id": "F4NGh9HrLw", "executionTime": "28096ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1747041764396, "type": "tap"}, {"action_id": "qHdMgerbTE", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "2758ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1748313766307, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "2p13JoJbbA", "executionTime": "17689ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1746834873588, "type": "tap"}, {"action_id": "XjclKOaCTh", "executionTime": "474ms", "image_filename": "keyboard_done_iphoneSE.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"<PERSON>\"]", "method": "locator", "package_id": "au.com.kmart", "threshold": 0.7, "timeout": 10, "timestamp": 1755600589105, "type": "terminateApp"}], "labels": [], "created_at": "2025-08-24T08:17:34.747113", "modified_at": "2025-08-24T08:17:34.747113"}