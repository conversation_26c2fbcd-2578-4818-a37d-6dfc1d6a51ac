{"name": "WishList_AU-Android", "created": "2025-08-23 15:15:29", "device_id": "PJTCI7EMSSONYPU8", "actions": [{"action_id": "HotUJOd6oB", "cleanup": false, "display_depth": 0, "executionTime": "289ms", "loading_in_progress": false, "package_id": "au.com.kmart", "steps_loaded": true, "test_case_id": "Onboarding-Start-AU.json", "test_case_name": "Onboarding-Start-AU", "test_case_steps": [{"function_name": "clear_app", "package_name": "au.com.kmart", "timestamp": 1755074292641, "type": "androidFunctions"}, {"package_id": "au.com.kmart", "timestamp": 1755074261875, "type": "launchApp"}, {"interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnOnboardingLocationLaterButton", "method": "locator", "timeout": 10, "timestamp": 1755074478382, "type": "tap"}, {"interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnOnboardingLocationLaterButton", "method": "locator", "timeout": 10, "timestamp": 1755074501686, "type": "tap"}, {"interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnMayBeLater", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtOnePassOnboardingSkipForNow", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}], "test_case_steps_count": 6, "timestamp": *************, "type": "multiStep"}, {"action_id": "rkL0oz4kiL", "executionTime": "2301ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "rUH3kvaEH9", "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "KmartSigninAUANDROID_20250709191752.json", "test_case_name": "Kmart-Signin-AU-ANDROID", "test_case_steps": [{"action_id": "HEBUNq0P6l", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "3963ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "AtwFJEKbDH", "enter": false, "function_name": "text", "text": "<EMAIL>", "timestamp": *************, "type": "text"}, {"action_id": "zxSSc6NS3A", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Continue to password\"]", "method": "locator", "timeout": 10, "timestamp": 1754699352537, "type": "tap"}, {"action_id": "TkyyzFSlcu", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"password\"]", "method": "locator", "timeout": 10, "timestamp": 1751405157652, "type": "tap"}, {"action_id": "K7yV3GGsgr", "enter": true, "executionTime": "3905ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "text": "Wonderbaby@6", "timeout": 10, "timestamp": 1745666199850, "type": "text"}, {"action_id": "QugrQS9qYH", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Sign in\"]", "method": "locator", "timeout": 10, "timestamp": 1754709806783, "type": "tap"}], "test_case_steps_count": 7, "timestamp": 1752618403524, "type": "multiStep"}, {"action_id": "yiKyF5FJwN", "executionTime": "4396ms", "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtHomeGreetingText\"]", "timeout": 30, "timestamp": 1749434558505, "type": "exists"}, {"action_id": "rqLJpAP0mA", "double_tap": false, "executionTime": "2350ms", "image_filename": "homepage-search-se.png", "method": "image", "text_to_find": "Find", "threshold": 0.7, "timeout": 30, "timestamp": 1745980305734, "type": "tapOnText"}, {"action_id": "sc2KH9bG6H", "enter": true, "executionTime": "24125ms", "function_name": "text", "text": "Uno card", "timestamp": 1745484826180, "type": "text"}, {"action_id": "oy4DKDdbeL", "executionTime": "148ms", "function_name": "send_key_event", "key_event": "ENTER", "timestamp": 1754742303939, "type": "androidFunctions"}, {"action_id": "nAB6Q8LAdv", "executionTime": "23003ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Filter\"]", "timeout": 30, "timestamp": 1745485041367, "type": "waitTill"}, {"action_id": "eLxHVWKeDQ", "executionTime": "427ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "((//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView[1])[1]", "method": "locator", "timeout": 10, "timestamp": 1746836741255, "type": "tap"}, {"action_id": "ITHvSyXXmu", "executionTime": "3260ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[contains(@text,\"SKU\")]", "timeout": 30, "timestamp": 1755918951848, "type": "waitTill"}, {"action_id": "H3IAmq3r3i", "count": 3, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "4237ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@resource-id=\"wishlist-button\"]", "start_x": 50, "start_y": 70, "timestamp": 1746837343917, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "WbxRVpWtjw", "executionTime": "277ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@resource-id=\"wishlist-button\"]", "method": "locator", "timeout": 10, "timestamp": 1753264299817, "type": "tap"}, {"action_id": "F1olhgKhUt", "executionTime": "383ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 2 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1753268599128, "type": "tap"}, {"action_id": "wzxrm7WwXv", "executionTime": "20784ms", "image_filename": "search-glassimage-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1755917280111, "type": "tap"}, {"action_id": "H3IAmq3r3i", "count": 2, "direction": "up", "duration": 500, "end_x": 50, "end_y": 30, "enter": false, "executionTime": "331ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ListView[contains(@resource-id,\"swiper-wrapper\")]/android.view.View[1]//android.widget.TextView", "start_x": 50, "start_y": 70, "text": "mat", "timestamp": 1753261206509, "type": "text", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "wHCn7sSilS", "executionTime": "140ms", "function_name": "send_key_event", "key_event": "ENTER", "timestamp": 1754742325827, "type": "androidFunctions"}, {"action_id": "nAB6Q8LAdv", "executionTime": "4768ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Filter\"]", "timeout": 30, "timestamp": 1753264251861, "type": "waitTill"}, {"action_id": "eLxHVWKeDQ", "executionTime": "494ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "((//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView[1])[1]", "method": "locator", "timeout": 10, "timestamp": 1755810749723, "type": "tap"}, {"action_id": "ITHvSyXXmu", "executionTime": "9482ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[contains(@text,\"SKU\")]", "timeout": 30, "timestamp": 1746837237441, "type": "waitTill"}, {"action_id": "H3IAmq3r3i", "count": 3, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "2361ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@resource-id=\"wishlist-button\"]", "start_x": 50, "start_y": 70, "timestamp": 1753264283001, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "WbxRVpWtjw", "executionTime": "268ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@resource-id=\"wishlist-button\"]", "method": "locator", "timeout": 10, "timestamp": 1746837373321, "type": "tap"}, {"action_id": "F1olhgKhUt", "executionTime": "362ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 2 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1755917271675, "type": "tap"}, {"action_id": "wzxrm7WwXv", "executionTime": "25301ms", "image_filename": "search-glassimage-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1753267660276, "type": "tap"}, {"action_id": "H3IAmq3r3i", "count": 2, "direction": "up", "duration": 500, "end_x": 50, "end_y": 30, "enter": false, "executionTime": "445ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ListView[contains(@resource-id,\"swiper-wrapper\")]/android.view.View[1]//android.widget.TextView", "start_x": 50, "start_y": 70, "text": "notepads", "timestamp": 1755917302186, "type": "text", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "wHCn7sSilS", "executionTime": "134ms", "function_name": "send_key_event", "key_event": "ENTER", "timestamp": 1755917293625, "type": "androidFunctions"}, {"action_id": "nAB6Q8LAdv", "executionTime": "7445ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Filter\"]", "timeout": 30, "timestamp": 1755917336550, "type": "waitTill"}, {"action_id": "eLxHVWKeDQ", "executionTime": "1274ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "((//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView[1])[1]", "method": "locator", "timeout": 10, "timestamp": 1755917341293, "type": "tap"}, {"action_id": "ITHvSyXXmu", "executionTime": "10777ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[contains(@text,\"SKU\")]", "timeout": 30, "timestamp": 1755918973657, "type": "waitTill"}, {"action_id": "H3IAmq3r3i", "count": 3, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "2887ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@resource-id=\"wishlist-button\"]", "start_x": 50, "start_y": 70, "timestamp": 1755917353833, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "WbxRVpWtjw", "executionTime": "576ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@resource-id=\"wishlist-button\"]", "method": "locator", "timeout": 10, "timestamp": 1755917346963, "type": "tap"}, {"action_id": "F1olhgKhUt", "executionTime": "516ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 3 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1748258009231, "type": "tap"}, {"action_id": "Q0fomJIDoQ", "executionTime": "1856ms", "image_filename": "banner-close-updated.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//android.widget.Button[@content-desc=\"txtAdd to Bag\"])[1]/preceding-sibling::android.widget.ImageView[1]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746842287810, "type": "tap"}, {"action_id": "y4i304JeJj", "executionTime": "1750ms", "text_to_find": "Move", "timeout": 30, "timestamp": 1746838675751, "type": "tapOnText"}, {"action_id": "Q0fomJIDoQ", "executionTime": "2039ms", "image_filename": "banner-close-updated.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//android.widget.Button[@content-desc=\"txtAdd to Bag\"])[1]/preceding-sibling::android.widget.ImageView[1]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1752620368500, "type": "tap"}, {"action_id": "<PERSON><PERSON><PERSON>ynQyu", "double_tap": false, "executionTime": "15998ms", "text_to_find": "Remove", "timeout": 30, "timestamp": 1746838738528, "type": "tapOnText"}, {"action_id": "F1olhgKhUt", "executionTime": "936ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1752620415165, "type": "tap"}, {"duration": 5, "executionTime": "5069ms", "time": 5, "timestamp": 1755925225043, "type": "wait"}, {"action_id": "bGqhW1Kciz", "executionTime": "264ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"Checkout\"]", "method": "locator", "timeout": 20, "timestamp": 1752717021234, "type": "tap"}, {"duration": 5, "executionTime": "5069ms", "time": 5, "timestamp": 1755601809810, "type": "wait"}, {"type": "swipeTillVisible", "timestamp": 1755926019564, "locator_value": "//android.widget.Button[@text=\"Move to Wishlist\"]", "locator_type": "xpath", "start_x": 50, "start_y": 70, "end_x": 50, "end_y": 30, "vector_start": [0.5, 0.7], "vector_end": [0.5, 0.3], "duration": 600, "count": 2, "interval": 0.5, "direction": "up"}, {"action_id": "uOt2cFGhGr", "executionTime": "675ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Move to Wishlist\"]", "method": "locator", "timeout": 10, "timestamp": 1746838817503, "type": "tap"}, {"action_id": "lWIRxRm6HE", "executionTime": "427ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[@text=\"Continue shopping\"]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746840700159, "type": "tap"}, {"action_id": "F1olhgKhUt", "executionTime": "1261ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 3 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1752620584233, "type": "tap"}, {"condition": {"locator_type": "xpath", "locator_value": "(//android.widget.Button[@content-desc=\"txtAdd to Bag\"])[1]/preceding-sibling::android.widget.ImageView[1]", "timeout": 10}, "condition_type": "exists", "then_action": {"locator_type": "xpath", "locator_value": "(//android.widget.Button[@content-desc=\"txtAdd to Bag\"])[1]/preceding-sibling::android.widget.ImageView[1]", "method": "locator", "timeout": 10, "type": "tap"}, "timestamp": 1755124925627, "type": "ifThenSteps"}, {"action_id": "<PERSON><PERSON><PERSON>ynQyu", "double_tap": false, "executionTime": "1804ms", "text_to_find": "Remove", "timeout": 30, "timestamp": 1746842300771, "type": "tapOnText"}, {"condition": {"locator_type": "xpath", "locator_value": "(//android.widget.Button[@content-desc=\"txtAdd to Bag\"])[1]/preceding-sibling::android.widget.ImageView[1]", "timeout": 10}, "condition_type": "exists", "then_action": {"locator_type": "xpath", "locator_value": "(//android.widget.Button[@content-desc=\"txtAdd to Bag\"])[1]/preceding-sibling::android.widget.ImageView[1]", "method": "locator", "timeout": 10, "type": "tap"}, "timestamp": 1755124947245, "type": "ifThenSteps"}, {"action_id": "<PERSON><PERSON><PERSON>ynQyu", "double_tap": false, "executionTime": "1647ms", "text_to_find": "Remove", "timeout": 30, "timestamp": 1752620739170, "type": "tapOnText"}, {"action_id": "F1olhgKhUt", "executionTime": "904ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1752620774047, "type": "tap"}, {"count": 1, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1755124417874, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "OyUowAaBzD", "executionTime": "1791ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1746100379226, "type": "tap"}], "labels": [], "created_at": "2025-08-23T15:15:29.507361", "modified_at": "2025-08-23T15:15:29.507361"}