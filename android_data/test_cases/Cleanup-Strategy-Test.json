{"test_case_name": "Cleanup Strategy Test - All Scenarios", "description": "Test case to verify simplified cleanup step execution strategy works in all scenarios", "actions": [{"type": "info", "action_type": "info", "message": "Starting cleanup strategy test - this should pass", "timeout": 5, "test_case_id": "info_step_1", "filename": "Cleanup-Strategy-Test.json"}, {"type": "tap", "action_type": "tap", "locator_type": "xpath", "locator_value": "//android.widget.Button[@text='<PERSON><PERSON>']", "timeout": 5, "test_case_id": "valid_step_2", "filename": "Cleanup-Strategy-Test.json"}, {"type": "tap", "action_type": "tap", "locator_type": "xpath", "locator_value": "//android.widget.Button[@text='This Will Fail']", "timeout": 5, "test_case_id": "failing_step_3", "filename": "Cleanup-Strategy-Test.json"}, {"type": "info", "action_type": "info", "message": "This step should be skipped due to previous failure", "timeout": 5, "test_case_id": "skipped_step_4", "filename": "Cleanup-Strategy-Test.json"}, {"type": "multiStep", "action_type": "multiStep", "cleanup": true, "test_case_id": "cleanup_multistep", "test_case_steps": [{"type": "info", "action_type": "info", "message": "Regular step in multiStep - should execute normally", "timeout": 5, "test_case_id": "regular_step_1", "is_cleanup_step": false}, {"type": "info", "action_type": "info", "message": "CLEANUP STEP 1 - Should execute at end regardless of test outcome", "timeout": 5, "test_case_id": "cleanup_step_1", "is_cleanup_step": true}, {"type": "info", "action_type": "info", "message": "CLEANUP STEP 2 - Should execute at end regardless of test outcome", "timeout": 5, "test_case_id": "cleanup_step_2", "is_cleanup_step": true}, {"type": "tap", "action_type": "tap", "locator_type": "xpath", "locator_value": "//android.widget.Button[@text='Cleanup Tap Action']", "timeout": 5, "test_case_id": "cleanup_step_3", "is_cleanup_step": true}], "filename": "Cleanup-Strategy-Test.json"}, {"type": "cleanupSteps", "action_type": "cleanupSteps", "test_case_id": "legacy_cleanup_step", "message": "LEGACY CLEANUP STEP - Should also execute at end", "timeout": 5, "filename": "Cleanup-Strategy-Test.json"}]}