{"name": "App Settings AU_ANDROID", "created": "2025-08-22 12:29:00", "device_id": "PJTCI7EMSSONYPU8", "actions": [{"cleanup": false, "display_depth": 0, "interval": 0.5, "loading_in_progress": false, "locator_type": "accessibility_id", "locator_value": "txtOnePassOnboardingSkipForNow", "method": "locator", "steps_loaded": true, "test_case_id": "Onboarding-Start-AU.json", "test_case_name": "Onboarding-Start-AU", "test_case_steps": [{"function_name": "clear_app", "package_name": "au.com.kmart", "timestamp": 1755074292641, "type": "androidFunctions"}, {"package_id": "au.com.kmart", "timestamp": 1755074261875, "type": "launchApp"}, {"interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnOnboardingLocationLaterButton", "method": "locator", "timeout": 10, "timestamp": 1755074478382, "type": "tap"}, {"interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnOnboardingLocationLaterButton", "method": "locator", "timeout": 10, "timestamp": 1755074501686, "type": "tap"}, {"interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnMayBeLater", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtOnePassOnboardingSkipForNow", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}], "test_case_steps_count": 6, "timeout": 10, "timestamp": *************, "type": "multiStep"}, {"action_id": "veukWo4573", "executionTime": "2543ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtHomeAccountCtaSignIn\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "ArAkdzcpEN", "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "KmartSigninAUANDROID_20250709191752.json", "test_case_name": "Kmart-Signin-AU-ANDROID", "test_case_steps": [{"action_id": "HEBUNq0P6l", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "3963ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "AtwFJEKbDH", "enter": false, "function_name": "text", "text": "<EMAIL>", "timestamp": *************, "type": "text"}, {"action_id": "zxSSc6NS3A", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Continue to password\"]", "method": "locator", "timeout": 10, "timestamp": 1754699352537, "type": "tap"}, {"action_id": "TkyyzFSlcu", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"password\"]", "method": "locator", "timeout": 10, "timestamp": 1751405157652, "type": "tap"}, {"action_id": "K7yV3GGsgr", "enter": true, "executionTime": "3905ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "text": "Wonderbaby@6", "timeout": 10, "timestamp": 1745666199850, "type": "text"}, {"action_id": "QugrQS9qYH", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Sign in\"]", "method": "locator", "timeout": 10, "timestamp": 1754709806783, "type": "tap"}], "test_case_steps_count": 7, "timestamp": 1752580492266, "type": "multiStep"}, {"action_id": "mIKA85kXaW", "executionTime": "628ms", "package_id": "com.android.settings", "timestamp": 1749444829578, "type": "terminateApp"}, {"action_id": "LfyQctrEJn", "executionTime": "251ms", "package_id": "com.android.settings", "timestamp": 1749444810598, "type": "launchApp"}, {"action_id": "w1RV76df9x", "executionTime": "245ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//androidx.recyclerview.widget.RecyclerView[@resource-id=\"com.android.settings:id/recycler_view\"]/android.widget.LinearLayout[3]", "method": "locator", "text_to_find": "Wi-Fi", "timeout": 10, "timestamp": 1749444898707, "type": "tap"}, {"action_id": "V42eHtTRYW", "duration": 5, "executionTime": "5065ms", "time": 5, "timestamp": 1749445273934, "type": "wait"}, {"action_id": "jUCAk6GJc4", "executionTime": "1485ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Switch[@resource-id=\"android:id/switch_widget\"]", "method": "locator", "timeout": 10, "timestamp": 1749445081254, "type": "tap"}, {"action_id": "V42eHtTRYW", "duration": 5, "executionTime": "5065ms", "time": 5, "timestamp": 1750843578808, "type": "wait"}, {"action_id": "oSQ8sPdVOJ", "executionTime": "500ms", "package_id": "au.com.kmart", "timestamp": 1752581195187, "type": "terminateApp"}, {"action_id": "oSQ8sPdVOJ", "executionTime": "347ms", "package_id": "au.com.kmart", "timestamp": 1749445437019, "type": "launchApp"}, {"action_id": "cokvFXhj4c", "executionTime": "15455ms", "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtNo internet connection\"]", "timeout": 30, "timestamp": 1749445125565, "type": "exists"}, {"action_id": "3KNqlNy6Bj", "executionTime": "798ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 2 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749445177888, "type": "tap"}, {"action_id": "cokvFXhj4c", "executionTime": "141ms", "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtNo internet connection\"]", "timeout": 10, "timestamp": 1752581287764, "type": "exists"}, {"action_id": "3KNqlNy6Bj", "executionTime": "730ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 3 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752581297924, "type": "tap"}, {"action_id": "cokvFXhj4c", "executionTime": "148ms", "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtNo internet connection\"]", "timeout": 10, "timestamp": 1752581320274, "type": "exists"}, {"action_id": "3KNqlNy6Bj", "executionTime": "742ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752581338392, "type": "tap"}, {"action_id": "cokvFXhj4c", "executionTime": "197ms", "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtNo internet connection\"]", "timeout": 10, "timestamp": 1752581366187, "type": "exists"}, {"action_id": "mIKA85kXaW", "executionTime": "457ms", "package_id": "com.android.settings", "timestamp": 1752581395092, "type": "terminateApp"}, {"action_id": "LfyQctrEJn", "executionTime": "243ms", "package_id": "com.android.settings", "timestamp": 1752581426909, "type": "launchApp"}, {"action_id": "w1RV76df9x", "executionTime": "332ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//androidx.recyclerview.widget.RecyclerView[@resource-id=\"com.android.settings:id/recycler_view\"]/android.widget.LinearLayout[3]", "method": "locator", "text_to_find": "Wi-Fi", "timeout": 10, "timestamp": 1752581453309, "type": "tap"}, {"action_id": "jUCAk6GJc4", "executionTime": "1384ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Switch[@resource-id=\"android:id/switch_widget\"]", "method": "locator", "timeout": 10, "timestamp": 1752581473143, "type": "tap"}, {"action_id": "V42eHtTRYW", "duration": 5, "executionTime": "5067ms", "time": 5, "timestamp": 1750843560629, "type": "wait"}, {"action_id": "hCCEvRtj1A", "executionTime": "404ms", "package_id": "au.com.kmart", "timestamp": 1749445309230, "type": "terminateApp"}, {"action_id": "hCCEvRtj1A", "executionTime": "353ms", "package_id": "au.com.kmart", "timestamp": 1752581522673, "type": "launchApp"}, {"action_id": "UpUSVInizv", "executionTime": "3065ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749445687300, "type": "tap"}, {"action_id": "ZZPNqTJ65s", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 10, "executionTime": "3254ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1749445784426, "type": "swipe", "vector_end": [0.5, 0.1], "vector_start": [0.5, 0.7]}, {"action_id": "LcYLwUffqj", "executionTime": "21473ms", "text_to_find": "out", "timeout": 30, "timestamp": 1749445809311, "type": "tapOnText"}, {"executionTime": "464ms", "method": "coordinates", "package_id": "com.android.chrome", "timestamp": 1755595927420, "type": "terminateApp", "x": 0, "y": 0}, {"action_id": "rmqVgsHPp8", "executionTime": "440ms", "package_id": "com.android.chrome", "timestamp": 1753532546014, "type": "restartApp"}, {"action_id": "KAyXxO6c02", "executionTime": "5669ms", "locator_type": "xpath", "locator_value": "//android.widget.Button[@resource-id=\"com.android.chrome:id/signin_fre_dismiss_button\"]", "timeout": 10, "timestamp": 1753532616815, "type": "tapIfLocatorExists"}, {"action_id": "pOJrD4NK3F", "executionTime": "10071ms", "locator_type": "xpath", "locator_value": "//android.widget.Button[@resource-id=\"com.android.chrome:id/more_button\"]", "timeout": 10, "timestamp": 1753532718163, "type": "tapIfLocatorExists"}, {"action_id": "t6c0RSgUVq", "executionTime": "5544ms", "locator_type": "xpath", "locator_value": "//android.widget.Button[@resource-id=\"com.android.chrome:id/ack_button\"]", "timeout": 10, "timestamp": 1753532783771, "type": "tapIfLocatorExists"}, {"executionTime": "678ms", "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"com.android.chrome:id/url_bar\"]", "timeout": 10, "timestamp": 1755596011396, "type": "tapIfLocatorExists"}, {"action_id": "fTdGMJ3NH3", "enter": true, "executionTime": "2592ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"https://www.kmart.com.au\"]", "method": "locator", "text": "kmart au", "timeout": 10, "timestamp": 1749446027317, "type": "text"}, {"action_id": "mPIklklBO0", "executionTime": "238ms", "function_name": "send_key_event", "key_event": "ENTER", "timestamp": 1754741841119, "type": "androidFunctions"}, {"action_id": "UpUSVInizv", "executionTime": "771ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[@text=\"https://www.kmart.com.au\"]", "method": "locator", "timeout": 10, "timestamp": 1749472397515, "type": "tap"}, {"action_id": "QUeGIASAxV", "count": 1, "direction": "up", "duration": 300, "enabled": true, "end_x": 50, "end_y": 30, "executionTime": "38886ms", "interval": 0.5, "method": "coordinates", "start_x": 50, "start_y": 70, "timestamp": 1751711412051, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7], "x": 0, "y": 0}, {"action_id": "Cmvm82hiAa", "executionTime": "260ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"Home & Living\"]", "method": "locator", "timeout": 10, "timestamp": 1749472379198, "type": "tap"}, {"action_id": "QUeGIASAxV", "count": 1, "direction": "up", "duration": 300, "enabled": true, "end_x": 50, "end_y": 30, "executionTime": "263ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[@content-desc=\"Styled by You\"]/android.view.View[2]/android.view.View/android.widget.ImageView[1]", "method": "locator", "start_x": 50, "start_y": 70, "timeout": 10, "timestamp": 1752582182766, "type": "tap", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7], "x": 0, "y": 0}, {"action_id": "gkkQzTCmma", "executionTime": "31621ms", "fallback_text": "$", "fallback_type": "text", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"$\")]", "method": "locator", "text_to_find": "Catalogue", "timeout": 10, "timestamp": 1749470262060, "type": "tap"}, {"action_id": "gkkQzTCmma", "executionTime": "19008ms", "function_name": "send_key_event", "interval": 0.5, "key_event": "BACK", "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Add to bag\"]", "method": "locator", "text_to_find": "Catalogue", "timeout": 10, "timestamp": 1752582616397, "type": "androidFunctions"}, {"action_id": "gkkQzTCmma", "executionTime": "10139ms", "function_name": "send_key_event", "interval": 0.5, "key_event": "BACK", "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Add to bag\"]", "method": "locator", "text_to_find": "Catalogue", "timeout": 10, "timestamp": 1752582664264, "type": "androidFunctions"}, {"cleanup": false, "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "Browse_Add_to_Bag.json", "test_case_name": "Browse Add to Bag", "test_case_steps": [{"interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 2 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1755678065061, "type": "tap"}, {"text_to_find": "Home", "timeout": 30, "timestamp": 1755678081990, "type": "tapOnText"}, {"text_to_find": "Arrivals", "timeout": 30, "timestamp": 1755678108517, "type": "tapOnText"}, {"interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Filter\"]", "method": "locator", "timeout": 10, "timestamp": 1755678150106, "type": "tap"}, {"interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Image[@text=\"Unchecked\"]", "method": "locator", "timeout": 10, "timestamp": 1755678180254, "type": "tap"}, {"interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"Show\")]", "timeout": 30, "timestamp": 1755678275062, "type": "waitTill"}, {"interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"Show\")]", "method": "locator", "timeout": 10, "timestamp": 1755678244768, "type": "tap"}, {"interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[contains(@content-desc,\"to bag Add\")]", "method": "locator", "timeout": 10, "timestamp": 1755678639569, "type": "tap"}, {"count": 2, "direction": "up", "duration": 600, "end_x": 50, "end_y": 30, "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Add to bag\"]", "start_x": 50, "start_y": 70, "timestamp": 1755678698933, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Add to bag\"]", "method": "locator", "timeout": 10, "timestamp": 1755678748420, "type": "tap"}], "test_case_steps_count": 10, "timestamp": 1755679551156, "type": "multiStep"}, {"action_id": "UpUSVInizv", "executionTime": "1295ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752582676415, "type": "tap"}, {"executionTime": "25074ms", "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"Checkout\"]", "timeout": 25, "timestamp": 1755680438851, "type": "tapIfLocatorExists"}, {"action_id": "igReeDqips", "executionTime": "60678ms", "image_filename": "delivery-tab-android.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@text=\"Delivery\"]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1749471352255, "type": "tap"}, {"action_id": "ZZPNqTJ65s", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 50, "executionTime": "2085ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1753536378673, "type": "swipe", "vector_end": [0.5, 0.5], "vector_start": [0.5, 0.7]}, {"action_id": "Pd7cReoJM6", "executionTime": "629ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Increase quantity\"]", "method": "locator", "text_to_find": "List", "timeout": 10, "timestamp": 1749472320276, "type": "tap"}, {"action_id": "Pd7cReoJM6", "executionTime": "1777ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Decrease quantity\"]", "method": "locator", "text_to_find": "List", "timeout": 10, "timestamp": 1752582906295, "type": "tap"}, {"action_id": "JcAR0JctQ6", "executionTime": "1074ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"Remove\")]", "method": "locator", "timeout": 10, "timestamp": 1749472290567, "type": "tap"}, {"action_id": "KqTcr10JDm", "executionTime": "16973ms", "image_filename": "bag-close-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752583076309, "type": "tap"}, {"action_id": "UpUSVInizv", "executionTime": "771ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 2 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752583125267, "type": "tap"}, {"action_id": "UpUSVInizv", "executionTime": "1241ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 2 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1755680457607, "type": "tap"}, {"action_id": "ZZPNqTJ65s", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "1426ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1752583159491, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "gkkQzTCmma", "executionTime": "15910ms", "text_to_find": "Catalogue", "timeout": 30, "timestamp": 1749472424498, "type": "tapOnText"}, {"action_id": "S7PVvWSmaK", "executionTime": "5594ms", "image_filename": "catalogue-menu-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1753536639512, "type": "tap"}, {"action_id": "YHaMIjULRf", "executionTime": "4428ms", "text_to_find": "Search", "timeout": 30, "timestamp": 1749472769571, "type": "tapOnText", "double_tap": false}, {"type": "text", "timestamp": 1755829447826, "x": 0, "y": 0, "method": "coordinates", "text": "Toys", "enter": true}, {"type": "androidFunctions", "timestamp": 1755829486222, "x": 0, "y": 0, "method": "coordinates", "function_name": "send_key_event", "key_event": "ENTER"}, {"type": "waitTill", "timestamp": 1755829560874, "x": 0, "y": 0, "method": "coordinates", "locator_type": "xpath", "locator_value": "(//android.widget.TextView[contains(@text,\"$\")])[1]", "timeout": 30, "interval": 0.5}, {"action_id": "Qy0Y0uJchm", "executionTime": "1635ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//android.widget.TextView[contains(@text,\"$\")])[1]", "method": "locator", "timeout": 20, "timestamp": 1749472775719, "type": "tap"}, {"type": "waitTill", "timestamp": 1755829656401, "x": 0, "y": 0, "method": "coordinates", "locator_type": "xpath", "locator_value": "//android.widget.TextView[contains(@text,\"SKU\")]", "timeout": 30, "interval": 0.5}, {"action_id": "ZZPNqTJ65s", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "3787ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1753534634063, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "Iab9zCfpqO", "double_tap": false, "executionTime": "3276ms", "fallback_text": "Add", "fallback_type": "text", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Add to bag\"]", "method": "locator", "text_to_find": "Add", "timeout": 10, "timestamp": 1749472786795, "type": "tap"}, {"action_id": "UpUSVInizv", "executionTime": "29381ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1753534156795, "type": "tap"}, {"action_id": "jW6OPorKBq", "executionTime": "5470ms", "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"Checkout\"]", "timeout": 10, "timestamp": 1753534201332, "type": "tapIfLocatorExists"}, {"action_id": "igReeDqips", "executionTime": "22689ms", "image_filename": "delivery-tab-android.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@text=\"Delivery\"]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1755596600471, "type": "tap"}, {"count": 2, "direction": "up", "duration": 500, "end_x": 50, "end_y": 30, "executionTime": "2205ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"Remove\")]", "start_x": 50, "start_y": 70, "timestamp": 1755595487275, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "K0c1gL9UK1", "executionTime": "374ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"Remove\")]", "method": "locator", "timeout": 10, "timestamp": 1749472862396, "type": "tap"}, {"action_id": "3NOS1fbxZs", "executionTime": "55776ms", "image_filename": "banner-close-updated.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1749473253040, "type": "tap"}, {"action_id": "Qb1AArnpCH", "duration": 5, "executionTime": "5069ms", "time": 5, "timestamp": 1750975463814, "type": "wait"}], "labels": [], "created_at": "2025-08-22T12:29:00.487434", "modified_at": "2025-08-22T12:29:00.487434"}