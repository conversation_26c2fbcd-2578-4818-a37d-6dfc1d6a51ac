{"name": "AU- MyAccount_Android", "created": "2025-08-20 17:43:53", "device_id": "PJTCI7EMSSONYPU8", "actions": [{"cleanup": false, "display_depth": 0, "interval": 0.5, "loading_in_progress": false, "locator_type": "accessibility_id", "locator_value": "txtOnePassOnboardingSkipForNow", "method": "locator", "steps_loaded": true, "test_case_id": "Onboarding-Start-AU.json", "test_case_name": "Onboarding-Start-AU", "test_case_steps": [{"function_name": "clear_app", "package_name": "au.com.kmart", "timestamp": *************, "type": "androidFunctions"}, {"package_id": "au.com.kmart", "timestamp": *************, "type": "launchApp"}, {"interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnOnboardingLocationLaterButton", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnOnboardingLocationLaterButton", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnMayBeLater", "method": "locator", "timeout": 10, "timestamp": 1755074524127, "type": "tap"}, {"interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtOnePassOnboardingSkipForNow", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}], "test_case_steps_count": 6, "timeout": 10, "timestamp": *************, "type": "multiStep"}, {"action_id": "u6bRYZZFAv", "duration": 5, "executionTime": "5067ms", "time": 5, "timestamp": *************, "type": "wait"}, {"action_id": "xAPeBnVHrT", "executionTime": "746ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"cleanup": false, "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "Kmart-Signin-AU-ANDROID-U1.json", "test_case_name": "Kmart-Signin-AU-ANDROID-U1", "test_case_steps": [{"action_id": "HEBUNq0P6l", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "3963ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "AtwFJEKbDH", "enter": false, "function_name": "text", "text": "<EMAIL>", "timestamp": *************, "type": "text"}, {"action_id": "zxSSc6NS3A", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Continue to password\"]", "method": "locator", "timeout": 10, "timestamp": 1754699352537, "type": "tap"}, {"action_id": "TkyyzFSlcu", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"password\"]", "method": "locator", "timeout": 10, "timestamp": 1751405157652, "type": "tap"}, {"action_id": "K7yV3GGsgr", "enter": false, "executionTime": "3905ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "text": "Wonderbaby@5", "timeout": 10, "timestamp": 1745666199850, "type": "text"}, {"action_id": "QugrQS9qYH", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Sign in\"]", "method": "locator", "timeout": 10, "timestamp": 1754709806783, "type": "tap"}], "test_case_steps_count": 7, "timestamp": 1755673559190, "type": "multiStep"}, {"locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Skip for now\"]", "timeout": 10, "timestamp": *************, "type": "tapIfLocatorExists", "executionTime": "1324ms"}, {"action_id": "sl3Wk1gK8X", "executionTime": "4094ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "V59u3l1wkM", "executionTime": "332ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[contains(@content-desc,\"Manage your account\")]", "timeout": 20, "timestamp": *************, "type": "waitTill"}, {"action_id": "RIFpYdpN3M", "executionTime": "1040ms", "locator_type": "xpath", "locator_value": "//android.view.View[contains(@content-desc,\"live chat now\")]/android.widget.ImageView[2]", "timeout": 10, "timestamp": *************, "type": "tapIfLocatorExists"}, {"action_id": "pFlYwTS53v", "double_tap": false, "executionTime": "28681ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtMy orders & receipts\"]", "method": "locator", "text_to_find": "receipts", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "7g6MFJSGIO", "executionTime": "4795ms", "image_filename": "order-link-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Start shopping\"]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "Z6g3sGuHTp", "duration": 5, "executionTime": "1032ms", "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtHomeGreetingText\"]", "time": 5, "timeout": 10, "timestamp": 1746575221594, "type": "exists"}, {"action_id": "ADHRFCY0LX", "duration": 3, "executionTime": "3061ms", "method": "coordinates", "time": 3, "timestamp": 1752302090675, "type": "wait", "x": 0, "y": 0}, {"action_id": "sl3Wk1gK8X", "executionTime": "320ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1755674601676, "type": "tap"}, {"action_id": "pFlYwTS53v", "double_tap": false, "executionTime": "40433ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtMy orders & receipts\"]", "method": "locator", "text_to_find": "receipts", "timeout": 10, "timestamp": 1755674717943, "type": "tap"}, {"action_id": "Rl6s389Qsd", "count": 4, "direction": "up", "double_tap": false, "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "4611ms", "interval": 0.5, "start_x": 50, "start_y": 70, "text_to_find": "Store", "timeout": 30, "timestamp": 1752297398436, "type": "tapOnText", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "GgQaBLWYkb", "count": 2, "direction": "up", "double_tap": false, "duration": 500, "end_x": 50, "end_y": 30, "executionTime": "632ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[@text=\"My details\"]", "method": "locator", "start_x": 50, "start_y": 70, "text_to_find": "invoice", "timeout": 10, "timestamp": 1746521169867, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "zNwyPagPE1", "duration": 6, "executionTime": "614ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[@text=\"My details\"]", "method": "locator", "time": 6, "timeout": 10, "timestamp": 1752303153123, "type": "tap"}, {"action_id": "g0PE7Mofye", "double_tap": false, "executionTime": "3938ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[@text=\"My details\"]", "method": "locator", "text_to_find": "Print", "timeout": 10, "timestamp": 1746521217340, "type": "exists"}, {"action_id": "zNwyPagPE1", "duration": 6, "executionTime": "6063ms", "time": 6, "timestamp": 1752303186513, "type": "wait"}, {"action_id": "YuuQe2KupX", "executionTime": "24877ms", "function_name": "send_key_event", "interval": 0.5, "key_event": "BACK", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Cancel\"]", "method": "locator", "timeout": 10, "timestamp": 1746521262417, "type": "androidFunctions"}, {"action_id": "zNwyPagPE1", "duration": 5, "executionTime": "5064ms", "time": 5, "timestamp": 1746575257741, "type": "wait"}, {"action_id": "3hOTINBVMf", "executionTime": "1331ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtMy details\"]", "method": "locator", "text_to_find": "details", "timeout": 10, "timestamp": 1746522842993, "type": "tap"}, {"action_id": "YuuQe2KupX", "executionTime": "186ms", "function_name": "send_key_event", "interval": 0.5, "key_event": "BACK", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Cancel\"]", "method": "locator", "timeout": 10, "timestamp": 1752298239924, "type": "androidFunctions"}, {"action_id": "20qUCJgpE9", "double_tap": false, "executionTime": "1013ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtMy addresses\"]", "method": "locator", "text_to_find": "address", "timeout": 10, "timestamp": 1746523053141, "type": "tap"}, {"action_id": "YuuQe2KupX", "executionTime": "177ms", "function_name": "send_key_event", "interval": 0.5, "key_event": "BACK", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Cancel\"]", "method": "locator", "timeout": 10, "timestamp": 1752298379652, "type": "androidFunctions"}, {"action_id": "napKDohf3Z", "double_tap": false, "executionTime": "39628ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@content-desc,\"payment methods\")]", "method": "locator", "text_to_find": "payment", "timeout": 10, "timestamp": 1746523090959, "type": "tap"}, {"action_id": "YuuQe2KupX", "executionTime": "206ms", "function_name": "send_key_event", "interval": 0.5, "key_event": "BACK", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Cancel\"]", "method": "locator", "timeout": 10, "timestamp": 1752298409386, "type": "androidFunctions"}, {"action_id": "BracBsfa3Y", "executionTime": "2379ms", "text_to_find": "Flybuys", "timeout": 30, "timestamp": 1746573390611, "type": "tapOnText"}, {"action_id": "Gxhf3XGc6e", "executionTime": "756ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnLinkFlyBuys", "method": "locator", "timeout": 10, "timestamp": 1746573488051, "type": "tap"}, {"action_id": "Ey86YRVRzU", "executionTime": "224ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText", "method": "locator", "timeout": 10, "timestamp": 1746573530684, "type": "tap"}, {"action_id": "sLe0Wurhgm", "executionTime": "2611ms", "locator_type": "accessibility_id", "locator_value": "Flybuys barcode number", "method": "locator", "text": "2791234567890", "timeout": 15, "timestamp": 1746573561688, "type": "text"}, {"action_id": "biRyWs3nSs", "executionTime": "270ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnSaveFlybuysCard", "method": "locator", "timeout": 10, "timestamp": 1746573806006, "type": "tap"}, {"action_id": "YuuQe2KupX", "executionTime": "9263ms", "function_name": "send_key_event", "interval": 0.5, "key_event": "BACK", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Cancel\"]", "method": "locator", "timeout": 10, "timestamp": 1755515698868, "type": "androidFunctions"}, {"action_id": "2M0KHOVecv", "executionTime": "155ms", "locator_type": "accessibility_id", "locator_value": "txtMy Flybuys card", "timeout": 10, "timestamp": 1746573880079, "type": "exists"}, {"action_id": "BracBsfa3Y", "executionTime": "2404ms", "text_to_find": "Flybuys", "timeout": 30, "timestamp": 1755514617596, "type": "tapOnText"}, {"action_id": "40hnWPsQ9P", "executionTime": "181ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btneditFlybuysCard", "method": "locator", "timeout": 15, "timestamp": 1746523229356, "type": "waitTill"}, {"action_id": "40hnWPsQ9P", "executionTime": "19566ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btneditFlybuysCard", "method": "locator", "timeout": 10, "timestamp": 1746577799791, "type": "tap"}, {"action_id": "3ZFgwFaiXp", "executionTime": "937ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Remove card", "method": "locator", "timeout": 15, "timestamp": 1746523274958, "type": "tap"}, {"action_id": "Ds5GfNVb3x", "executionTime": "721ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnRemove", "method": "locator", "timeout": 15, "timestamp": 1746523316209, "type": "tap"}, {"duration": 6, "executionTime": "6073ms", "method": "coordinates", "time": 6, "timestamp": 1755514782011, "type": "wait", "x": 0, "y": 0}, {"action_id": "s6tWdQ5URW", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "3318ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1748085981704, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "BracBsfa3Y", "double_tap": false, "executionTime": "39544ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtStore locator\"]", "method": "locator", "text_to_find": "locator", "timeout": 10, "timestamp": 1746573912172, "type": "tap"}, {"locator_type": "xpath", "locator_value": "//android.widget.Button[@resource-id=\"com.android.permissioncontroller:id/permission_allow_foreground_only_button\"]", "timeout": 10, "timestamp": 1755516751017, "type": "tapIfLocatorExists", "executionTime": "934ms"}, {"action_id": "BracBsfa3Y", "double_tap": false, "executionTime": "2508ms", "text_to_find": "Nearby", "timeout": 30, "timestamp": 1746573997116, "type": "tapOnText"}, {"action_id": "3Si0csRNaw", "enter": true, "executionTime": "2841ms", "method": "coordinates", "text": "3000", "timeout": 15, "timestamp": 1749444122904, "type": "text", "x": "env[store-locator-x]", "y": "env[store-locator-y]"}, {"action_id": "YuuQe2KupX", "executionTime": "186ms", "function_name": "send_key_event", "interval": 0.5, "key_event": "ENTER", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Cancel\"]", "method": "locator", "timeout": 10, "timestamp": 1754741949705, "type": "androidFunctions"}, {"action_id": "PgjJCrKFYo", "double_tap": false, "executionTime": "41390ms", "image_filename": "storelocator-3000-se.png", "method": "image", "text_to_find": "VIC", "threshold": 0.7, "timeout": 30, "timestamp": 1746574179875, "type": "tapOnText"}, {"action_id": "2BfJyzwQFx", "executionTime": "1404ms", "locator_type": "text", "locator_value": "Cbd", "timeout": 10, "timestamp": 1752299100846, "type": "exists"}, {"action_id": "YuuQe2KupX", "executionTime": "212ms", "function_name": "send_key_event", "interval": 0.5, "key_event": "BACK", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Cancel\"]", "method": "locator", "timeout": 10, "timestamp": 1752299122236, "type": "androidFunctions"}, {"action_id": "BracBsfa3Y", "double_tap": false, "executionTime": "254ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtInvite a friend\"]", "method": "locator", "text_to_find": "Invite", "timeout": 10, "timestamp": 1746574278949, "type": "tap"}, {"action_id": "ePyaYpttQA", "executionTime": "248ms", "locator_type": "xpath", "locator_value": "//android.widget.TextView[@resource-id=\"android:id/content_preview_text\"]", "timeout": 10, "timestamp": 1746574369210, "type": "exists"}, {"action_id": "YuuQe2KupX", "executionTime": "202ms", "function_name": "send_key_event", "interval": 0.5, "key_event": "BACK", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Cancel\"]", "method": "locator", "timeout": 10, "timestamp": 1752299253777, "type": "androidFunctions"}, {"action_id": "BracBsfa3Y", "double_tap": false, "executionTime": "956ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtCustomer Help\"]", "method": "locator", "text_to_find": "Customer", "timeout": 10, "timestamp": 1746574406045, "type": "tap"}, {"action_id": "YuuQe2KupX", "executionTime": "148ms", "function_name": "send_key_event", "interval": 0.5, "key_event": "BACK", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Cancel\"]", "method": "locator", "timeout": 10, "timestamp": 1752299271313, "type": "androidFunctions"}, {"action_id": "s6tWdQ5URW", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "42119ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1746575871287, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "BracBsfa3Y", "double_tap": false, "executionTime": "1290ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtSign out\"]", "method": "locator", "text_to_find": "out", "timeout": 10, "timestamp": 1746574489503, "type": "tap"}], "labels": [], "created_at": "2025-08-20T17:43:53.432256", "modified_at": "2025-08-20T17:43:53.432256"}