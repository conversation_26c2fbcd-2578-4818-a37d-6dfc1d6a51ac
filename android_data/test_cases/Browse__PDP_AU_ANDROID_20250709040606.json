{"name": "Browse & PDP_AU_ANDROID", "created": "2025-08-24 07:59:42", "device_id": "PJTCI7EMSSONYPU8", "actions": [{"action_id": "H9fy9qcFbZ", "cleanup": false, "display_depth": 0, "executionTime": "307ms", "loading_in_progress": false, "package_id": "au.com.kmart", "steps_loaded": true, "test_case_id": "Onboarding-Start-AU.json", "test_case_name": "Onboarding-Start-AU", "test_case_steps": [{"function_name": "clear_app", "package_name": "au.com.kmart", "timestamp": 1755074292641, "type": "androidFunctions"}, {"package_id": "au.com.kmart", "timestamp": 1755074261875, "type": "launchApp"}, {"interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnOnboardingLocationLaterButton", "method": "locator", "timeout": 10, "timestamp": 1755074478382, "type": "tap"}, {"interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnOnboardingLocationLaterButton", "method": "locator", "timeout": 10, "timestamp": 1755074501686, "type": "tap"}, {"interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnMayBeLater", "method": "locator", "timeout": 10, "timestamp": 1755074524127, "type": "tap"}, {"interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtOnePassOnboardingSkipForNow", "method": "locator", "timeout": 10, "timestamp": 1755074573524, "type": "tap"}], "test_case_steps_count": 6, "timestamp": 1746597492636, "type": "multiStep"}, {"action_id": "H9fy9qcFbZ", "executionTime": "842ms", "package_id": "au.com.kmart", "timestamp": 1752002461224, "type": "launchApp"}, {"action_id": "F4NGh9HrLw", "executionTime": "1785ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 2 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746830724911, "type": "tap"}, {"action_id": "o74txS2f4j", "executionTime": "7301ms", "image_filename": "find-products-browse.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1751998546829, "type": "tap"}, {"action_id": "vfwUVEyq6X", "executionTime": "1588ms", "locator_type": "xpath", "locator_value": "//android.widget.ImageView[@content-desc=\"More\"]", "timeout": 10, "timestamp": 1746834702853, "type": "exists"}, {"action_id": "QPKR6jUF9O", "executionTime": "918ms", "locator_type": "xpath", "locator_value": "//android.widget.ImageView[@content-desc=\"Scan barcode\"]", "timeout": 10, "timestamp": 1746834725468, "type": "exists"}, {"action_id": "ltDXyWvtEz", "executionTime": "1284ms", "function_name": "send_key_event", "image_filename": "env[device-back-img]", "interval": 0.5, "key_event": "BACK", "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"Search\"]/preceding::android.widget.ImageView[1]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1749298419980, "type": "tap"}, {"action_id": "RbNtEW6N9T", "double_tap": false, "executionTime": "13645ms", "text_to_find": "Toys", "timeout": 30, "timestamp": 1746830828429, "type": "tapOnText"}, {"action_id": "xUbWFa8Ok2", "double_tap": false, "executionTime": "3422ms", "text_to_find": "Latest", "timeout": 30, "timestamp": 1746830873534, "type": "tapOnText"}, {"action_id": "lYPskZt0Ya", "executionTime": "7571ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Filter\"]", "timeout": 30, "timestamp": 1748003003230, "type": "waitTill"}, {"action_id": "Y1O1clhMSJ", "executionTime": "2126ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Filter\"]", "method": "locator", "timeout": 15, "timestamp": 1746834148265, "type": "tap"}, {"action_id": "a50JhCx0ir", "double_tap": false, "executionTime": "2597ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSwitch[@name=\"Unchecked In stock only items\"]", "method": "locator", "text_to_find": "only", "timeout": 30, "timestamp": 1749298898179, "type": "tapOnText"}, {"action_id": "dMl1PH9Dlc", "duration": 5, "executionTime": "5061ms", "time": 5, "timestamp": 1755172671811, "type": "wait"}, {"action_id": "a50JhCx0ir", "double_tap": false, "executionTime": "2727ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSwitch[@name=\"Unchecked In stock only items\"]", "method": "locator", "text_to_find": "Show", "timeout": 30, "timestamp": 1751999330542, "type": "tapOnText"}, {"action_id": "XmAxcBtFI0", "executionTime": "2379ms", "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"In stock only i... ChipsClose\"]", "timeout": 10, "timestamp": 1746834533008, "type": "exists"}, {"action_id": "huUnpMMjVR", "executionTime": "1878ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"In stock only i... ChipsClose\"]", "method": "locator", "timeout": 30, "timestamp": 1746834553339, "type": "tap"}, {"action_id": "eHLWiRoqqS", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "2835ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1746835476969, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "dMl1PH9Dlc", "duration": 10, "executionTime": "10061ms", "time": 10, "timestamp": 1747985121748, "type": "wait"}, {"action_id": "OmKfD9iBjD", "executionTime": "6869ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "((//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView[1])[1]", "timeout": 30, "timestamp": 1746835134218, "type": "waitTill"}, {"action_id": "kAQ1yIIw3h", "executionTime": "2196ms", "fallback_type": "coordinates", "fallback_x": 98, "fallback_y": 308, "interval": 0.5, "locator_type": "xpath", "locator_value": "((//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView[1])[1]", "method": "locator", "timeout": 30, "timestamp": 1746831616342, "type": "tap"}, {"action_id": "dCqKBG3e7u", "executionTime": "1044ms", "image_filename": "env[product-share-img]", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"Product Details\"]/following-sibling::android.widget.ImageView[1]", "method": "locator", "threshold": 0.7, "timeout": 30, "timestamp": 1749298579395, "type": "tap"}, {"action_id": "EEx673tuI0", "executionTime": "2880ms", "locator_type": "text", "locator_value": "Share", "timeout": 10, "timestamp": 1752000197122, "type": "exists"}, {"action_id": "y5FboDiRLS", "executionTime": "7517ms", "image_filename": "share-close.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752004354431, "type": "tap"}, {"action_id": "ShJSdXvmVL", "count": 3, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "3224ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[@text=\"Learn moreabout AfterPay\"]", "start_x": 50, "start_y": 70, "timestamp": 1746832248527, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "pk2DLZFBmx", "executionTime": "1315ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[@text=\"Learn moreabout AfterPay\"]", "method": "locator", "timeout": 10, "timestamp": 1746832294456, "type": "tap"}, {"action_id": "DhWa2PCBXE", "enabled": true, "executionTime": "3739ms", "interval": 0.5, "locator_type": "text", "locator_value": "Apply", "method": "locator", "timeout": 10, "timestamp": 1746832373193, "type": "exists"}, {"action_id": "Et3kvnFdxh", "executionTime": "676ms", "function_name": "send_key_event", "image_filename": "env[device-back-img]", "key_event": "BACK", "method": "image", "threshold": 0.7, "timeout": 30, "timestamp": 1749298408507, "type": "androidFunctions"}, {"action_id": "inrxgdWzXr", "executionTime": "1159ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[@text=\"Learn moreabout Zip\"]", "method": "locator", "timeout": 10, "timestamp": 1746832507125, "type": "tap"}, {"action_id": "P4b2BITpCf", "executionTime": "4638ms", "locator_type": "text", "locator_value": "What", "timeout": 20, "timestamp": 1746832583435, "type": "exists"}, {"action_id": "Et3kvnFdxh", "executionTime": "629ms", "function_name": "send_key_event", "image_filename": "env[device-back-img]", "key_event": "BACK", "method": "image", "threshold": 0.7, "timeout": 30, "timestamp": 1752000539283, "type": "androidFunctions"}, {"action_id": "q6cKxgMAIn", "executionTime": "1129ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Learn more about PayPal Pay in 4\"]", "method": "locator", "timeout": 10, "timestamp": 1746832657816, "type": "tap"}, {"action_id": "P4b2BITpCf", "executionTime": "2906ms", "locator_type": "text", "locator_value": "interest-", "timeout": 20, "timestamp": 1752000628249, "type": "exists"}, {"action_id": "mtYqeDttRc", "executionTime": "1139ms", "image_filename": "paypal-close-android.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@resource-id=\"close-btn\"]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1749431777668, "type": "tap"}, {"action_id": "eHLWiRoqqS", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 40, "executionTime": "2325ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1752004208629, "type": "swipe", "vector_end": [0.5, 0.4], "vector_start": [0.5, 0.7]}, {"action_id": "B6GDXWAmWp", "executionTime": "1156ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[@text=\"Shop at\"]/following-sibling::android.widget.Button", "method": "locator", "timeout": 15, "timestamp": 1746832967047, "type": "tap"}, {"action_id": "GWoppouz1l", "executionTime": "1184ms", "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtLocationTitle\"]", "timeout": 10, "timestamp": 1746833124593, "type": "exists"}, {"action_id": "Et3kvnFdxh", "executionTime": "1680ms", "function_name": "send_key_event", "image_filename": "env[device-back-img]", "interval": 0.5, "key_event": "BACK", "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"stnPostCodeSelectionScreenBodyWidget\"]/android.view.View[1]/android.widget.ImageView", "method": "locator", "threshold": 0.7, "timeout": 15, "timestamp": 1752001063293, "type": "tap"}, {"action_id": "XPEr3w6Zof", "executionTime": "1042ms", "package_id": "au.com.kmart", "timestamp": 1748255164286, "type": "terminateApp"}, {"action_id": "XPEr3w6Zof", "executionTime": "1217ms", "package_id": "au.com.kmart", "timestamp": 1752001232922, "type": "launchApp"}, {"action_id": "F4NGh9HrLw", "executionTime": "4340ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 2 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752001264866, "type": "tap"}, {"action_id": "o74txS2f4j", "executionTime": "26113ms", "image_filename": "find-products-browse.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752001315505, "type": "tap"}, {"action_id": "o1gHFWHXTL", "double_tap": false, "executionTime": "2648ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"edtFind products & categories\"]/android.view.View", "method": "locator", "text_to_find": "Find", "timeout": 30, "timestamp": 1752001523774, "type": "tapOnText"}, {"action_id": "JRheDTvpJf", "enter": true, "executionTime": "1497ms", "function_name": "text", "text": "Kids Toys", "timestamp": 1748255239813, "type": "text"}, {"action_id": "YaIypAHEOz", "executionTime": "12123ms", "function_name": "send_key_event", "key_event": "ENTER", "timestamp": 1754742401058, "type": "androidFunctions"}, {"action_id": "lYPskZt0Ya", "executionTime": "6984ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Filter\"]", "timeout": 30, "timestamp": 1752001701222, "type": "waitTill"}, {"action_id": "kAQ1yIIw3h", "executionTime": "2424ms", "fallback_type": "coordinates", "fallback_x": 98, "fallback_y": 308, "interval": 0.5, "locator_type": "xpath", "locator_value": "((//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView[1])[1]", "method": "locator", "timeout": 30, "timestamp": 1752001894693, "type": "tap"}, {"action_id": "eHLWiRoqqS", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "13653ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1755986367038, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "JRheDTvpJf", "double_tap": false, "enter": true, "executionTime": "2429ms", "function_name": "text", "text": "Kids Toys", "text_to_find": "Add", "timeout": 30, "timestamp": 1752002165808, "type": "tapOnText"}, {"action_id": "F4NGh9HrLw", "executionTime": "1092ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 2 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752001949098, "type": "tap"}, {"action_id": "o74txS2f4j", "executionTime": "32377ms", "image_filename": "find-products-browse.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752001960619, "type": "tap"}, {"action_id": "o1gHFWHXTL", "double_tap": false, "executionTime": "3020ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"edtFind products & categories\"]/android.view.View", "method": "locator", "text_to_find": "Find", "timeout": 30, "timestamp": 1752001972260, "type": "tapOnText"}, {"action_id": "JRheDTvpJf", "enter": true, "executionTime": "1168ms", "function_name": "text", "text": "mat", "timestamp": 1752001982805, "type": "text"}, {"action_id": "oNKCP9pqiF", "executionTime": "11095ms", "function_name": "send_key_event", "key_event": "ENTER", "timestamp": 1754742417001, "type": "androidFunctions"}, {"action_id": "lYPskZt0Ya", "executionTime": "6871ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Filter\"]", "timeout": 30, "timestamp": 1752002005454, "type": "waitTill"}, {"action_id": "kAQ1yIIw3h", "executionTime": "2364ms", "fallback_type": "coordinates", "fallback_x": 98, "fallback_y": 308, "interval": 0.5, "locator_type": "xpath", "locator_value": "((//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView[1])[1]", "method": "locator", "timeout": 30, "timestamp": 1752002236533, "type": "tap"}, {"action_id": "kwF3J9NbRc", "executionTime": "3501ms", "interval": 0.5, "locator_type": "text", "locator_value": "SKU", "timeout": 30, "timestamp": 1752003281037, "type": "waitTill"}, {"action_id": "eHLWiRoqqS", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "13653ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1752002249659, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "JRheDTvpJf", "double_tap": false, "enter": true, "executionTime": "4178ms", "function_name": "text", "text": "Kids Toys", "text_to_find": "Add", "timeout": 30, "timestamp": 1752002277484, "type": "tapOnText"}, {"action_id": "F4NGh9HrLw", "executionTime": "1758ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752002299940, "type": "tap"}, {"action_id": "cTLBS0O1ot", "executionTime": "1725ms", "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"Checkout\"]", "timeout": 10, "timestamp": 1752314711044, "type": "tapIfLocatorExists"}, {"action_id": "XoMyLp2unA", "executionTime": "5836ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@text=\"Delivery\"]", "timeout": 30, "timestamp": 1752003300507, "type": "waitTill"}, {"action_id": "VYgfSNx3GG", "executionTime": "1130ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@text=\"Delivery\"]", "method": "locator", "timeout": 10, "timestamp": 1753531558822, "type": "tap", "x": 0, "y": 0}, {"action_id": "eHLWiRoqqS", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 50, "executionTime": "2644ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1752002347806, "type": "swipe", "vector_end": [0.5, 0.5], "vector_start": [0.5, 0.7]}, {"action_id": "2p13JoJbbA", "executionTime": "1328ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"Remove\")]", "method": "locator", "timeout": 10, "timestamp": 1746834873588, "type": "tap"}, {"action_id": "rbzkUOQMtf", "duration": 4, "executionTime": "4056ms", "time": 4, "timestamp": 1752004166428, "type": "wait"}, {"action_id": "2p13JoJbbA", "executionTime": "1332ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"Remove\")]", "method": "locator", "timeout": 10, "timestamp": 1752002413851, "type": "tap"}, {"action_id": "QspAF2MJsL", "duration": 4, "executionTime": "4061ms", "time": 4, "timestamp": 1752004174638, "type": "wait"}, {"action_id": "x4yLCZHaCR", "executionTime": "17238ms", "package_id": "au.com.kmart", "timestamp": 1746834909467, "type": "terminateApp"}], "labels": [], "created_at": "2025-08-24T07:59:42.233557", "modified_at": "2025-08-24T07:59:42.233557"}