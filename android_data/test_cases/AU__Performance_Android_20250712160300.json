{"name": "AU - Performance_Android", "created": "2025-08-22 07:17:16", "device_id": null, "actions": [{"cleanup": false, "display_depth": 0, "interval": 0.5, "loading_in_progress": false, "locator_type": "accessibility_id", "locator_value": "txtOnePassOnboardingSkipForNow", "method": "locator", "steps_loaded": true, "test_case_id": "Onboarding-Start-AU.json", "test_case_name": "Onboarding-Start-AU", "test_case_steps": [{"function_name": "clear_app", "package_name": "au.com.kmart", "timestamp": 1755074292641, "type": "androidFunctions"}, {"package_id": "au.com.kmart", "timestamp": 1755074261875, "type": "launchApp"}, {"interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnOnboardingLocationLaterButton", "method": "locator", "timeout": 10, "timestamp": 1755074478382, "type": "tap"}, {"interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnOnboardingLocationLaterButton", "method": "locator", "timeout": 10, "timestamp": 1755074501686, "type": "tap"}, {"interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnMayBeLater", "method": "locator", "timeout": 10, "timestamp": 1755074524127, "type": "tap"}, {"interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtOnePassOnboardingSkipForNow", "method": "locator", "timeout": 10, "timestamp": 1755074573524, "type": "tap"}], "test_case_steps_count": 6, "timeout": 10, "timestamp": 1755074777011, "type": "multiStep"}, {"action_id": "SqDiBhmyOG", "executionTime": "51645ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749943707923, "type": "tap"}, {"action_id": "t6L5vWfBYM", "count": 6, "direction": "right", "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "55968ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1749944110469, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "g17Boaefhg", "executionTime": "1790ms", "text_to_find": "Help", "timeout": 30, "timestamp": 1749944026170, "type": "tapOnText"}, {"action_id": "DhFJzlme9K", "double_tap": false, "executionTime": "4043ms", "text_to_find": "FAQ", "timeout": 30, "timestamp": 1749944058180, "type": "tapOnText"}, {"action_id": "t6L5vWfBYM", "count": 6, "direction": "right", "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "45455ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1752300324074, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "I0tM87Yjhc", "executionTime": "1949ms", "text_to_find": "click", "timeout": 30, "timestamp": 1749944178932, "type": "tapOnText"}, {"action_id": "f7IqzjeNBe", "executionTime": "15652ms", "image_filename": "custcare-no-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752388220379, "type": "tap"}, {"action_id": "VqSa9z9R2Q", "executionTime": "209ms", "package_id": "au.com.kmart", "timestamp": 1749944381353, "type": "launchApp"}, {"action_id": "7xs3GiydGF", "executionTime": "313ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 1 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749944408274, "type": "tap"}, {"action_id": "6G6P3UE7Uy", "executionTime": "3253ms", "text_to_find": "Find", "timeout": 30, "timestamp": 1749944417423, "type": "tapOnText"}, {"action_id": "IL6kON0uQ9", "enter": true, "executionTime": "2679ms", "function_name": "text", "text": "kids toys", "timestamp": 1749944478911, "type": "text"}, {"action_id": "ZOvSdSTPuq", "executionTime": "121ms", "function_name": "send_key_event", "key_event": "ENTER", "timestamp": 1754741997574, "type": "androidFunctions"}, {"action_id": "6G6P3UE7Uy", "executionTime": "6382ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Filter\"]", "text_to_find": "Find", "timeout": 20, "timestamp": 1752382826112, "type": "waitTill"}, {"cleanup": false, "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "Click_Paginations_Android_20250713102050.json", "test_case_name": "Click_Paginations_Android", "test_case_steps": [{"action_id": "yo1lzlMRge", "count": 15, "direction": "up", "duration": 500, "end_x": 50, "end_y": 10, "executionTime": "16222ms", "interval": 0.2, "start_x": 50, "start_y": 80, "timestamp": 1752366314644, "type": "swipe", "vector_end": [0.5, 0.1], "vector_start": [0.5, 0.8]}, {"action_id": "88BYVcWtJZ", "count": 3, "direction": "down", "double_tap": false, "duration": 300, "end_x": 50, "end_y": 70, "executionTime": "2549ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Go to next page\"]", "method": "locator", "start_x": 50, "start_y": 30, "text_to_find": ">", "timeout": 10, "timestamp": 1749649130831, "type": "tap", "vector_end": [0.5, 0.7], "vector_start": [0.5, 0.3]}, {"action_id": "kaJNq07XtA", "executionTime": "5708ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Filter\"]", "method": "locator", "timeout": 10, "timestamp": 1752366464562, "type": "waitTill"}, {"action_id": "AJN5D84x5Q", "count": 15, "direction": "up", "duration": 500, "end_x": 50, "end_y": 10, "executionTime": "15665ms", "interval": 0.2, "start_x": 50, "start_y": 80, "timestamp": 1752366496043, "type": "swipe", "vector_end": [0.5, 0.1], "vector_start": [0.5, 0.8]}, {"action_id": "88BYVcWtJZ", "count": 3, "direction": "down", "double_tap": false, "duration": 300, "end_x": 50, "end_y": 70, "executionTime": "2549ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Go to next page\"]", "method": "locator", "start_x": 50, "start_y": 30, "text_to_find": ">", "timeout": 10, "timestamp": 1754476841856, "type": "tap", "vector_end": [0.5, 0.7], "vector_start": [0.5, 0.3]}, {"action_id": "kaJNq07XtA", "executionTime": "5708ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Filter\"]", "method": "locator", "timeout": 10, "timestamp": 1755550419802, "type": "waitTill"}, {"action_id": "lDPGwjOA3A", "count": 15, "direction": "up", "duration": 500, "end_x": 50, "end_y": 10, "executionTime": "16222ms", "interval": 0.2, "start_x": 50, "start_y": 80, "timestamp": 1752366935758, "type": "swipe", "vector_end": [0.5, 0.1], "vector_start": [0.5, 0.8]}, {"action_id": "88BYVcWtJZ", "count": 3, "direction": "down", "double_tap": false, "duration": 300, "end_x": 50, "end_y": 70, "executionTime": "2549ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Go to next page\"]", "method": "locator", "start_x": 50, "start_y": 30, "text_to_find": ">", "timeout": 10, "timestamp": 1754476850753, "type": "tap", "vector_end": [0.5, 0.7], "vector_start": [0.5, 0.3]}, {"action_id": "kaJNq07XtA", "executionTime": "5708ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Filter\"]", "method": "locator", "timeout": 10, "timestamp": 1755550429987, "type": "waitTill"}, {"action_id": "Gx9zqxjXJB", "count": 15, "direction": "up", "duration": 500, "end_x": 50, "end_y": 10, "executionTime": "16222ms", "interval": 0.2, "start_x": 50, "start_y": 80, "timestamp": 1752366928542, "type": "swipe", "vector_end": [0.5, 0.1], "vector_start": [0.5, 0.8]}, {"action_id": "lJG7i4xWjM", "executionTime": "5424ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Go to previous page\"]", "method": "locator", "timeout": 10, "timestamp": 1752366672947, "type": "tap"}, {"action_id": "kaJNq07XtA", "executionTime": "5708ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Filter\"]", "method": "locator", "timeout": 10, "timestamp": 1755550439231, "type": "waitTill"}, {"action_id": "SeiSGMuR9u", "count": 15, "direction": "up", "duration": 500, "end_x": 50, "end_y": 10, "executionTime": "16222ms", "interval": 0.2, "start_x": 50, "start_y": 80, "timestamp": 1752366914594, "type": "swipe", "vector_end": [0.5, 0.1], "vector_start": [0.5, 0.8]}, {"action_id": "lJG7i4xWjM", "executionTime": "5424ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Go to previous page\"]", "method": "locator", "timeout": 10, "timestamp": 1754476915643, "type": "tap"}, {"action_id": "kaJNq07XtA", "executionTime": "5708ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Filter\"]", "method": "locator", "timeout": 10, "timestamp": 1755550447941, "type": "waitTill"}], "test_case_steps_count": 15, "timestamp": 1755550478058, "type": "multiStep"}, {"action_id": "5e4LeoW1YU", "executionTime": "199ms", "package_id": "au.com.kmart", "timestamp": *************, "type": "launchApp"}, {"action_id": "WEB5St2Mb7", "executionTime": "290ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 1 of 5\")]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "SPE01N6pyp", "executionTime": "120ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "9SUMcj33xo", "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "KmartSigninAUANDROID_20250709191752.json", "test_case_name": "Kmart-Signin-AU-ANDROID", "test_case_steps": [{"action_id": "HEBUNq0P6l", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "3963ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "AtwFJEKbDH", "enter": false, "function_name": "text", "text": "<EMAIL>", "timestamp": *************, "type": "text"}, {"action_id": "zxSSc6NS3A", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Continue to password\"]", "method": "locator", "timeout": 10, "timestamp": 1754699352537, "type": "tap"}, {"action_id": "TkyyzFSlcu", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"password\"]", "method": "locator", "timeout": 10, "timestamp": 1751405157652, "type": "tap"}, {"action_id": "K7yV3GGsgr", "enter": true, "executionTime": "3905ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "text": "Wonderbaby@6", "timeout": 10, "timestamp": 1745666199850, "type": "text"}, {"action_id": "QugrQS9qYH", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Sign in\"]", "method": "locator", "timeout": 10, "timestamp": 1754709806783, "type": "tap"}], "test_case_steps_count": 7, "timestamp": 1752367838000, "type": "multiStep"}, {"action_id": "GTXmST3hEA", "executionTime": "5388ms", "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtHomeGreetingText\"]", "timeout": 10, "timestamp": 1749959340082, "type": "exists"}, {"action_id": "ewuLtuqVuo", "executionTime": "51467ms", "text_to_find": "Find", "timeout": 30, "timestamp": 1749959489189, "type": "tapOnText"}, {"action_id": "RuPGkdCdah", "enter": true, "executionTime": "2654ms", "function_name": "text", "text": "cooker", "timestamp": 1749959531162, "type": "text"}, {"action_id": "cv6q3z3WDg", "executionTime": "49555ms", "function_name": "send_key_event", "key_event": "ENTER", "timestamp": 1754742036245, "type": "androidFunctions"}, {"action_id": "ksCBjJiwHZ", "executionTime": "5032ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Filter\"]", "timeout": 10, "timestamp": 1749959553346, "type": "waitTill"}, {"action_id": "xmelRkcdVx", "executionTime": "987ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "((//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView[1])[1]", "method": "locator", "timeout": 10, "timestamp": 1749959577226, "type": "tap"}, {"action_id": "0bnBNoqPt8", "executionTime": "47962ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[contains(@text,\"SKU\")]", "timeout": 10, "timestamp": 1749959593634, "type": "waitTill"}, {"action_id": "HZT2s0AzX7", "count": 3, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "2299ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@resource-id=\"wishlist-button\"]", "method": "locator", "start_x": 50, "start_y": 70, "timeout": 10, "timestamp": 1750323423267, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "Ef6OumM2eS", "executionTime": "50282ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@resource-id=\"wishlist-button\"]", "method": "locator", "timeout": 10, "timestamp": 1749959612145, "type": "tap"}, {"action_id": "OKCHAK6HCJ", "executionTime": "477ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 3 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749959627955, "type": "tap"}, {"action_id": "x4Mid4HQ0Z", "count": 1, "direction": "left", "duration": 300, "end_x": 30, "end_y": 20, "executionTime": "634ms", "interval": 0.5, "start_x": 90, "start_y": 20, "timestamp": 1749959799044, "type": "swipe", "vector_end": [0.3, 0.2], "vector_start": [0.9, 0.2]}, {"action_id": "RDQCFIxjA0", "count": 1, "direction": "left", "duration": 300, "end_x": 30, "end_y": 20, "executionTime": "649ms", "interval": 0.5, "start_x": 90, "start_y": 20, "timestamp": 1749959805826, "type": "swipe", "vector_end": [0.3, 0.2], "vector_start": [0.9, 0.2]}, {"action_id": "wguGCt7OoB", "executionTime": "773ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750323885443, "type": "tap"}, {"action_id": "ylslyLAYKb", "count": 1, "direction": "left", "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "646ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1749959866252, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "RbD937Xbte", "executionTime": "2410ms", "text_to_find": "out", "timeout": 30, "timestamp": 1749959881521, "type": "tapOnText"}, {"action_id": "OKCHAK6HCJ", "executionTime": "145ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 1 of 5\")]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "ipT2XD9io6", "executionTime": "2911ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtJoinTodayButton\"]", "method": "locator", "text_to_find": "Join", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "UqgDn5CuPY", "executionTime": "29992ms", "locator_type": "xpath", "locator_value": "//android.widget.TextView[@text=\"Create account\"]", "timeout": 10, "timestamp": *************, "type": "exists"}, {"action_id": "JLAJhxPdsl", "executionTime": "289ms", "function_name": "send_key_event", "key_event": "BACK", "text_to_find": "Cancel", "timeout": 30, "timestamp": *************, "type": "androidFunctions"}, {"action_id": "wvthDzpm0c", "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "Search_and_Add_Notebooks_ANDROID_20250713103600.json", "test_case_name": "Search and Add (Notebooks)_ANDROID", "test_case_steps": [{"action_id": "r9Ef3eOmlj", "text_to_find": "Find", "timeout": 30, "timestamp": *************, "type": "tapOnText"}, {"action_id": "8S8UskeUvp", "enter": true, "function_name": "text", "text": "Notebooks", "timestamp": *************, "type": "text"}, {"action_id": "hueJtZF1HD", "function_name": "send_key_event", "key_event": "ENTER", "timestamp": *************, "type": "androidFunctions"}, {"action_id": "qSYJYPHhYJ", "interval": 0.5, "locator_type": "text", "locator_value": "Filter", "timeout": 30, "timestamp": 1750379714689, "type": "waitTill"}, {"action_id": "u7kJ2m8mFs", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView/following-sibling::android.view.View/android.widget.Button", "method": "locator", "timeout": 10, "timestamp": 1750379679764, "type": "tap"}, {"action_id": "fpJSA5zFxF", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750379828258, "type": "tap"}, {"action_id": "8ZdLHXBbvY", "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"Checkout\"]", "timeout": 10, "timestamp": 1753568134160, "type": "tapIfLocatorExists"}, {"action_id": "Ko32VlFCRX", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@text=\"Delivery\"]", "timeout": 10, "timestamp": 1753568341343, "type": "waitTill"}], "test_case_steps_count": 8, "timestamp": 1754794009674, "type": "multiStep"}, {"action_id": "S24l2PQODy", "executionTime": "50734ms", "image_filename": "cnc-tab-android.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[contains(@text,\"Click\") and contains(@text,\"Collect\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1752387871700, "type": "tap"}, {"action_id": "93bAew9Y4Y", "double_tap": false, "executionTime": "302ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//android.widget.Button[contains(@text,\"store details\")])[1]", "method": "locator", "text_to_find": "Store", "timeout": 10, "timestamp": 1750381507288, "type": "tap"}, {"action_id": "njiHWyVooT", "executionTime": "248ms", "image_filename": "bag-close-android.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"close\"]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1750381675953, "type": "tap"}, {"action_id": "dJNRgTXoqs", "count": 1, "direction": "up", "duration": 300, "end_x": 50, "end_y": 40, "executionTime": "656ms", "interval": 0.5, "max_swipes": 50, "start_x": 50, "start_y": 80, "timeout": 30, "timestamp": 1750382217101, "type": "swipe", "vector_end": [0.5, 0.4], "vector_start": [0.5, 0.8]}, {"action_id": "z1CfcW4xYT", "executionTime": "299ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"Remove\")]", "method": "locator", "timeout": 10, "timestamp": 1750382292209, "type": "tap"}, {"action_id": "AEnFqnkOa1", "executionTime": "15115ms", "image_filename": "bag-close-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": *************, "type": "tap"}, {"action_id": "OKCHAK6HCJ", "executionTime": "723ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "ipT2XD9io6", "executionTime": "2254ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtMoreAccountJoinTodayButton\"]", "method": "locator", "text_to_find": "Join", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "UqgDn5CuPY", "executionTime": "20537ms", "locator_type": "xpath", "locator_value": "//android.widget.TextView[@text=\"Create account\"]", "timeout": 10, "timestamp": *************, "type": "exists"}, {"action_id": "JLAJhxPdsl", "executionTime": "273ms", "function_name": "send_key_event", "key_event": "BACK", "text_to_find": "Cancel", "timeout": 30, "timestamp": *************, "type": "androidFunctions"}, {"action_id": "25UEKPIknm", "executionTime": "595ms", "package_id": "au.com.kmart", "timestamp": *************, "type": "terminateApp"}, {"action_id": "BlQ3vdsleV", "duration": 5, "executionTime": "5013ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "method": "locator", "time": 5, "timeout": 10, "timestamp": *************, "type": "wait"}], "labels": [], "created_at": "2025-08-22T07:17:16.594173", "modified_at": "2025-08-22T07:17:16.594173"}