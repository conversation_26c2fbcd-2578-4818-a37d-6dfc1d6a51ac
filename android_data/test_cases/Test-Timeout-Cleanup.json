{"test_case_name": "Test Timeout and Cleanup", "actions": [{"type": "tap", "action_type": "tap", "locator_type": "xpath", "locator_value": "//android.widget.Button[@text='<PERSON><PERSON>']", "timeout": 5, "test_case_id": "step_1", "filename": "Test-Timeout-Cleanup.json"}, {"type": "tap", "action_type": "tap", "locator_type": "xpath", "locator_value": "//android.widget.Button[@text='Invalid Button XYZ']", "timeout": 10, "test_case_id": "step_2", "filename": "Test-Timeout-Cleanup.json"}, {"type": "multiStep", "action_type": "multiStep", "cleanup": true, "test_case_id": "cleanup_multistep", "test_case_steps": [{"type": "tap", "action_type": "tap", "locator_type": "xpath", "locator_value": "//android.widget.Button[@text='Regular Step']", "timeout": 5, "test_case_id": "regular_step_1", "is_cleanup_step": false}, {"type": "tap", "action_type": "tap", "locator_type": "xpath", "locator_value": "//android.widget.Button[@text='Cleanup Step 1']", "timeout": 5, "test_case_id": "cleanup_step_1", "is_cleanup_step": true}, {"type": "tap", "action_type": "tap", "locator_type": "xpath", "locator_value": "//android.widget.Button[@text='Cleanup Step 2']", "timeout": 5, "test_case_id": "cleanup_step_2", "is_cleanup_step": true}], "filename": "Test-Timeout-Cleanup.json"}]}