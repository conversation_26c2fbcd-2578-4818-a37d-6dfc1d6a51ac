{"test_case_name": "Timeout Optimization Test", "description": "Test case to verify Android timeout optimizations work correctly with both valid and invalid locators", "actions": [{"type": "info", "action_type": "info", "message": "Starting timeout optimization test - testing fast element finding", "timeout": 5, "test_case_id": "info_step_1", "filename": "Timeout-Optimization-Test.json"}, {"type": "tap", "action_type": "tap", "locator_type": "id", "locator_value": "com.android.settings:id/search_action_bar", "timeout": 10, "test_case_id": "fast_element_test", "filename": "Timeout-Optimization-Test.json"}, {"type": "info", "action_type": "info", "message": "Testing optimized timeout with invalid locator - should fail quickly", "timeout": 3, "test_case_id": "info_step_2", "filename": "Timeout-Optimization-Test.json"}, {"type": "tap", "action_type": "tap", "locator_type": "xpath", "locator_value": "//android.widget.Button[@text='NonExistentButton12345']", "timeout": 5, "test_case_id": "invalid_element_test", "filename": "Timeout-Optimization-Test.json"}, {"type": "info", "action_type": "info", "message": "Testing conditional action with optimized timeout", "timeout": 3, "test_case_id": "info_step_3", "filename": "Timeout-Optimization-Test.json"}, {"type": "tapIfLocatorExists", "action_type": "tapIfLocatorExists", "locator_type": "id", "locator_value": "com.android.settings:id/search_action_bar", "timeout": 8, "test_case_id": "conditional_element_test", "filename": "Timeout-Optimization-Test.json"}, {"type": "info", "action_type": "info", "message": "Testing UISelector with optimized timeout", "timeout": 3, "test_case_id": "info_step_4", "filename": "Timeout-Optimization-Test.json"}, {"type": "tap", "action_type": "tap", "locator_type": "uiselector", "locator_value": "new UiSelector().text(\"Settings\")", "timeout": 10, "test_case_id": "uiselector_test", "filename": "Timeout-Optimization-Test.json"}, {"type": "info", "action_type": "info", "message": "Timeout optimization test completed", "timeout": 3, "test_case_id": "info_step_5", "filename": "Timeout-Optimization-Test.json"}]}