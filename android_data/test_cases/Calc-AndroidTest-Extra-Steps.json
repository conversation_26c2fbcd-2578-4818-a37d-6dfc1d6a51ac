{"name": "Calc-AndroidTest-Extra-Steps", "created": "2025-08-27 07:46:54", "device_id": "PJTCI7EMSSONYPU8", "actions": [{"package_id": "com.coloros.calculator", "timestamp": 1755031307904, "type": "restartApp"}, {"interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[@content-desc=\"More options,\"]", "method": "locator", "timeout": 10, "timestamp": 1755031342299, "type": "tap"}, {"double_tap": false, "text_to_find": "Settings", "timeout": 30, "timestamp": 1755031377995, "type": "tapOnText"}, {"interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[@content-desc=\"Scientific\"]", "method": "locator", "timeout": 10, "timestamp": 1755031472628, "type": "tap"}, {"package_id": "com.coloros.calculator", "timestamp": 1755031521472, "type": "terminateApp"}, {"display_depth": 0, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "Calc-subtest", "test_case_name": "Calc-subtest", "test_case_steps": [{"package_id": "com.coloros.calculator", "timestamp": 1755031846544, "type": "launchApp"}, {"interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"Backspace\"]", "method": "locator", "timeout": 10, "timestamp": 1755031869021, "type": "tap"}, {"package_id": "com.coloros.calculator", "timestamp": 1755031902487, "type": "terminateApp"}], "test_case_steps_count": 3, "timestamp": 1755056432350, "type": "cleanupSteps"}], "labels": [], "created_at": "2025-08-27T07:46:54.156194", "modified_at": "2025-08-27T07:46:54.156194"}