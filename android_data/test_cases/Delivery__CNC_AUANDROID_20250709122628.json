{"name": "Delivery & CNC_AU-ANDROID", "created": "2025-08-24 07:00:10", "device_id": "PJTCI7EMSSONYPU8", "actions": [{"cleanup": false, "display_depth": 0, "loading_in_progress": false, "method": "coordinates", "steps_loaded": true, "test_case_id": "Onboarding-Start-AU.json", "test_case_name": "Onboarding-Start-AU", "test_case_steps": [{"function_name": "clear_app", "package_name": "au.com.kmart", "timestamp": 1755074292641, "type": "androidFunctions"}, {"package_id": "au.com.kmart", "timestamp": 1755074261875, "type": "launchApp"}, {"interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnOnboardingLocationLaterButton", "method": "locator", "timeout": 10, "timestamp": 1755074478382, "type": "tap"}, {"interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnOnboardingLocationLaterButton", "method": "locator", "timeout": 10, "timestamp": 1755074501686, "type": "tap"}, {"interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnMayBeLater", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtOnePassOnboardingSkipForNow", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}], "test_case_steps_count": 6, "timestamp": *************, "type": "multiStep", "x": 0, "y": 0}, {"action_id": "Y8v5g7AJD1i", "executionTime": "3636ms", "fallback_locators": [{"locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtHomeAccountCtaSignIn\"]"}], "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "J9loj6Zs95K", "executionTime": "1182ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "g052Oo1Gcl", "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "KmartSigninAUANDROID_20250709191752.json", "test_case_name": "Kmart-Signin-AU-ANDROID", "test_case_steps": [{"action_id": "HEBUNq0P6l", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "3963ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "method": "locator", "timeout": 10, "timestamp": 1745665879530, "type": "tap"}, {"action_id": "AtwFJEKbDH", "enter": false, "function_name": "text", "text": "<EMAIL>", "timestamp": 1749384094911, "type": "text"}, {"action_id": "zxSSc6NS3A", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Continue to password\"]", "method": "locator", "timeout": 10, "timestamp": 1754699352537, "type": "tap"}, {"action_id": "TkyyzFSlcu", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"password\"]", "method": "locator", "timeout": 10, "timestamp": 1751405157652, "type": "tap"}, {"action_id": "K7yV3GGsgr", "enter": true, "executionTime": "3905ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "text": "Wonderbaby@6", "timeout": 10, "timestamp": 1745666199850, "type": "text"}, {"action_id": "QugrQS9qYH", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Sign in\"]", "method": "locator", "timeout": 10, "timestamp": 1754709806783, "type": "tap"}], "test_case_steps_count": 7, "timestamp": 1752052785440, "type": "multiStep"}, {"action_id": "RLznb7o3ag", "executionTime": "4901ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtHomeGreetingText\"]", "method": "locator", "timeout": 10, "timestamp": 1751404760227, "type": "waitTill"}, {"action_id": "o1gHFWhXTL", "double_tap": false, "executionTime": "3619ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"edtFind products & categories\"]/android.view.View", "method": "locator", "text_to_find": "Find", "timeout": 30, "timestamp": 1752001972260, "type": "tapOnText"}, {"action_id": "JRheeTvpJf", "enter": true, "executionTime": "1486ms", "function_name": "text", "text": "Uno Card", "timestamp": 1752001982805, "type": "text"}, {"action_id": "uybtup9gEo", "executionTime": "1594ms", "function_name": "send_key_event", "key_event": "ENTER", "timestamp": 1754742477924, "type": "androidFunctions"}, {"action_id": "nAB6Q8LAdv", "executionTime": "5968ms", "interval": 0.5, "locator_type": "text", "locator_value": "Filter", "timeout": 30, "timestamp": 1745485041367, "type": "waitTill"}, {"action_id": "FnrbyHq7bU", "executionTime": "1335ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView/following-sibling::android.view.View/android.widget.Button", "method": "locator", "timeout": 10, "timestamp": 1745485077480, "type": "tap"}, {"action_id": "F1olhgKhUt", "executionTime": "2309ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746100706297, "type": "tap"}, {"action_id": "drbQBpgBfM", "executionTime": "1970ms", "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"Checkout\"]", "timeout": 10, "timestamp": 1752315390781, "type": "tapIfLocatorExists"}, {"action_id": "7SpDO20tS2", "duration": 10, "executionTime": "10060ms", "time": 10, "timestamp": 1752058579398, "type": "wait"}, {"action_id": "Qbg9bipTGs", "count": 6, "direction": "up", "duration": 600, "end_x": 50, "end_y": 30, "executionTime": "12555ms", "image_filename": "remove-btn-android.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"Remove\")]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1752057053146, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "LWXWKZE4UV", "executionTime": "18720ms", "image_filename": "remove-btn-android.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"Remove\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1752065304144, "type": "tap"}, {"action_id": "FFM0CCo6Qg", "executionTime": "7726ms", "image_filename": "bag-close-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752030268858, "type": "tap"}, {"action_id": "k3mu9Mt7Ec", "enabled": true, "executionTime": "717ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746100354856, "type": "tap"}, {"count": 2, "direction": "up", "duration": 500, "end_x": 50, "end_y": 30, "executionTime": "6261ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtSign out\"]", "start_x": 50, "start_y": 70, "timestamp": 1755503915429, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "OyUowAaBzD", "executionTime": "1985ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1746100379226, "type": "tap"}, {"action_id": "cKNu2QoRC1", "executionTime": "1047ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 1 of 5\")]", "method": "locator", "timeout": 20, "timestamp": 1746426742221, "type": "tap"}, {"action_id": "o1gHFWhXTL", "double_tap": false, "executionTime": "3603ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"edtFind products & categories\"]/android.view.View", "method": "locator", "text_to_find": "Find", "timeout": 30, "timestamp": 1752053419264, "type": "tapOnText"}, {"action_id": "JRheeTvpJf", "enter": true, "executionTime": "1129ms", "function_name": "text", "text": "Uno Card", "timestamp": 1752053435644, "type": "text"}, {"action_id": "OAZvliPL2h", "executionTime": "1199ms", "function_name": "send_key_event", "key_event": "ENTER", "timestamp": 1754742488464, "type": "androidFunctions"}, {"action_id": "nAB6Q8LAdv", "executionTime": "4760ms", "interval": 0.5, "locator_type": "text", "locator_value": "Filter", "timeout": 30, "timestamp": 1752352042784, "type": "waitTill"}, {"action_id": "FnrbyHq7bU", "executionTime": "1681ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView/following-sibling::android.view.View/android.widget.Button", "method": "locator", "timeout": 10, "timestamp": 1752053469273, "type": "tap"}, {"action_id": "F1olhgKhUt", "executionTime": "1330ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1752315441174, "type": "tap"}, {"action_id": "zrdO3PVkX3", "double_tap": false, "executionTime": "1392ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"Checkout\"]", "method": "locator", "text_to_find": "//android.widget.Button[@content-desc=\"Checkout\"]", "timeout": 10, "timestamp": 1752315408026, "type": "tap"}, {"type": "multiStep", "timestamp": 1755982802793, "test_case_id": "Delivery_Buy_Steps_AUANDROID_20250709080728.json", "test_case_name": "Delivery Buy Steps_AU-ANDROID", "test_case_steps_count": 54, "cleanup": false, "expanded": false, "loading_in_progress": false, "test_case_steps": [{"action_id": "aqBkqyVhrZ", "executionTime": "16354ms", "image_filename": "delivery-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//android.widget.TextView[@text=\"Delivery\"])[2]", "threshold": 0.7, "timeout": 40, "timestamp": 1746427811120, "type": "waitTill"}, {"action_id": "hwdyCKFAUJ", "executionTime": "1814ms", "image_filename": "delivery-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//android.widget.TextView[@text=\"Delivery\"])[2]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746427830202, "type": "tap"}, {"text_to_find": "Continue", "timeout": 10, "timestamp": 1755505260890, "type": "tapIfTextExists"}, {"count": 2, "direction": "up", "duration": 500, "end_x": 50, "end_y": 30, "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"Continue\")]", "method": "locator", "start_x": 50, "start_y": 70, "timeout": 10, "timestamp": 1755505189074, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "yWSq8MkarI", "image_filename": "continue-to-details-android.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"Continue\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1752013699515, "type": "tap"}, {"action_id": "h9trcMrvxt", "double_tap": false, "executionTime": "3038ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"First Name\"]", "method": "locator", "text_to_find": "First", "timeout": 60, "timestamp": 1745486361281, "type": "tapOnText"}, {"action_id": "A9WXEiyRlw", "method": "auto", "timestamp": 1752354844316, "type": "clearText"}, {"action_id": "CLMmkV1OIM", "delay": 500, "enter": false, "executionTime": "3392ms", "function_name": "text", "text": "FirstName", "timestamp": 1752013110627, "type": "text"}, {"action_id": "h9trcMrvxt", "double_tap": false, "executionTime": "3038ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"lastName\"]", "method": "locator", "text_to_find": "Last", "timeout": 10, "timestamp": 1752013091371, "type": "tap"}, {"action_id": "MO3Or5F7bJ", "method": "auto", "timestamp": 1752354910646, "type": "clearText"}, {"action_id": "CLMmkV1OIM", "delay": 500, "enter": true, "executionTime": "3392ms", "function_name": "text", "text": "LastName", "timestamp": 1752353212516, "type": "text"}, {"action_id": "h9trcMrvxt", "double_tap": false, "executionTime": "3038ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"email\"]", "method": "locator", "text_to_find": "Email", "timeout": 10, "timestamp": 1752013140869, "type": "tap"}, {"action_id": "svX9BDSecB", "method": "auto", "timestamp": 1752354922124, "type": "clearText"}, {"action_id": "CLMmkV1OIM", "delay": 500, "enter": true, "executionTime": "3392ms", "function_name": "text", "text": "<EMAIL>", "timestamp": 1752013229068, "type": "text"}, {"action_id": "h9trcMrvxt", "double_tap": false, "executionTime": "3038ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"phone\"]", "method": "locator", "text_to_find": "Mobile", "timeout": 10, "timestamp": 1752013253357, "type": "tap"}, {"action_id": "py5TMT4nGE", "method": "auto", "timestamp": 1752354935697, "type": "clearText"}, {"action_id": "CLMmkV1OIM", "delay": 500, "enter": true, "executionTime": "3392ms", "function_name": "text", "text": "0400000000", "timestamp": 1752013288768, "type": "text"}, {"duration": 5, "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"Checkout\"]", "method": "locator", "time": 5, "timeout": 10, "timestamp": 1755505315938, "type": "wait"}, {"action_id": "2bcxKJ2cPg", "count": 2, "direction": "up", "duration": 600, "end_x": 50, "end_y": 30, "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"Continue\")]", "start_x": 50, "start_y": 70, "timestamp": 1755814029972, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "p8rfQL9ara", "double_tap": false, "executionTime": "3153ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"Continue\")]", "method": "locator", "text_to_find": "Continue", "timeout": 10, "timestamp": 1745486401162, "type": "tap"}, {"action_id": "txCZCXWjZy", "duration": 10, "method": "coordinates", "time": 10, "timestamp": 1752393112542, "type": "wait", "x": 0, "y": 0}, {"action_id": "QvuueoTR8W", "delay": 500, "double_tap": false, "executionTime": "3368ms", "function_name": "text", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"addressInfo\"]", "method": "locator", "text": "Last Name", "text_to_find": "address", "timeout": 10, "timestamp": 1745486416273, "type": "tap"}, {"action_id": "5ZzW1VVSzy", "double_tap": false, "enter": true, "executionTime": "2068ms", "image_filename": "delivery_addreess_input.png", "method": "image", "text": "305 238 Flinders Street", "text_to_find": "address", "threshold": 0.7, "timeout": 30, "timestamp": 1745562034217, "type": "text"}, {"action_id": "Wb6cwBudqO", "image_filename": "delivery-address-options-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752319573081, "type": "tap"}, {"action_id": "2bcxKJ2cPg", "count": 2, "direction": "up", "duration": 600, "end_x": 50, "end_y": 30, "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"Continue\")]", "start_x": 50, "start_y": 70, "timestamp": 1755810877973, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "1Lirmyxkft", "double_tap": false, "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"Continue\")]", "method": "locator", "text_to_find": "Continue", "timeout": 10, "timestamp": 1747044123748, "type": "tap"}, {"action_id": "6LQ5cq0f6N", "double_tap": false, "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"Select a payment method\"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[2]/XCUIElementTypeOther", "method": "locator", "text_to_find": "PayPal", "timeout": 30, "timestamp": 1747044256988, "type": "tapOnText"}, {"count": 1, "direction": "up", "duration": 300, "end_x": 50, "end_y": 40, "interval": 0.5, "method": "coordinates", "start_x": 50, "start_y": 70, "timestamp": 1755504064523, "type": "swipe", "vector_end": [0.5, 0.4], "vector_start": [0.5, 0.7], "x": 0, "y": 0}, {"action_id": "uFLk3ZE1vy", "image_filename": "paypal-payment-btn-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752014159220, "type": "tap"}, {"action_id": "uZEEeTeb7p", "duration": 5, "time": 5, "timestamp": 1752055297465, "type": "wait"}, {"action_id": "eEZcYUvCsS", "image_filename": "paypal-close-btn-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752014631385, "type": "tap"}, {"action_id": "uZEEeTeb7p", "duration": 5, "time": 5, "timestamp": 1752057105044, "type": "wait"}, {"action_id": "YKLcruMmBb", "image_filename": "PaypalIn4-Chkbox-Android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752027052273, "type": "tap"}, {"action_id": "uZEEeTeb7p", "duration": 5, "time": 5, "timestamp": 1752057111493, "type": "wait"}, {"action_id": "ejkRu2tGN0", "image_filename": "Payin4-btn-Android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752027189435, "type": "tap"}, {"action_id": "eEZcYUvCsS", "image_filename": "paypal-close-btn-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752027218034, "type": "tap"}, {"action_id": "uZEEeTeb7p", "duration": 5, "time": 5, "timestamp": 1752057116561, "type": "wait"}, {"action_id": "MRkOQpNybH", "image_filename": "afterpay-chkbox-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752027321632, "type": "tap"}, {"action_id": "mZBT5KezoO", "duration": 5, "time": 5, "timestamp": 1752055316968, "type": "wait"}, {"action_id": "YBT2MVclAv", "double_tap": false, "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"Select a payment method\"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[4]/XCUIElementTypeOther", "method": "locator", "text_to_find": "Check", "timeout": 60, "timestamp": 1747044719683, "type": "tapOnText"}, {"action_id": "VXo5C08UOn", "image_filename": "bag-close-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752355054265, "type": "tap", "x": 0, "y": 0}, {"action_id": "B1LewDSEWO", "duration": 5, "time": 5, "timestamp": 1752055277905, "type": "wait"}, {"action_id": "J2jslpsWRH", "image_filename": "zippay-chkbox-btn.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752027542997, "type": "tap"}, {"action_id": "B1LewDSEWO", "duration": 5, "time": 5, "timestamp": 1752057131204, "type": "wait"}, {"action_id": "YBT2MVclAv", "double_tap": false, "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"Select a payment method\"]/XCUIElementTypeOther[2]/XCUIElementTypeOther[4]/XCUIElementTypeOther", "method": "locator", "text_to_find": "Check", "timeout": 60, "timestamp": 1752027586080, "type": "tapOnText"}, {"action_id": "w53ZvIuyH3", "duration": 5, "time": 5, "timestamp": 1752055307951, "type": "wait"}, {"action_id": "VXo5C08UOn", "image_filename": "bag-close-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752361841871, "type": "tap", "x": 0, "y": 0}, {"action_id": "w53ZvIuyH3", "duration": 5, "time": 5, "timestamp": 1752062411590, "type": "wait"}, {"action_id": "gPYNwJ0HKo", "executionTime": "3312ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1745490084698, "type": "tap"}, {"action_id": "rv29mjptZ6", "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"Checkout\"]", "timeout": 10, "timestamp": 1753531649976, "type": "tapIfLocatorExists"}, {"action_id": "wSHsGWAwPm", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[contains(@text,\"Click\")]", "timeout": 20, "timestamp": 1751780577492, "type": "waitTill"}, {"action_id": "2bcxKJ2cPg", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"Remove\")]", "start_x": 50, "start_y": 70, "timestamp": 1748156595054, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "a4pJa7EAyI", "executionTime": "5059ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"Remove\")]", "method": "locator", "timeout": 10, "timestamp": 1745490172397, "type": "tap"}, {"action_id": "q6kSH9e0MI", "executionTime": "2804ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[@text=\"Continue shopping\"]", "method": "locator", "timeout": 10, "timestamp": 1745490217950, "type": "tap"}], "steps_loaded": true, "display_depth": 0}], "labels": [], "created_at": "2025-08-24T07:00:10.808295", "modified_at": "2025-08-24T07:00:10.808295"}