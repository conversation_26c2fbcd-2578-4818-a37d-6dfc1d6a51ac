{"name": "UI Execution 27/08/2025, 07:46:02", "testCases": [{"name": "Calc-AndroidTest-Extra-Steps\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Edit\n                            \n                            \n                                 Remove\n                            \n                            7 actions", "status": "failed", "steps": [{"name": "Restart app: com.coloros.calculator", "status": "passed", "duration": "2159ms", "action_id": "calculator", "screenshot_filename": "calculator.png", "report_screenshot": "calculator.png", "resolved_screenshot": "screenshots/calculator.png", "clean_action_id": "calculator", "prefixed_action_id": "al_calculator", "timestamp": "2025-08-27 07:46:02", "screenshot": "screenshots/calculator.png", "action_id_screenshot": "screenshots/calculator.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[@content-desc=\"More options,\"]", "status": "passed", "duration": "568ms", "action_id": "screenshot_20250827_074536", "screenshot_filename": "screenshot_20250827_074536.png", "report_screenshot": "screenshot_20250827_074536.png", "resolved_screenshot": "screenshots/screenshot_20250827_074536.png", "clean_action_id": "screenshot_20250827_074536", "prefixed_action_id": "al_screenshot_20250827_074536", "timestamp": "2025-08-27 07:46:02", "screenshot": "screenshots/screenshot_20250827_074536.png", "action_id_screenshot": "screenshots/screenshot_20250827_074536.png"}, {"name": "Tap on Text: \"<PERSON>ting<PERSON>\"", "status": "passed", "duration": "2698ms", "action_id": "screenshot_20250827_074551", "screenshot_filename": "screenshot_20250827_074551.png", "report_screenshot": "screenshot_20250827_074551.png", "resolved_screenshot": "screenshots/screenshot_20250827_074551.png", "clean_action_id": "screenshot_20250827_074551", "prefixed_action_id": "al_screenshot_20250827_074551", "timestamp": "2025-08-27 07:46:02", "screenshot": "screenshots/screenshot_20250827_074551.png", "action_id_screenshot": "screenshots/screenshot_20250827_074551.png"}, {"name": "Tap on element with xpath: //android.widget.ImageButton[@content-desc=\"xyz\"]", "status": "failed", "duration": "0ms", "action_id": "ImageButto", "screenshot_filename": "ImageButto.png", "report_screenshot": "ImageButto.png", "resolved_screenshot": "screenshots/ImageButto.png", "clean_action_id": "ImageButto", "prefixed_action_id": "al_ImageButto", "timestamp": "2025-08-27 07:46:02", "screenshot": "screenshots/ImageButto.png", "action_id_screenshot": "screenshots/ImageButto.png"}, {"name": "Tap on element with xpath: //android.widget.TextView[@content-desc=\"Scientific\"]", "status": "unknown", "duration": "0ms", "action_id": "Scientific", "screenshot_filename": "Scientific.png", "report_screenshot": "Scientific.png", "resolved_screenshot": "screenshots/Scientific.png", "clean_action_id": "Scientific", "prefixed_action_id": "al_Scientific", "timestamp": "2025-08-27 07:46:02", "screenshot": "screenshots/Scientific.png", "action_id_screenshot": "screenshots/Scientific.png"}, {"name": "Terminate app: com.coloros.calculator", "status": "unknown", "duration": "0ms", "action_id": "calculator", "screenshot_filename": "calculator.png", "report_screenshot": "calculator.png", "resolved_screenshot": "screenshots/calculator.png", "clean_action_id": "calculator", "prefixed_action_id": "al_calculator", "timestamp": "2025-08-27 07:46:02", "screenshot": "screenshots/calculator.png", "action_id_screenshot": "screenshots/calculator.png"}, {"name": "cleanupSteps action", "status": "unknown", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png", "clean_action_id": "cleanupSte", "prefixed_action_id": "al_cleanupSte", "timestamp": "2025-08-27 07:46:02", "screenshot": "screenshots/cleanupSte.png", "action_id_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "Calc-AndroidTest\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Edit\n                            \n                            \n                                 Remove\n                            \n                            6 actions", "status": "passed", "steps": [{"name": "Restart app: com.coloros.calculator", "status": "passed", "duration": "633ms", "action_id": "calculator", "screenshot_filename": "calculator.png", "report_screenshot": "calculator.png", "resolved_screenshot": "screenshots/calculator.png", "clean_action_id": "aLsEL0rdbN", "prefixed_action_id": "al_aLsEL0rdbN", "timestamp": "2025-08-27 07:46:02", "screenshot": "screenshots/aLsEL0rdbN.png", "action_id_screenshot": "screenshots/calculator.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[@content-desc=\"More options,\"]", "status": "passed", "duration": "463ms", "action_id": "mrCTI6auS1", "screenshot_filename": "mrCTI6auS1.png", "report_screenshot": "mrCTI6auS1.png", "resolved_screenshot": "screenshots/mrCTI6auS1.png", "clean_action_id": "mrCTI6auS1", "prefixed_action_id": "al_mrCTI6auS1", "timestamp": "2025-08-27 07:46:02", "screenshot": "screenshots/mrCTI6auS1.png", "action_id_screenshot": "screenshots/mrCTI6auS1.png"}, {"name": "Tap on Text: \"<PERSON>ting<PERSON>\"", "status": "passed", "duration": "2545ms", "action_id": "screenshot_20250827_074417", "screenshot_filename": "screenshot_20250827_074417.png", "report_screenshot": "screenshot_20250827_074417.png", "resolved_screenshot": "screenshots/screenshot_20250827_074417.png", "clean_action_id": "screenshot_20250827_074417", "prefixed_action_id": "al_screenshot_20250827_074417", "timestamp": "2025-08-27 07:46:02", "screenshot": "screenshots/screenshot_20250827_074417.png", "action_id_screenshot": "screenshots/screenshot_20250827_074417.png"}, {"name": "Tap on element with xpath: //android.widget.ImageButton[@content-desc=\"Navigate up\"]", "status": "passed", "duration": "713ms", "action_id": "ImageButto", "screenshot_filename": "ImageButto.png", "report_screenshot": "ImageButto.png", "resolved_screenshot": "screenshots/ImageButto.png", "clean_action_id": "screenshot_20250827_074536", "prefixed_action_id": "al_screenshot_20250827_074536", "timestamp": "2025-08-27 07:46:02", "screenshot": "screenshots/screenshot_20250827_074536.png", "action_id_screenshot": "screenshots/ImageButto.png"}, {"name": "Tap on element with xpath: //android.widget.TextView[@content-desc=\"Scientific\"]", "status": "passed", "duration": "901ms", "action_id": "Scientific", "screenshot_filename": "Scientific.png", "report_screenshot": "Scientific.png", "resolved_screenshot": "screenshots/Scientific.png", "clean_action_id": "screenshot_20250827_074548", "prefixed_action_id": "al_screenshot_20250827_074548", "timestamp": "2025-08-27 07:46:02", "screenshot": "screenshots/screenshot_20250827_074548.png", "action_id_screenshot": "screenshots/Scientific.png"}, {"name": "Terminate app: com.coloros.calculator", "status": "passed", "duration": "3175ms", "action_id": "calculator", "screenshot_filename": "calculator.png", "report_screenshot": "calculator.png", "resolved_screenshot": "screenshots/calculator.png", "clean_action_id": "screenshot_20250827_074551", "prefixed_action_id": "al_screenshot_20250827_074551", "timestamp": "2025-08-27 07:46:02", "screenshot": "screenshots/screenshot_20250827_074551.png", "action_id_screenshot": "screenshots/calculator.png"}]}], "passed": 1, "failed": 1, "skipped": 0, "status": "failed", "id": "d815b1ea-386a-4efe-ac3e-63595de3e9c6"}