Action Log - 2025-08-26 20:26:50
================================================================================

[[20:26:50]] [INFO] Generating execution report...
[[20:26:50]] [WARNING] 1 test failed.
[[20:26:50]] [SUCCESS] Screenshot refreshed
[[20:26:50]] [INFO] Refreshing screenshot...
[[20:26:49]] [SUCCESS] Screenshot refreshed successfully
[[20:26:49]] [SUCCESS] Screenshot refreshed successfully
[[20:26:48]] [INFO] Executing action 13/13: Terminate app: com.coloros.calculator
[[20:26:48]] [SUCCESS] Screenshot refreshed
[[20:26:48]] [INFO] Refreshing screenshot...
[[20:26:46]] [SUCCESS] Screenshot refreshed successfully
[[20:26:46]] [SUCCESS] Screenshot refreshed successfully
[[20:26:46]] [INFO] Executing action 12/13: Tap on element with xpath: //android.widget.TextView[@content-desc="Scientific"]
[[20:26:46]] [SUCCESS] Screenshot refreshed
[[20:26:46]] [INFO] Refreshing screenshot...
[[20:26:44]] [SUCCESS] Screenshot refreshed successfully
[[20:26:44]] [SUCCESS] Screenshot refreshed successfully
[[20:26:10]] [INFO] Executing action 11/13: Tap on element with xpath: //android.widget.ImageButton[@content-desc="Navigate up"]
[[20:26:10]] [SUCCESS] Screenshot refreshed
[[20:26:10]] [INFO] Refreshing screenshot...
[[20:26:07]] [SUCCESS] Screenshot refreshed successfully
[[20:26:07]] [SUCCESS] Screenshot refreshed successfully
[[20:26:06]] [INFO] Executing action 10/13: Tap on Text: "Settings"
[[20:26:06]] [SUCCESS] Screenshot refreshed
[[20:26:06]] [INFO] Refreshing screenshot...
[[20:26:05]] [SUCCESS] Screenshot refreshed successfully
[[20:26:05]] [SUCCESS] Screenshot refreshed successfully
[[20:26:04]] [INFO] Executing action 9/13: Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"]
[[20:26:04]] [SUCCESS] Screenshot refreshed
[[20:26:04]] [INFO] Refreshing screenshot...
[[20:25:51]] [INFO] Executing action 8/13: Restart app: com.coloros.calculator
[[20:25:51]] [INFO] Skipping remaining steps in failed test case (moving from action 4 to next test case at 7)
[[20:25:51]] [ERROR] Action 4 failed: Element not found or not tappable after all retry strategies: xpath='//android.widget.ImageButton[@content-desc="xyz"]'
[[20:24:07]] [SUCCESS] Screenshot refreshed successfully
[[20:24:07]] [SUCCESS] Screenshot refreshed successfully
[[20:24:06]] [INFO] Executing action 4/13: Tap on element with xpath: //android.widget.ImageButton[@content-desc="xyz"]
[[20:24:06]] [SUCCESS] Screenshot refreshed
[[20:24:06]] [INFO] Refreshing screenshot...
[[20:24:03]] [SUCCESS] Screenshot refreshed successfully
[[20:24:03]] [SUCCESS] Screenshot refreshed successfully
[[20:24:02]] [INFO] Executing action 3/13: Tap on Text: "Settings"
[[20:24:02]] [SUCCESS] Screenshot refreshed
[[20:24:02]] [INFO] Refreshing screenshot...
[[20:24:00]] [SUCCESS] Screenshot refreshed successfully
[[20:24:00]] [SUCCESS] Screenshot refreshed successfully
[[20:23:54]] [INFO] Executing action 2/13: Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"]
[[20:23:54]] [SUCCESS] Screenshot refreshed
[[20:23:54]] [INFO] Refreshing screenshot...
[[20:23:52]] [INFO] Executing action 1/13: Restart app: com.coloros.calculator
[[20:23:52]] [INFO] ExecutionManager: Starting execution of 13 actions...
[[20:23:52]] [SUCCESS] Cleared 0 screenshots from database
[[20:23:52]] [INFO] Clearing screenshots from database before execution...
[[20:23:52]] [SUCCESS] All screenshots deleted successfully
[[20:23:52]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[20:23:52]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250826_202352/screenshots
[[20:23:52]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250826_202352
[[20:23:52]] [SUCCESS] Report directory initialized successfully
[[20:23:52]] [INFO] Initializing report directory and screenshots folder for test suite...
[[20:23:47]] [SUCCESS] All screenshots deleted successfully
[[20:23:47]] [INFO] All actions cleared
[[20:23:47]] [INFO] Cleaning up screenshots...
[[20:23:37]] [SUCCESS] All screenshots deleted successfully
[[20:23:37]] [INFO] All actions cleared
[[20:23:37]] [INFO] Cleaning up screenshots...
[[20:23:13]] [SUCCESS] Screenshot refreshed successfully
[[20:23:12]] [SUCCESS] Screenshot refreshed
[[20:23:12]] [INFO] Refreshing screenshot...
[[20:23:11]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8
[[20:23:11]] [INFO] Device info updated: RMX2151
[[20:22:59]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[20:22:57]] [SUCCESS] Found 1 device(s)
[[20:22:57]] [INFO] Refreshing device list...
