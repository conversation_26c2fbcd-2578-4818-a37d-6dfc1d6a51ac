Action Log - 2025-08-27 18:16:01
================================================================================

[[18:16:01]] [INFO] Generating execution report...
[[18:16:01]] [WARNING] 1 test failed.
[[18:16:01]] [SUCCESS] Screenshot refreshed
[[18:16:01]] [INFO] Refreshing screenshot...
[[18:16:01]] [INFO] Qb1AArnpCH=pass
[[18:15:35]] [SUCCESS] Screenshot refreshed successfully
[[18:15:34]] [INFO] Qb1AArnpCH=running
[[18:15:34]] [INFO] Executing action 482/482: Wait for 5 ms
[[18:15:34]] [SUCCESS] Screenshot refreshed
[[18:15:34]] [INFO] Refreshing screenshot...
[[18:15:34]] [INFO] 3NOS1fbxZs=pass
[[18:15:09]] [SUCCESS] Screenshot refreshed successfully
[[18:15:09]] [INFO] 3NOS1fbxZs=running
[[18:15:09]] [INFO] Executing action 481/482: Tap on image: banner-close-updated.png
[[18:15:09]] [SUCCESS] Screenshot refreshed
[[18:15:09]] [INFO] Refreshing screenshot...
[[18:15:09]] [INFO] K0c1gL9UK1=pass
[[18:15:07]] [SUCCESS] Screenshot refreshed successfully
[[18:15:06]] [INFO] K0c1gL9UK1=running
[[18:15:06]] [INFO] Executing action 480/482: Tap on element with xpath: //android.widget.Button[contains(@text,"Remove")]
[[18:15:06]] [SUCCESS] Screenshot refreshed
[[18:15:06]] [INFO] Refreshing screenshot...
[[18:15:03]] [SUCCESS] Screenshot refreshed successfully
[[18:15:03]] [INFO] Executing action 479/482: Swipe up till element xpath: "//android.widget.Button[contains(@text,"Remove")]" is visible
[[18:15:03]] [SUCCESS] Screenshot refreshed
[[18:15:03]] [INFO] Refreshing screenshot...
[[18:15:03]] [INFO] igReeDqips=pass
[[18:14:55]] [SUCCESS] Screenshot refreshed successfully
[[18:14:54]] [INFO] igReeDqips=running
[[18:14:54]] [INFO] Executing action 478/482: Tap on element with xpath: //android.view.View[@text="Delivery"]
[[18:14:54]] [SUCCESS] Screenshot refreshed
[[18:14:54]] [INFO] Refreshing screenshot...
[[18:14:54]] [INFO] jW6OPorKBq=pass
[[18:14:53]] [SUCCESS] Screenshot refreshed successfully
[[18:14:52]] [INFO] jW6OPorKBq=running
[[18:14:52]] [INFO] Executing action 477/482: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[18:14:52]] [SUCCESS] Screenshot refreshed
[[18:14:52]] [INFO] Refreshing screenshot...
[[18:14:52]] [INFO] UpUSVInizv=pass
[[18:14:50]] [SUCCESS] Screenshot refreshed successfully
[[18:14:50]] [INFO] UpUSVInizv=running
[[18:14:50]] [INFO] Executing action 476/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[18:14:50]] [SUCCESS] Screenshot refreshed
[[18:14:50]] [INFO] Refreshing screenshot...
[[18:14:50]] [INFO] Iab9zCfpqO=pass
[[18:14:48]] [SUCCESS] Screenshot refreshed successfully
[[18:14:47]] [INFO] Iab9zCfpqO=running
[[18:14:47]] [INFO] Executing action 475/482: Tap on element with xpath: //android.widget.Button[@text="Add to bag"] with fallback: Text "Add"
[[18:14:47]] [SUCCESS] Screenshot refreshed
[[18:14:47]] [INFO] Refreshing screenshot...
[[18:14:47]] [INFO] ZZPNqTJ65s=pass
[[18:14:43]] [SUCCESS] Screenshot refreshed successfully
[[18:14:41]] [INFO] ZZPNqTJ65s=running
[[18:14:41]] [INFO] Executing action 474/482: Swipe from (50%, 70%) to (50%, 30%)
[[18:14:41]] [SUCCESS] Screenshot refreshed
[[18:14:41]] [INFO] Refreshing screenshot...
[[18:14:34]] [SUCCESS] Screenshot refreshed successfully
[[18:14:34]] [INFO] Executing action 473/482: Wait till xpath=//android.widget.TextView[contains(@text,"SKU")]
[[18:14:34]] [SUCCESS] Screenshot refreshed
[[18:14:34]] [INFO] Refreshing screenshot...
[[18:14:34]] [INFO] Qy0Y0uJchm=pass
[[18:14:32]] [SUCCESS] Screenshot refreshed successfully
[[18:14:31]] [INFO] Qy0Y0uJchm=running
[[18:14:31]] [INFO] Executing action 472/482: Tap on element with xpath: (//android.widget.TextView[contains(@text,"$")])[1]
[[18:14:31]] [SUCCESS] Screenshot refreshed
[[18:14:31]] [INFO] Refreshing screenshot...
[[18:14:28]] [SUCCESS] Screenshot refreshed successfully
[[18:14:27]] [INFO] Executing action 471/482: Wait till xpath=(//android.widget.TextView[contains(@text,"$")])[1]
[[18:14:27]] [SUCCESS] Screenshot refreshed
[[18:14:27]] [INFO] Refreshing screenshot...
[[18:14:26]] [SUCCESS] Screenshot refreshed successfully
[[18:14:24]] [INFO] Executing action 470/482: Android Function: send_key_event - Key Event: ENTER
[[18:14:24]] [SUCCESS] Screenshot refreshed
[[18:14:24]] [INFO] Refreshing screenshot...
[[18:14:22]] [SUCCESS] Screenshot refreshed successfully
[[18:14:21]] [INFO] Executing action 469/482: Input text: "Toys"
[[18:14:21]] [SUCCESS] Screenshot refreshed
[[18:14:21]] [INFO] Refreshing screenshot...
[[18:14:21]] [INFO] YHaMIjULRf=pass
[[18:14:16]] [SUCCESS] Screenshot refreshed successfully
[[18:14:14]] [INFO] YHaMIjULRf=running
[[18:14:14]] [INFO] Executing action 468/482: Tap on Text: "Search"
[[18:14:14]] [SUCCESS] Screenshot refreshed
[[18:14:14]] [INFO] Refreshing screenshot...
[[18:14:14]] [INFO] S7PVvWSmaK=pass
[[18:14:03]] [SUCCESS] Screenshot refreshed successfully
[[18:14:02]] [INFO] S7PVvWSmaK=running
[[18:14:02]] [INFO] Executing action 467/482: Tap on image: catalogue-menu-android.png
[[18:14:02]] [SUCCESS] Screenshot refreshed
[[18:14:02]] [INFO] Refreshing screenshot...
[[18:14:02]] [INFO] gkkQzTCmma=pass
[[18:13:40]] [SUCCESS] Screenshot refreshed successfully
[[18:13:39]] [INFO] gkkQzTCmma=running
[[18:13:39]] [INFO] Executing action 466/482: Tap on Text: "Catalogue"
[[18:13:39]] [SUCCESS] Screenshot refreshed
[[18:13:39]] [INFO] Refreshing screenshot...
[[18:13:39]] [INFO] ZZPNqTJ65s=pass
[[18:13:36]] [SUCCESS] Screenshot refreshed successfully
[[18:13:36]] [INFO] ZZPNqTJ65s=running
[[18:13:36]] [INFO] Executing action 465/482: Swipe from (50%, 70%) to (50%, 30%)
[[18:13:36]] [SUCCESS] Screenshot refreshed
[[18:13:36]] [INFO] Refreshing screenshot...
[[18:13:36]] [INFO] UpUSVInizv=pass
[[18:13:34]] [SUCCESS] Screenshot refreshed successfully
[[18:13:33]] [INFO] UpUSVInizv=running
[[18:13:33]] [INFO] Executing action 464/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 2 of 5")]
[[18:13:33]] [SUCCESS] Screenshot refreshed
[[18:13:33]] [INFO] Refreshing screenshot...
[[18:13:33]] [INFO] UpUSVInizv=pass
[[18:13:23]] [SUCCESS] Screenshot refreshed successfully
[[18:13:22]] [INFO] UpUSVInizv=running
[[18:13:22]] [INFO] Executing action 463/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 2 of 5")]
[[18:13:22]] [SUCCESS] Screenshot refreshed
[[18:13:22]] [INFO] Refreshing screenshot...
[[18:13:22]] [INFO] KqTcr10JDm=pass
[[18:13:14]] [SUCCESS] Screenshot refreshed successfully
[[18:13:13]] [INFO] KqTcr10JDm=running
[[18:13:13]] [INFO] Executing action 462/482: Tap on image: bag-close-android.png
[[18:13:13]] [SUCCESS] Screenshot refreshed
[[18:13:13]] [INFO] Refreshing screenshot...
[[18:13:13]] [INFO] JcAR0JctQ6=pass
[[18:13:11]] [SUCCESS] Screenshot refreshed successfully
[[18:13:10]] [INFO] JcAR0JctQ6=running
[[18:13:10]] [INFO] Executing action 461/482: Tap on element with xpath: //android.widget.Button[contains(@text,"Remove")]
[[18:13:10]] [SUCCESS] Screenshot refreshed
[[18:13:10]] [INFO] Refreshing screenshot...
[[18:13:10]] [INFO] Pd7cReoJM6=pass
[[18:13:07]] [SUCCESS] Screenshot refreshed successfully
[[18:13:06]] [INFO] Pd7cReoJM6=running
[[18:13:06]] [INFO] Executing action 460/482: Tap on element with xpath: //android.widget.Button[@text="Decrease quantity"]
[[18:13:06]] [SUCCESS] Screenshot refreshed
[[18:13:06]] [INFO] Refreshing screenshot...
[[18:13:06]] [INFO] Pd7cReoJM6=pass
[[18:13:05]] [SUCCESS] Screenshot refreshed successfully
[[18:13:04]] [INFO] Pd7cReoJM6=running
[[18:13:04]] [INFO] Executing action 459/482: Tap on element with xpath: //android.widget.Button[@text="Increase quantity"]
[[18:13:04]] [SUCCESS] Screenshot refreshed
[[18:13:04]] [INFO] Refreshing screenshot...
[[18:13:04]] [INFO] ZZPNqTJ65s=pass
[[18:13:01]] [SUCCESS] Screenshot refreshed successfully
[[18:13:01]] [INFO] ZZPNqTJ65s=running
[[18:13:01]] [INFO] Executing action 458/482: Swipe from (50%, 70%) to (50%, 50%)
[[18:13:01]] [SUCCESS] Screenshot refreshed
[[18:13:01]] [INFO] Refreshing screenshot...
[[18:13:01]] [INFO] igReeDqips=pass
[[18:12:53]] [SUCCESS] Screenshot refreshed successfully
[[18:12:52]] [INFO] igReeDqips=running
[[18:12:52]] [INFO] Executing action 457/482: Tap on element with xpath: //android.view.View[@text="Delivery"]
[[18:12:52]] [SUCCESS] Screenshot refreshed
[[18:12:52]] [INFO] Refreshing screenshot...
[[18:12:51]] [SUCCESS] Screenshot refreshed successfully
[[18:12:51]] [INFO] Executing action 456/482: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[18:12:51]] [SUCCESS] Screenshot refreshed
[[18:12:51]] [INFO] Refreshing screenshot...
[[18:12:51]] [INFO] UpUSVInizv=pass
[[18:12:49]] [SUCCESS] Screenshot refreshed successfully
[[18:12:47]] [INFO] UpUSVInizv=running
[[18:12:47]] [INFO] Executing action 455/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[18:12:47]] [SUCCESS] Screenshot refreshed
[[18:12:47]] [INFO] Refreshing screenshot...
[[18:12:47]] [SUCCESS] Screenshot refreshed
[[18:12:47]] [INFO] Refreshing screenshot...
[[18:12:46]] [SUCCESS] Screenshot refreshed successfully
[[18:12:45]] [INFO] Executing Multi Step action step 10/10: Tap on element with xpath: //android.widget.Button[@text="Add to bag"]
[[18:12:45]] [SUCCESS] Screenshot refreshed
[[18:12:45]] [INFO] Refreshing screenshot...
[[18:12:40]] [SUCCESS] Screenshot refreshed successfully
[[18:12:39]] [INFO] Executing Multi Step action step 9/10: Swipe up till element xpath: "//android.widget.Button[@text="Add to bag"]" is visible
[[18:12:39]] [SUCCESS] Screenshot refreshed
[[18:12:39]] [INFO] Refreshing screenshot...
[[18:12:37]] [SUCCESS] Screenshot refreshed successfully
[[18:12:36]] [INFO] Executing Multi Step action step 8/10: Tap on element with xpath: //android.view.View[contains(@content-desc,"to bag Add")]
[[18:12:36]] [SUCCESS] Screenshot refreshed
[[18:12:36]] [INFO] Refreshing screenshot...
[[18:12:34]] [SUCCESS] Screenshot refreshed successfully
[[18:12:33]] [INFO] Executing Multi Step action step 7/10: Tap on element with xpath: //android.widget.Button[contains(@text,"Show")]
[[18:12:33]] [SUCCESS] Screenshot refreshed
[[18:12:33]] [INFO] Refreshing screenshot...
[[18:12:19]] [SUCCESS] Screenshot refreshed successfully
[[18:12:18]] [INFO] Executing Multi Step action step 6/10: Wait till xpath=//android.widget.Button[contains(@text,"Show")]
[[18:12:18]] [SUCCESS] Screenshot refreshed
[[18:12:18]] [INFO] Refreshing screenshot...
[[18:12:16]] [SUCCESS] Screenshot refreshed successfully
[[18:12:16]] [INFO] Executing Multi Step action step 5/10: Tap on element with xpath: //android.widget.Image[@text="Unchecked"]
[[18:12:16]] [SUCCESS] Screenshot refreshed
[[18:12:16]] [INFO] Refreshing screenshot...
[[18:12:07]] [SUCCESS] Screenshot refreshed successfully
[[18:12:06]] [INFO] Executing Multi Step action step 4/10: Tap on element with xpath: //android.widget.Button[@text="Filter"]
[[18:12:06]] [SUCCESS] Screenshot refreshed
[[18:12:06]] [INFO] Refreshing screenshot...
[[18:12:03]] [SUCCESS] Screenshot refreshed successfully
[[18:12:02]] [INFO] Executing Multi Step action step 3/10: Tap on Text: "Arrivals"
[[18:12:02]] [SUCCESS] Screenshot refreshed
[[18:12:02]] [INFO] Refreshing screenshot...
[[18:11:59]] [SUCCESS] Screenshot refreshed successfully
[[18:11:58]] [INFO] Executing Multi Step action step 2/10: Tap on Text: "Home"
[[18:11:58]] [SUCCESS] Screenshot refreshed
[[18:11:58]] [INFO] Refreshing screenshot...
[[18:11:54]] [SUCCESS] Screenshot refreshed successfully
[[18:11:53]] [INFO] Executing Multi Step action step 1/10: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 2 of 5")]
[[18:11:53]] [INFO] Loaded 10 steps from test case: Browse Add to Bag
[[18:11:53]] [INFO] Loading steps for Multi Step action: Browse Add to Bag
[[18:11:53]] [INFO] Executing action 454/482: Execute Test Case: Browse Add to Bag (10 steps)
[[18:11:53]] [SUCCESS] Screenshot refreshed
[[18:11:53]] [INFO] Refreshing screenshot...
[[18:11:53]] [INFO] gkkQzTCmma=pass
[[18:11:41]] [SUCCESS] Screenshot refreshed successfully
[[18:11:40]] [INFO] gkkQzTCmma=running
[[18:11:40]] [INFO] Executing action 453/482: Android Function: send_key_event - Key Event: BACK
[[18:11:40]] [SUCCESS] Screenshot refreshed
[[18:11:40]] [INFO] Refreshing screenshot...
[[18:11:40]] [INFO] gkkQzTCmma=pass
[[18:11:39]] [SUCCESS] Screenshot refreshed successfully
[[18:11:38]] [INFO] gkkQzTCmma=running
[[18:11:38]] [INFO] Executing action 452/482: Android Function: send_key_event - Key Event: BACK
[[18:11:38]] [SUCCESS] Screenshot refreshed
[[18:11:38]] [INFO] Refreshing screenshot...
[[18:11:38]] [INFO] gkkQzTCmma=pass
[[18:11:06]] [SUCCESS] Screenshot refreshed successfully
[[18:11:05]] [INFO] gkkQzTCmma=running
[[18:11:05]] [INFO] Executing action 451/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"$")] with fallback: Text "$"
[[18:11:05]] [SUCCESS] Screenshot refreshed
[[18:11:05]] [INFO] Refreshing screenshot...
[[18:11:05]] [INFO] QUeGIASAxV=pass
[[18:11:03]] [SUCCESS] Screenshot refreshed successfully
[[18:11:02]] [INFO] QUeGIASAxV=running
[[18:11:02]] [INFO] Executing action 450/482: Tap on element with xpath: //android.widget.ImageView[@content-desc="Styled by You"]/android.view.View[2]/android.view.View/android.widget.ImageView[1]
[[18:11:02]] [SUCCESS] Screenshot refreshed
[[18:11:02]] [INFO] Refreshing screenshot...
[[18:11:02]] [INFO] Cmvm82hiAa=pass
[[18:11:00]] [SUCCESS] Screenshot refreshed successfully
[[18:10:59]] [INFO] Cmvm82hiAa=running
[[18:10:59]] [INFO] Executing action 449/482: Tap on element with xpath: //android.widget.Button[@content-desc="Home & Living"]
[[18:10:59]] [SUCCESS] Screenshot refreshed
[[18:10:59]] [INFO] Refreshing screenshot...
[[18:10:59]] [INFO] QUeGIASAxV=pass
[[18:10:58]] [SUCCESS] Screenshot refreshed successfully
[[18:10:57]] [INFO] QUeGIASAxV=running
[[18:10:57]] [INFO] Executing action 448/482: Swipe from (50%, 70%) to (50%, 30%)
[[18:10:57]] [SUCCESS] Screenshot refreshed
[[18:10:57]] [INFO] Refreshing screenshot...
[[18:10:57]] [INFO] UpUSVInizv=pass
[[18:10:55]] [SUCCESS] Screenshot refreshed successfully
[[18:10:54]] [INFO] UpUSVInizv=running
[[18:10:54]] [INFO] Executing action 447/482: Tap on element with xpath: //android.widget.TextView[@text="https://www.kmart.com.au"]
[[18:10:54]] [SUCCESS] Screenshot refreshed
[[18:10:54]] [INFO] Refreshing screenshot...
[[18:10:54]] [INFO] mPIklklBO0=pass
[[18:10:52]] [SUCCESS] Screenshot refreshed successfully
[[18:10:51]] [INFO] mPIklklBO0=running
[[18:10:51]] [INFO] Executing action 446/482: Android Function: send_key_event - Key Event: ENTER
[[18:10:51]] [SUCCESS] Screenshot refreshed
[[18:10:51]] [INFO] Refreshing screenshot...
[[18:10:51]] [INFO] fTdGMJ3NH3=pass
[[18:10:50]] [SUCCESS] Screenshot refreshed successfully
[[18:10:49]] [INFO] fTdGMJ3NH3=running
[[18:10:49]] [INFO] Executing action 445/482: Input text: "kmart au"
[[18:10:49]] [SUCCESS] Screenshot refreshed
[[18:10:49]] [INFO] Refreshing screenshot...
[[18:10:36]] [SUCCESS] Screenshot refreshed successfully
[[18:10:35]] [INFO] Executing action 444/482: Tap if locator exists: xpath="//android.widget.EditText[@resource-id="com.android.chrome:id/url_bar"]"
[[18:10:35]] [SUCCESS] Screenshot refreshed
[[18:10:35]] [INFO] Refreshing screenshot...
[[18:10:35]] [INFO] t6c0RSgUVq=pass
[[18:10:23]] [SUCCESS] Screenshot refreshed successfully
[[18:10:22]] [INFO] t6c0RSgUVq=running
[[18:10:22]] [INFO] Executing action 443/482: Tap if locator exists: xpath="//android.widget.Button[@resource-id="com.android.chrome:id/ack_button"]"
[[18:10:22]] [SUCCESS] Screenshot refreshed
[[18:10:22]] [INFO] Refreshing screenshot...
[[18:10:22]] [INFO] pOJrD4NK3F=pass
[[18:10:10]] [SUCCESS] Screenshot refreshed successfully
[[18:10:09]] [INFO] pOJrD4NK3F=running
[[18:10:09]] [INFO] Executing action 442/482: Tap if locator exists: xpath="//android.widget.Button[@resource-id="com.android.chrome:id/more_button"]"
[[18:10:09]] [SUCCESS] Screenshot refreshed
[[18:10:09]] [INFO] Refreshing screenshot...
[[18:10:09]] [INFO] KAyXxO6c02=pass
[[18:09:36]] [SUCCESS] Screenshot refreshed successfully
[[18:09:36]] [INFO] KAyXxO6c02=running
[[18:09:36]] [INFO] Executing action 441/482: Tap if locator exists: xpath="//android.widget.Button[@resource-id="com.android.chrome:id/signin_fre_dismiss_button"]"
[[18:09:36]] [SUCCESS] Screenshot refreshed
[[18:09:36]] [INFO] Refreshing screenshot...
[[18:09:36]] [INFO] rmqVgsHPp8=pass
[[18:09:34]] [SUCCESS] Screenshot refreshed successfully
[[18:09:34]] [INFO] rmqVgsHPp8=running
[[18:09:34]] [INFO] Executing action 440/482: Restart app: com.android.chrome
[[18:09:34]] [SUCCESS] Screenshot refreshed
[[18:09:34]] [INFO] Refreshing screenshot...
[[18:09:32]] [SUCCESS] Screenshot refreshed successfully
[[18:09:31]] [INFO] Executing action 439/482: Terminate app: com.android.chrome
[[18:09:31]] [SUCCESS] Screenshot refreshed
[[18:09:31]] [INFO] Refreshing screenshot...
[[18:09:31]] [INFO] LcYLwUffqj=pass
[[18:09:28]] [SUCCESS] Screenshot refreshed successfully
[[18:09:28]] [INFO] LcYLwUffqj=running
[[18:09:28]] [INFO] Executing action 438/482: Tap on Text: "out"
[[18:09:28]] [SUCCESS] Screenshot refreshed
[[18:09:28]] [INFO] Refreshing screenshot...
[[18:09:28]] [INFO] ZZPNqTJ65s=pass
[[18:09:22]] [SUCCESS] Screenshot refreshed successfully
[[18:09:22]] [INFO] ZZPNqTJ65s=running
[[18:09:22]] [INFO] Executing action 437/482: Swipe from (50%, 70%) to (50%, 10%)
[[18:09:22]] [SUCCESS] Screenshot refreshed
[[18:09:22]] [INFO] Refreshing screenshot...
[[18:09:22]] [INFO] UpUSVInizv=pass
[[18:09:17]] [SUCCESS] Screenshot refreshed successfully
[[18:09:16]] [INFO] UpUSVInizv=running
[[18:09:16]] [INFO] Executing action 436/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[18:09:16]] [SUCCESS] Screenshot refreshed
[[18:09:16]] [INFO] Refreshing screenshot...
[[18:09:16]] [INFO] hCCEvRtj1A=pass
[[18:09:15]] [SUCCESS] Screenshot refreshed successfully
[[18:09:11]] [INFO] hCCEvRtj1A=running
[[18:09:11]] [INFO] Executing action 435/482: Launch app: au.com.kmart
[[18:09:11]] [SUCCESS] Screenshot refreshed
[[18:09:11]] [INFO] Refreshing screenshot...
[[18:09:11]] [INFO] hCCEvRtj1A=pass
[[18:09:10]] [SUCCESS] Screenshot refreshed successfully
[[18:09:09]] [INFO] hCCEvRtj1A=running
[[18:09:09]] [INFO] Executing action 434/482: Terminate app: au.com.kmart
[[18:09:09]] [SUCCESS] Screenshot refreshed
[[18:09:09]] [INFO] Refreshing screenshot...
[[18:09:09]] [INFO] V42eHtTRYW=pass
[[18:09:02]] [SUCCESS] Screenshot refreshed successfully
[[18:09:02]] [INFO] V42eHtTRYW=running
[[18:09:02]] [INFO] Executing action 433/482: Wait for 5 ms
[[18:09:02]] [SUCCESS] Screenshot refreshed
[[18:09:02]] [INFO] Refreshing screenshot...
[[18:09:02]] [INFO] jUCAk6GJc4=pass
[[18:09:00]] [SUCCESS] Screenshot refreshed successfully
[[18:08:41]] [INFO] jUCAk6GJc4=running
[[18:08:41]] [INFO] Executing action 432/482: Tap on element with xpath: //android.widget.Switch[@resource-id="android:id/switch_widget"]
[[18:08:41]] [SUCCESS] Screenshot refreshed
[[18:08:41]] [INFO] Refreshing screenshot...
[[18:08:41]] [INFO] w1RV76df9x=pass
[[18:08:39]] [SUCCESS] Screenshot refreshed successfully
[[18:08:39]] [INFO] w1RV76df9x=running
[[18:08:39]] [INFO] Executing action 431/482: Tap on element with xpath: //androidx.recyclerview.widget.RecyclerView[@resource-id="com.android.settings:id/recycler_view"]/android.widget.LinearLayout[3]
[[18:08:39]] [SUCCESS] Screenshot refreshed
[[18:08:39]] [INFO] Refreshing screenshot...
[[18:08:39]] [INFO] LfyQctrEJn=pass
[[18:08:38]] [SUCCESS] Screenshot refreshed successfully
[[18:08:37]] [INFO] LfyQctrEJn=running
[[18:08:37]] [INFO] Executing action 430/482: Launch app: com.android.settings
[[18:08:37]] [SUCCESS] Screenshot refreshed
[[18:08:37]] [INFO] Refreshing screenshot...
[[18:08:37]] [INFO] mIKA85kXaW=pass
[[18:08:35]] [SUCCESS] Screenshot refreshed successfully
[[18:08:35]] [INFO] mIKA85kXaW=running
[[18:08:35]] [INFO] Executing action 429/482: Terminate app: com.android.settings
[[18:08:35]] [SUCCESS] Screenshot refreshed
[[18:08:35]] [INFO] Refreshing screenshot...
[[18:08:35]] [INFO] cokvFXhj4c=pass
[[18:08:34]] [SUCCESS] Screenshot refreshed successfully
[[18:08:33]] [INFO] cokvFXhj4c=running
[[18:08:33]] [INFO] Executing action 428/482: Check if element with xpath="//android.view.View[@content-desc="txtNo internet connection"]" exists
[[18:08:33]] [SUCCESS] Screenshot refreshed
[[18:08:33]] [INFO] Refreshing screenshot...
[[18:08:33]] [INFO] 3KNqlNy6Bj=pass
[[18:08:31]] [SUCCESS] Screenshot refreshed successfully
[[18:08:07]] [INFO] 3KNqlNy6Bj=running
[[18:08:07]] [INFO] Executing action 427/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[18:08:07]] [SUCCESS] Screenshot refreshed
[[18:08:07]] [INFO] Refreshing screenshot...
[[18:08:07]] [INFO] cokvFXhj4c=pass
[[18:08:05]] [SUCCESS] Screenshot refreshed successfully
[[18:08:05]] [INFO] cokvFXhj4c=running
[[18:08:05]] [INFO] Executing action 426/482: Check if element with xpath="//android.view.View[@content-desc="txtNo internet connection"]" exists
[[18:08:05]] [SUCCESS] Screenshot refreshed
[[18:08:05]] [INFO] Refreshing screenshot...
[[18:08:05]] [INFO] 3KNqlNy6Bj=pass
[[18:08:03]] [SUCCESS] Screenshot refreshed successfully
[[18:08:03]] [INFO] 3KNqlNy6Bj=running
[[18:08:03]] [INFO] Executing action 425/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 3 of 5")]
[[18:08:03]] [SUCCESS] Screenshot refreshed
[[18:08:03]] [INFO] Refreshing screenshot...
[[18:08:03]] [INFO] cokvFXhj4c=pass
[[18:08:01]] [SUCCESS] Screenshot refreshed successfully
[[18:08:01]] [INFO] cokvFXhj4c=running
[[18:08:01]] [INFO] Executing action 424/482: Check if element with xpath="//android.view.View[@content-desc="txtNo internet connection"]" exists
[[18:08:01]] [SUCCESS] Screenshot refreshed
[[18:08:01]] [INFO] Refreshing screenshot...
[[18:08:01]] [INFO] 3KNqlNy6Bj=pass
[[18:07:59]] [SUCCESS] Screenshot refreshed successfully
[[18:07:58]] [INFO] 3KNqlNy6Bj=running
[[18:07:58]] [INFO] Executing action 423/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 2 of 5")]
[[18:07:58]] [SUCCESS] Screenshot refreshed
[[18:07:58]] [INFO] Refreshing screenshot...
[[18:07:58]] [INFO] cokvFXhj4c=pass
[[18:07:56]] [SUCCESS] Screenshot refreshed successfully
[[18:07:41]] [INFO] cokvFXhj4c=running
[[18:07:41]] [INFO] Executing action 422/482: Check if element with xpath="//android.view.View[@content-desc="txtNo internet connection"]" exists
[[18:07:41]] [SUCCESS] Screenshot refreshed
[[18:07:41]] [INFO] Refreshing screenshot...
[[18:07:41]] [INFO] oSQ8sPdVOJ=pass
[[18:07:40]] [SUCCESS] Screenshot refreshed successfully
[[18:07:39]] [INFO] oSQ8sPdVOJ=running
[[18:07:39]] [INFO] Executing action 421/482: Launch app: au.com.kmart
[[18:07:39]] [SUCCESS] Screenshot refreshed
[[18:07:39]] [INFO] Refreshing screenshot...
[[18:07:39]] [INFO] oSQ8sPdVOJ=pass
[[18:07:38]] [SUCCESS] Screenshot refreshed successfully
[[18:07:37]] [INFO] oSQ8sPdVOJ=running
[[18:07:37]] [INFO] Executing action 420/482: Terminate app: au.com.kmart
[[18:07:37]] [SUCCESS] Screenshot refreshed
[[18:07:37]] [INFO] Refreshing screenshot...
[[18:07:37]] [INFO] V42eHtTRYW=pass
[[18:07:31]] [SUCCESS] Screenshot refreshed successfully
[[18:07:00]] [INFO] V42eHtTRYW=running
[[18:07:00]] [INFO] Executing action 419/482: Wait for 5 ms
[[18:07:00]] [SUCCESS] Screenshot refreshed
[[18:07:00]] [INFO] Refreshing screenshot...
[[18:07:00]] [INFO] jUCAk6GJc4=pass
[[18:06:58]] [SUCCESS] Screenshot refreshed successfully
[[18:06:57]] [INFO] jUCAk6GJc4=running
[[18:06:57]] [INFO] Executing action 418/482: Tap on element with xpath: //android.widget.Switch[@resource-id="android:id/switch_widget"]
[[18:06:57]] [SUCCESS] Screenshot refreshed
[[18:06:57]] [INFO] Refreshing screenshot...
[[18:06:57]] [INFO] V42eHtTRYW=pass
[[18:06:51]] [SUCCESS] Screenshot refreshed successfully
[[18:06:51]] [INFO] V42eHtTRYW=running
[[18:06:51]] [INFO] Executing action 417/482: Wait for 5 ms
[[18:06:51]] [SUCCESS] Screenshot refreshed
[[18:06:51]] [INFO] Refreshing screenshot...
[[18:06:51]] [INFO] w1RV76df9x=pass
[[18:06:49]] [SUCCESS] Screenshot refreshed successfully
[[18:06:49]] [INFO] w1RV76df9x=running
[[18:06:49]] [INFO] Executing action 416/482: Tap on element with xpath: //androidx.recyclerview.widget.RecyclerView[@resource-id="com.android.settings:id/recycler_view"]/android.widget.LinearLayout[3]
[[18:06:49]] [SUCCESS] Screenshot refreshed
[[18:06:49]] [INFO] Refreshing screenshot...
[[18:06:49]] [INFO] LfyQctrEJn=pass
[[18:06:47]] [SUCCESS] Screenshot refreshed successfully
[[18:06:46]] [INFO] LfyQctrEJn=running
[[18:06:46]] [INFO] Executing action 415/482: Launch app: com.android.settings
[[18:06:46]] [SUCCESS] Screenshot refreshed
[[18:06:46]] [INFO] Refreshing screenshot...
[[18:06:46]] [INFO] mIKA85kXaW=pass
[[18:06:45]] [SUCCESS] Screenshot refreshed successfully
[[18:06:43]] [INFO] mIKA85kXaW=running
[[18:06:43]] [INFO] Executing action 414/482: Terminate app: com.android.settings
[[18:06:43]] [SUCCESS] Screenshot refreshed
[[18:06:43]] [INFO] Refreshing screenshot...
[[18:06:43]] [SUCCESS] Screenshot refreshed
[[18:06:43]] [INFO] Refreshing screenshot...
[[18:06:42]] [SUCCESS] Screenshot refreshed successfully
[[18:06:41]] [INFO] Executing Multi Step action step 7/7: Tap on element with xpath: //android.widget.Button[@text="Sign in"]
[[18:06:41]] [SUCCESS] Screenshot refreshed
[[18:06:41]] [INFO] Refreshing screenshot...
[[18:06:40]] [SUCCESS] Screenshot refreshed successfully
[[18:06:39]] [INFO] Executing Multi Step action step 6/7: Input text: "Wonderbaby@6"
[[18:06:39]] [SUCCESS] Screenshot refreshed
[[18:06:39]] [INFO] Refreshing screenshot...
[[18:06:38]] [SUCCESS] Screenshot refreshed successfully
[[18:06:37]] [INFO] Executing Multi Step action step 5/7: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[18:06:37]] [SUCCESS] Screenshot refreshed
[[18:06:37]] [INFO] Refreshing screenshot...
[[18:06:36]] [SUCCESS] Screenshot refreshed successfully
[[18:06:35]] [INFO] Executing Multi Step action step 4/7: Tap on element with xpath: //android.widget.Button[@text="Continue to password"]
[[18:06:35]] [SUCCESS] Screenshot refreshed
[[18:06:35]] [INFO] Refreshing screenshot...
[[18:06:34]] [SUCCESS] Screenshot refreshed successfully
[[18:06:33]] [INFO] Executing Multi Step action step 3/7: Input text: "<EMAIL>"
[[18:06:33]] [SUCCESS] Screenshot refreshed
[[18:06:33]] [INFO] Refreshing screenshot...
[[18:06:32]] [SUCCESS] Screenshot refreshed successfully
[[18:06:31]] [INFO] Executing Multi Step action step 2/7: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[18:06:31]] [SUCCESS] Screenshot refreshed
[[18:06:31]] [INFO] Refreshing screenshot...
[[18:06:28]] [SUCCESS] Screenshot refreshed successfully
[[18:06:28]] [INFO] Executing Multi Step action step 1/7: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[18:06:28]] [INFO] Loaded 7 steps from test case: Kmart-Signin-AU-ANDROID
[[18:06:28]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[18:06:28]] [INFO] ArAkdzcpEN=running
[[18:06:28]] [INFO] Executing action 413/482: Execute Test Case: Kmart-Signin-AU-ANDROID (7 steps)
[[18:06:28]] [SUCCESS] Screenshot refreshed
[[18:06:28]] [INFO] Refreshing screenshot...
[[18:06:28]] [INFO] veukWo4573=pass
[[18:06:24]] [SUCCESS] Screenshot refreshed successfully
[[18:06:23]] [INFO] veukWo4573=running
[[18:06:23]] [INFO] Executing action 412/482: Tap on element with xpath: //android.widget.Button[@content-desc="txtHomeAccountCtaSignIn"]
[[18:06:23]] [SUCCESS] Screenshot refreshed
[[18:06:23]] [INFO] Refreshing screenshot...
[[18:06:23]] [SUCCESS] Screenshot refreshed
[[18:06:23]] [INFO] Refreshing screenshot...
[[18:06:19]] [SUCCESS] Screenshot refreshed successfully
[[18:06:18]] [INFO] Executing Multi Step action step 6/6: Tap on element with accessibility_id: txtOnePassOnboardingSkipForNow
[[18:06:18]] [SUCCESS] Screenshot refreshed
[[18:06:18]] [INFO] Refreshing screenshot...
[[18:06:17]] [SUCCESS] Screenshot refreshed successfully
[[18:06:17]] [INFO] Executing Multi Step action step 5/6: Tap on element with accessibility_id: btnMayBeLater
[[18:06:17]] [SUCCESS] Screenshot refreshed
[[18:06:17]] [INFO] Refreshing screenshot...
[[18:06:15]] [SUCCESS] Screenshot refreshed successfully
[[18:06:14]] [INFO] Executing Multi Step action step 4/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[18:06:14]] [SUCCESS] Screenshot refreshed
[[18:06:14]] [INFO] Refreshing screenshot...
[[18:06:09]] [SUCCESS] Screenshot refreshed successfully
[[18:06:09]] [INFO] Executing Multi Step action step 3/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[18:06:09]] [SUCCESS] Screenshot refreshed
[[18:06:09]] [INFO] Refreshing screenshot...
[[18:06:08]] [SUCCESS] Screenshot refreshed successfully
[[18:06:07]] [INFO] Executing Multi Step action step 2/6: Launch app: au.com.kmart
[[18:06:07]] [SUCCESS] Screenshot refreshed
[[18:06:07]] [INFO] Refreshing screenshot...
[[18:06:04]] [SUCCESS] Screenshot refreshed successfully
[[18:06:03]] [INFO] Executing Multi Step action step 1/6: Android Function: clear_app - Package: au.com.kmart
[[18:06:03]] [INFO] Loaded 6 steps from test case: Onboarding-Start-AU
[[18:06:03]] [INFO] Loading steps for Multi Step action: Onboarding-Start-AU
[[18:06:03]] [INFO] Executing action 411/482: Execute Test Case: Onboarding-Start-AU (6 steps)
[[18:06:03]] [SUCCESS] Screenshot refreshed
[[18:06:03]] [INFO] Refreshing screenshot...
[[18:06:03]] [INFO] XjclKOaCTh=pass
[[18:06:02]] [SUCCESS] Screenshot refreshed successfully
[[18:06:01]] [INFO] XjclKOaCTh=running
[[18:06:01]] [INFO] Executing action 410/482: Terminate app: au.com.kmart
[[18:06:01]] [SUCCESS] Screenshot refreshed
[[18:06:01]] [INFO] Refreshing screenshot...
[[18:06:01]] [INFO] 2p13JoJbbA=pass
[[18:05:59]] [SUCCESS] Screenshot refreshed successfully
[[18:05:59]] [INFO] 2p13JoJbbA=running
[[18:05:59]] [INFO] Executing action 409/482: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[18:05:59]] [SUCCESS] Screenshot refreshed
[[18:05:59]] [INFO] Refreshing screenshot...
[[18:05:59]] [INFO] qHdMgerbTE=pass
[[18:05:54]] [SUCCESS] Screenshot refreshed successfully
[[18:05:54]] [INFO] qHdMgerbTE=running
[[18:05:54]] [INFO] Executing action 408/482: Swipe from (50%, 70%) to (50%, 30%)
[[18:05:54]] [SUCCESS] Screenshot refreshed
[[18:05:54]] [INFO] Refreshing screenshot...
[[18:05:54]] [INFO] F4NGh9HrLw=pass
[[18:05:52]] [SUCCESS] Screenshot refreshed successfully
[[18:05:51]] [INFO] F4NGh9HrLw=running
[[18:05:51]] [INFO] Executing action 407/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[18:05:51]] [SUCCESS] Screenshot refreshed
[[18:05:51]] [INFO] Refreshing screenshot...
[[18:05:48]] [SUCCESS] Screenshot refreshed successfully
[[18:05:47]] [INFO] Executing action 406/482: Tap if locator exists: xpath="//android.widget.Button[@content-desc="btnSaveOrContinue"]"
[[18:05:47]] [SUCCESS] Screenshot refreshed
[[18:05:47]] [INFO] Refreshing screenshot...
[[18:05:47]] [INFO] RlDZFks4Lc=pass
[[18:05:46]] [SUCCESS] Screenshot refreshed successfully
[[18:05:46]] [INFO] RlDZFks4Lc=running
[[18:05:46]] [INFO] Executing action 405/482: Tap on element with xpath: //android.widget.Button[contains(@text,"While using the app")]
[[18:05:46]] [SUCCESS] Screenshot refreshed
[[18:05:46]] [INFO] Refreshing screenshot...
[[18:05:46]] [INFO] jmKjclMUWT=pass
[[18:05:42]] [SUCCESS] Screenshot refreshed successfully
[[18:05:42]] [INFO] jmKjclMUWT=running
[[18:05:42]] [INFO] Executing action 404/482: Tap on Text: "current"
[[18:05:42]] [SUCCESS] Screenshot refreshed
[[18:05:42]] [INFO] Refreshing screenshot...
[[18:05:42]] [INFO] UoH0wdtcLk=pass
[[18:05:35]] [SUCCESS] Screenshot refreshed successfully
[[18:05:35]] [INFO] UoH0wdtcLk=running
[[18:05:35]] [INFO] Executing action 403/482: Tap on Text: "Edit"
[[18:05:35]] [SUCCESS] Screenshot refreshed
[[18:05:35]] [INFO] Refreshing screenshot...
[[18:05:35]] [INFO] U48qCNydwd=pass
[[18:05:34]] [SUCCESS] Screenshot refreshed successfully
[[18:05:33]] [INFO] U48qCNydwd=running
[[18:05:33]] [INFO] Executing action 402/482: Launch app: au.com.kmart
[[18:05:33]] [SUCCESS] Screenshot refreshed
[[18:05:33]] [INFO] Refreshing screenshot...
[[18:05:33]] [INFO] XjclKOaCTh=pass
[[18:05:31]] [SUCCESS] Screenshot refreshed successfully
[[18:05:11]] [INFO] XjclKOaCTh=running
[[18:05:11]] [INFO] Executing action 401/482: Terminate app: au.com.kmart
[[18:05:11]] [SUCCESS] Screenshot refreshed
[[18:05:11]] [INFO] Refreshing screenshot...
[[18:05:11]] [INFO] q6cKxgMAIn=pass
[[18:05:09]] [SUCCESS] Screenshot refreshed successfully
[[18:05:08]] [INFO] q6cKxgMAIn=running
[[18:05:08]] [INFO] Executing action 400/482: Check if element with xpath="//android.webkit.WebView[@text="Slyp Receipt"]" exists
[[18:05:08]] [SUCCESS] Screenshot refreshed
[[18:05:08]] [INFO] Refreshing screenshot...
[[18:05:08]] [INFO] zdh8hKYC1a=pass
[[18:05:06]] [SUCCESS] Screenshot refreshed successfully
[[18:05:06]] [INFO] zdh8hKYC1a=running
[[18:05:06]] [INFO] Executing action 399/482: Tap on element with xpath: (//android.widget.GridView/android.view.View/android.view.View[2])[1]
[[18:05:06]] [SUCCESS] Screenshot refreshed
[[18:05:06]] [INFO] Refreshing screenshot...
[[18:05:06]] [INFO] P4b2BITpCf=pass
[[18:05:03]] [SUCCESS] Screenshot refreshed successfully
[[18:05:03]] [INFO] P4b2BITpCf=running
[[18:05:03]] [INFO] Executing action 398/482: Check if element with xpath="(//android.widget.GridView/android.view.View/android.view.View[2])[1]" exists
[[18:05:03]] [SUCCESS] Screenshot refreshed
[[18:05:03]] [INFO] Refreshing screenshot...
[[18:05:03]] [INFO] inrxgdWzXr=pass
[[18:04:36]] [SUCCESS] Screenshot refreshed successfully
[[18:04:36]] [INFO] inrxgdWzXr=running
[[18:04:36]] [INFO] Executing action 397/482: Tap on Text: "Store"
[[18:04:36]] [SUCCESS] Screenshot refreshed
[[18:04:36]] [INFO] Refreshing screenshot...
[[18:04:34]] [SUCCESS] Screenshot refreshed successfully
[[18:04:33]] [INFO] Executing action 396/482: Tap on element with xpath: //android.widget.Button[@content-desc="txtMy orders & receipts"]
[[18:04:33]] [SUCCESS] Screenshot refreshed
[[18:04:33]] [INFO] Refreshing screenshot...
[[18:04:33]] [INFO] GEMv6goQtW=pass
[[18:03:56]] [SUCCESS] Screenshot refreshed successfully
[[18:03:56]] [INFO] GEMv6goQtW=running
[[18:03:56]] [INFO] Executing action 395/482: Tap on image: android-app-back.png
[[18:03:56]] [SUCCESS] Screenshot refreshed
[[18:03:56]] [INFO] Refreshing screenshot...
[[18:03:56]] [INFO] DhWa2PCBXE=pass
[[18:03:50]] [SUCCESS] Screenshot refreshed successfully
[[18:03:50]] [INFO] DhWa2PCBXE=running
[[18:03:50]] [INFO] Executing action 394/482: Check if element with xpath="//android.view.View[@content-desc="txtOnePassSubscritionBox"]" exists
[[18:03:50]] [SUCCESS] Screenshot refreshed
[[18:03:50]] [INFO] Refreshing screenshot...
[[18:03:50]] [INFO] pk2DLZFBmx=pass
[[18:03:49]] [SUCCESS] Screenshot refreshed successfully
[[18:03:48]] [INFO] pk2DLZFBmx=running
[[18:03:48]] [INFO] Executing action 393/482: Tap on element with xpath: //android.widget.Button[@content-desc="txtMy OnePass Account"]
[[18:03:48]] [SUCCESS] Screenshot refreshed
[[18:03:48]] [INFO] Refreshing screenshot...
[[18:03:48]] [INFO] ShJSdXvmVL=pass
[[18:03:42]] [SUCCESS] Screenshot refreshed successfully
[[18:03:41]] [INFO] ShJSdXvmVL=running
[[18:03:41]] [INFO] Executing action 392/482: Check if element with xpath="//android.widget.Button[@content-desc="txtMy OnePass Account"]" exists
[[18:03:41]] [SUCCESS] Screenshot refreshed
[[18:03:41]] [INFO] Refreshing screenshot...
[[18:03:41]] [SUCCESS] Screenshot refreshed
[[18:03:41]] [INFO] Refreshing screenshot...
[[18:03:40]] [SUCCESS] Screenshot refreshed successfully
[[18:03:40]] [INFO] Executing Multi Step action step 7/7: Tap on element with xpath: //android.widget.Button[@text="Sign in"]
[[18:03:39]] [SUCCESS] Screenshot refreshed
[[18:03:39]] [INFO] Refreshing screenshot...
[[18:03:38]] [SUCCESS] Screenshot refreshed successfully
[[18:03:37]] [INFO] Executing Multi Step action step 6/7: Input text: "Wonderbaby@6"
[[18:03:37]] [SUCCESS] Screenshot refreshed
[[18:03:37]] [INFO] Refreshing screenshot...
[[18:03:36]] [SUCCESS] Screenshot refreshed successfully
[[18:03:35]] [INFO] Executing Multi Step action step 5/7: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[18:03:35]] [SUCCESS] Screenshot refreshed
[[18:03:35]] [INFO] Refreshing screenshot...
[[18:03:34]] [SUCCESS] Screenshot refreshed successfully
[[18:03:33]] [INFO] Executing Multi Step action step 4/7: Tap on element with xpath: //android.widget.Button[@text="Continue to password"]
[[18:03:33]] [SUCCESS] Screenshot refreshed
[[18:03:33]] [INFO] Refreshing screenshot...
[[18:03:10]] [SUCCESS] Screenshot refreshed successfully
[[18:03:09]] [INFO] Executing Multi Step action step 3/7: Input text: "<EMAIL>"
[[18:03:09]] [SUCCESS] Screenshot refreshed
[[18:03:09]] [INFO] Refreshing screenshot...
[[18:03:08]] [SUCCESS] Screenshot refreshed successfully
[[18:03:08]] [INFO] Executing Multi Step action step 2/7: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[18:03:08]] [SUCCESS] Screenshot refreshed
[[18:03:08]] [INFO] Refreshing screenshot...
[[18:03:05]] [SUCCESS] Screenshot refreshed successfully
[[18:03:04]] [INFO] Executing Multi Step action step 1/7: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[18:03:04]] [INFO] Loaded 7 steps from test case: Kmart-Signin-AU-ANDROID
[[18:03:04]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[18:03:04]] [INFO] s0WyiD1w0B=running
[[18:03:04]] [INFO] Executing action 391/482: Execute Test Case: Kmart-Signin-AU-ANDROID (7 steps)
[[18:03:04]] [SUCCESS] Screenshot refreshed
[[18:03:04]] [INFO] Refreshing screenshot...
[[18:03:04]] [INFO] gekNSY5O2E=pass
[[18:03:01]] [SUCCESS] Screenshot refreshed successfully
[[18:02:41]] [INFO] gekNSY5O2E=running
[[18:02:41]] [INFO] Executing action 390/482: Tap on element with xpath: //android.widget.Button[@content-desc="txtMoreAccountCtaSignIn"]
[[18:02:41]] [SUCCESS] Screenshot refreshed
[[18:02:41]] [INFO] Refreshing screenshot...
[[18:02:41]] [INFO] GEMv6goQtW=pass
[[18:02:31]] [SUCCESS] Screenshot refreshed successfully
[[18:02:10]] [INFO] GEMv6goQtW=running
[[18:02:10]] [INFO] Executing action 389/482: Tap on image: android-app-back.png
[[18:02:10]] [SUCCESS] Screenshot refreshed
[[18:02:10]] [INFO] Refreshing screenshot...
[[18:02:10]] [INFO] 83tV9A4NOn=pass
[[18:02:08]] [SUCCESS] Screenshot refreshed successfully
[[18:02:08]] [INFO] 83tV9A4NOn=running
[[18:02:08]] [INFO] Executing action 388/482: Check if element with xpath="//android.view.View[@text="Order ********* is refunded"]" exists
[[18:02:08]] [SUCCESS] Screenshot refreshed
[[18:02:08]] [INFO] Refreshing screenshot...
[[18:02:08]] [INFO] aNN0yYFLEd=pass
[[18:02:06]] [SUCCESS] Screenshot refreshed successfully
[[18:02:06]] [INFO] aNN0yYFLEd=running
[[18:02:06]] [INFO] Executing action 387/482: Tap on element with xpath: //android.widget.Button[@text="Search for order"]
[[18:02:06]] [SUCCESS] Screenshot refreshed
[[18:02:06]] [INFO] Refreshing screenshot...
[[18:02:06]] [INFO] XJv08Gkucs=pass
[[18:02:02]] [SUCCESS] Screenshot refreshed successfully
[[18:02:02]] [INFO] XJv08Gkucs=running
[[18:02:02]] [INFO] Executing action 386/482: Input text: "<EMAIL>"
[[18:02:02]] [SUCCESS] Screenshot refreshed
[[18:02:02]] [INFO] Refreshing screenshot...
[[18:02:02]] [INFO] aNN0yYFLEd=pass
[[18:01:55]] [SUCCESS] Screenshot refreshed successfully
[[18:01:54]] [INFO] aNN0yYFLEd=running
[[18:01:54]] [INFO] Executing action 385/482: Tap on element with xpath: //android.widget.EditText[@resource-id="email"]
[[18:01:54]] [SUCCESS] Screenshot refreshed
[[18:01:54]] [INFO] Refreshing screenshot...
[[18:01:54]] [INFO] 7YbjwQH1Jc=pass
[[18:01:52]] [SUCCESS] Screenshot refreshed successfully
[[18:01:51]] [INFO] 7YbjwQH1Jc=running
[[18:01:51]] [INFO] Executing action 384/482: Input text: "*********"
[[18:01:51]] [SUCCESS] Screenshot refreshed
[[18:01:51]] [INFO] Refreshing screenshot...
[[18:01:51]] [INFO] OmKfD9iBjD=pass
[[18:01:46]] [SUCCESS] Screenshot refreshed successfully
[[18:01:45]] [INFO] OmKfD9iBjD=running
[[18:01:45]] [INFO] Executing action 383/482: Tap on element with xpath: //android.widget.EditText[@resource-id="orderId"]
[[18:01:45]] [SUCCESS] Screenshot refreshed
[[18:01:45]] [INFO] Refreshing screenshot...
[[18:01:45]] [INFO] eHLWiRoqqS=pass
[[18:01:43]] [SUCCESS] Screenshot refreshed successfully
[[18:01:42]] [INFO] eHLWiRoqqS=running
[[18:01:42]] [INFO] Executing action 382/482: Tap on element with xpath: //android.widget.Button[@content-desc="txtTrack My Order"]
[[18:01:42]] [SUCCESS] Screenshot refreshed
[[18:01:42]] [INFO] Refreshing screenshot...
[[18:01:42]] [INFO] F4NGh9HrLw=pass
[[18:01:40]] [SUCCESS] Screenshot refreshed successfully
[[18:01:39]] [INFO] F4NGh9HrLw=running
[[18:01:39]] [INFO] Executing action 381/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[18:01:39]] [SUCCESS] Screenshot refreshed
[[18:01:39]] [INFO] Refreshing screenshot...
[[18:01:39]] [INFO] 74XW7x54ad=pass
[[18:01:38]] [SUCCESS] Screenshot refreshed successfully
[[18:01:37]] [INFO] 74XW7x54ad=running
[[18:01:37]] [INFO] Executing action 380/482: Tap on element with xpath: //android.widget.ImageView[@content-desc="imgBackArrow"]
[[18:01:37]] [SUCCESS] Screenshot refreshed
[[18:01:37]] [INFO] Refreshing screenshot...
[[18:01:37]] [INFO] xUbWFa8Ok2=pass
[[18:01:36]] [SUCCESS] Screenshot refreshed successfully
[[18:01:34]] [INFO] xUbWFa8Ok2=running
[[18:01:34]] [INFO] Executing action 379/482: Check if element with xpath="//android.view.View[contains(@content-desc,"rectangle frame to view helpful product information")]" exists
[[18:01:34]] [SUCCESS] Screenshot refreshed
[[18:01:34]] [INFO] Refreshing screenshot...
[[18:01:34]] [INFO] RbNtEW6N9T=pass
[[18:01:33]] [SUCCESS] Screenshot refreshed successfully
[[18:01:31]] [INFO] RbNtEW6N9T=running
[[18:01:31]] [INFO] Executing action 378/482: Check if element with xpath="//android.widget.ImageView[@content-desc="imgHelp"]" exists
[[18:01:31]] [SUCCESS] Screenshot refreshed
[[18:01:31]] [INFO] Refreshing screenshot...
[[18:01:31]] [INFO] F4NGh9HrLw=pass
[[18:01:30]] [SUCCESS] Screenshot refreshed successfully
[[18:01:28]] [INFO] F4NGh9HrLw=running
[[18:01:28]] [INFO] Executing action 377/482: Check if element with xpath="//android.view.View[@content-desc="Barcode Scanner"]" exists
[[18:01:28]] [SUCCESS] Screenshot refreshed
[[18:01:28]] [INFO] Refreshing screenshot...
[[18:01:28]] [INFO] RlDZFks4Lc=pass
[[18:01:27]] [SUCCESS] Screenshot refreshed successfully
[[18:01:26]] [INFO] RlDZFks4Lc=running
[[18:01:26]] [INFO] Executing action 376/482: Tap on element with xpath: //android.widget.Button[contains(@text,"While using the app")]
[[18:01:26]] [SUCCESS] Screenshot refreshed
[[18:01:26]] [INFO] Refreshing screenshot...
[[18:01:26]] [INFO] Dzn2Q7JTe0=pass
[[18:01:24]] [SUCCESS] Screenshot refreshed successfully
[[18:01:23]] [INFO] Dzn2Q7JTe0=running
[[18:01:23]] [INFO] Executing action 375/482: Tap on element with xpath: //android.widget.Button[@content-desc="btnBarcodeScanner"]
[[18:01:23]] [SUCCESS] Screenshot refreshed
[[18:01:23]] [INFO] Refreshing screenshot...
[[18:01:23]] [SUCCESS] Screenshot refreshed
[[18:01:23]] [INFO] Refreshing screenshot...
[[18:01:20]] [SUCCESS] Screenshot refreshed successfully
[[18:01:19]] [INFO] Executing Multi Step action step 6/6: Tap on element with accessibility_id: txtOnePassOnboardingSkipForNow
[[18:01:19]] [SUCCESS] Screenshot refreshed
[[18:01:19]] [INFO] Refreshing screenshot...
[[18:01:18]] [SUCCESS] Screenshot refreshed successfully
[[18:01:17]] [INFO] Executing Multi Step action step 5/6: Tap on element with accessibility_id: btnMayBeLater
[[18:01:17]] [SUCCESS] Screenshot refreshed
[[18:01:17]] [INFO] Refreshing screenshot...
[[18:01:16]] [SUCCESS] Screenshot refreshed successfully
[[18:01:11]] [INFO] Executing Multi Step action step 4/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[18:01:11]] [SUCCESS] Screenshot refreshed
[[18:01:11]] [INFO] Refreshing screenshot...
[[18:01:06]] [SUCCESS] Screenshot refreshed successfully
[[18:01:06]] [INFO] Executing Multi Step action step 3/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[18:01:06]] [SUCCESS] Screenshot refreshed
[[18:01:06]] [INFO] Refreshing screenshot...
[[18:01:05]] [SUCCESS] Screenshot refreshed successfully
[[18:01:04]] [INFO] Executing Multi Step action step 2/6: Launch app: au.com.kmart
[[18:01:04]] [SUCCESS] Screenshot refreshed
[[18:01:04]] [INFO] Refreshing screenshot...
[[18:01:01]] [SUCCESS] Screenshot refreshed successfully
[[18:01:01]] [INFO] Executing Multi Step action step 1/6: Android Function: clear_app - Package: au.com.kmart
[[18:01:01]] [INFO] Loaded 6 steps from test case: Onboarding-Start-AU
[[18:01:00]] [INFO] Loading steps for Multi Step action: Onboarding-Start-AU
[[18:01:00]] [INFO] Executing action 374/482: Execute Test Case: Onboarding-Start-AU (6 steps)
[[18:01:00]] [SUCCESS] Screenshot refreshed
[[18:01:00]] [INFO] Refreshing screenshot...
[[18:01:00]] [INFO] BlQ3vdsleV=pass
[[18:00:36]] [SUCCESS] Screenshot refreshed successfully
[[18:00:35]] [INFO] BlQ3vdsleV=running
[[18:00:35]] [INFO] Executing action 373/482: Wait for 5 ms
[[18:00:35]] [SUCCESS] Screenshot refreshed
[[18:00:35]] [INFO] Refreshing screenshot...
[[18:00:35]] [INFO] 25UEKPIknm=pass
[[18:00:33]] [SUCCESS] Screenshot refreshed successfully
[[18:00:33]] [INFO] 25UEKPIknm=running
[[18:00:33]] [INFO] Executing action 372/482: Terminate app: au.com.kmart
[[18:00:33]] [SUCCESS] Screenshot refreshed
[[18:00:33]] [INFO] Refreshing screenshot...
[[18:00:33]] [INFO] JLAJhxPdsl=pass
[[18:00:31]] [SUCCESS] Screenshot refreshed successfully
[[18:00:10]] [INFO] JLAJhxPdsl=running
[[18:00:10]] [INFO] Executing action 371/482: Android Function: send_key_event - Key Event: BACK
[[18:00:10]] [SUCCESS] Screenshot refreshed
[[18:00:10]] [INFO] Refreshing screenshot...
[[18:00:10]] [INFO] UqgDn5CuPY=pass
[[18:00:08]] [SUCCESS] Screenshot refreshed successfully
[[18:00:07]] [INFO] UqgDn5CuPY=running
[[18:00:07]] [INFO] Executing action 370/482: Check if element with xpath="//android.widget.TextView[@text="Create account"]" exists
[[18:00:07]] [SUCCESS] Screenshot refreshed
[[18:00:07]] [INFO] Refreshing screenshot...
[[18:00:07]] [INFO] ipT2XD9io6=pass
[[18:00:06]] [SUCCESS] Screenshot refreshed successfully
[[18:00:05]] [INFO] ipT2XD9io6=running
[[18:00:05]] [INFO] Executing action 369/482: Tap on element with xpath: //android.widget.Button[@content-desc="txtMoreAccountJoinTodayButton"]
[[18:00:05]] [SUCCESS] Screenshot refreshed
[[18:00:05]] [INFO] Refreshing screenshot...
[[18:00:05]] [INFO] OKCHAK6HCJ=pass
[[18:00:04]] [SUCCESS] Screenshot refreshed successfully
[[18:00:03]] [INFO] OKCHAK6HCJ=running
[[18:00:03]] [INFO] Executing action 368/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[18:00:03]] [SUCCESS] Screenshot refreshed
[[18:00:03]] [INFO] Refreshing screenshot...
[[18:00:03]] [INFO] AEnFqnkOa1=pass
[[17:59:49]] [SUCCESS] Screenshot refreshed successfully
[[17:59:49]] [INFO] AEnFqnkOa1=running
[[17:59:49]] [INFO] Executing action 367/482: Tap on image: bag-close-android.png
[[17:59:49]] [SUCCESS] Screenshot refreshed
[[17:59:49]] [INFO] Refreshing screenshot...
[[17:59:49]] [INFO] z1CfcW4xYT=pass
[[17:59:47]] [SUCCESS] Screenshot refreshed successfully
[[17:59:47]] [INFO] z1CfcW4xYT=running
[[17:59:47]] [INFO] Executing action 366/482: Tap on element with xpath: //android.widget.Button[contains(@text,"Remove")]
[[17:59:47]] [SUCCESS] Screenshot refreshed
[[17:59:47]] [INFO] Refreshing screenshot...
[[17:59:47]] [INFO] dJNRgTXoqs=pass
[[17:59:45]] [SUCCESS] Screenshot refreshed successfully
[[17:59:44]] [INFO] dJNRgTXoqs=running
[[17:59:44]] [INFO] Executing action 365/482: Swipe from (50%, 80%) to (50%, 40%)
[[17:59:44]] [SUCCESS] Screenshot refreshed
[[17:59:44]] [INFO] Refreshing screenshot...
[[17:59:44]] [INFO] njiHWyVooT=pass
[[17:59:42]] [SUCCESS] Screenshot refreshed successfully
[[17:59:41]] [INFO] njiHWyVooT=running
[[17:59:41]] [INFO] Executing action 364/482: Tap on element with xpath: //android.widget.Button[@text="close"]
[[17:59:41]] [SUCCESS] Screenshot refreshed
[[17:59:41]] [INFO] Refreshing screenshot...
[[17:59:41]] [INFO] 93bAew9Y4Y=pass
[[17:59:38]] [SUCCESS] Screenshot refreshed successfully
[[17:59:38]] [INFO] 93bAew9Y4Y=running
[[17:59:38]] [INFO] Executing action 363/482: Tap on element with xpath: (//android.widget.Button[contains(@text,"store details")])[1]
[[17:59:38]] [SUCCESS] Screenshot refreshed
[[17:59:38]] [INFO] Refreshing screenshot...
[[17:59:38]] [INFO] S24l2PQODy=pass
[[17:59:37]] [SUCCESS] Screenshot refreshed successfully
[[17:59:35]] [INFO] S24l2PQODy=running
[[17:59:35]] [INFO] Executing action 362/482: Tap on element with xpath: //android.view.View[contains(@text,"Click") and contains(@text,"Collect")]
[[17:59:35]] [SUCCESS] Screenshot refreshed
[[17:59:35]] [INFO] Refreshing screenshot...
[[17:59:35]] [SUCCESS] Screenshot refreshed
[[17:59:35]] [INFO] Refreshing screenshot...
[[17:59:28]] [SUCCESS] Screenshot refreshed successfully
[[17:59:27]] [INFO] Executing Multi Step action step 8/8: Wait till xpath=//android.view.View[@text="Delivery"]
[[17:59:27]] [SUCCESS] Screenshot refreshed
[[17:59:27]] [INFO] Refreshing screenshot...
[[17:59:25]] [SUCCESS] Screenshot refreshed successfully
[[17:59:25]] [INFO] Executing Multi Step action step 7/8: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[17:59:24]] [SUCCESS] Screenshot refreshed
[[17:59:24]] [INFO] Refreshing screenshot...
[[17:59:22]] [SUCCESS] Screenshot refreshed successfully
[[17:59:21]] [INFO] Executing Multi Step action step 6/8: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[17:59:21]] [SUCCESS] Screenshot refreshed
[[17:59:21]] [INFO] Refreshing screenshot...
[[17:59:19]] [SUCCESS] Screenshot refreshed successfully
[[17:59:19]] [INFO] Executing Multi Step action step 5/8: Tap on element with xpath: (//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView/following-sibling::android.view.View/android.widget.Button
[[17:59:19]] [SUCCESS] Screenshot refreshed
[[17:59:19]] [INFO] Refreshing screenshot...
[[17:59:13]] [SUCCESS] Screenshot refreshed successfully
[[17:59:12]] [INFO] Executing Multi Step action step 4/8: Wait till text appears: "Filter"
[[17:59:12]] [SUCCESS] Screenshot refreshed
[[17:59:12]] [INFO] Refreshing screenshot...
[[17:59:11]] [SUCCESS] Screenshot refreshed successfully
[[17:59:09]] [INFO] Executing Multi Step action step 3/8: Android Function: send_key_event - Key Event: ENTER
[[17:59:09]] [SUCCESS] Screenshot refreshed
[[17:59:09]] [INFO] Refreshing screenshot...
[[17:59:08]] [SUCCESS] Screenshot refreshed successfully
[[17:59:07]] [INFO] Executing Multi Step action step 2/8: Input text: "Notebooks"
[[17:59:07]] [SUCCESS] Screenshot refreshed
[[17:59:07]] [INFO] Refreshing screenshot...
[[17:59:02]] [SUCCESS] Screenshot refreshed successfully
[[17:59:01]] [INFO] Executing Multi Step action step 1/8: Tap on Text: "Find"
[[17:59:01]] [INFO] Loaded 8 steps from test case: Search and Add (Notebooks)_ANDROID
[[17:59:01]] [INFO] Loading steps for Multi Step action: Search and Add (Notebooks)_ANDROID
[[17:59:01]] [INFO] wvthDzpm0c=running
[[17:59:01]] [INFO] Executing action 361/482: Execute Test Case: Search and Add (Notebooks)_ANDROID (8 steps)
[[17:59:01]] [SUCCESS] Screenshot refreshed
[[17:59:01]] [INFO] Refreshing screenshot...
[[17:59:01]] [INFO] JLAJhxPdsl=pass
[[17:58:59]] [SUCCESS] Screenshot refreshed successfully
[[17:58:59]] [INFO] JLAJhxPdsl=running
[[17:58:59]] [INFO] Executing action 360/482: Android Function: send_key_event - Key Event: BACK
[[17:58:59]] [SUCCESS] Screenshot refreshed
[[17:58:59]] [INFO] Refreshing screenshot...
[[17:58:59]] [INFO] UqgDn5CuPY=pass
[[17:58:56]] [SUCCESS] Screenshot refreshed successfully
[[17:58:56]] [INFO] UqgDn5CuPY=running
[[17:58:56]] [INFO] Executing action 359/482: Check if element with xpath="//android.widget.TextView[@text="Create account"]" exists
[[17:58:56]] [SUCCESS] Screenshot refreshed
[[17:58:56]] [INFO] Refreshing screenshot...
[[17:58:56]] [INFO] ipT2XD9io6=pass
[[17:58:54]] [SUCCESS] Screenshot refreshed successfully
[[17:58:53]] [INFO] ipT2XD9io6=running
[[17:58:53]] [INFO] Executing action 358/482: Tap on element with xpath: //android.widget.Button[@content-desc="txtJoinTodayButton"]
[[17:58:53]] [SUCCESS] Screenshot refreshed
[[17:58:53]] [INFO] Refreshing screenshot...
[[17:58:53]] [INFO] OKCHAK6HCJ=pass
[[17:58:52]] [SUCCESS] Screenshot refreshed successfully
[[17:58:51]] [INFO] OKCHAK6HCJ=running
[[17:58:51]] [INFO] Executing action 357/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 1 of 5")]
[[17:58:51]] [SUCCESS] Screenshot refreshed
[[17:58:51]] [INFO] Refreshing screenshot...
[[17:58:51]] [INFO] RbD937Xbte=pass
[[17:58:47]] [SUCCESS] Screenshot refreshed successfully
[[17:58:47]] [INFO] RbD937Xbte=running
[[17:58:47]] [INFO] Executing action 356/482: Tap on Text: "out"
[[17:58:47]] [SUCCESS] Screenshot refreshed
[[17:58:47]] [INFO] Refreshing screenshot...
[[17:58:47]] [INFO] ylslyLAYKb=pass
[[17:58:45]] [SUCCESS] Screenshot refreshed successfully
[[17:58:44]] [INFO] ylslyLAYKb=running
[[17:58:44]] [INFO] Executing action 355/482: Swipe from (50%, 70%) to (50%, 30%)
[[17:58:44]] [SUCCESS] Screenshot refreshed
[[17:58:44]] [INFO] Refreshing screenshot...
[[17:58:44]] [INFO] wguGCt7OoB=pass
[[17:58:42]] [SUCCESS] Screenshot refreshed successfully
[[17:58:42]] [INFO] wguGCt7OoB=running
[[17:58:42]] [INFO] Executing action 354/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[17:58:42]] [SUCCESS] Screenshot refreshed
[[17:58:42]] [INFO] Refreshing screenshot...
[[17:58:42]] [INFO] RDQCFIxjA0=pass
[[17:58:40]] [SUCCESS] Screenshot refreshed successfully
[[17:58:39]] [INFO] RDQCFIxjA0=running
[[17:58:39]] [INFO] Executing action 353/482: Swipe from (90%, 20%) to (30%, 20%)
[[17:58:39]] [SUCCESS] Screenshot refreshed
[[17:58:39]] [INFO] Refreshing screenshot...
[[17:58:39]] [INFO] x4Mid4HQ0Z=pass
[[17:58:38]] [SUCCESS] Screenshot refreshed successfully
[[17:58:37]] [INFO] x4Mid4HQ0Z=running
[[17:58:37]] [INFO] Executing action 352/482: Swipe from (90%, 20%) to (30%, 20%)
[[17:58:37]] [SUCCESS] Screenshot refreshed
[[17:58:37]] [INFO] Refreshing screenshot...
[[17:58:37]] [INFO] OKCHAK6HCJ=pass
[[17:58:36]] [SUCCESS] Screenshot refreshed successfully
[[17:58:35]] [INFO] OKCHAK6HCJ=running
[[17:58:35]] [INFO] Executing action 351/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 3 of 5")]
[[17:58:35]] [SUCCESS] Screenshot refreshed
[[17:58:35]] [INFO] Refreshing screenshot...
[[17:58:35]] [INFO] Ef6OumM2eS=pass
[[17:58:34]] [SUCCESS] Screenshot refreshed successfully
[[17:58:34]] [INFO] Ef6OumM2eS=running
[[17:58:34]] [INFO] Executing action 350/482: Tap on element with xpath: //android.widget.Button[@resource-id="wishlist-button"]
[[17:58:34]] [SUCCESS] Screenshot refreshed
[[17:58:34]] [INFO] Refreshing screenshot...
[[17:58:34]] [INFO] HZT2s0AzX7=pass
[[17:58:24]] [SUCCESS] Screenshot refreshed successfully
[[17:58:23]] [INFO] HZT2s0AzX7=running
[[17:58:23]] [INFO] Executing action 349/482: Swipe up till element xpath: "//android.widget.Button[@resource-id="wishlist-button"]" is visible
[[17:58:23]] [SUCCESS] Screenshot refreshed
[[17:58:23]] [INFO] Refreshing screenshot...
[[17:58:23]] [INFO] 0bnBNoqPt8=pass
[[17:58:19]] [SUCCESS] Screenshot refreshed successfully
[[17:58:19]] [INFO] 0bnBNoqPt8=running
[[17:58:19]] [INFO] Executing action 348/482: Wait till xpath=//android.widget.TextView[contains(@text,"SKU")]
[[17:58:19]] [SUCCESS] Screenshot refreshed
[[17:58:19]] [INFO] Refreshing screenshot...
[[17:58:19]] [INFO] xmelRkcdVx=pass
[[17:58:17]] [SUCCESS] Screenshot refreshed successfully
[[17:58:16]] [INFO] xmelRkcdVx=running
[[17:58:16]] [INFO] Executing action 347/482: Tap on element with xpath: ((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1]
[[17:58:16]] [SUCCESS] Screenshot refreshed
[[17:58:16]] [INFO] Refreshing screenshot...
[[17:58:16]] [INFO] ksCBjJiwHZ=pass
[[17:58:10]] [SUCCESS] Screenshot refreshed successfully
[[17:58:09]] [INFO] ksCBjJiwHZ=running
[[17:58:09]] [INFO] Executing action 346/482: Wait till xpath=//android.widget.Button[@text="Filter"]
[[17:58:09]] [SUCCESS] Screenshot refreshed
[[17:58:09]] [INFO] Refreshing screenshot...
[[17:58:09]] [INFO] cv6q3z3WDg=pass
[[17:58:08]] [SUCCESS] Screenshot refreshed successfully
[[17:58:07]] [INFO] cv6q3z3WDg=running
[[17:58:07]] [INFO] Executing action 345/482: Android Function: send_key_event - Key Event: ENTER
[[17:58:07]] [SUCCESS] Screenshot refreshed
[[17:58:07]] [INFO] Refreshing screenshot...
[[17:58:07]] [INFO] RuPGkdCdah=pass
[[17:58:06]] [SUCCESS] Screenshot refreshed successfully
[[17:58:05]] [INFO] RuPGkdCdah=running
[[17:58:05]] [INFO] Executing action 344/482: Input text: "cooker"
[[17:58:05]] [SUCCESS] Screenshot refreshed
[[17:58:05]] [INFO] Refreshing screenshot...
[[17:58:05]] [INFO] ewuLtuqVuo=pass
[[17:58:00]] [SUCCESS] Screenshot refreshed successfully
[[17:58:00]] [INFO] ewuLtuqVuo=running
[[17:58:00]] [INFO] Executing action 343/482: Tap on Text: "Find"
[[17:58:00]] [SUCCESS] Screenshot refreshed
[[17:58:00]] [INFO] Refreshing screenshot...
[[17:58:00]] [INFO] GTXmST3hEA=pass
[[17:57:57]] [SUCCESS] Screenshot refreshed successfully
[[17:57:55]] [INFO] GTXmST3hEA=running
[[17:57:55]] [INFO] Executing action 342/482: Check if element with xpath="//android.view.View[@content-desc="txtHomeGreetingText"]" exists
[[17:57:55]] [SUCCESS] Screenshot refreshed
[[17:57:55]] [INFO] Refreshing screenshot...
[[17:57:55]] [SUCCESS] Screenshot refreshed
[[17:57:55]] [INFO] Refreshing screenshot...
[[17:57:54]] [SUCCESS] Screenshot refreshed successfully
[[17:57:53]] [INFO] Executing Multi Step action step 7/7: Tap on element with xpath: //android.widget.Button[@text="Sign in"]
[[17:57:53]] [SUCCESS] Screenshot refreshed
[[17:57:53]] [INFO] Refreshing screenshot...
[[17:57:52]] [SUCCESS] Screenshot refreshed successfully
[[17:57:51]] [INFO] Executing Multi Step action step 6/7: Input text: "Wonderbaby@6"
[[17:57:51]] [SUCCESS] Screenshot refreshed
[[17:57:51]] [INFO] Refreshing screenshot...
[[17:57:50]] [SUCCESS] Screenshot refreshed successfully
[[17:57:50]] [INFO] Executing Multi Step action step 5/7: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[17:57:50]] [SUCCESS] Screenshot refreshed
[[17:57:50]] [INFO] Refreshing screenshot...
[[17:57:48]] [SUCCESS] Screenshot refreshed successfully
[[17:57:48]] [INFO] Executing Multi Step action step 4/7: Tap on element with xpath: //android.widget.Button[@text="Continue to password"]
[[17:57:48]] [SUCCESS] Screenshot refreshed
[[17:57:48]] [INFO] Refreshing screenshot...
[[17:57:46]] [SUCCESS] Screenshot refreshed successfully
[[17:57:46]] [INFO] Executing Multi Step action step 3/7: Input text: "<EMAIL>"
[[17:57:45]] [SUCCESS] Screenshot refreshed
[[17:57:45]] [INFO] Refreshing screenshot...
[[17:57:44]] [SUCCESS] Screenshot refreshed successfully
[[17:57:44]] [INFO] Executing Multi Step action step 2/7: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[17:57:44]] [SUCCESS] Screenshot refreshed
[[17:57:44]] [INFO] Refreshing screenshot...
[[17:57:41]] [SUCCESS] Screenshot refreshed successfully
[[17:57:40]] [INFO] Executing Multi Step action step 1/7: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[17:57:40]] [INFO] Loaded 7 steps from test case: Kmart-Signin-AU-ANDROID
[[17:57:40]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[17:57:40]] [INFO] 9SUMcj33xo=running
[[17:57:40]] [INFO] Executing action 341/482: Execute Test Case: Kmart-Signin-AU-ANDROID (7 steps)
[[17:57:40]] [SUCCESS] Screenshot refreshed
[[17:57:40]] [INFO] Refreshing screenshot...
[[17:57:40]] [INFO] SPE01N6pyp=pass
[[17:57:39]] [SUCCESS] Screenshot refreshed successfully
[[17:57:38]] [INFO] SPE01N6pyp=running
[[17:57:38]] [INFO] Executing action 340/482: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[17:57:38]] [SUCCESS] Screenshot refreshed
[[17:57:38]] [INFO] Refreshing screenshot...
[[17:57:38]] [INFO] WEB5St2Mb7=pass
[[17:57:37]] [SUCCESS] Screenshot refreshed successfully
[[17:57:36]] [INFO] WEB5St2Mb7=running
[[17:57:36]] [INFO] Executing action 339/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 1 of 5")]
[[17:57:36]] [SUCCESS] Screenshot refreshed
[[17:57:36]] [INFO] Refreshing screenshot...
[[17:57:36]] [INFO] 5e4LeoW1YU=pass
[[17:57:35]] [SUCCESS] Screenshot refreshed successfully
[[17:57:34]] [INFO] 5e4LeoW1YU=running
[[17:57:34]] [INFO] Executing action 338/482: Launch app: au.com.kmart
[[17:57:34]] [SUCCESS] Screenshot refreshed
[[17:57:34]] [INFO] Refreshing screenshot...
[[17:57:34]] [SUCCESS] Screenshot refreshed
[[17:57:34]] [INFO] Refreshing screenshot...
[[17:57:31]] [SUCCESS] Screenshot refreshed successfully
[[17:57:30]] [INFO] Executing Multi Step action step 15/15: Wait till xpath=//android.widget.Button[@text="Filter"]
[[17:57:30]] [SUCCESS] Screenshot refreshed
[[17:57:30]] [INFO] Refreshing screenshot...
[[17:57:29]] [SUCCESS] Screenshot refreshed successfully
[[17:57:28]] [INFO] Executing Multi Step action step 14/15: Tap on element with xpath: //android.widget.Button[@text="Go to previous page"]
[[17:57:28]] [SUCCESS] Screenshot refreshed
[[17:57:28]] [INFO] Refreshing screenshot...
[[17:57:07]] [SUCCESS] Screenshot refreshed successfully
[[17:57:07]] [INFO] Executing Multi Step action step 13/15: Swipe from (50%, 80%) to (50%, 10%)
[[17:57:07]] [SUCCESS] Screenshot refreshed
[[17:57:07]] [INFO] Refreshing screenshot...
[[17:57:03]] [SUCCESS] Screenshot refreshed successfully
[[17:57:02]] [INFO] Executing Multi Step action step 12/15: Wait till xpath=//android.widget.Button[@text="Filter"]
[[17:57:02]] [SUCCESS] Screenshot refreshed
[[17:57:02]] [INFO] Refreshing screenshot...
[[17:57:00]] [SUCCESS] Screenshot refreshed successfully
[[17:56:41]] [INFO] Executing Multi Step action step 11/15: Tap on element with xpath: //android.widget.Button[@text="Go to previous page"]
[[17:56:41]] [SUCCESS] Screenshot refreshed
[[17:56:41]] [INFO] Refreshing screenshot...
[[17:56:03]] [SUCCESS] Screenshot refreshed successfully
[[17:56:02]] [INFO] Executing Multi Step action step 10/15: Swipe from (50%, 80%) to (50%, 10%)
[[17:56:02]] [SUCCESS] Screenshot refreshed
[[17:56:02]] [INFO] Refreshing screenshot...
[[17:56:01]] [SUCCESS] Screenshot refreshed successfully
[[17:55:42]] [INFO] Executing Multi Step action step 9/15: Wait till xpath=//android.widget.Button[@text="Filter"]
[[17:55:42]] [SUCCESS] Screenshot refreshed
[[17:55:42]] [INFO] Refreshing screenshot...
[[17:55:40]] [SUCCESS] Screenshot refreshed successfully
[[17:55:40]] [INFO] Executing Multi Step action step 8/15: Tap on element with xpath: //android.widget.Button[@text="Go to next page"]
[[17:55:39]] [SUCCESS] Screenshot refreshed
[[17:55:39]] [INFO] Refreshing screenshot...
[[17:54:55]] [SUCCESS] Screenshot refreshed successfully
[[17:54:54]] [INFO] Executing Multi Step action step 7/15: Swipe from (50%, 80%) to (50%, 10%)
[[17:54:54]] [SUCCESS] Screenshot refreshed
[[17:54:54]] [INFO] Refreshing screenshot...
[[17:54:49]] [SUCCESS] Screenshot refreshed successfully
[[17:54:49]] [INFO] Executing Multi Step action step 6/15: Wait till xpath=//android.widget.Button[@text="Filter"]
[[17:54:49]] [SUCCESS] Screenshot refreshed
[[17:54:49]] [INFO] Refreshing screenshot...
[[17:54:47]] [SUCCESS] Screenshot refreshed successfully
[[17:54:47]] [INFO] Executing Multi Step action step 5/15: Tap on element with xpath: //android.widget.Button[@text="Go to next page"]
[[17:54:47]] [SUCCESS] Screenshot refreshed
[[17:54:47]] [INFO] Refreshing screenshot...
[[17:54:07]] [SUCCESS] Screenshot refreshed successfully
[[17:54:07]] [INFO] Executing Multi Step action step 4/15: Swipe from (50%, 80%) to (50%, 10%)
[[17:54:07]] [SUCCESS] Screenshot refreshed
[[17:54:07]] [INFO] Refreshing screenshot...
[[17:54:03]] [SUCCESS] Screenshot refreshed successfully
[[17:54:03]] [INFO] Executing Multi Step action step 3/15: Wait till xpath=//android.widget.Button[@text="Filter"]
[[17:54:02]] [SUCCESS] Screenshot refreshed
[[17:54:02]] [INFO] Refreshing screenshot...
[[17:54:00]] [SUCCESS] Screenshot refreshed successfully
[[17:53:50]] [INFO] Executing Multi Step action step 2/15: Tap on element with xpath: //android.widget.Button[@text="Go to next page"]
[[17:53:50]] [SUCCESS] Screenshot refreshed
[[17:53:50]] [INFO] Refreshing screenshot...
[[17:53:32]] [SUCCESS] Screenshot refreshed successfully
[[17:53:31]] [INFO] Executing Multi Step action step 1/15: Swipe from (50%, 80%) to (50%, 10%)
[[17:53:31]] [INFO] Loaded 15 steps from test case: Click_Paginations_Android
[[17:53:31]] [INFO] Loading steps for Multi Step action: Click_Paginations_Android
[[17:53:31]] [INFO] Executing action 337/482: Execute Test Case: Click_Paginations_Android (15 steps)
[[17:53:31]] [SUCCESS] Screenshot refreshed
[[17:53:31]] [INFO] Refreshing screenshot...
[[17:53:31]] [INFO] 6G6P3UE7Uy=pass
[[17:53:23]] [SUCCESS] Screenshot refreshed successfully
[[17:53:22]] [INFO] 6G6P3UE7Uy=running
[[17:53:22]] [INFO] Executing action 336/482: Wait till xpath=//android.widget.Button[@text="Filter"]
[[17:53:22]] [SUCCESS] Screenshot refreshed
[[17:53:22]] [INFO] Refreshing screenshot...
[[17:53:22]] [INFO] ZOvSdSTPuq=pass
[[17:53:21]] [SUCCESS] Screenshot refreshed successfully
[[17:53:20]] [INFO] ZOvSdSTPuq=running
[[17:53:20]] [INFO] Executing action 335/482: Android Function: send_key_event - Key Event: ENTER
[[17:53:20]] [SUCCESS] Screenshot refreshed
[[17:53:20]] [INFO] Refreshing screenshot...
[[17:53:20]] [INFO] IL6kON0uQ9=pass
[[17:53:19]] [SUCCESS] Screenshot refreshed successfully
[[17:53:18]] [INFO] IL6kON0uQ9=running
[[17:53:18]] [INFO] Executing action 334/482: Input text: "kids toys"
[[17:53:18]] [SUCCESS] Screenshot refreshed
[[17:53:18]] [INFO] Refreshing screenshot...
[[17:53:18]] [INFO] 6G6P3UE7Uy=pass
[[17:53:14]] [SUCCESS] Screenshot refreshed successfully
[[17:53:13]] [INFO] 6G6P3UE7Uy=running
[[17:53:13]] [INFO] Executing action 333/482: Tap on Text: "Find"
[[17:53:13]] [SUCCESS] Screenshot refreshed
[[17:53:13]] [INFO] Refreshing screenshot...
[[17:53:13]] [INFO] 7xs3GiydGF=pass
[[17:53:12]] [SUCCESS] Screenshot refreshed successfully
[[17:53:11]] [INFO] 7xs3GiydGF=running
[[17:53:11]] [INFO] Executing action 332/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 1 of 5")]
[[17:53:11]] [SUCCESS] Screenshot refreshed
[[17:53:11]] [INFO] Refreshing screenshot...
[[17:53:11]] [INFO] VqSa9z9R2Q=pass
[[17:53:10]] [SUCCESS] Screenshot refreshed successfully
[[17:53:09]] [INFO] VqSa9z9R2Q=running
[[17:53:09]] [INFO] Executing action 331/482: Launch app: au.com.kmart
[[17:53:09]] [SUCCESS] Screenshot refreshed
[[17:53:09]] [INFO] Refreshing screenshot...
[[17:53:09]] [INFO] f7IqzjeNBe=pass
[[17:53:00]] [SUCCESS] Screenshot refreshed successfully
[[17:52:32]] [INFO] f7IqzjeNBe=running
[[17:52:32]] [INFO] Executing action 330/482: Tap on image: custcare-no-android.png
[[17:52:32]] [SUCCESS] Screenshot refreshed
[[17:52:32]] [INFO] Refreshing screenshot...
[[17:52:32]] [INFO] I0tM87Yjhc=pass
[[17:52:29]] [SUCCESS] Screenshot refreshed successfully
[[17:52:28]] [INFO] I0tM87Yjhc=running
[[17:52:28]] [INFO] Executing action 329/482: Tap on Text: "click"
[[17:52:28]] [SUCCESS] Screenshot refreshed
[[17:52:28]] [INFO] Refreshing screenshot...
[[17:52:28]] [INFO] t6L5vWfBYM=pass
[[17:52:17]] [SUCCESS] Screenshot refreshed successfully
[[17:52:17]] [INFO] t6L5vWfBYM=running
[[17:52:17]] [INFO] Executing action 328/482: Swipe from (50%, 70%) to (50%, 30%)
[[17:52:17]] [SUCCESS] Screenshot refreshed
[[17:52:17]] [INFO] Refreshing screenshot...
[[17:52:17]] [INFO] DhFJzlme9K=pass
[[17:52:13]] [SUCCESS] Screenshot refreshed successfully
[[17:52:13]] [INFO] DhFJzlme9K=running
[[17:52:13]] [INFO] Executing action 327/482: Tap on Text: "FAQ"
[[17:52:13]] [SUCCESS] Screenshot refreshed
[[17:52:13]] [INFO] Refreshing screenshot...
[[17:52:13]] [INFO] g17Boaefhg=pass
[[17:52:10]] [SUCCESS] Screenshot refreshed successfully
[[17:52:09]] [INFO] g17Boaefhg=running
[[17:52:09]] [INFO] Executing action 326/482: Tap on Text: "Help"
[[17:52:09]] [SUCCESS] Screenshot refreshed
[[17:52:09]] [INFO] Refreshing screenshot...
[[17:52:09]] [INFO] t6L5vWfBYM=pass
[[17:52:01]] [SUCCESS] Screenshot refreshed successfully
[[17:52:01]] [INFO] t6L5vWfBYM=running
[[17:52:01]] [INFO] Executing action 325/482: Swipe from (50%, 70%) to (50%, 30%)
[[17:52:01]] [SUCCESS] Screenshot refreshed
[[17:52:01]] [INFO] Refreshing screenshot...
[[17:52:01]] [INFO] SqDiBhmyOG=pass
[[17:52:00]] [SUCCESS] Screenshot refreshed successfully
[[17:51:58]] [INFO] SqDiBhmyOG=running
[[17:51:58]] [INFO] Executing action 324/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[17:51:58]] [SUCCESS] Screenshot refreshed
[[17:51:58]] [INFO] Refreshing screenshot...
[[17:51:58]] [SUCCESS] Screenshot refreshed
[[17:51:58]] [INFO] Refreshing screenshot...
[[17:51:54]] [SUCCESS] Screenshot refreshed successfully
[[17:51:53]] [INFO] Executing Multi Step action step 6/6: Tap on element with accessibility_id: txtOnePassOnboardingSkipForNow
[[17:51:53]] [SUCCESS] Screenshot refreshed
[[17:51:53]] [INFO] Refreshing screenshot...
[[17:51:52]] [SUCCESS] Screenshot refreshed successfully
[[17:51:51]] [INFO] Executing Multi Step action step 5/6: Tap on element with accessibility_id: btnMayBeLater
[[17:51:51]] [SUCCESS] Screenshot refreshed
[[17:51:51]] [INFO] Refreshing screenshot...
[[17:51:50]] [SUCCESS] Screenshot refreshed successfully
[[17:51:48]] [INFO] Executing Multi Step action step 4/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[17:51:48]] [SUCCESS] Screenshot refreshed
[[17:51:48]] [INFO] Refreshing screenshot...
[[17:51:44]] [SUCCESS] Screenshot refreshed successfully
[[17:51:43]] [INFO] Executing Multi Step action step 3/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[17:51:43]] [SUCCESS] Screenshot refreshed
[[17:51:43]] [INFO] Refreshing screenshot...
[[17:51:42]] [SUCCESS] Screenshot refreshed successfully
[[17:51:40]] [INFO] Executing Multi Step action step 2/6: Launch app: au.com.kmart
[[17:51:40]] [SUCCESS] Screenshot refreshed
[[17:51:40]] [INFO] Refreshing screenshot...
[[17:51:34]] [INFO] Executing Multi Step action step 1/6: Android Function: clear_app - Package: au.com.kmart
[[17:51:34]] [INFO] Loaded 6 steps from test case: Onboarding-Start-AU
[[17:51:34]] [INFO] Loading steps for Multi Step action: Onboarding-Start-AU
[[17:51:34]] [INFO] Executing action 323/482: Execute Test Case: Onboarding-Start-AU (6 steps)
[[17:51:32]] [INFO] === RETRYING TEST CASE: AU__Performance_Android_20250712160300.json (Attempt 2 of 3) ===
[[17:51:32]] [INFO] I0tM87Yjhc=fail
[[17:51:32]] [ERROR] Action 329 failed: Text 'click' not found within timeout (30s)
[[17:50:31]] [SUCCESS] Screenshot refreshed successfully
[[17:50:30]] [INFO] I0tM87Yjhc=running
[[17:50:30]] [INFO] Executing action 329/482: Tap on Text: "click"
[[17:50:30]] [SUCCESS] Screenshot refreshed
[[17:50:30]] [INFO] Refreshing screenshot...
[[17:50:30]] [INFO] t6L5vWfBYM=pass
[[17:50:22]] [SUCCESS] Screenshot refreshed successfully
[[17:50:22]] [INFO] t6L5vWfBYM=running
[[17:50:22]] [INFO] Executing action 328/482: Swipe from (50%, 70%) to (50%, 30%)
[[17:50:22]] [SUCCESS] Screenshot refreshed
[[17:50:22]] [INFO] Refreshing screenshot...
[[17:50:22]] [INFO] DhFJzlme9K=pass
[[17:50:18]] [SUCCESS] Screenshot refreshed successfully
[[17:50:18]] [INFO] DhFJzlme9K=running
[[17:50:18]] [INFO] Executing action 327/482: Tap on Text: "FAQ"
[[17:50:18]] [SUCCESS] Screenshot refreshed
[[17:50:18]] [INFO] Refreshing screenshot...
[[17:50:18]] [INFO] g17Boaefhg=pass
[[17:50:15]] [SUCCESS] Screenshot refreshed successfully
[[17:50:14]] [INFO] g17Boaefhg=running
[[17:50:14]] [INFO] Executing action 326/482: Tap on Text: "Help"
[[17:50:14]] [SUCCESS] Screenshot refreshed
[[17:50:14]] [INFO] Refreshing screenshot...
[[17:50:14]] [INFO] t6L5vWfBYM=pass
[[17:50:07]] [SUCCESS] Screenshot refreshed successfully
[[17:50:06]] [INFO] t6L5vWfBYM=running
[[17:50:06]] [INFO] Executing action 325/482: Swipe from (50%, 70%) to (50%, 30%)
[[17:50:06]] [SUCCESS] Screenshot refreshed
[[17:50:06]] [INFO] Refreshing screenshot...
[[17:50:06]] [INFO] SqDiBhmyOG=pass
[[17:50:05]] [SUCCESS] Screenshot refreshed successfully
[[17:50:04]] [INFO] SqDiBhmyOG=running
[[17:50:04]] [INFO] Executing action 324/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[17:50:04]] [SUCCESS] Screenshot refreshed
[[17:50:04]] [INFO] Refreshing screenshot...
[[17:50:04]] [SUCCESS] Screenshot refreshed
[[17:50:04]] [INFO] Refreshing screenshot...
[[17:50:00]] [SUCCESS] Screenshot refreshed successfully
[[17:49:59]] [INFO] Executing Multi Step action step 6/6: Tap on element with accessibility_id: txtOnePassOnboardingSkipForNow
[[17:49:59]] [SUCCESS] Screenshot refreshed
[[17:49:59]] [INFO] Refreshing screenshot...
[[17:49:58]] [SUCCESS] Screenshot refreshed successfully
[[17:49:57]] [INFO] Executing Multi Step action step 5/6: Tap on element with accessibility_id: btnMayBeLater
[[17:49:57]] [SUCCESS] Screenshot refreshed
[[17:49:57]] [INFO] Refreshing screenshot...
[[17:49:56]] [SUCCESS] Screenshot refreshed successfully
[[17:49:55]] [INFO] Executing Multi Step action step 4/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[17:49:54]] [SUCCESS] Screenshot refreshed
[[17:49:54]] [INFO] Refreshing screenshot...
[[17:49:50]] [SUCCESS] Screenshot refreshed successfully
[[17:49:50]] [INFO] Executing Multi Step action step 3/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[17:49:50]] [SUCCESS] Screenshot refreshed
[[17:49:50]] [INFO] Refreshing screenshot...
[[17:49:48]] [SUCCESS] Screenshot refreshed successfully
[[17:49:46]] [INFO] Executing Multi Step action step 2/6: Launch app: au.com.kmart
[[17:49:46]] [SUCCESS] Screenshot refreshed
[[17:49:46]] [INFO] Refreshing screenshot...
[[17:49:44]] [SUCCESS] Screenshot refreshed successfully
[[17:49:43]] [INFO] Executing Multi Step action step 1/6: Android Function: clear_app - Package: au.com.kmart
[[17:49:43]] [INFO] Loaded 6 steps from test case: Onboarding-Start-AU
[[17:49:43]] [INFO] Loading steps for Multi Step action: Onboarding-Start-AU
[[17:49:43]] [INFO] Executing action 323/482: Execute Test Case: Onboarding-Start-AU (6 steps)
[[17:49:43]] [SUCCESS] Screenshot refreshed
[[17:49:43]] [INFO] Refreshing screenshot...
[[17:49:43]] [SUCCESS] Screenshot refreshed
[[17:49:43]] [INFO] Refreshing screenshot...
[[17:49:41]] [SUCCESS] Screenshot refreshed successfully
[[17:49:40]] [INFO] Executing Multi Step action step 54/54: Tap on element with xpath: //android.widget.TextView[@text="Continue shopping"]
[[17:49:40]] [SUCCESS] Screenshot refreshed
[[17:49:40]] [INFO] Refreshing screenshot...
[[17:49:39]] [SUCCESS] Screenshot refreshed successfully
[[17:49:38]] [INFO] Executing Multi Step action step 53/54: Tap on element with xpath: //android.widget.Button[contains(@text,"Remove")]
[[17:49:38]] [SUCCESS] Screenshot refreshed
[[17:49:38]] [INFO] Refreshing screenshot...
[[17:49:34]] [SUCCESS] Screenshot refreshed successfully
[[17:49:33]] [INFO] Executing Multi Step action step 52/54: Swipe up till element xpath: "//android.widget.Button[contains(@text,"Remove")]" is visible
[[17:49:33]] [SUCCESS] Screenshot refreshed
[[17:49:33]] [INFO] Refreshing screenshot...
[[17:49:03]] [SUCCESS] Screenshot refreshed successfully
[[17:49:03]] [INFO] Executing Multi Step action step 51/54: Wait till xpath=//android.widget.TextView[contains(@text,"Click")]
[[17:49:02]] [SUCCESS] Screenshot refreshed
[[17:49:02]] [INFO] Refreshing screenshot...
[[17:49:00]] [SUCCESS] Screenshot refreshed successfully
[[17:48:41]] [INFO] Executing Multi Step action step 50/54: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[17:48:40]] [SUCCESS] Screenshot refreshed
[[17:48:40]] [INFO] Refreshing screenshot...
[[17:48:39]] [SUCCESS] Screenshot refreshed successfully
[[17:48:38]] [INFO] Executing Multi Step action step 49/54: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[17:48:38]] [SUCCESS] Screenshot refreshed
[[17:48:38]] [INFO] Refreshing screenshot...
[[17:48:32]] [SUCCESS] Screenshot refreshed successfully
[[17:48:31]] [INFO] Executing Multi Step action step 48/54: Wait for 5 ms
[[17:48:31]] [SUCCESS] Screenshot refreshed
[[17:48:31]] [INFO] Refreshing screenshot...
[[17:48:01]] [SUCCESS] Screenshot refreshed successfully
[[17:48:01]] [INFO] Executing Multi Step action step 47/54: Tap on image: bag-close-android.png
[[17:48:01]] [SUCCESS] Screenshot refreshed
[[17:48:01]] [INFO] Refreshing screenshot...
[[17:47:36]] [SUCCESS] Screenshot refreshed successfully
[[17:47:35]] [INFO] Executing Multi Step action step 46/54: Wait for 5 ms
[[17:47:35]] [SUCCESS] Screenshot refreshed
[[17:47:35]] [INFO] Refreshing screenshot...
[[17:47:32]] [SUCCESS] Screenshot refreshed successfully
[[17:47:31]] [INFO] Executing Multi Step action step 45/54: Tap on Text: "Check"
[[17:47:31]] [SUCCESS] Screenshot refreshed
[[17:47:31]] [INFO] Refreshing screenshot...
[[17:47:04]] [SUCCESS] Screenshot refreshed successfully
[[17:47:04]] [INFO] Executing Multi Step action step 44/54: Wait for 5 ms
[[17:47:04]] [SUCCESS] Screenshot refreshed
[[17:47:04]] [INFO] Refreshing screenshot...
[[17:46:39]] [SUCCESS] Screenshot refreshed successfully
[[17:46:38]] [INFO] Executing Multi Step action step 43/54: Tap on image: zippay-chkbox-btn.png
[[17:46:38]] [SUCCESS] Screenshot refreshed
[[17:46:38]] [INFO] Refreshing screenshot...
[[17:46:32]] [SUCCESS] Screenshot refreshed successfully
[[17:46:31]] [INFO] Executing Multi Step action step 42/54: Wait for 5 ms
[[17:46:31]] [SUCCESS] Screenshot refreshed
[[17:46:31]] [INFO] Refreshing screenshot...
[[17:46:02]] [SUCCESS] Screenshot refreshed successfully
[[17:46:02]] [INFO] Executing Multi Step action step 41/54: Tap on image: bag-close-android.png
[[17:46:02]] [SUCCESS] Screenshot refreshed
[[17:46:02]] [INFO] Refreshing screenshot...
[[17:45:38]] [SUCCESS] Screenshot refreshed successfully
[[17:45:38]] [INFO] Executing Multi Step action step 40/54: Tap on Text: "Check"
[[17:45:38]] [SUCCESS] Screenshot refreshed
[[17:45:38]] [INFO] Refreshing screenshot...
[[17:45:31]] [SUCCESS] Screenshot refreshed successfully
[[17:45:10]] [INFO] Executing Multi Step action step 39/54: Wait for 5 ms
[[17:45:10]] [SUCCESS] Screenshot refreshed
[[17:45:10]] [INFO] Refreshing screenshot...
[[17:45:01]] [SUCCESS] Screenshot refreshed successfully
[[17:45:01]] [INFO] Executing Multi Step action step 38/54: Tap on image: afterpay-chkbox-android.png
[[17:45:01]] [SUCCESS] Screenshot refreshed
[[17:45:01]] [INFO] Refreshing screenshot...
[[17:44:35]] [SUCCESS] Screenshot refreshed successfully
[[17:44:34]] [INFO] Executing Multi Step action step 37/54: Wait for 5 ms
[[17:44:34]] [SUCCESS] Screenshot refreshed
[[17:44:34]] [INFO] Refreshing screenshot...
[[17:44:04]] [SUCCESS] Screenshot refreshed successfully
[[17:44:04]] [INFO] Executing Multi Step action step 36/54: Tap on image: paypal-close-btn-android.png
[[17:44:04]] [SUCCESS] Screenshot refreshed
[[17:44:04]] [INFO] Refreshing screenshot...
[[17:44:00]] [SUCCESS] Screenshot refreshed successfully
[[17:43:40]] [INFO] Executing Multi Step action step 35/54: Tap on image: Payin4-btn-Android.png
[[17:43:40]] [SUCCESS] Screenshot refreshed
[[17:43:40]] [INFO] Refreshing screenshot...
[[17:43:34]] [SUCCESS] Screenshot refreshed successfully
[[17:43:34]] [INFO] Executing Multi Step action step 34/54: Wait for 5 ms
[[17:43:34]] [SUCCESS] Screenshot refreshed
[[17:43:34]] [INFO] Refreshing screenshot...
[[17:43:17]] [SUCCESS] Screenshot refreshed successfully
[[17:43:16]] [INFO] Executing Multi Step action step 33/54: Tap on image: PaypalIn4-Chkbox-Android.png
[[17:43:16]] [SUCCESS] Screenshot refreshed
[[17:43:16]] [INFO] Refreshing screenshot...
[[17:43:12]] [SUCCESS] Screenshot refreshed successfully
[[17:43:10]] [INFO] Executing Multi Step action step 32/54: Wait for 5 ms
[[17:43:10]] [SUCCESS] Screenshot refreshed
[[17:43:10]] [INFO] Refreshing screenshot...
[[17:43:01]] [SUCCESS] Screenshot refreshed successfully
[[17:43:00]] [INFO] Executing Multi Step action step 31/54: Tap on image: paypal-close-btn-android.png
[[17:43:00]] [SUCCESS] Screenshot refreshed
[[17:43:00]] [INFO] Refreshing screenshot...
[[17:42:35]] [SUCCESS] Screenshot refreshed successfully
[[17:42:35]] [INFO] Executing Multi Step action step 30/54: Wait for 5 ms
[[17:42:35]] [SUCCESS] Screenshot refreshed
[[17:42:35]] [INFO] Refreshing screenshot...
[[17:42:16]] [SUCCESS] Screenshot refreshed successfully
[[17:42:15]] [INFO] Executing Multi Step action step 29/54: Tap on image: paypal-payment-btn-android.png
[[17:42:15]] [SUCCESS] Screenshot refreshed
[[17:42:15]] [INFO] Refreshing screenshot...
[[17:42:14]] [SUCCESS] Screenshot refreshed successfully
[[17:42:13]] [INFO] Executing Multi Step action step 28/54: Swipe from (50%, 70%) to (50%, 40%)
[[17:42:13]] [SUCCESS] Screenshot refreshed
[[17:42:13]] [INFO] Refreshing screenshot...
[[17:42:10]] [SUCCESS] Screenshot refreshed successfully
[[17:42:09]] [INFO] Executing Multi Step action step 27/54: Tap on Text: "PayPal"
[[17:42:09]] [SUCCESS] Screenshot refreshed
[[17:42:09]] [INFO] Refreshing screenshot...
[[17:42:08]] [SUCCESS] Screenshot refreshed successfully
[[17:42:08]] [INFO] Executing Multi Step action step 26/54: Tap on element with xpath: //android.widget.Button[contains(@text,"Continue")]
[[17:42:07]] [SUCCESS] Screenshot refreshed
[[17:42:07]] [INFO] Refreshing screenshot...
[[17:42:05]] [SUCCESS] Screenshot refreshed successfully
[[17:42:04]] [INFO] Executing Multi Step action step 25/54: Swipe up till element xpath: "//android.widget.Button[contains(@text,"Continue")]" is visible
[[17:42:04]] [SUCCESS] Screenshot refreshed
[[17:42:04]] [INFO] Refreshing screenshot...
[[17:41:37]] [SUCCESS] Screenshot refreshed successfully
[[17:41:36]] [INFO] Executing Multi Step action step 24/54: Tap on image: delivery-address-options-android.png
[[17:41:36]] [SUCCESS] Screenshot refreshed
[[17:41:36]] [INFO] Refreshing screenshot...
[[17:41:34]] [SUCCESS] Screenshot refreshed successfully
[[17:41:33]] [INFO] Executing Multi Step action step 23/54: Input text: "305 238 Flinders Street"
[[17:41:33]] [SUCCESS] Screenshot refreshed
[[17:41:33]] [INFO] Refreshing screenshot...
[[17:41:32]] [SUCCESS] Screenshot refreshed successfully
[[17:41:31]] [INFO] Executing Multi Step action step 22/54: Tap on element with xpath: //android.widget.EditText[@resource-id="addressInfo"]
[[17:41:31]] [SUCCESS] Screenshot refreshed
[[17:41:31]] [INFO] Refreshing screenshot...
[[17:41:07]] [SUCCESS] Screenshot refreshed successfully
[[17:41:06]] [INFO] Executing Multi Step action step 21/54: Wait for 10 ms
[[17:41:06]] [SUCCESS] Screenshot refreshed
[[17:41:06]] [INFO] Refreshing screenshot...
[[17:41:05]] [SUCCESS] Screenshot refreshed successfully
[[17:41:04]] [INFO] Executing Multi Step action step 20/54: Tap on element with xpath: //android.widget.Button[contains(@text,"Continue")]
[[17:41:04]] [SUCCESS] Screenshot refreshed
[[17:41:04]] [INFO] Refreshing screenshot...
[[17:41:02]] [SUCCESS] Screenshot refreshed successfully
[[17:41:01]] [INFO] Executing Multi Step action step 19/54: Swipe up till element xpath: "//android.widget.Button[contains(@text,"Continue")]" is visible
[[17:41:01]] [SUCCESS] Screenshot refreshed
[[17:41:01]] [INFO] Refreshing screenshot...
[[17:41:00]] [SUCCESS] Screenshot refreshed successfully
[[17:40:40]] [INFO] Executing Multi Step action step 18/54: Wait for 5 ms
[[17:40:40]] [SUCCESS] Screenshot refreshed
[[17:40:40]] [INFO] Refreshing screenshot...
[[17:40:39]] [SUCCESS] Screenshot refreshed successfully
[[17:40:38]] [INFO] Executing Multi Step action step 17/54: Input text: "0400000000"
[[17:40:38]] [SUCCESS] Screenshot refreshed
[[17:40:38]] [INFO] Refreshing screenshot...
[[17:40:37]] [SUCCESS] Screenshot refreshed successfully
[[17:40:37]] [INFO] Executing Multi Step action step 16/54: Clear Text (auto)
[[17:40:36]] [SUCCESS] Screenshot refreshed
[[17:40:36]] [INFO] Refreshing screenshot...
[[17:40:35]] [SUCCESS] Screenshot refreshed successfully
[[17:40:35]] [INFO] Executing Multi Step action step 15/54: Tap on element with xpath: //android.widget.EditText[@resource-id="phone"]
[[17:40:35]] [SUCCESS] Screenshot refreshed
[[17:40:35]] [INFO] Refreshing screenshot...
[[17:40:33]] [SUCCESS] Screenshot refreshed successfully
[[17:40:32]] [INFO] Executing Multi Step action step 14/54: Input text: "<EMAIL>"
[[17:40:32]] [SUCCESS] Screenshot refreshed
[[17:40:32]] [INFO] Refreshing screenshot...
[[17:40:31]] [SUCCESS] Screenshot refreshed successfully
[[17:40:10]] [INFO] Executing Multi Step action step 13/54: Clear Text (auto)
[[17:40:10]] [SUCCESS] Screenshot refreshed
[[17:40:10]] [INFO] Refreshing screenshot...
[[17:40:08]] [SUCCESS] Screenshot refreshed successfully
[[17:40:08]] [INFO] Executing Multi Step action step 12/54: Tap on element with xpath: //android.widget.EditText[@resource-id="email"]
[[17:40:08]] [SUCCESS] Screenshot refreshed
[[17:40:08]] [INFO] Refreshing screenshot...
[[17:40:06]] [SUCCESS] Screenshot refreshed successfully
[[17:40:06]] [INFO] Executing Multi Step action step 11/54: Input text: "LastName"
[[17:40:06]] [SUCCESS] Screenshot refreshed
[[17:40:06]] [INFO] Refreshing screenshot...
[[17:40:05]] [SUCCESS] Screenshot refreshed successfully
[[17:40:04]] [INFO] Executing Multi Step action step 10/54: Clear Text (auto)
[[17:40:04]] [SUCCESS] Screenshot refreshed
[[17:40:04]] [INFO] Refreshing screenshot...
[[17:40:03]] [SUCCESS] Screenshot refreshed successfully
[[17:40:02]] [INFO] Executing Multi Step action step 9/54: Tap on element with xpath: //android.widget.EditText[@resource-id="lastName"]
[[17:40:02]] [SUCCESS] Screenshot refreshed
[[17:40:02]] [INFO] Refreshing screenshot...
[[17:40:00]] [SUCCESS] Screenshot refreshed successfully
[[17:39:41]] [INFO] Executing Multi Step action step 8/54: Input text: "FirstName"
[[17:39:41]] [SUCCESS] Screenshot refreshed
[[17:39:41]] [INFO] Refreshing screenshot...
[[17:39:40]] [SUCCESS] Screenshot refreshed successfully
[[17:39:39]] [INFO] Executing Multi Step action step 7/54: Clear Text (auto)
[[17:39:39]] [SUCCESS] Screenshot refreshed
[[17:39:39]] [INFO] Refreshing screenshot...
[[17:39:36]] [SUCCESS] Screenshot refreshed successfully
[[17:39:35]] [INFO] Executing Multi Step action step 6/54: Tap on Text: "First"
[[17:39:35]] [SUCCESS] Screenshot refreshed
[[17:39:35]] [INFO] Refreshing screenshot...
[[17:39:34]] [SUCCESS] Screenshot refreshed successfully
[[17:39:33]] [INFO] Executing Multi Step action step 5/54: Tap on element with xpath: //android.widget.Button[contains(@text,"Continue")]
[[17:39:33]] [SUCCESS] Screenshot refreshed
[[17:39:33]] [INFO] Refreshing screenshot...
[[17:39:30]] [SUCCESS] Screenshot refreshed successfully
[[17:39:29]] [INFO] Executing Multi Step action step 4/54: Swipe up till element xpath: "//android.widget.Button[contains(@text,"Continue")]" is visible
[[17:39:29]] [SUCCESS] Screenshot refreshed
[[17:39:29]] [INFO] Refreshing screenshot...
[[17:39:18]] [SUCCESS] Screenshot refreshed successfully
[[17:39:18]] [INFO] Executing Multi Step action step 3/54: Tap if text exists: "Continue"
[[17:39:18]] [SUCCESS] Screenshot refreshed
[[17:39:18]] [INFO] Refreshing screenshot...
[[17:39:17]] [SUCCESS] Screenshot refreshed successfully
[[17:39:16]] [INFO] Executing Multi Step action step 2/54: Tap on element with xpath: (//android.widget.TextView[@text="Delivery"])[2]
[[17:39:16]] [SUCCESS] Screenshot refreshed
[[17:39:16]] [INFO] Refreshing screenshot...
[[17:39:09]] [SUCCESS] Screenshot refreshed successfully
[[17:39:08]] [INFO] Executing Multi Step action step 1/54: Wait till xpath=(//android.widget.TextView[@text="Delivery"])[2]
[[17:39:08]] [INFO] Loaded 54 steps from test case: Delivery Buy Steps_AU-ANDROID
[[17:39:08]] [INFO] Loading steps for Multi Step action: Delivery Buy Steps_AU-ANDROID
[[17:39:08]] [INFO] Executing action 322/482: Execute Test Case: Delivery Buy Steps_AU-ANDROID (54 steps)
[[17:39:08]] [SUCCESS] Screenshot refreshed
[[17:39:08]] [INFO] Refreshing screenshot...
[[17:39:08]] [INFO] zrdO3PVkX3=pass
[[17:39:06]] [SUCCESS] Screenshot refreshed successfully
[[17:39:05]] [INFO] zrdO3PVkX3=running
[[17:39:05]] [INFO] Executing action 321/482: Tap on element with xpath: //android.widget.Button[@content-desc="Checkout"]
[[17:39:05]] [SUCCESS] Screenshot refreshed
[[17:39:05]] [INFO] Refreshing screenshot...
[[17:39:05]] [INFO] F1olhgKhUt=pass
[[17:39:03]] [SUCCESS] Screenshot refreshed successfully
[[17:39:02]] [INFO] F1olhgKhUt=running
[[17:39:02]] [INFO] Executing action 320/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[17:39:02]] [SUCCESS] Screenshot refreshed
[[17:39:02]] [INFO] Refreshing screenshot...
[[17:39:02]] [INFO] FnrbyHq7bU=pass
[[17:39:00]] [SUCCESS] Screenshot refreshed successfully
[[17:38:50]] [INFO] FnrbyHq7bU=running
[[17:38:50]] [INFO] Executing action 319/482: Tap on element with xpath: (//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView/following-sibling::android.view.View/android.widget.Button
[[17:38:50]] [SUCCESS] Screenshot refreshed
[[17:38:50]] [INFO] Refreshing screenshot...
[[17:38:50]] [INFO] nAB6Q8LAdv=pass
[[17:38:39]] [SUCCESS] Screenshot refreshed successfully
[[17:38:39]] [INFO] nAB6Q8LAdv=running
[[17:38:39]] [INFO] Executing action 318/482: Wait till text appears: "Filter"
[[17:38:39]] [SUCCESS] Screenshot refreshed
[[17:38:39]] [INFO] Refreshing screenshot...
[[17:38:39]] [INFO] OAZvliPL2h=pass
[[17:38:38]] [SUCCESS] Screenshot refreshed successfully
[[17:38:37]] [INFO] OAZvliPL2h=running
[[17:38:37]] [INFO] Executing action 317/482: Android Function: send_key_event - Key Event: ENTER
[[17:38:37]] [SUCCESS] Screenshot refreshed
[[17:38:37]] [INFO] Refreshing screenshot...
[[17:38:37]] [INFO] JRheeTvpJf=pass
[[17:38:35]] [SUCCESS] Screenshot refreshed successfully
[[17:38:35]] [INFO] JRheeTvpJf=running
[[17:38:35]] [INFO] Executing action 316/482: Input text: "Uno Card"
[[17:38:35]] [SUCCESS] Screenshot refreshed
[[17:38:35]] [INFO] Refreshing screenshot...
[[17:38:35]] [INFO] o1gHFWhXTL=pass
[[17:38:31]] [SUCCESS] Screenshot refreshed successfully
[[17:38:09]] [INFO] o1gHFWhXTL=running
[[17:38:09]] [INFO] Executing action 315/482: Tap on Text: "Find"
[[17:38:09]] [SUCCESS] Screenshot refreshed
[[17:38:09]] [INFO] Refreshing screenshot...
[[17:38:09]] [INFO] cKNu2QoRC1=pass
[[17:38:08]] [SUCCESS] Screenshot refreshed successfully
[[17:38:06]] [INFO] cKNu2QoRC1=running
[[17:38:06]] [INFO] Executing action 314/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 1 of 5")]
[[17:38:06]] [SUCCESS] Screenshot refreshed
[[17:38:06]] [INFO] Refreshing screenshot...
[[17:38:06]] [INFO] OyUowAaBzD=pass
[[17:38:04]] [SUCCESS] Screenshot refreshed successfully
[[17:38:04]] [INFO] OyUowAaBzD=running
[[17:38:04]] [INFO] Executing action 313/482: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[17:38:04]] [SUCCESS] Screenshot refreshed
[[17:38:04]] [INFO] Refreshing screenshot...
[[17:37:38]] [SUCCESS] Screenshot refreshed successfully
[[17:37:37]] [INFO] Executing action 312/482: Swipe up till element xpath: "//android.widget.Button[@content-desc="txtSign out"]" is visible
[[17:37:37]] [SUCCESS] Screenshot refreshed
[[17:37:37]] [INFO] Refreshing screenshot...
[[17:37:37]] [INFO] k3mu9Mt7Ec=pass
[[17:37:35]] [SUCCESS] Screenshot refreshed successfully
[[17:37:34]] [INFO] k3mu9Mt7Ec=running
[[17:37:34]] [INFO] Executing action 311/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[17:37:34]] [SUCCESS] Screenshot refreshed
[[17:37:34]] [INFO] Refreshing screenshot...
[[17:37:34]] [INFO] FFM0CCo6Qg=pass
[[17:37:06]] [SUCCESS] Screenshot refreshed successfully
[[17:37:06]] [INFO] FFM0CCo6Qg=running
[[17:37:06]] [INFO] Executing action 310/482: Tap on image: bag-close-android.png
[[17:37:06]] [SUCCESS] Screenshot refreshed
[[17:37:06]] [INFO] Refreshing screenshot...
[[17:37:06]] [INFO] LWXWKZE4UV=pass
[[17:37:04]] [SUCCESS] Screenshot refreshed successfully
[[17:37:04]] [INFO] LWXWKZE4UV=running
[[17:37:04]] [INFO] Executing action 309/482: Tap on element with xpath: //android.widget.Button[contains(@text,"Remove")]
[[17:37:04]] [SUCCESS] Screenshot refreshed
[[17:37:04]] [INFO] Refreshing screenshot...
[[17:37:04]] [INFO] Qbg9bipTGs=pass
[[17:37:00]] [SUCCESS] Screenshot refreshed successfully
[[17:36:32]] [INFO] Qbg9bipTGs=running
[[17:36:32]] [INFO] Executing action 308/482: Swipe up till image "remove-btn-android.png" is visible
[[17:36:32]] [SUCCESS] Screenshot refreshed
[[17:36:32]] [INFO] Refreshing screenshot...
[[17:36:32]] [INFO] 7SpDO20tS2=pass
[[17:36:21]] [SUCCESS] Screenshot refreshed successfully
[[17:36:21]] [INFO] 7SpDO20tS2=running
[[17:36:21]] [INFO] Executing action 307/482: Wait for 10 ms
[[17:36:21]] [SUCCESS] Screenshot refreshed
[[17:36:21]] [INFO] Refreshing screenshot...
[[17:36:21]] [INFO] drbQBpgBfM=pass
[[17:36:18]] [SUCCESS] Screenshot refreshed successfully
[[17:36:18]] [INFO] drbQBpgBfM=running
[[17:36:18]] [INFO] Executing action 306/482: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[17:36:18]] [SUCCESS] Screenshot refreshed
[[17:36:18]] [INFO] Refreshing screenshot...
[[17:36:18]] [INFO] F1olhgKhUt=pass
[[17:36:15]] [SUCCESS] Screenshot refreshed successfully
[[17:36:14]] [INFO] F1olhgKhUt=running
[[17:36:14]] [INFO] Executing action 305/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[17:36:14]] [SUCCESS] Screenshot refreshed
[[17:36:14]] [INFO] Refreshing screenshot...
[[17:36:14]] [INFO] FnrbyHq7bU=pass
[[17:36:13]] [SUCCESS] Screenshot refreshed successfully
[[17:36:12]] [INFO] FnrbyHq7bU=running
[[17:36:12]] [INFO] Executing action 304/482: Tap on element with xpath: (//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView/following-sibling::android.view.View/android.widget.Button
[[17:36:12]] [SUCCESS] Screenshot refreshed
[[17:36:12]] [INFO] Refreshing screenshot...
[[17:36:12]] [INFO] nAB6Q8LAdv=pass
[[17:36:06]] [SUCCESS] Screenshot refreshed successfully
[[17:36:05]] [INFO] nAB6Q8LAdv=running
[[17:36:05]] [INFO] Executing action 303/482: Wait till text appears: "Filter"
[[17:36:05]] [SUCCESS] Screenshot refreshed
[[17:36:05]] [INFO] Refreshing screenshot...
[[17:36:05]] [INFO] uybtup9gEo=pass
[[17:36:04]] [SUCCESS] Screenshot refreshed successfully
[[17:36:03]] [INFO] uybtup9gEo=running
[[17:36:03]] [INFO] Executing action 302/482: Android Function: send_key_event - Key Event: ENTER
[[17:36:03]] [SUCCESS] Screenshot refreshed
[[17:36:03]] [INFO] Refreshing screenshot...
[[17:36:03]] [INFO] JRheeTvpJf=pass
[[17:36:01]] [SUCCESS] Screenshot refreshed successfully
[[17:36:01]] [INFO] JRheeTvpJf=running
[[17:36:01]] [INFO] Executing action 301/482: Input text: "Uno Card"
[[17:36:01]] [SUCCESS] Screenshot refreshed
[[17:36:01]] [INFO] Refreshing screenshot...
[[17:36:01]] [INFO] o1gHFWhXTL=pass
[[17:35:57]] [SUCCESS] Screenshot refreshed successfully
[[17:35:56]] [INFO] o1gHFWhXTL=running
[[17:35:56]] [INFO] Executing action 300/482: Tap on Text: "Find"
[[17:35:56]] [SUCCESS] Screenshot refreshed
[[17:35:56]] [INFO] Refreshing screenshot...
[[17:35:56]] [INFO] RLznb7o3ag=pass
[[17:35:51]] [SUCCESS] Screenshot refreshed successfully
[[17:35:51]] [INFO] RLznb7o3ag=running
[[17:35:51]] [INFO] Executing action 299/482: Wait till xpath=//android.view.View[@content-desc="txtHomeGreetingText"]
[[17:35:51]] [SUCCESS] Screenshot refreshed
[[17:35:51]] [INFO] Refreshing screenshot...
[[17:35:51]] [SUCCESS] Screenshot refreshed
[[17:35:51]] [INFO] Refreshing screenshot...
[[17:35:49]] [SUCCESS] Screenshot refreshed successfully
[[17:35:49]] [INFO] Executing Multi Step action step 7/7: Tap on element with xpath: //android.widget.Button[@text="Sign in"]
[[17:35:49]] [SUCCESS] Screenshot refreshed
[[17:35:49]] [INFO] Refreshing screenshot...
[[17:35:47]] [SUCCESS] Screenshot refreshed successfully
[[17:35:47]] [INFO] Executing Multi Step action step 6/7: Input text: "Wonderbaby@6"
[[17:35:47]] [SUCCESS] Screenshot refreshed
[[17:35:47]] [INFO] Refreshing screenshot...
[[17:35:45]] [SUCCESS] Screenshot refreshed successfully
[[17:35:45]] [INFO] Executing Multi Step action step 5/7: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[17:35:45]] [SUCCESS] Screenshot refreshed
[[17:35:45]] [INFO] Refreshing screenshot...
[[17:35:43]] [SUCCESS] Screenshot refreshed successfully
[[17:35:43]] [INFO] Executing Multi Step action step 4/7: Tap on element with xpath: //android.widget.Button[@text="Continue to password"]
[[17:35:43]] [SUCCESS] Screenshot refreshed
[[17:35:43]] [INFO] Refreshing screenshot...
[[17:35:41]] [SUCCESS] Screenshot refreshed successfully
[[17:35:40]] [INFO] Executing Multi Step action step 3/7: Input text: "<EMAIL>"
[[17:35:40]] [SUCCESS] Screenshot refreshed
[[17:35:40]] [INFO] Refreshing screenshot...
[[17:35:39]] [SUCCESS] Screenshot refreshed successfully
[[17:35:39]] [INFO] Executing Multi Step action step 2/7: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[17:35:39]] [SUCCESS] Screenshot refreshed
[[17:35:39]] [INFO] Refreshing screenshot...
[[17:35:36]] [SUCCESS] Screenshot refreshed successfully
[[17:35:35]] [INFO] Executing Multi Step action step 1/7: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[17:35:35]] [INFO] Loaded 7 steps from test case: Kmart-Signin-AU-ANDROID
[[17:35:35]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[17:35:35]] [INFO] g052Oo1Gcl=running
[[17:35:35]] [INFO] Executing action 298/482: Execute Test Case: Kmart-Signin-AU-ANDROID (7 steps)
[[17:35:35]] [SUCCESS] Screenshot refreshed
[[17:35:35]] [INFO] Refreshing screenshot...
[[17:35:35]] [INFO] J9loj6Zs95K=pass
[[17:35:34]] [SUCCESS] Screenshot refreshed successfully
[[17:35:33]] [INFO] J9loj6Zs95K=running
[[17:35:33]] [INFO] Executing action 297/482: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[17:35:33]] [SUCCESS] Screenshot refreshed
[[17:35:33]] [INFO] Refreshing screenshot...
[[17:35:33]] [INFO] Y8v5g7AJD1i=pass
[[17:35:29]] [SUCCESS] Screenshot refreshed successfully
[[17:35:28]] [INFO] Y8v5g7AJD1i=running
[[17:35:28]] [INFO] Executing action 296/482: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[17:35:28]] [SUCCESS] Screenshot refreshed
[[17:35:28]] [INFO] Refreshing screenshot...
[[17:35:28]] [SUCCESS] Screenshot refreshed
[[17:35:28]] [INFO] Refreshing screenshot...
[[17:35:25]] [SUCCESS] Screenshot refreshed successfully
[[17:35:24]] [INFO] Executing Multi Step action step 6/6: Tap on element with accessibility_id: txtOnePassOnboardingSkipForNow
[[17:35:24]] [SUCCESS] Screenshot refreshed
[[17:35:24]] [INFO] Refreshing screenshot...
[[17:35:23]] [SUCCESS] Screenshot refreshed successfully
[[17:35:22]] [INFO] Executing Multi Step action step 5/6: Tap on element with accessibility_id: btnMayBeLater
[[17:35:22]] [SUCCESS] Screenshot refreshed
[[17:35:22]] [INFO] Refreshing screenshot...
[[17:35:21]] [SUCCESS] Screenshot refreshed successfully
[[17:35:19]] [INFO] Executing Multi Step action step 4/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[17:35:19]] [SUCCESS] Screenshot refreshed
[[17:35:19]] [INFO] Refreshing screenshot...
[[17:35:15]] [SUCCESS] Screenshot refreshed successfully
[[17:35:15]] [INFO] Executing Multi Step action step 3/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[17:35:15]] [SUCCESS] Screenshot refreshed
[[17:35:15]] [INFO] Refreshing screenshot...
[[17:35:13]] [SUCCESS] Screenshot refreshed successfully
[[17:35:10]] [INFO] Executing Multi Step action step 2/6: Launch app: au.com.kmart
[[17:35:10]] [SUCCESS] Screenshot refreshed
[[17:35:10]] [INFO] Refreshing screenshot...
[[17:35:06]] [INFO] Executing Multi Step action step 1/6: Android Function: clear_app - Package: au.com.kmart
[[17:35:06]] [INFO] Loaded 6 steps from test case: Onboarding-Start-AU
[[17:35:06]] [INFO] Loading steps for Multi Step action: Onboarding-Start-AU
[[17:35:06]] [INFO] Executing action 295/482: Execute Test Case: Onboarding-Start-AU (6 steps)
[[17:35:06]] [INFO] Skipping remaining steps in failed test case (moving from action 292 to next test case at 294)
[[17:35:06]] [INFO] 2p13JoJbbA=fail
[[17:35:06]] [ERROR] Action 292 failed: Element not found or not tappable after all retry strategies: xpath='//android.widget.Button[contains(@text,"Remove")]'
[[17:34:01]] [SUCCESS] Screenshot refreshed successfully
[[17:34:00]] [INFO] 2p13JoJbbA=running
[[17:34:00]] [INFO] Executing action 292/482: Tap on element with xpath: //android.widget.Button[contains(@text,"Remove")]
[[17:34:00]] [SUCCESS] Screenshot refreshed
[[17:34:00]] [INFO] Refreshing screenshot...
[[17:34:00]] [INFO] rbzkUOQMtf=pass
[[17:33:37]] [SUCCESS] Screenshot refreshed successfully
[[17:33:37]] [INFO] rbzkUOQMtf=running
[[17:33:37]] [INFO] Executing action 291/482: Wait for 4 ms
[[17:33:37]] [SUCCESS] Screenshot refreshed
[[17:33:37]] [INFO] Refreshing screenshot...
[[17:33:37]] [INFO] 2p13JoJbbA=pass
[[17:33:35]] [SUCCESS] Screenshot refreshed successfully
[[17:33:34]] [INFO] 2p13JoJbbA=running
[[17:33:34]] [INFO] Executing action 290/482: Tap on element with xpath: //android.widget.Button[contains(@text,"Remove")]
[[17:33:34]] [SUCCESS] Screenshot refreshed
[[17:33:34]] [INFO] Refreshing screenshot...
[[17:33:34]] [INFO] eHLWiRoqqS=pass
[[17:33:31]] [SUCCESS] Screenshot refreshed successfully
[[17:33:00]] [INFO] eHLWiRoqqS=running
[[17:33:00]] [INFO] Executing action 289/482: Swipe from (50%, 70%) to (50%, 50%)
[[17:33:00]] [SUCCESS] Screenshot refreshed
[[17:33:00]] [INFO] Refreshing screenshot...
[[17:33:00]] [INFO] VYgfSNx3GG=pass
[[17:32:59]] [SUCCESS] Screenshot refreshed successfully
[[17:32:59]] [INFO] VYgfSNx3GG=running
[[17:32:59]] [INFO] Executing action 288/482: Tap on element with xpath: //android.view.View[@text="Delivery"]
[[17:32:59]] [SUCCESS] Screenshot refreshed
[[17:32:59]] [INFO] Refreshing screenshot...
[[17:32:59]] [INFO] XoMyLp2unA=pass
[[17:32:50]] [SUCCESS] Screenshot refreshed successfully
[[17:32:49]] [INFO] XoMyLp2unA=running
[[17:32:49]] [INFO] Executing action 287/482: Wait till xpath=//android.view.View[@text="Delivery"]
[[17:32:49]] [SUCCESS] Screenshot refreshed
[[17:32:49]] [INFO] Refreshing screenshot...
[[17:32:49]] [INFO] cTLBS0O1ot=pass
[[17:32:47]] [SUCCESS] Screenshot refreshed successfully
[[17:32:46]] [INFO] cTLBS0O1ot=running
[[17:32:46]] [INFO] Executing action 286/482: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[17:32:46]] [SUCCESS] Screenshot refreshed
[[17:32:46]] [INFO] Refreshing screenshot...
[[17:32:46]] [INFO] F4NGh9HrLw=pass
[[17:32:44]] [SUCCESS] Screenshot refreshed successfully
[[17:32:43]] [INFO] F4NGh9HrLw=running
[[17:32:43]] [INFO] Executing action 285/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[17:32:43]] [SUCCESS] Screenshot refreshed
[[17:32:43]] [INFO] Refreshing screenshot...
[[17:32:43]] [INFO] JRheDTvpJf=pass
[[17:32:39]] [SUCCESS] Screenshot refreshed successfully
[[17:32:38]] [INFO] JRheDTvpJf=running
[[17:32:38]] [INFO] Executing action 284/482: Tap on Text: "Add"
[[17:32:38]] [SUCCESS] Screenshot refreshed
[[17:32:38]] [INFO] Refreshing screenshot...
[[17:32:38]] [INFO] eHLWiRoqqS=pass
[[17:32:35]] [SUCCESS] Screenshot refreshed successfully
[[17:32:34]] [INFO] eHLWiRoqqS=running
[[17:32:34]] [INFO] Executing action 283/482: Swipe from (50%, 70%) to (50%, 30%)
[[17:32:34]] [SUCCESS] Screenshot refreshed
[[17:32:34]] [INFO] Refreshing screenshot...
[[17:32:34]] [INFO] kwF3J9NbRc=pass
[[17:32:15]] [SUCCESS] Screenshot refreshed successfully
[[17:32:14]] [INFO] kwF3J9NbRc=running
[[17:32:14]] [INFO] Executing action 282/482: Wait till text appears: "SKU"
[[17:32:14]] [SUCCESS] Screenshot refreshed
[[17:32:14]] [INFO] Refreshing screenshot...
[[17:32:14]] [INFO] kAQ1yIIw3h=pass
[[17:32:12]] [SUCCESS] Screenshot refreshed successfully
[[17:32:12]] [INFO] kAQ1yIIw3h=running
[[17:32:12]] [INFO] Executing action 281/482: Tap on element with xpath: ((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1] with fallback: Coordinates (98, 308)
[[17:32:12]] [SUCCESS] Screenshot refreshed
[[17:32:12]] [INFO] Refreshing screenshot...
[[17:32:12]] [INFO] lYPskZt0Ya=pass
[[17:32:02]] [SUCCESS] Screenshot refreshed successfully
[[17:32:02]] [INFO] lYPskZt0Ya=running
[[17:32:02]] [INFO] Executing action 280/482: Wait till xpath=//android.widget.Button[@text="Filter"]
[[17:32:02]] [SUCCESS] Screenshot refreshed
[[17:32:02]] [INFO] Refreshing screenshot...
[[17:32:02]] [INFO] oNKCP9pqiF=pass
[[17:32:00]] [SUCCESS] Screenshot refreshed successfully
[[17:31:41]] [INFO] oNKCP9pqiF=running
[[17:31:41]] [INFO] Executing action 279/482: Android Function: send_key_event - Key Event: ENTER
[[17:31:41]] [SUCCESS] Screenshot refreshed
[[17:31:41]] [INFO] Refreshing screenshot...
[[17:31:41]] [INFO] JRheDTvpJf=pass
[[17:31:40]] [SUCCESS] Screenshot refreshed successfully
[[17:31:39]] [INFO] JRheDTvpJf=running
[[17:31:39]] [INFO] Executing action 278/482: Input text: "mat"
[[17:31:39]] [SUCCESS] Screenshot refreshed
[[17:31:39]] [INFO] Refreshing screenshot...
[[17:31:39]] [INFO] o1gHFWHXTL=pass
[[17:31:36]] [SUCCESS] Screenshot refreshed successfully
[[17:31:35]] [INFO] o1gHFWHXTL=running
[[17:31:35]] [INFO] Executing action 277/482: Tap on Text: "Find"
[[17:31:35]] [SUCCESS] Screenshot refreshed
[[17:31:35]] [INFO] Refreshing screenshot...
[[17:31:35]] [INFO] o74txS2f4j=pass
[[17:31:27]] [SUCCESS] Screenshot refreshed successfully
[[17:31:26]] [INFO] o74txS2f4j=running
[[17:31:26]] [INFO] Executing action 276/482: Tap on image: find-products-browse.png
[[17:31:26]] [SUCCESS] Screenshot refreshed
[[17:31:26]] [INFO] Refreshing screenshot...
[[17:31:26]] [INFO] F4NGh9HrLw=pass
[[17:31:25]] [SUCCESS] Screenshot refreshed successfully
[[17:31:24]] [INFO] F4NGh9HrLw=running
[[17:31:24]] [INFO] Executing action 275/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 2 of 5")]
[[17:31:24]] [SUCCESS] Screenshot refreshed
[[17:31:24]] [INFO] Refreshing screenshot...
[[17:31:24]] [INFO] JRheDTvpJf=pass
[[17:31:20]] [SUCCESS] Screenshot refreshed successfully
[[17:31:20]] [INFO] JRheDTvpJf=running
[[17:31:20]] [INFO] Executing action 274/482: Tap on Text: "Add"
[[17:31:20]] [SUCCESS] Screenshot refreshed
[[17:31:20]] [INFO] Refreshing screenshot...
[[17:31:20]] [INFO] eHLWiRoqqS=pass
[[17:31:17]] [SUCCESS] Screenshot refreshed successfully
[[17:31:16]] [INFO] eHLWiRoqqS=running
[[17:31:16]] [INFO] Executing action 273/482: Swipe from (50%, 70%) to (50%, 30%)
[[17:31:16]] [SUCCESS] Screenshot refreshed
[[17:31:16]] [INFO] Refreshing screenshot...
[[17:31:16]] [INFO] kAQ1yIIw3h=pass
[[17:31:14]] [SUCCESS] Screenshot refreshed successfully
[[17:31:13]] [INFO] kAQ1yIIw3h=running
[[17:31:13]] [INFO] Executing action 272/482: Tap on element with xpath: ((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1] with fallback: Coordinates (98, 308)
[[17:31:13]] [SUCCESS] Screenshot refreshed
[[17:31:13]] [INFO] Refreshing screenshot...
[[17:31:13]] [INFO] lYPskZt0Ya=pass
[[17:31:02]] [SUCCESS] Screenshot refreshed successfully
[[17:31:02]] [INFO] lYPskZt0Ya=running
[[17:31:02]] [INFO] Executing action 271/482: Wait till xpath=//android.widget.Button[@text="Filter"]
[[17:31:02]] [SUCCESS] Screenshot refreshed
[[17:31:02]] [INFO] Refreshing screenshot...
[[17:31:02]] [INFO] YaIypAHEOz=pass
[[17:31:01]] [SUCCESS] Screenshot refreshed successfully
[[17:30:41]] [INFO] YaIypAHEOz=running
[[17:30:41]] [INFO] Executing action 270/482: Android Function: send_key_event - Key Event: ENTER
[[17:30:41]] [SUCCESS] Screenshot refreshed
[[17:30:41]] [INFO] Refreshing screenshot...
[[17:30:41]] [INFO] JRheDTvpJf=pass
[[17:30:40]] [SUCCESS] Screenshot refreshed successfully
[[17:30:39]] [INFO] JRheDTvpJf=running
[[17:30:39]] [INFO] Executing action 269/482: Input text: "Kids Toys"
[[17:30:39]] [SUCCESS] Screenshot refreshed
[[17:30:39]] [INFO] Refreshing screenshot...
[[17:30:39]] [INFO] o1gHFWHXTL=pass
[[17:30:35]] [SUCCESS] Screenshot refreshed successfully
[[17:30:35]] [INFO] o1gHFWHXTL=running
[[17:30:35]] [INFO] Executing action 268/482: Tap on Text: "Find"
[[17:30:35]] [SUCCESS] Screenshot refreshed
[[17:30:35]] [INFO] Refreshing screenshot...
[[17:30:35]] [INFO] o74txS2f4j=pass
[[17:29:56]] [SUCCESS] Screenshot refreshed successfully
[[17:29:55]] [INFO] o74txS2f4j=running
[[17:29:55]] [INFO] Executing action 267/482: Tap on image: find-products-browse.png
[[17:29:55]] [SUCCESS] Screenshot refreshed
[[17:29:55]] [INFO] Refreshing screenshot...
[[17:29:55]] [INFO] F4NGh9HrLw=pass
[[17:29:51]] [SUCCESS] Screenshot refreshed successfully
[[17:29:50]] [INFO] F4NGh9HrLw=running
[[17:29:50]] [INFO] Executing action 266/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 2 of 5")]
[[17:29:50]] [SUCCESS] Screenshot refreshed
[[17:29:50]] [INFO] Refreshing screenshot...
[[17:29:50]] [INFO] XPEr3w6Zof=pass
[[17:29:49]] [SUCCESS] Screenshot refreshed successfully
[[17:29:47]] [INFO] XPEr3w6Zof=running
[[17:29:47]] [INFO] Executing action 265/482: Launch app: au.com.kmart
[[17:29:47]] [SUCCESS] Screenshot refreshed
[[17:29:47]] [INFO] Refreshing screenshot...
[[17:29:47]] [INFO] XPEr3w6Zof=pass
[[17:29:45]] [SUCCESS] Screenshot refreshed successfully
[[17:29:45]] [INFO] XPEr3w6Zof=running
[[17:29:45]] [INFO] Executing action 264/482: Terminate app: au.com.kmart
[[17:29:45]] [SUCCESS] Screenshot refreshed
[[17:29:45]] [INFO] Refreshing screenshot...
[[17:29:45]] [INFO] Et3kvnFdxh=pass
[[17:29:44]] [SUCCESS] Screenshot refreshed successfully
[[17:29:43]] [INFO] Et3kvnFdxh=running
[[17:29:43]] [INFO] Executing action 263/482: Tap on element with xpath: //android.view.View[@content-desc="stnPostCodeSelectionScreenBodyWidget"]/android.view.View[1]/android.widget.ImageView
[[17:29:43]] [SUCCESS] Screenshot refreshed
[[17:29:43]] [INFO] Refreshing screenshot...
[[17:29:43]] [INFO] GWoppouz1l=pass
[[17:29:42]] [SUCCESS] Screenshot refreshed successfully
[[17:29:41]] [INFO] GWoppouz1l=running
[[17:29:41]] [INFO] Executing action 262/482: Check if element with xpath="//android.view.View[@content-desc="txtLocationTitle"]" exists
[[17:29:41]] [SUCCESS] Screenshot refreshed
[[17:29:41]] [INFO] Refreshing screenshot...
[[17:29:41]] [INFO] B6GDXWAmWp=pass
[[17:29:39]] [SUCCESS] Screenshot refreshed successfully
[[17:29:39]] [INFO] B6GDXWAmWp=running
[[17:29:39]] [INFO] Executing action 261/482: Tap on element with xpath: //android.widget.TextView[@text="Shop at"]/following-sibling::android.widget.Button
[[17:29:39]] [SUCCESS] Screenshot refreshed
[[17:29:39]] [INFO] Refreshing screenshot...
[[17:29:39]] [INFO] eHLWiRoqqS=pass
[[17:29:36]] [SUCCESS] Screenshot refreshed successfully
[[17:29:36]] [INFO] eHLWiRoqqS=running
[[17:29:36]] [INFO] Executing action 260/482: Swipe from (50%, 70%) to (50%, 40%)
[[17:29:36]] [SUCCESS] Screenshot refreshed
[[17:29:36]] [INFO] Refreshing screenshot...
[[17:29:36]] [INFO] mtYqeDttRc=pass
[[17:29:34]] [SUCCESS] Screenshot refreshed successfully
[[17:29:34]] [INFO] mtYqeDttRc=running
[[17:29:34]] [INFO] Executing action 259/482: Tap on element with xpath: //android.widget.Button[@resource-id="close-btn"]
[[17:29:34]] [SUCCESS] Screenshot refreshed
[[17:29:34]] [INFO] Refreshing screenshot...
[[17:29:34]] [INFO] P4b2BITpCf=pass
[[17:29:04]] [SUCCESS] Screenshot refreshed successfully
[[17:29:04]] [INFO] P4b2BITpCf=running
[[17:29:04]] [INFO] Executing action 258/482: Check if element with text="interest-" exists
[[17:29:04]] [SUCCESS] Screenshot refreshed
[[17:29:04]] [INFO] Refreshing screenshot...
[[17:29:04]] [INFO] q6cKxgMAIn=pass
[[17:29:02]] [SUCCESS] Screenshot refreshed successfully
[[17:29:02]] [INFO] q6cKxgMAIn=running
[[17:29:02]] [INFO] Executing action 257/482: Tap on element with xpath: //android.widget.Button[@text="Learn more about PayPal Pay in 4"]
[[17:29:02]] [SUCCESS] Screenshot refreshed
[[17:29:02]] [INFO] Refreshing screenshot...
[[17:29:02]] [INFO] Et3kvnFdxh=pass
[[17:29:00]] [SUCCESS] Screenshot refreshed successfully
[[17:28:59]] [INFO] Et3kvnFdxh=running
[[17:28:59]] [INFO] Executing action 256/482: Android Function: send_key_event - Key Event: BACK
[[17:28:59]] [SUCCESS] Screenshot refreshed
[[17:28:59]] [INFO] Refreshing screenshot...
[[17:28:59]] [INFO] P4b2BITpCf=pass
[[17:28:49]] [SUCCESS] Screenshot refreshed successfully
[[17:28:48]] [INFO] P4b2BITpCf=running
[[17:28:48]] [INFO] Executing action 255/482: Check if element with text="What" exists
[[17:28:48]] [SUCCESS] Screenshot refreshed
[[17:28:48]] [INFO] Refreshing screenshot...
[[17:28:48]] [INFO] inrxgdWzXr=pass
[[17:28:47]] [SUCCESS] Screenshot refreshed successfully
[[17:28:46]] [INFO] inrxgdWzXr=running
[[17:28:46]] [INFO] Executing action 254/482: Tap on element with xpath: //android.widget.TextView[@text="Learn moreabout Zip"]
[[17:28:46]] [SUCCESS] Screenshot refreshed
[[17:28:46]] [INFO] Refreshing screenshot...
[[17:28:46]] [INFO] Et3kvnFdxh=pass
[[17:28:45]] [SUCCESS] Screenshot refreshed successfully
[[17:28:44]] [INFO] Et3kvnFdxh=running
[[17:28:44]] [INFO] Executing action 253/482: Android Function: send_key_event - Key Event: BACK
[[17:28:44]] [SUCCESS] Screenshot refreshed
[[17:28:44]] [INFO] Refreshing screenshot...
[[17:28:44]] [INFO] DhWa2PCBXE=pass
[[17:28:40]] [SUCCESS] Screenshot refreshed successfully
[[17:28:39]] [INFO] DhWa2PCBXE=running
[[17:28:39]] [INFO] Executing action 252/482: Check if element with text="Apply" exists
[[17:28:39]] [SUCCESS] Screenshot refreshed
[[17:28:39]] [INFO] Refreshing screenshot...
[[17:28:39]] [INFO] pk2DLZFBmx=pass
[[17:28:38]] [SUCCESS] Screenshot refreshed successfully
[[17:28:37]] [INFO] pk2DLZFBmx=running
[[17:28:37]] [INFO] Executing action 251/482: Tap on element with xpath: //android.widget.TextView[@text="Learn moreabout AfterPay"]
[[17:28:37]] [SUCCESS] Screenshot refreshed
[[17:28:37]] [INFO] Refreshing screenshot...
[[17:28:37]] [INFO] ShJSdXvmVL=pass
[[17:28:34]] [SUCCESS] Screenshot refreshed successfully
[[17:28:33]] [INFO] ShJSdXvmVL=running
[[17:28:33]] [INFO] Executing action 250/482: Swipe up till element xpath: "//android.widget.TextView[@text="Learn moreabout AfterPay"]" is visible
[[17:28:33]] [SUCCESS] Screenshot refreshed
[[17:28:33]] [INFO] Refreshing screenshot...
[[17:28:33]] [INFO] y5FboDiRLS=pass
[[17:28:13]] [SUCCESS] Screenshot refreshed successfully
[[17:28:13]] [INFO] y5FboDiRLS=running
[[17:28:13]] [INFO] Executing action 249/482: Tap on image: share-close.png
[[17:28:13]] [SUCCESS] Screenshot refreshed
[[17:28:13]] [INFO] Refreshing screenshot...
[[17:28:13]] [INFO] EEx673tuI0=pass
[[17:28:09]] [SUCCESS] Screenshot refreshed successfully
[[17:28:09]] [INFO] EEx673tuI0=running
[[17:28:09]] [INFO] Executing action 248/482: Check if element with text="Share" exists
[[17:28:09]] [SUCCESS] Screenshot refreshed
[[17:28:09]] [INFO] Refreshing screenshot...
[[17:28:09]] [INFO] dCqKBG3e7u=pass
[[17:28:08]] [SUCCESS] Screenshot refreshed successfully
[[17:28:07]] [INFO] dCqKBG3e7u=running
[[17:28:07]] [INFO] Executing action 247/482: Tap on element with xpath: //android.view.View[@content-desc="Product Details"]/following-sibling::android.widget.ImageView[1]
[[17:28:07]] [SUCCESS] Screenshot refreshed
[[17:28:07]] [INFO] Refreshing screenshot...
[[17:28:07]] [INFO] kAQ1yIIw3h=pass
[[17:28:05]] [SUCCESS] Screenshot refreshed successfully
[[17:28:04]] [INFO] kAQ1yIIw3h=running
[[17:28:04]] [INFO] Executing action 246/482: Tap on element with xpath: ((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1] with fallback: Coordinates (98, 308)
[[17:28:04]] [SUCCESS] Screenshot refreshed
[[17:28:04]] [INFO] Refreshing screenshot...
[[17:28:04]] [INFO] OmKfD9iBjD=pass
[[17:28:02]] [SUCCESS] Screenshot refreshed successfully
[[17:28:01]] [INFO] OmKfD9iBjD=running
[[17:28:01]] [INFO] Executing action 245/482: Wait till xpath=((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1]
[[17:28:01]] [SUCCESS] Screenshot refreshed
[[17:28:01]] [INFO] Refreshing screenshot...
[[17:28:01]] [INFO] dMl1PH9Dlc=pass
[[17:27:41]] [SUCCESS] Screenshot refreshed successfully
[[17:27:40]] [INFO] dMl1PH9Dlc=running
[[17:27:40]] [INFO] Executing action 244/482: Wait for 10 ms
[[17:27:40]] [SUCCESS] Screenshot refreshed
[[17:27:40]] [INFO] Refreshing screenshot...
[[17:27:40]] [INFO] eHLWiRoqqS=pass
[[17:27:37]] [SUCCESS] Screenshot refreshed successfully
[[17:27:36]] [INFO] eHLWiRoqqS=running
[[17:27:36]] [INFO] Executing action 243/482: Swipe from (50%, 70%) to (50%, 30%)
[[17:27:36]] [SUCCESS] Screenshot refreshed
[[17:27:36]] [INFO] Refreshing screenshot...
[[17:27:36]] [INFO] huUnpMMjVR=pass
[[17:27:34]] [SUCCESS] Screenshot refreshed successfully
[[17:27:33]] [INFO] huUnpMMjVR=running
[[17:27:33]] [INFO] Executing action 242/482: Tap on element with xpath: //android.widget.Button[@text="In stock only i... ChipsClose"]
[[17:27:33]] [SUCCESS] Screenshot refreshed
[[17:27:33]] [INFO] Refreshing screenshot...
[[17:27:33]] [INFO] XmAxcBtFI0=pass
[[17:27:31]] [SUCCESS] Screenshot refreshed successfully
[[17:27:21]] [INFO] XmAxcBtFI0=running
[[17:27:21]] [INFO] Executing action 241/482: Check if element with xpath="//android.widget.Button[@text="In stock only i... ChipsClose"]" exists
[[17:27:21]] [SUCCESS] Screenshot refreshed
[[17:27:21]] [INFO] Refreshing screenshot...
[[17:27:21]] [INFO] a50JhCx0ir=pass
[[17:27:18]] [SUCCESS] Screenshot refreshed successfully
[[17:27:17]] [INFO] a50JhCx0ir=running
[[17:27:17]] [INFO] Executing action 240/482: Tap on Text: "Show"
[[17:27:17]] [SUCCESS] Screenshot refreshed
[[17:27:17]] [INFO] Refreshing screenshot...
[[17:27:17]] [INFO] dMl1PH9Dlc=pass
[[17:27:11]] [SUCCESS] Screenshot refreshed successfully
[[17:27:10]] [INFO] dMl1PH9Dlc=running
[[17:27:10]] [INFO] Executing action 239/482: Wait for 5 ms
[[17:27:10]] [SUCCESS] Screenshot refreshed
[[17:27:10]] [INFO] Refreshing screenshot...
[[17:27:10]] [INFO] a50JhCx0ir=pass
[[17:27:07]] [SUCCESS] Screenshot refreshed successfully
[[17:27:07]] [INFO] a50JhCx0ir=running
[[17:27:07]] [INFO] Executing action 238/482: Tap on Text: "only"
[[17:27:07]] [SUCCESS] Screenshot refreshed
[[17:27:07]] [INFO] Refreshing screenshot...
[[17:27:07]] [INFO] Y1O1clhMSJ=pass
[[17:27:05]] [SUCCESS] Screenshot refreshed successfully
[[17:27:04]] [INFO] Y1O1clhMSJ=running
[[17:27:04]] [INFO] Executing action 237/482: Tap on element with xpath: //android.widget.Button[@text="Filter"]
[[17:27:04]] [SUCCESS] Screenshot refreshed
[[17:27:04]] [INFO] Refreshing screenshot...
[[17:27:04]] [INFO] lYPskZt0Ya=pass
[[17:26:39]] [SUCCESS] Screenshot refreshed successfully
[[17:26:38]] [INFO] lYPskZt0Ya=running
[[17:26:38]] [INFO] Executing action 236/482: Wait till xpath=//android.widget.Button[@text="Filter"]
[[17:26:38]] [SUCCESS] Screenshot refreshed
[[17:26:38]] [INFO] Refreshing screenshot...
[[17:26:38]] [INFO] xUbWFa8Ok2=pass
[[17:26:35]] [SUCCESS] Screenshot refreshed successfully
[[17:26:34]] [INFO] xUbWFa8Ok2=running
[[17:26:34]] [INFO] Executing action 235/482: Tap on Text: "Latest"
[[17:26:34]] [SUCCESS] Screenshot refreshed
[[17:26:34]] [INFO] Refreshing screenshot...
[[17:26:34]] [INFO] RbNtEW6N9T=pass
[[17:26:30]] [SUCCESS] Screenshot refreshed successfully
[[17:26:20]] [INFO] RbNtEW6N9T=running
[[17:26:20]] [INFO] Executing action 234/482: Tap on Text: "Toys"
[[17:26:20]] [SUCCESS] Screenshot refreshed
[[17:26:20]] [INFO] Refreshing screenshot...
[[17:26:20]] [INFO] ltDXyWvtEz=pass
[[17:26:19]] [SUCCESS] Screenshot refreshed successfully
[[17:26:18]] [INFO] ltDXyWvtEz=running
[[17:26:18]] [INFO] Executing action 233/482: Tap on element with xpath: //android.view.View[@content-desc="Search"]/preceding::android.widget.ImageView[1]
[[17:26:18]] [SUCCESS] Screenshot refreshed
[[17:26:18]] [INFO] Refreshing screenshot...
[[17:26:18]] [INFO] QPKR6jUF9O=pass
[[17:26:17]] [SUCCESS] Screenshot refreshed successfully
[[17:26:16]] [INFO] QPKR6jUF9O=running
[[17:26:16]] [INFO] Executing action 232/482: Check if element with xpath="//android.widget.ImageView[@content-desc="Scan barcode"]" exists
[[17:26:16]] [SUCCESS] Screenshot refreshed
[[17:26:16]] [INFO] Refreshing screenshot...
[[17:26:16]] [INFO] vfwUVEyq6X=pass
[[17:26:15]] [SUCCESS] Screenshot refreshed successfully
[[17:26:15]] [INFO] vfwUVEyq6X=running
[[17:26:15]] [INFO] Executing action 231/482: Check if element with xpath="//android.widget.ImageView[@content-desc="More"]" exists
[[17:26:15]] [SUCCESS] Screenshot refreshed
[[17:26:15]] [INFO] Refreshing screenshot...
[[17:26:15]] [INFO] o74txS2f4j=pass
[[17:26:06]] [SUCCESS] Screenshot refreshed successfully
[[17:26:06]] [INFO] o74txS2f4j=running
[[17:26:06]] [INFO] Executing action 230/482: Tap on image: find-products-browse.png
[[17:26:06]] [SUCCESS] Screenshot refreshed
[[17:26:06]] [INFO] Refreshing screenshot...
[[17:26:06]] [INFO] F4NGh9HrLw=pass
[[17:26:05]] [SUCCESS] Screenshot refreshed successfully
[[17:26:04]] [INFO] F4NGh9HrLw=running
[[17:26:04]] [INFO] Executing action 229/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 2 of 5")]
[[17:26:04]] [SUCCESS] Screenshot refreshed
[[17:26:04]] [INFO] Refreshing screenshot...
[[17:26:04]] [INFO] H9fy9qcFbZ=pass
[[17:26:02]] [SUCCESS] Screenshot refreshed successfully
[[17:26:01]] [INFO] H9fy9qcFbZ=running
[[17:26:01]] [INFO] Executing action 228/482: Launch app: au.com.kmart
[[17:26:01]] [SUCCESS] Screenshot refreshed
[[17:26:01]] [INFO] Refreshing screenshot...
[[17:26:01]] [SUCCESS] Screenshot refreshed
[[17:26:01]] [INFO] Refreshing screenshot...
[[17:25:57]] [SUCCESS] Screenshot refreshed successfully
[[17:25:57]] [INFO] Executing Multi Step action step 6/6: Tap on element with accessibility_id: txtOnePassOnboardingSkipForNow
[[17:25:57]] [SUCCESS] Screenshot refreshed
[[17:25:57]] [INFO] Refreshing screenshot...
[[17:25:55]] [SUCCESS] Screenshot refreshed successfully
[[17:25:55]] [INFO] Executing Multi Step action step 5/6: Tap on element with accessibility_id: btnMayBeLater
[[17:25:55]] [SUCCESS] Screenshot refreshed
[[17:25:55]] [INFO] Refreshing screenshot...
[[17:25:53]] [SUCCESS] Screenshot refreshed successfully
[[17:25:52]] [INFO] Executing Multi Step action step 4/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[17:25:52]] [SUCCESS] Screenshot refreshed
[[17:25:52]] [INFO] Refreshing screenshot...
[[17:25:48]] [SUCCESS] Screenshot refreshed successfully
[[17:25:47]] [INFO] Executing Multi Step action step 3/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[17:25:47]] [SUCCESS] Screenshot refreshed
[[17:25:47]] [INFO] Refreshing screenshot...
[[17:25:46]] [SUCCESS] Screenshot refreshed successfully
[[17:25:44]] [INFO] Executing Multi Step action step 2/6: Launch app: au.com.kmart
[[17:25:44]] [SUCCESS] Screenshot refreshed
[[17:25:44]] [INFO] Refreshing screenshot...
[[17:25:39]] [INFO] Executing Multi Step action step 1/6: Android Function: clear_app - Package: au.com.kmart
[[17:25:39]] [INFO] Loaded 6 steps from test case: Onboarding-Start-AU
[[17:25:38]] [INFO] Loading steps for Multi Step action: Onboarding-Start-AU
[[17:25:38]] [INFO] H9fy9qcFbZ=running
[[17:25:38]] [INFO] Executing action 227/482: Execute Test Case: Onboarding-Start-AU (6 steps)
[[17:25:36]] [INFO] === RETRYING TEST CASE: Browse__PDP_AU_ANDROID_20250709040606.json (Attempt 3 of 3) ===
[[17:25:36]] [INFO] 2p13JoJbbA=fail
[[17:25:36]] [ERROR] Action 292 failed: Element not found or not tappable after all retry strategies: xpath='//android.widget.Button[contains(@text,"Remove")]'
[[17:24:38]] [SUCCESS] Screenshot refreshed successfully
[[17:24:37]] [INFO] 2p13JoJbbA=running
[[17:24:37]] [INFO] Executing action 292/482: Tap on element with xpath: //android.widget.Button[contains(@text,"Remove")]
[[17:24:37]] [SUCCESS] Screenshot refreshed
[[17:24:37]] [INFO] Refreshing screenshot...
[[17:24:37]] [INFO] rbzkUOQMtf=pass
[[17:24:32]] [SUCCESS] Screenshot refreshed successfully
[[17:24:31]] [INFO] rbzkUOQMtf=running
[[17:24:31]] [INFO] Executing action 291/482: Wait for 4 ms
[[17:24:31]] [SUCCESS] Screenshot refreshed
[[17:24:31]] [INFO] Refreshing screenshot...
[[17:24:31]] [INFO] 2p13JoJbbA=pass
[[17:24:30]] [SUCCESS] Screenshot refreshed successfully
[[17:24:29]] [INFO] 2p13JoJbbA=running
[[17:24:29]] [INFO] Executing action 290/482: Tap on element with xpath: //android.widget.Button[contains(@text,"Remove")]
[[17:24:29]] [SUCCESS] Screenshot refreshed
[[17:24:29]] [INFO] Refreshing screenshot...
[[17:24:29]] [INFO] eHLWiRoqqS=pass
[[17:24:26]] [SUCCESS] Screenshot refreshed successfully
[[17:24:25]] [INFO] eHLWiRoqqS=running
[[17:24:25]] [INFO] Executing action 289/482: Swipe from (50%, 70%) to (50%, 50%)
[[17:24:25]] [SUCCESS] Screenshot refreshed
[[17:24:25]] [INFO] Refreshing screenshot...
[[17:24:25]] [INFO] VYgfSNx3GG=pass
[[17:24:24]] [SUCCESS] Screenshot refreshed successfully
[[17:24:23]] [INFO] VYgfSNx3GG=running
[[17:24:23]] [INFO] Executing action 288/482: Tap on element with xpath: //android.view.View[@text="Delivery"]
[[17:24:23]] [SUCCESS] Screenshot refreshed
[[17:24:23]] [INFO] Refreshing screenshot...
[[17:24:23]] [INFO] XoMyLp2unA=pass
[[17:24:17]] [SUCCESS] Screenshot refreshed successfully
[[17:24:16]] [INFO] XoMyLp2unA=running
[[17:24:16]] [INFO] Executing action 287/482: Wait till xpath=//android.view.View[@text="Delivery"]
[[17:24:16]] [SUCCESS] Screenshot refreshed
[[17:24:16]] [INFO] Refreshing screenshot...
[[17:24:16]] [INFO] cTLBS0O1ot=pass
[[17:24:14]] [SUCCESS] Screenshot refreshed successfully
[[17:24:13]] [INFO] cTLBS0O1ot=running
[[17:24:13]] [INFO] Executing action 286/482: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[17:24:13]] [SUCCESS] Screenshot refreshed
[[17:24:13]] [INFO] Refreshing screenshot...
[[17:24:13]] [INFO] F4NGh9HrLw=pass
[[17:24:12]] [SUCCESS] Screenshot refreshed successfully
[[17:24:11]] [INFO] F4NGh9HrLw=running
[[17:24:11]] [INFO] Executing action 285/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[17:24:11]] [SUCCESS] Screenshot refreshed
[[17:24:11]] [INFO] Refreshing screenshot...
[[17:24:11]] [INFO] JRheDTvpJf=pass
[[17:24:06]] [SUCCESS] Screenshot refreshed successfully
[[17:24:05]] [INFO] JRheDTvpJf=running
[[17:24:05]] [INFO] Executing action 284/482: Tap on Text: "Add"
[[17:24:05]] [SUCCESS] Screenshot refreshed
[[17:24:05]] [INFO] Refreshing screenshot...
[[17:24:05]] [INFO] eHLWiRoqqS=pass
[[17:24:03]] [SUCCESS] Screenshot refreshed successfully
[[17:24:02]] [INFO] eHLWiRoqqS=running
[[17:24:02]] [INFO] Executing action 283/482: Swipe from (50%, 70%) to (50%, 30%)
[[17:24:02]] [SUCCESS] Screenshot refreshed
[[17:24:02]] [INFO] Refreshing screenshot...
[[17:24:02]] [INFO] kwF3J9NbRc=pass
[[17:23:56]] [SUCCESS] Screenshot refreshed successfully
[[17:23:55]] [INFO] kwF3J9NbRc=running
[[17:23:55]] [INFO] Executing action 282/482: Wait till text appears: "SKU"
[[17:23:55]] [SUCCESS] Screenshot refreshed
[[17:23:55]] [INFO] Refreshing screenshot...
[[17:23:55]] [INFO] kAQ1yIIw3h=pass
[[17:23:53]] [SUCCESS] Screenshot refreshed successfully
[[17:23:52]] [INFO] kAQ1yIIw3h=running
[[17:23:52]] [INFO] Executing action 281/482: Tap on element with xpath: ((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1] with fallback: Coordinates (98, 308)
[[17:23:52]] [SUCCESS] Screenshot refreshed
[[17:23:52]] [INFO] Refreshing screenshot...
[[17:23:52]] [INFO] lYPskZt0Ya=pass
[[17:23:44]] [SUCCESS] Screenshot refreshed successfully
[[17:23:43]] [INFO] lYPskZt0Ya=running
[[17:23:43]] [INFO] Executing action 280/482: Wait till xpath=//android.widget.Button[@text="Filter"]
[[17:23:43]] [SUCCESS] Screenshot refreshed
[[17:23:43]] [INFO] Refreshing screenshot...
[[17:23:43]] [INFO] oNKCP9pqiF=pass
[[17:23:42]] [SUCCESS] Screenshot refreshed successfully
[[17:23:41]] [INFO] oNKCP9pqiF=running
[[17:23:41]] [INFO] Executing action 279/482: Android Function: send_key_event - Key Event: ENTER
[[17:23:41]] [SUCCESS] Screenshot refreshed
[[17:23:41]] [INFO] Refreshing screenshot...
[[17:23:41]] [INFO] JRheDTvpJf=pass
[[17:23:40]] [SUCCESS] Screenshot refreshed successfully
[[17:23:39]] [INFO] JRheDTvpJf=running
[[17:23:39]] [INFO] Executing action 278/482: Input text: "mat"
[[17:23:39]] [SUCCESS] Screenshot refreshed
[[17:23:39]] [INFO] Refreshing screenshot...
[[17:23:39]] [INFO] o1gHFWHXTL=pass
[[17:23:36]] [SUCCESS] Screenshot refreshed successfully
[[17:23:35]] [INFO] o1gHFWHXTL=running
[[17:23:35]] [INFO] Executing action 277/482: Tap on Text: "Find"
[[17:23:35]] [SUCCESS] Screenshot refreshed
[[17:23:35]] [INFO] Refreshing screenshot...
[[17:23:35]] [INFO] o74txS2f4j=pass
[[17:23:07]] [SUCCESS] Screenshot refreshed successfully
[[17:23:07]] [INFO] o74txS2f4j=running
[[17:23:07]] [INFO] Executing action 276/482: Tap on image: find-products-browse.png
[[17:23:07]] [SUCCESS] Screenshot refreshed
[[17:23:07]] [INFO] Refreshing screenshot...
[[17:23:07]] [INFO] F4NGh9HrLw=pass
[[17:23:05]] [SUCCESS] Screenshot refreshed successfully
[[17:23:04]] [INFO] F4NGh9HrLw=running
[[17:23:04]] [INFO] Executing action 275/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 2 of 5")]
[[17:23:04]] [SUCCESS] Screenshot refreshed
[[17:23:04]] [INFO] Refreshing screenshot...
[[17:23:04]] [INFO] JRheDTvpJf=pass
[[17:23:01]] [SUCCESS] Screenshot refreshed successfully
[[17:22:41]] [INFO] JRheDTvpJf=running
[[17:22:41]] [INFO] Executing action 274/482: Tap on Text: "Add"
[[17:22:41]] [SUCCESS] Screenshot refreshed
[[17:22:41]] [INFO] Refreshing screenshot...
[[17:22:41]] [INFO] eHLWiRoqqS=pass
[[17:22:38]] [SUCCESS] Screenshot refreshed successfully
[[17:22:37]] [INFO] eHLWiRoqqS=running
[[17:22:37]] [INFO] Executing action 273/482: Swipe from (50%, 70%) to (50%, 30%)
[[17:22:37]] [SUCCESS] Screenshot refreshed
[[17:22:37]] [INFO] Refreshing screenshot...
[[17:22:37]] [INFO] kAQ1yIIw3h=pass
[[17:22:35]] [SUCCESS] Screenshot refreshed successfully
[[17:22:34]] [INFO] kAQ1yIIw3h=running
[[17:22:34]] [INFO] Executing action 272/482: Tap on element with xpath: ((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1] with fallback: Coordinates (98, 308)
[[17:22:34]] [SUCCESS] Screenshot refreshed
[[17:22:34]] [INFO] Refreshing screenshot...
[[17:22:34]] [INFO] lYPskZt0Ya=pass
[[17:22:31]] [SUCCESS] Screenshot refreshed successfully
[[17:22:10]] [INFO] lYPskZt0Ya=running
[[17:22:10]] [INFO] Executing action 271/482: Wait till xpath=//android.widget.Button[@text="Filter"]
[[17:22:10]] [SUCCESS] Screenshot refreshed
[[17:22:10]] [INFO] Refreshing screenshot...
[[17:22:10]] [INFO] YaIypAHEOz=pass
[[17:22:09]] [SUCCESS] Screenshot refreshed successfully
[[17:22:08]] [INFO] YaIypAHEOz=running
[[17:22:08]] [INFO] Executing action 270/482: Android Function: send_key_event - Key Event: ENTER
[[17:22:08]] [SUCCESS] Screenshot refreshed
[[17:22:08]] [INFO] Refreshing screenshot...
[[17:22:08]] [INFO] JRheDTvpJf=pass
[[17:22:06]] [SUCCESS] Screenshot refreshed successfully
[[17:22:06]] [INFO] JRheDTvpJf=running
[[17:22:06]] [INFO] Executing action 269/482: Input text: "Kids Toys"
[[17:22:06]] [SUCCESS] Screenshot refreshed
[[17:22:06]] [INFO] Refreshing screenshot...
[[17:22:06]] [INFO] o1gHFWHXTL=pass
[[17:22:03]] [SUCCESS] Screenshot refreshed successfully
[[17:22:02]] [INFO] o1gHFWHXTL=running
[[17:22:02]] [INFO] Executing action 268/482: Tap on Text: "Find"
[[17:22:02]] [SUCCESS] Screenshot refreshed
[[17:22:02]] [INFO] Refreshing screenshot...
[[17:22:02]] [INFO] o74txS2f4j=pass
[[17:21:37]] [SUCCESS] Screenshot refreshed successfully
[[17:21:37]] [INFO] o74txS2f4j=running
[[17:21:37]] [INFO] Executing action 267/482: Tap on image: find-products-browse.png
[[17:21:37]] [SUCCESS] Screenshot refreshed
[[17:21:37]] [INFO] Refreshing screenshot...
[[17:21:37]] [INFO] F4NGh9HrLw=pass
[[17:21:33]] [SUCCESS] Screenshot refreshed successfully
[[17:21:32]] [INFO] F4NGh9HrLw=running
[[17:21:32]] [INFO] Executing action 266/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 2 of 5")]
[[17:21:32]] [SUCCESS] Screenshot refreshed
[[17:21:32]] [INFO] Refreshing screenshot...
[[17:21:32]] [INFO] XPEr3w6Zof=pass
[[17:21:31]] [SUCCESS] Screenshot refreshed successfully
[[17:21:29]] [INFO] XPEr3w6Zof=running
[[17:21:29]] [INFO] Executing action 265/482: Launch app: au.com.kmart
[[17:21:29]] [SUCCESS] Screenshot refreshed
[[17:21:29]] [INFO] Refreshing screenshot...
[[17:21:29]] [INFO] XPEr3w6Zof=pass
[[17:21:27]] [SUCCESS] Screenshot refreshed successfully
[[17:21:27]] [INFO] XPEr3w6Zof=running
[[17:21:27]] [INFO] Executing action 264/482: Terminate app: au.com.kmart
[[17:21:27]] [SUCCESS] Screenshot refreshed
[[17:21:27]] [INFO] Refreshing screenshot...
[[17:21:27]] [INFO] Et3kvnFdxh=pass
[[17:21:25]] [SUCCESS] Screenshot refreshed successfully
[[17:21:24]] [INFO] Et3kvnFdxh=running
[[17:21:24]] [INFO] Executing action 263/482: Tap on element with xpath: //android.view.View[@content-desc="stnPostCodeSelectionScreenBodyWidget"]/android.view.View[1]/android.widget.ImageView
[[17:21:24]] [SUCCESS] Screenshot refreshed
[[17:21:24]] [INFO] Refreshing screenshot...
[[17:21:24]] [INFO] GWoppouz1l=pass
[[17:21:23]] [SUCCESS] Screenshot refreshed successfully
[[17:21:22]] [INFO] GWoppouz1l=running
[[17:21:22]] [INFO] Executing action 262/482: Check if element with xpath="//android.view.View[@content-desc="txtLocationTitle"]" exists
[[17:21:22]] [SUCCESS] Screenshot refreshed
[[17:21:22]] [INFO] Refreshing screenshot...
[[17:21:22]] [INFO] B6GDXWAmWp=pass
[[17:21:20]] [SUCCESS] Screenshot refreshed successfully
[[17:21:20]] [INFO] B6GDXWAmWp=running
[[17:21:20]] [INFO] Executing action 261/482: Tap on element with xpath: //android.widget.TextView[@text="Shop at"]/following-sibling::android.widget.Button
[[17:21:20]] [SUCCESS] Screenshot refreshed
[[17:21:20]] [INFO] Refreshing screenshot...
[[17:21:20]] [INFO] eHLWiRoqqS=pass
[[17:21:17]] [SUCCESS] Screenshot refreshed successfully
[[17:21:16]] [INFO] eHLWiRoqqS=running
[[17:21:16]] [INFO] Executing action 260/482: Swipe from (50%, 70%) to (50%, 40%)
[[17:21:16]] [SUCCESS] Screenshot refreshed
[[17:21:16]] [INFO] Refreshing screenshot...
[[17:21:16]] [INFO] mtYqeDttRc=pass
[[17:21:15]] [SUCCESS] Screenshot refreshed successfully
[[17:21:14]] [INFO] mtYqeDttRc=running
[[17:21:14]] [INFO] Executing action 259/482: Tap on element with xpath: //android.widget.Button[@resource-id="close-btn"]
[[17:21:14]] [SUCCESS] Screenshot refreshed
[[17:21:14]] [INFO] Refreshing screenshot...
[[17:21:14]] [INFO] P4b2BITpCf=pass
[[17:21:08]] [SUCCESS] Screenshot refreshed successfully
[[17:21:08]] [INFO] P4b2BITpCf=running
[[17:21:08]] [INFO] Executing action 258/482: Check if element with text="interest-" exists
[[17:21:08]] [SUCCESS] Screenshot refreshed
[[17:21:08]] [INFO] Refreshing screenshot...
[[17:21:08]] [INFO] q6cKxgMAIn=pass
[[17:21:06]] [SUCCESS] Screenshot refreshed successfully
[[17:21:05]] [INFO] q6cKxgMAIn=running
[[17:21:05]] [INFO] Executing action 257/482: Tap on element with xpath: //android.widget.Button[@text="Learn more about PayPal Pay in 4"]
[[17:21:05]] [SUCCESS] Screenshot refreshed
[[17:21:05]] [INFO] Refreshing screenshot...
[[17:21:05]] [INFO] Et3kvnFdxh=pass
[[17:21:04]] [SUCCESS] Screenshot refreshed successfully
[[17:21:04]] [INFO] Et3kvnFdxh=running
[[17:21:04]] [INFO] Executing action 256/482: Android Function: send_key_event - Key Event: BACK
[[17:21:04]] [SUCCESS] Screenshot refreshed
[[17:21:04]] [INFO] Refreshing screenshot...
[[17:21:04]] [INFO] P4b2BITpCf=pass
[[17:20:58]] [SUCCESS] Screenshot refreshed successfully
[[17:20:58]] [INFO] P4b2BITpCf=running
[[17:20:58]] [INFO] Executing action 255/482: Check if element with text="What" exists
[[17:20:58]] [SUCCESS] Screenshot refreshed
[[17:20:58]] [INFO] Refreshing screenshot...
[[17:20:58]] [INFO] inrxgdWzXr=pass
[[17:20:56]] [SUCCESS] Screenshot refreshed successfully
[[17:20:55]] [INFO] inrxgdWzXr=running
[[17:20:55]] [INFO] Executing action 254/482: Tap on element with xpath: //android.widget.TextView[@text="Learn moreabout Zip"]
[[17:20:55]] [SUCCESS] Screenshot refreshed
[[17:20:55]] [INFO] Refreshing screenshot...
[[17:20:55]] [INFO] Et3kvnFdxh=pass
[[17:20:54]] [SUCCESS] Screenshot refreshed successfully
[[17:20:54]] [INFO] Et3kvnFdxh=running
[[17:20:54]] [INFO] Executing action 253/482: Android Function: send_key_event - Key Event: BACK
[[17:20:54]] [SUCCESS] Screenshot refreshed
[[17:20:54]] [INFO] Refreshing screenshot...
[[17:20:54]] [INFO] DhWa2PCBXE=pass
[[17:20:50]] [SUCCESS] Screenshot refreshed successfully
[[17:20:49]] [INFO] DhWa2PCBXE=running
[[17:20:49]] [INFO] Executing action 252/482: Check if element with text="Apply" exists
[[17:20:49]] [SUCCESS] Screenshot refreshed
[[17:20:49]] [INFO] Refreshing screenshot...
[[17:20:49]] [INFO] pk2DLZFBmx=pass
[[17:20:48]] [SUCCESS] Screenshot refreshed successfully
[[17:20:47]] [INFO] pk2DLZFBmx=running
[[17:20:47]] [INFO] Executing action 251/482: Tap on element with xpath: //android.widget.TextView[@text="Learn moreabout AfterPay"]
[[17:20:47]] [SUCCESS] Screenshot refreshed
[[17:20:47]] [INFO] Refreshing screenshot...
[[17:20:47]] [INFO] ShJSdXvmVL=pass
[[17:20:44]] [SUCCESS] Screenshot refreshed successfully
[[17:20:43]] [INFO] ShJSdXvmVL=running
[[17:20:43]] [INFO] Executing action 250/482: Swipe up till element xpath: "//android.widget.TextView[@text="Learn moreabout AfterPay"]" is visible
[[17:20:43]] [SUCCESS] Screenshot refreshed
[[17:20:43]] [INFO] Refreshing screenshot...
[[17:20:43]] [INFO] y5FboDiRLS=pass
[[17:20:35]] [SUCCESS] Screenshot refreshed successfully
[[17:20:34]] [INFO] y5FboDiRLS=running
[[17:20:34]] [INFO] Executing action 249/482: Tap on image: share-close.png
[[17:20:34]] [SUCCESS] Screenshot refreshed
[[17:20:34]] [INFO] Refreshing screenshot...
[[17:20:34]] [INFO] EEx673tuI0=pass
[[17:20:31]] [SUCCESS] Screenshot refreshed successfully
[[17:20:30]] [INFO] EEx673tuI0=running
[[17:20:30]] [INFO] Executing action 248/482: Check if element with text="Share" exists
[[17:20:30]] [SUCCESS] Screenshot refreshed
[[17:20:30]] [INFO] Refreshing screenshot...
[[17:20:30]] [INFO] dCqKBG3e7u=pass
[[17:20:29]] [SUCCESS] Screenshot refreshed successfully
[[17:20:28]] [INFO] dCqKBG3e7u=running
[[17:20:28]] [INFO] Executing action 247/482: Tap on element with xpath: //android.view.View[@content-desc="Product Details"]/following-sibling::android.widget.ImageView[1]
[[17:20:28]] [SUCCESS] Screenshot refreshed
[[17:20:28]] [INFO] Refreshing screenshot...
[[17:20:28]] [INFO] kAQ1yIIw3h=pass
[[17:20:27]] [SUCCESS] Screenshot refreshed successfully
[[17:20:25]] [INFO] kAQ1yIIw3h=running
[[17:20:25]] [INFO] Executing action 246/482: Tap on element with xpath: ((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1] with fallback: Coordinates (98, 308)
[[17:20:25]] [SUCCESS] Screenshot refreshed
[[17:20:25]] [INFO] Refreshing screenshot...
[[17:20:25]] [INFO] OmKfD9iBjD=pass
[[17:20:23]] [SUCCESS] Screenshot refreshed successfully
[[17:20:22]] [INFO] OmKfD9iBjD=running
[[17:20:22]] [INFO] Executing action 245/482: Wait till xpath=((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1]
[[17:20:22]] [SUCCESS] Screenshot refreshed
[[17:20:22]] [INFO] Refreshing screenshot...
[[17:20:22]] [INFO] dMl1PH9Dlc=pass
[[17:20:10]] [SUCCESS] Screenshot refreshed successfully
[[17:20:09]] [INFO] dMl1PH9Dlc=running
[[17:20:09]] [INFO] Executing action 244/482: Wait for 10 ms
[[17:20:09]] [SUCCESS] Screenshot refreshed
[[17:20:09]] [INFO] Refreshing screenshot...
[[17:20:09]] [INFO] eHLWiRoqqS=pass
[[17:20:07]] [SUCCESS] Screenshot refreshed successfully
[[17:20:06]] [INFO] eHLWiRoqqS=running
[[17:20:06]] [INFO] Executing action 243/482: Swipe from (50%, 70%) to (50%, 30%)
[[17:20:06]] [SUCCESS] Screenshot refreshed
[[17:20:06]] [INFO] Refreshing screenshot...
[[17:20:06]] [INFO] huUnpMMjVR=pass
[[17:20:04]] [SUCCESS] Screenshot refreshed successfully
[[17:20:03]] [INFO] huUnpMMjVR=running
[[17:20:03]] [INFO] Executing action 242/482: Tap on element with xpath: //android.widget.Button[@text="In stock only i... ChipsClose"]
[[17:20:03]] [SUCCESS] Screenshot refreshed
[[17:20:03]] [INFO] Refreshing screenshot...
[[17:20:03]] [INFO] XmAxcBtFI0=pass
[[17:20:01]] [SUCCESS] Screenshot refreshed successfully
[[17:19:50]] [INFO] XmAxcBtFI0=running
[[17:19:50]] [INFO] Executing action 241/482: Check if element with xpath="//android.widget.Button[@text="In stock only i... ChipsClose"]" exists
[[17:19:50]] [SUCCESS] Screenshot refreshed
[[17:19:50]] [INFO] Refreshing screenshot...
[[17:19:50]] [INFO] a50JhCx0ir=pass
[[17:19:47]] [SUCCESS] Screenshot refreshed successfully
[[17:19:46]] [INFO] a50JhCx0ir=running
[[17:19:46]] [INFO] Executing action 240/482: Tap on Text: "Show"
[[17:19:46]] [SUCCESS] Screenshot refreshed
[[17:19:46]] [INFO] Refreshing screenshot...
[[17:19:46]] [INFO] dMl1PH9Dlc=pass
[[17:19:41]] [SUCCESS] Screenshot refreshed successfully
[[17:19:40]] [INFO] dMl1PH9Dlc=running
[[17:19:40]] [INFO] Executing action 239/482: Wait for 5 ms
[[17:19:40]] [SUCCESS] Screenshot refreshed
[[17:19:40]] [INFO] Refreshing screenshot...
[[17:19:40]] [INFO] a50JhCx0ir=pass
[[17:19:37]] [SUCCESS] Screenshot refreshed successfully
[[17:19:36]] [INFO] a50JhCx0ir=running
[[17:19:36]] [INFO] Executing action 238/482: Tap on Text: "only"
[[17:19:36]] [SUCCESS] Screenshot refreshed
[[17:19:36]] [INFO] Refreshing screenshot...
[[17:19:36]] [INFO] Y1O1clhMSJ=pass
[[17:19:34]] [SUCCESS] Screenshot refreshed successfully
[[17:19:33]] [INFO] Y1O1clhMSJ=running
[[17:19:33]] [INFO] Executing action 237/482: Tap on element with xpath: //android.widget.Button[@text="Filter"]
[[17:19:33]] [SUCCESS] Screenshot refreshed
[[17:19:33]] [INFO] Refreshing screenshot...
[[17:19:33]] [INFO] lYPskZt0Ya=pass
[[17:19:08]] [SUCCESS] Screenshot refreshed successfully
[[17:19:07]] [INFO] lYPskZt0Ya=running
[[17:19:07]] [INFO] Executing action 236/482: Wait till xpath=//android.widget.Button[@text="Filter"]
[[17:19:07]] [SUCCESS] Screenshot refreshed
[[17:19:07]] [INFO] Refreshing screenshot...
[[17:19:07]] [INFO] xUbWFa8Ok2=pass
[[17:19:04]] [SUCCESS] Screenshot refreshed successfully
[[17:19:03]] [INFO] xUbWFa8Ok2=running
[[17:19:03]] [INFO] Executing action 235/482: Tap on Text: "Latest"
[[17:19:03]] [SUCCESS] Screenshot refreshed
[[17:19:03]] [INFO] Refreshing screenshot...
[[17:19:03]] [INFO] RbNtEW6N9T=pass
[[17:19:00]] [SUCCESS] Screenshot refreshed successfully
[[17:18:50]] [INFO] RbNtEW6N9T=running
[[17:18:50]] [INFO] Executing action 234/482: Tap on Text: "Toys"
[[17:18:50]] [SUCCESS] Screenshot refreshed
[[17:18:50]] [INFO] Refreshing screenshot...
[[17:18:50]] [INFO] ltDXyWvtEz=pass
[[17:18:49]] [SUCCESS] Screenshot refreshed successfully
[[17:18:48]] [INFO] ltDXyWvtEz=running
[[17:18:48]] [INFO] Executing action 233/482: Tap on element with xpath: //android.view.View[@content-desc="Search"]/preceding::android.widget.ImageView[1]
[[17:18:48]] [SUCCESS] Screenshot refreshed
[[17:18:48]] [INFO] Refreshing screenshot...
[[17:18:48]] [INFO] QPKR6jUF9O=pass
[[17:18:47]] [SUCCESS] Screenshot refreshed successfully
[[17:18:46]] [INFO] QPKR6jUF9O=running
[[17:18:46]] [INFO] Executing action 232/482: Check if element with xpath="//android.widget.ImageView[@content-desc="Scan barcode"]" exists
[[17:18:46]] [SUCCESS] Screenshot refreshed
[[17:18:46]] [INFO] Refreshing screenshot...
[[17:18:46]] [INFO] vfwUVEyq6X=pass
[[17:18:45]] [SUCCESS] Screenshot refreshed successfully
[[17:18:45]] [INFO] vfwUVEyq6X=running
[[17:18:45]] [INFO] Executing action 231/482: Check if element with xpath="//android.widget.ImageView[@content-desc="More"]" exists
[[17:18:45]] [SUCCESS] Screenshot refreshed
[[17:18:45]] [INFO] Refreshing screenshot...
[[17:18:45]] [INFO] o74txS2f4j=pass
[[17:18:36]] [SUCCESS] Screenshot refreshed successfully
[[17:18:36]] [INFO] o74txS2f4j=running
[[17:18:36]] [INFO] Executing action 230/482: Tap on image: find-products-browse.png
[[17:18:36]] [SUCCESS] Screenshot refreshed
[[17:18:36]] [INFO] Refreshing screenshot...
[[17:18:36]] [INFO] F4NGh9HrLw=pass
[[17:18:35]] [SUCCESS] Screenshot refreshed successfully
[[17:18:34]] [INFO] F4NGh9HrLw=running
[[17:18:34]] [INFO] Executing action 229/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 2 of 5")]
[[17:18:34]] [SUCCESS] Screenshot refreshed
[[17:18:34]] [INFO] Refreshing screenshot...
[[17:18:34]] [INFO] H9fy9qcFbZ=pass
[[17:18:32]] [SUCCESS] Screenshot refreshed successfully
[[17:18:30]] [INFO] H9fy9qcFbZ=running
[[17:18:30]] [INFO] Executing action 228/482: Launch app: au.com.kmart
[[17:18:30]] [SUCCESS] Screenshot refreshed
[[17:18:30]] [INFO] Refreshing screenshot...
[[17:18:30]] [SUCCESS] Screenshot refreshed
[[17:18:30]] [INFO] Refreshing screenshot...
[[17:18:27]] [SUCCESS] Screenshot refreshed successfully
[[17:18:26]] [INFO] Executing Multi Step action step 6/6: Tap on element with accessibility_id: txtOnePassOnboardingSkipForNow
[[17:18:26]] [SUCCESS] Screenshot refreshed
[[17:18:26]] [INFO] Refreshing screenshot...
[[17:18:25]] [SUCCESS] Screenshot refreshed successfully
[[17:18:24]] [INFO] Executing Multi Step action step 5/6: Tap on element with accessibility_id: btnMayBeLater
[[17:18:24]] [SUCCESS] Screenshot refreshed
[[17:18:24]] [INFO] Refreshing screenshot...
[[17:18:23]] [SUCCESS] Screenshot refreshed successfully
[[17:18:21]] [INFO] Executing Multi Step action step 4/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[17:18:21]] [SUCCESS] Screenshot refreshed
[[17:18:21]] [INFO] Refreshing screenshot...
[[17:18:17]] [SUCCESS] Screenshot refreshed successfully
[[17:18:17]] [INFO] Executing Multi Step action step 3/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[17:18:17]] [SUCCESS] Screenshot refreshed
[[17:18:17]] [INFO] Refreshing screenshot...
[[17:18:16]] [SUCCESS] Screenshot refreshed successfully
[[17:18:14]] [INFO] Executing Multi Step action step 2/6: Launch app: au.com.kmart
[[17:18:14]] [SUCCESS] Screenshot refreshed
[[17:18:14]] [INFO] Refreshing screenshot...
[[17:18:08]] [INFO] Executing Multi Step action step 1/6: Android Function: clear_app - Package: au.com.kmart
[[17:18:08]] [INFO] Loaded 6 steps from test case: Onboarding-Start-AU
[[17:18:08]] [INFO] Loading steps for Multi Step action: Onboarding-Start-AU
[[17:18:08]] [INFO] H9fy9qcFbZ=running
[[17:18:08]] [INFO] Executing action 227/482: Execute Test Case: Onboarding-Start-AU (6 steps)
[[17:18:06]] [INFO] === RETRYING TEST CASE: Browse__PDP_AU_ANDROID_20250709040606.json (Attempt 2 of 3) ===
[[17:18:06]] [INFO] 2p13JoJbbA=fail
[[17:18:06]] [ERROR] Action 292 failed: Element not found or not tappable after all retry strategies: xpath='//android.widget.Button[contains(@text,"Remove")]'
[[17:17:14]] [SUCCESS] Screenshot refreshed successfully
[[17:17:13]] [INFO] 2p13JoJbbA=running
[[17:17:13]] [INFO] Executing action 292/482: Tap on element with xpath: //android.widget.Button[contains(@text,"Remove")]
[[17:17:13]] [SUCCESS] Screenshot refreshed
[[17:17:13]] [INFO] Refreshing screenshot...
[[17:17:13]] [INFO] rbzkUOQMtf=pass
[[17:17:09]] [SUCCESS] Screenshot refreshed successfully
[[17:17:08]] [INFO] rbzkUOQMtf=running
[[17:17:08]] [INFO] Executing action 291/482: Wait for 4 ms
[[17:17:08]] [SUCCESS] Screenshot refreshed
[[17:17:08]] [INFO] Refreshing screenshot...
[[17:17:08]] [INFO] 2p13JoJbbA=pass
[[17:17:06]] [SUCCESS] Screenshot refreshed successfully
[[17:17:05]] [INFO] 2p13JoJbbA=running
[[17:17:05]] [INFO] Executing action 290/482: Tap on element with xpath: //android.widget.Button[contains(@text,"Remove")]
[[17:17:05]] [SUCCESS] Screenshot refreshed
[[17:17:05]] [INFO] Refreshing screenshot...
[[17:17:05]] [INFO] eHLWiRoqqS=pass
[[17:17:03]] [SUCCESS] Screenshot refreshed successfully
[[17:17:02]] [INFO] eHLWiRoqqS=running
[[17:17:02]] [INFO] Executing action 289/482: Swipe from (50%, 70%) to (50%, 50%)
[[17:17:02]] [SUCCESS] Screenshot refreshed
[[17:17:02]] [INFO] Refreshing screenshot...
[[17:17:02]] [INFO] VYgfSNx3GG=pass
[[17:17:00]] [SUCCESS] Screenshot refreshed successfully
[[17:17:00]] [INFO] VYgfSNx3GG=running
[[17:17:00]] [INFO] Executing action 288/482: Tap on element with xpath: //android.view.View[@text="Delivery"]
[[17:17:00]] [SUCCESS] Screenshot refreshed
[[17:17:00]] [INFO] Refreshing screenshot...
[[17:17:00]] [INFO] XoMyLp2unA=pass
[[17:16:49]] [SUCCESS] Screenshot refreshed successfully
[[17:16:49]] [INFO] XoMyLp2unA=running
[[17:16:49]] [INFO] Executing action 287/482: Wait till xpath=//android.view.View[@text="Delivery"]
[[17:16:49]] [SUCCESS] Screenshot refreshed
[[17:16:49]] [INFO] Refreshing screenshot...
[[17:16:49]] [INFO] cTLBS0O1ot=pass
[[17:16:47]] [SUCCESS] Screenshot refreshed successfully
[[17:16:46]] [INFO] cTLBS0O1ot=running
[[17:16:46]] [INFO] Executing action 286/482: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[17:16:46]] [SUCCESS] Screenshot refreshed
[[17:16:46]] [INFO] Refreshing screenshot...
[[17:16:46]] [INFO] F4NGh9HrLw=pass
[[17:16:44]] [SUCCESS] Screenshot refreshed successfully
[[17:16:43]] [INFO] F4NGh9HrLw=running
[[17:16:43]] [INFO] Executing action 285/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[17:16:43]] [SUCCESS] Screenshot refreshed
[[17:16:43]] [INFO] Refreshing screenshot...
[[17:16:43]] [INFO] JRheDTvpJf=pass
[[17:16:39]] [SUCCESS] Screenshot refreshed successfully
[[17:16:38]] [INFO] JRheDTvpJf=running
[[17:16:38]] [INFO] Executing action 284/482: Tap on Text: "Add"
[[17:16:38]] [SUCCESS] Screenshot refreshed
[[17:16:38]] [INFO] Refreshing screenshot...
[[17:16:38]] [INFO] eHLWiRoqqS=pass
[[17:16:35]] [SUCCESS] Screenshot refreshed successfully
[[17:16:34]] [INFO] eHLWiRoqqS=running
[[17:16:34]] [INFO] Executing action 283/482: Swipe from (50%, 70%) to (50%, 30%)
[[17:16:34]] [SUCCESS] Screenshot refreshed
[[17:16:34]] [INFO] Refreshing screenshot...
[[17:16:34]] [INFO] kwF3J9NbRc=pass
[[17:16:08]] [SUCCESS] Screenshot refreshed successfully
[[17:16:07]] [INFO] kwF3J9NbRc=running
[[17:16:07]] [INFO] Executing action 282/482: Wait till text appears: "SKU"
[[17:16:07]] [SUCCESS] Screenshot refreshed
[[17:16:07]] [INFO] Refreshing screenshot...
[[17:16:07]] [INFO] kAQ1yIIw3h=pass
[[17:16:05]] [SUCCESS] Screenshot refreshed successfully
[[17:16:04]] [INFO] kAQ1yIIw3h=running
[[17:16:04]] [INFO] Executing action 281/482: Tap on element with xpath: ((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1] with fallback: Coordinates (98, 308)
[[17:16:04]] [SUCCESS] Screenshot refreshed
[[17:16:04]] [INFO] Refreshing screenshot...
[[17:16:04]] [INFO] lYPskZt0Ya=pass
[[17:15:33]] [SUCCESS] Screenshot refreshed successfully
[[17:15:32]] [INFO] lYPskZt0Ya=running
[[17:15:32]] [INFO] Executing action 280/482: Wait till xpath=//android.widget.Button[@text="Filter"]
[[17:15:32]] [SUCCESS] Screenshot refreshed
[[17:15:32]] [INFO] Refreshing screenshot...
[[17:15:32]] [INFO] oNKCP9pqiF=pass
[[17:15:31]] [SUCCESS] Screenshot refreshed successfully
[[17:15:11]] [INFO] oNKCP9pqiF=running
[[17:15:11]] [INFO] Executing action 279/482: Android Function: send_key_event - Key Event: ENTER
[[17:15:11]] [SUCCESS] Screenshot refreshed
[[17:15:11]] [INFO] Refreshing screenshot...
[[17:15:11]] [INFO] JRheDTvpJf=pass
[[17:15:09]] [SUCCESS] Screenshot refreshed successfully
[[17:15:08]] [INFO] JRheDTvpJf=running
[[17:15:08]] [INFO] Executing action 278/482: Input text: "mat"
[[17:15:08]] [SUCCESS] Screenshot refreshed
[[17:15:08]] [INFO] Refreshing screenshot...
[[17:15:08]] [INFO] o1gHFWHXTL=pass
[[17:15:05]] [SUCCESS] Screenshot refreshed successfully
[[17:15:04]] [INFO] o1gHFWHXTL=running
[[17:15:04]] [INFO] Executing action 277/482: Tap on Text: "Find"
[[17:15:04]] [SUCCESS] Screenshot refreshed
[[17:15:04]] [INFO] Refreshing screenshot...
[[17:15:04]] [INFO] o74txS2f4j=pass
[[17:14:31]] [SUCCESS] Screenshot refreshed successfully
[[17:14:30]] [INFO] o74txS2f4j=running
[[17:14:30]] [INFO] Executing action 276/482: Tap on image: find-products-browse.png
[[17:14:30]] [SUCCESS] Screenshot refreshed
[[17:14:30]] [INFO] Refreshing screenshot...
[[17:14:30]] [INFO] F4NGh9HrLw=pass
[[17:14:28]] [SUCCESS] Screenshot refreshed successfully
[[17:14:28]] [INFO] F4NGh9HrLw=running
[[17:14:28]] [INFO] Executing action 275/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 2 of 5")]
[[17:14:28]] [SUCCESS] Screenshot refreshed
[[17:14:28]] [INFO] Refreshing screenshot...
[[17:14:28]] [INFO] JRheDTvpJf=pass
[[17:14:24]] [SUCCESS] Screenshot refreshed successfully
[[17:14:23]] [INFO] JRheDTvpJf=running
[[17:14:23]] [INFO] Executing action 274/482: Tap on Text: "Add"
[[17:14:23]] [SUCCESS] Screenshot refreshed
[[17:14:23]] [INFO] Refreshing screenshot...
[[17:14:23]] [INFO] eHLWiRoqqS=pass
[[17:14:21]] [SUCCESS] Screenshot refreshed successfully
[[17:14:20]] [INFO] eHLWiRoqqS=running
[[17:14:20]] [INFO] Executing action 273/482: Swipe from (50%, 70%) to (50%, 30%)
[[17:14:20]] [SUCCESS] Screenshot refreshed
[[17:14:20]] [INFO] Refreshing screenshot...
[[17:14:20]] [INFO] kAQ1yIIw3h=pass
[[17:14:18]] [SUCCESS] Screenshot refreshed successfully
[[17:14:17]] [INFO] kAQ1yIIw3h=running
[[17:14:17]] [INFO] Executing action 272/482: Tap on element with xpath: ((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1] with fallback: Coordinates (98, 308)
[[17:14:17]] [SUCCESS] Screenshot refreshed
[[17:14:17]] [INFO] Refreshing screenshot...
[[17:14:17]] [INFO] lYPskZt0Ya=pass
[[17:14:10]] [SUCCESS] Screenshot refreshed successfully
[[17:14:10]] [INFO] lYPskZt0Ya=running
[[17:14:10]] [INFO] Executing action 271/482: Wait till xpath=//android.widget.Button[@text="Filter"]
[[17:14:10]] [SUCCESS] Screenshot refreshed
[[17:14:10]] [INFO] Refreshing screenshot...
[[17:14:10]] [INFO] YaIypAHEOz=pass
[[17:14:09]] [SUCCESS] Screenshot refreshed successfully
[[17:14:08]] [INFO] YaIypAHEOz=running
[[17:14:08]] [INFO] Executing action 270/482: Android Function: send_key_event - Key Event: ENTER
[[17:14:08]] [SUCCESS] Screenshot refreshed
[[17:14:08]] [INFO] Refreshing screenshot...
[[17:14:08]] [INFO] JRheDTvpJf=pass
[[17:14:07]] [SUCCESS] Screenshot refreshed successfully
[[17:14:06]] [INFO] JRheDTvpJf=running
[[17:14:06]] [INFO] Executing action 269/482: Input text: "Kids Toys"
[[17:14:06]] [SUCCESS] Screenshot refreshed
[[17:14:06]] [INFO] Refreshing screenshot...
[[17:14:06]] [INFO] o1gHFWHXTL=pass
[[17:14:03]] [SUCCESS] Screenshot refreshed successfully
[[17:14:01]] [INFO] o1gHFWHXTL=running
[[17:14:01]] [INFO] Executing action 268/482: Tap on Text: "Find"
[[17:14:01]] [SUCCESS] Screenshot refreshed
[[17:14:01]] [INFO] Refreshing screenshot...
[[17:14:01]] [INFO] o74txS2f4j=pass
[[17:13:53]] [SUCCESS] Screenshot refreshed successfully
[[17:13:53]] [INFO] o74txS2f4j=running
[[17:13:53]] [INFO] Executing action 267/482: Tap on image: find-products-browse.png
[[17:13:53]] [SUCCESS] Screenshot refreshed
[[17:13:53]] [INFO] Refreshing screenshot...
[[17:13:53]] [INFO] F4NGh9HrLw=pass
[[17:13:48]] [SUCCESS] Screenshot refreshed successfully
[[17:13:47]] [INFO] F4NGh9HrLw=running
[[17:13:47]] [INFO] Executing action 266/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 2 of 5")]
[[17:13:47]] [SUCCESS] Screenshot refreshed
[[17:13:47]] [INFO] Refreshing screenshot...
[[17:13:47]] [INFO] XPEr3w6Zof=pass
[[17:13:46]] [SUCCESS] Screenshot refreshed successfully
[[17:13:44]] [INFO] XPEr3w6Zof=running
[[17:13:44]] [INFO] Executing action 265/482: Launch app: au.com.kmart
[[17:13:44]] [SUCCESS] Screenshot refreshed
[[17:13:44]] [INFO] Refreshing screenshot...
[[17:13:44]] [INFO] XPEr3w6Zof=pass
[[17:13:43]] [SUCCESS] Screenshot refreshed successfully
[[17:13:42]] [INFO] XPEr3w6Zof=running
[[17:13:42]] [INFO] Executing action 264/482: Terminate app: au.com.kmart
[[17:13:42]] [SUCCESS] Screenshot refreshed
[[17:13:42]] [INFO] Refreshing screenshot...
[[17:13:42]] [INFO] Et3kvnFdxh=pass
[[17:13:41]] [SUCCESS] Screenshot refreshed successfully
[[17:13:40]] [INFO] Et3kvnFdxh=running
[[17:13:40]] [INFO] Executing action 263/482: Tap on element with xpath: //android.view.View[@content-desc="stnPostCodeSelectionScreenBodyWidget"]/android.view.View[1]/android.widget.ImageView
[[17:13:40]] [SUCCESS] Screenshot refreshed
[[17:13:40]] [INFO] Refreshing screenshot...
[[17:13:40]] [INFO] GWoppouz1l=pass
[[17:13:39]] [SUCCESS] Screenshot refreshed successfully
[[17:13:38]] [INFO] GWoppouz1l=running
[[17:13:38]] [INFO] Executing action 262/482: Check if element with xpath="//android.view.View[@content-desc="txtLocationTitle"]" exists
[[17:13:38]] [SUCCESS] Screenshot refreshed
[[17:13:38]] [INFO] Refreshing screenshot...
[[17:13:38]] [INFO] B6GDXWAmWp=pass
[[17:13:37]] [SUCCESS] Screenshot refreshed successfully
[[17:13:36]] [INFO] B6GDXWAmWp=running
[[17:13:36]] [INFO] Executing action 261/482: Tap on element with xpath: //android.widget.TextView[@text="Shop at"]/following-sibling::android.widget.Button
[[17:13:36]] [SUCCESS] Screenshot refreshed
[[17:13:36]] [INFO] Refreshing screenshot...
[[17:13:36]] [INFO] eHLWiRoqqS=pass
[[17:13:33]] [SUCCESS] Screenshot refreshed successfully
[[17:13:33]] [INFO] eHLWiRoqqS=running
[[17:13:33]] [INFO] Executing action 260/482: Swipe from (50%, 70%) to (50%, 40%)
[[17:13:33]] [SUCCESS] Screenshot refreshed
[[17:13:33]] [INFO] Refreshing screenshot...
[[17:13:33]] [INFO] mtYqeDttRc=pass
[[17:13:31]] [SUCCESS] Screenshot refreshed successfully
[[17:13:31]] [INFO] mtYqeDttRc=running
[[17:13:31]] [INFO] Executing action 259/482: Tap on element with xpath: //android.widget.Button[@resource-id="close-btn"]
[[17:13:31]] [SUCCESS] Screenshot refreshed
[[17:13:31]] [INFO] Refreshing screenshot...
[[17:13:31]] [INFO] P4b2BITpCf=pass
[[17:13:28]] [SUCCESS] Screenshot refreshed successfully
[[17:13:27]] [INFO] P4b2BITpCf=running
[[17:13:27]] [INFO] Executing action 258/482: Check if element with text="interest-" exists
[[17:13:27]] [SUCCESS] Screenshot refreshed
[[17:13:27]] [INFO] Refreshing screenshot...
[[17:13:27]] [INFO] q6cKxgMAIn=pass
[[17:13:25]] [SUCCESS] Screenshot refreshed successfully
[[17:13:24]] [INFO] q6cKxgMAIn=running
[[17:13:24]] [INFO] Executing action 257/482: Tap on element with xpath: //android.widget.Button[@text="Learn more about PayPal Pay in 4"]
[[17:13:24]] [SUCCESS] Screenshot refreshed
[[17:13:24]] [INFO] Refreshing screenshot...
[[17:13:24]] [INFO] Et3kvnFdxh=pass
[[17:13:23]] [SUCCESS] Screenshot refreshed successfully
[[17:13:23]] [INFO] Et3kvnFdxh=running
[[17:13:23]] [INFO] Executing action 256/482: Android Function: send_key_event - Key Event: BACK
[[17:13:23]] [SUCCESS] Screenshot refreshed
[[17:13:23]] [INFO] Refreshing screenshot...
[[17:13:23]] [INFO] P4b2BITpCf=pass
[[17:13:17]] [SUCCESS] Screenshot refreshed successfully
[[17:13:17]] [INFO] P4b2BITpCf=running
[[17:13:17]] [INFO] Executing action 255/482: Check if element with text="What" exists
[[17:13:17]] [SUCCESS] Screenshot refreshed
[[17:13:17]] [INFO] Refreshing screenshot...
[[17:13:17]] [INFO] inrxgdWzXr=pass
[[17:13:15]] [SUCCESS] Screenshot refreshed successfully
[[17:13:14]] [INFO] inrxgdWzXr=running
[[17:13:14]] [INFO] Executing action 254/482: Tap on element with xpath: //android.widget.TextView[@text="Learn moreabout Zip"]
[[17:13:14]] [SUCCESS] Screenshot refreshed
[[17:13:14]] [INFO] Refreshing screenshot...
[[17:13:14]] [INFO] Et3kvnFdxh=pass
[[17:13:13]] [SUCCESS] Screenshot refreshed successfully
[[17:13:12]] [INFO] Et3kvnFdxh=running
[[17:13:12]] [INFO] Executing action 253/482: Android Function: send_key_event - Key Event: BACK
[[17:13:12]] [SUCCESS] Screenshot refreshed
[[17:13:12]] [INFO] Refreshing screenshot...
[[17:13:12]] [INFO] DhWa2PCBXE=pass
[[17:13:08]] [SUCCESS] Screenshot refreshed successfully
[[17:13:07]] [INFO] DhWa2PCBXE=running
[[17:13:07]] [INFO] Executing action 252/482: Check if element with text="Apply" exists
[[17:13:07]] [SUCCESS] Screenshot refreshed
[[17:13:07]] [INFO] Refreshing screenshot...
[[17:13:07]] [INFO] pk2DLZFBmx=pass
[[17:13:06]] [SUCCESS] Screenshot refreshed successfully
[[17:13:05]] [INFO] pk2DLZFBmx=running
[[17:13:05]] [INFO] Executing action 251/482: Tap on element with xpath: //android.widget.TextView[@text="Learn moreabout AfterPay"]
[[17:13:05]] [SUCCESS] Screenshot refreshed
[[17:13:05]] [INFO] Refreshing screenshot...
[[17:13:05]] [INFO] ShJSdXvmVL=pass
[[17:13:02]] [SUCCESS] Screenshot refreshed successfully
[[17:13:01]] [INFO] ShJSdXvmVL=running
[[17:13:01]] [INFO] Executing action 250/482: Swipe up till element xpath: "//android.widget.TextView[@text="Learn moreabout AfterPay"]" is visible
[[17:13:01]] [SUCCESS] Screenshot refreshed
[[17:13:01]] [INFO] Refreshing screenshot...
[[17:13:01]] [INFO] y5FboDiRLS=pass
[[17:12:40]] [SUCCESS] Screenshot refreshed successfully
[[17:12:40]] [INFO] y5FboDiRLS=running
[[17:12:40]] [INFO] Executing action 249/482: Tap on image: share-close.png
[[17:12:40]] [SUCCESS] Screenshot refreshed
[[17:12:40]] [INFO] Refreshing screenshot...
[[17:12:40]] [INFO] EEx673tuI0=pass
[[17:12:37]] [SUCCESS] Screenshot refreshed successfully
[[17:12:36]] [INFO] EEx673tuI0=running
[[17:12:36]] [INFO] Executing action 248/482: Check if element with text="Share" exists
[[17:12:36]] [SUCCESS] Screenshot refreshed
[[17:12:36]] [INFO] Refreshing screenshot...
[[17:12:36]] [INFO] dCqKBG3e7u=pass
[[17:12:34]] [SUCCESS] Screenshot refreshed successfully
[[17:12:34]] [INFO] dCqKBG3e7u=running
[[17:12:34]] [INFO] Executing action 247/482: Tap on element with xpath: //android.view.View[@content-desc="Product Details"]/following-sibling::android.widget.ImageView[1]
[[17:12:34]] [SUCCESS] Screenshot refreshed
[[17:12:34]] [INFO] Refreshing screenshot...
[[17:12:34]] [INFO] kAQ1yIIw3h=pass
[[17:12:32]] [SUCCESS] Screenshot refreshed successfully
[[17:12:31]] [INFO] kAQ1yIIw3h=running
[[17:12:31]] [INFO] Executing action 246/482: Tap on element with xpath: ((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1] with fallback: Coordinates (98, 308)
[[17:12:31]] [SUCCESS] Screenshot refreshed
[[17:12:31]] [INFO] Refreshing screenshot...
[[17:12:31]] [INFO] OmKfD9iBjD=pass
[[17:12:28]] [SUCCESS] Screenshot refreshed successfully
[[17:12:12]] [INFO] OmKfD9iBjD=running
[[17:12:12]] [INFO] Executing action 245/482: Wait till xpath=((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1]
[[17:12:12]] [SUCCESS] Screenshot refreshed
[[17:12:12]] [INFO] Refreshing screenshot...
[[17:12:12]] [INFO] dMl1PH9Dlc=pass
[[17:12:02]] [SUCCESS] Screenshot refreshed successfully
[[17:12:01]] [INFO] dMl1PH9Dlc=running
[[17:12:01]] [INFO] Executing action 244/482: Wait for 10 ms
[[17:12:01]] [SUCCESS] Screenshot refreshed
[[17:12:01]] [INFO] Refreshing screenshot...
[[17:12:01]] [INFO] eHLWiRoqqS=pass
[[17:11:58]] [SUCCESS] Screenshot refreshed successfully
[[17:11:58]] [INFO] eHLWiRoqqS=running
[[17:11:58]] [INFO] Executing action 243/482: Swipe from (50%, 70%) to (50%, 30%)
[[17:11:58]] [SUCCESS] Screenshot refreshed
[[17:11:58]] [INFO] Refreshing screenshot...
[[17:11:58]] [INFO] huUnpMMjVR=pass
[[17:11:56]] [SUCCESS] Screenshot refreshed successfully
[[17:11:55]] [INFO] huUnpMMjVR=running
[[17:11:55]] [INFO] Executing action 242/482: Tap on element with xpath: //android.widget.Button[@text="In stock only i... ChipsClose"]
[[17:11:55]] [SUCCESS] Screenshot refreshed
[[17:11:55]] [INFO] Refreshing screenshot...
[[17:11:55]] [INFO] XmAxcBtFI0=pass
[[17:11:53]] [SUCCESS] Screenshot refreshed successfully
[[17:11:52]] [INFO] XmAxcBtFI0=running
[[17:11:52]] [INFO] Executing action 241/482: Check if element with xpath="//android.widget.Button[@text="In stock only i... ChipsClose"]" exists
[[17:11:52]] [SUCCESS] Screenshot refreshed
[[17:11:52]] [INFO] Refreshing screenshot...
[[17:11:52]] [INFO] a50JhCx0ir=pass
[[17:11:49]] [SUCCESS] Screenshot refreshed successfully
[[17:11:48]] [INFO] a50JhCx0ir=running
[[17:11:48]] [INFO] Executing action 240/482: Tap on Text: "Show"
[[17:11:48]] [SUCCESS] Screenshot refreshed
[[17:11:48]] [INFO] Refreshing screenshot...
[[17:11:48]] [INFO] dMl1PH9Dlc=pass
[[17:11:42]] [SUCCESS] Screenshot refreshed successfully
[[17:11:41]] [INFO] dMl1PH9Dlc=running
[[17:11:41]] [INFO] Executing action 239/482: Wait for 5 ms
[[17:11:41]] [SUCCESS] Screenshot refreshed
[[17:11:41]] [INFO] Refreshing screenshot...
[[17:11:41]] [INFO] a50JhCx0ir=pass
[[17:11:38]] [SUCCESS] Screenshot refreshed successfully
[[17:11:37]] [INFO] a50JhCx0ir=running
[[17:11:37]] [INFO] Executing action 238/482: Tap on Text: "only"
[[17:11:37]] [SUCCESS] Screenshot refreshed
[[17:11:37]] [INFO] Refreshing screenshot...
[[17:11:37]] [INFO] Y1O1clhMSJ=pass
[[17:11:35]] [SUCCESS] Screenshot refreshed successfully
[[17:11:34]] [INFO] Y1O1clhMSJ=running
[[17:11:34]] [INFO] Executing action 237/482: Tap on element with xpath: //android.widget.Button[@text="Filter"]
[[17:11:34]] [SUCCESS] Screenshot refreshed
[[17:11:34]] [INFO] Refreshing screenshot...
[[17:11:34]] [INFO] lYPskZt0Ya=pass
[[17:11:07]] [SUCCESS] Screenshot refreshed successfully
[[17:11:06]] [INFO] lYPskZt0Ya=running
[[17:11:06]] [INFO] Executing action 236/482: Wait till xpath=//android.widget.Button[@text="Filter"]
[[17:11:06]] [SUCCESS] Screenshot refreshed
[[17:11:06]] [INFO] Refreshing screenshot...
[[17:11:06]] [INFO] xUbWFa8Ok2=pass
[[17:11:03]] [SUCCESS] Screenshot refreshed successfully
[[17:11:02]] [INFO] xUbWFa8Ok2=running
[[17:11:02]] [INFO] Executing action 235/482: Tap on Text: "Latest"
[[17:11:02]] [SUCCESS] Screenshot refreshed
[[17:11:02]] [INFO] Refreshing screenshot...
[[17:11:02]] [INFO] RbNtEW6N9T=pass
[[17:10:40]] [SUCCESS] Screenshot refreshed successfully
[[17:10:39]] [INFO] RbNtEW6N9T=running
[[17:10:39]] [INFO] Executing action 234/482: Tap on Text: "Toys"
[[17:10:39]] [SUCCESS] Screenshot refreshed
[[17:10:39]] [INFO] Refreshing screenshot...
[[17:10:39]] [INFO] ltDXyWvtEz=pass
[[17:10:37]] [SUCCESS] Screenshot refreshed successfully
[[17:10:37]] [INFO] ltDXyWvtEz=running
[[17:10:37]] [INFO] Executing action 233/482: Tap on element with xpath: //android.view.View[@content-desc="Search"]/preceding::android.widget.ImageView[1]
[[17:10:37]] [SUCCESS] Screenshot refreshed
[[17:10:37]] [INFO] Refreshing screenshot...
[[17:10:37]] [INFO] QPKR6jUF9O=pass
[[17:10:36]] [SUCCESS] Screenshot refreshed successfully
[[17:10:35]] [INFO] QPKR6jUF9O=running
[[17:10:35]] [INFO] Executing action 232/482: Check if element with xpath="//android.widget.ImageView[@content-desc="Scan barcode"]" exists
[[17:10:35]] [SUCCESS] Screenshot refreshed
[[17:10:35]] [INFO] Refreshing screenshot...
[[17:10:35]] [INFO] vfwUVEyq6X=pass
[[17:10:34]] [SUCCESS] Screenshot refreshed successfully
[[17:10:33]] [INFO] vfwUVEyq6X=running
[[17:10:33]] [INFO] Executing action 231/482: Check if element with xpath="//android.widget.ImageView[@content-desc="More"]" exists
[[17:10:33]] [SUCCESS] Screenshot refreshed
[[17:10:33]] [INFO] Refreshing screenshot...
[[17:10:33]] [INFO] o74txS2f4j=pass
[[17:10:25]] [SUCCESS] Screenshot refreshed successfully
[[17:10:25]] [INFO] o74txS2f4j=running
[[17:10:25]] [INFO] Executing action 230/482: Tap on image: find-products-browse.png
[[17:10:25]] [SUCCESS] Screenshot refreshed
[[17:10:25]] [INFO] Refreshing screenshot...
[[17:10:25]] [INFO] F4NGh9HrLw=pass
[[17:10:23]] [SUCCESS] Screenshot refreshed successfully
[[17:10:22]] [INFO] F4NGh9HrLw=running
[[17:10:22]] [INFO] Executing action 229/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 2 of 5")]
[[17:10:22]] [SUCCESS] Screenshot refreshed
[[17:10:22]] [INFO] Refreshing screenshot...
[[17:10:22]] [INFO] H9fy9qcFbZ=pass
[[17:10:21]] [SUCCESS] Screenshot refreshed successfully
[[17:10:20]] [INFO] H9fy9qcFbZ=running
[[17:10:20]] [INFO] Executing action 228/482: Launch app: au.com.kmart
[[17:10:20]] [SUCCESS] Screenshot refreshed
[[17:10:20]] [INFO] Refreshing screenshot...
[[17:10:20]] [SUCCESS] Screenshot refreshed
[[17:10:20]] [INFO] Refreshing screenshot...
[[17:10:16]] [SUCCESS] Screenshot refreshed successfully
[[17:10:15]] [INFO] Executing Multi Step action step 6/6: Tap on element with accessibility_id: txtOnePassOnboardingSkipForNow
[[17:10:15]] [SUCCESS] Screenshot refreshed
[[17:10:15]] [INFO] Refreshing screenshot...
[[17:10:14]] [SUCCESS] Screenshot refreshed successfully
[[17:10:14]] [INFO] Executing Multi Step action step 5/6: Tap on element with accessibility_id: btnMayBeLater
[[17:10:14]] [SUCCESS] Screenshot refreshed
[[17:10:14]] [INFO] Refreshing screenshot...
[[17:10:12]] [SUCCESS] Screenshot refreshed successfully
[[17:10:11]] [INFO] Executing Multi Step action step 4/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[17:10:11]] [SUCCESS] Screenshot refreshed
[[17:10:11]] [INFO] Refreshing screenshot...
[[17:10:07]] [SUCCESS] Screenshot refreshed successfully
[[17:10:06]] [INFO] Executing Multi Step action step 3/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[17:10:06]] [SUCCESS] Screenshot refreshed
[[17:10:06]] [INFO] Refreshing screenshot...
[[17:10:05]] [SUCCESS] Screenshot refreshed successfully
[[17:10:03]] [INFO] Executing Multi Step action step 2/6: Launch app: au.com.kmart
[[17:10:03]] [SUCCESS] Screenshot refreshed
[[17:10:03]] [INFO] Refreshing screenshot...
[[17:10:00]] [SUCCESS] Screenshot refreshed successfully
[[17:09:59]] [INFO] Executing Multi Step action step 1/6: Android Function: clear_app - Package: au.com.kmart
[[17:09:59]] [INFO] Loaded 6 steps from test case: Onboarding-Start-AU
[[17:09:59]] [INFO] Loading steps for Multi Step action: Onboarding-Start-AU
[[17:09:59]] [INFO] H9fy9qcFbZ=running
[[17:09:59]] [INFO] Executing action 227/482: Execute Test Case: Onboarding-Start-AU (6 steps)
[[17:09:59]] [SUCCESS] Screenshot refreshed
[[17:09:59]] [INFO] Refreshing screenshot...
[[17:09:59]] [INFO] OyUowAaBzD=pass
[[17:09:58]] [SUCCESS] Screenshot refreshed successfully
[[17:09:57]] [INFO] OyUowAaBzD=running
[[17:09:57]] [INFO] Executing action 226/482: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[17:09:57]] [SUCCESS] Screenshot refreshed
[[17:09:57]] [INFO] Refreshing screenshot...
[[17:09:56]] [SUCCESS] Screenshot refreshed successfully
[[17:09:55]] [INFO] Executing action 225/482: Swipe from (50%, 70%) to (50%, 30%)
[[17:09:55]] [SUCCESS] Screenshot refreshed
[[17:09:55]] [INFO] Refreshing screenshot...
[[17:09:55]] [INFO] F1olhgKhUt=pass
[[17:09:54]] [SUCCESS] Screenshot refreshed successfully
[[17:09:54]] [INFO] F1olhgKhUt=running
[[17:09:54]] [INFO] Executing action 224/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[17:09:54]] [SUCCESS] Screenshot refreshed
[[17:09:54]] [INFO] Refreshing screenshot...
[[17:09:54]] [INFO] yhmzeynQyu=pass
[[17:09:51]] [SUCCESS] Screenshot refreshed successfully
[[17:09:50]] [INFO] yhmzeynQyu=running
[[17:09:50]] [INFO] Executing action 223/482: Tap on Text: "Remove"
[[17:09:50]] [SUCCESS] Screenshot refreshed
[[17:09:50]] [INFO] Refreshing screenshot...
[[17:09:49]] [SUCCESS] Screenshot refreshed successfully
[[17:09:48]] [INFO] Executing action 222/482: Action: ifThenSteps
[[17:09:48]] [SUCCESS] Screenshot refreshed
[[17:09:48]] [INFO] Refreshing screenshot...
[[17:09:48]] [INFO] yhmzeynQyu=pass
[[17:09:45]] [SUCCESS] Screenshot refreshed successfully
[[17:09:44]] [INFO] yhmzeynQyu=running
[[17:09:44]] [INFO] Executing action 221/482: Tap on Text: "Remove"
[[17:09:44]] [SUCCESS] Screenshot refreshed
[[17:09:44]] [INFO] Refreshing screenshot...
[[17:09:42]] [SUCCESS] Screenshot refreshed successfully
[[17:09:41]] [INFO] Executing action 220/482: Action: ifThenSteps
[[17:09:41]] [SUCCESS] Screenshot refreshed
[[17:09:41]] [INFO] Refreshing screenshot...
[[17:09:41]] [INFO] F1olhgKhUt=pass
[[17:09:40]] [SUCCESS] Screenshot refreshed successfully
[[17:09:39]] [INFO] F1olhgKhUt=running
[[17:09:39]] [INFO] Executing action 219/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 3 of 5")]
[[17:09:39]] [SUCCESS] Screenshot refreshed
[[17:09:39]] [INFO] Refreshing screenshot...
[[17:09:39]] [INFO] lWIRxRm6HE=pass
[[17:09:36]] [SUCCESS] Screenshot refreshed successfully
[[17:09:36]] [INFO] lWIRxRm6HE=running
[[17:09:36]] [INFO] Executing action 218/482: Tap on element with xpath: //android.widget.TextView[@text="Continue shopping"]
[[17:09:36]] [SUCCESS] Screenshot refreshed
[[17:09:36]] [INFO] Refreshing screenshot...
[[17:09:36]] [INFO] uOt2cFGhGr=pass
[[17:09:34]] [SUCCESS] Screenshot refreshed successfully
[[17:09:34]] [INFO] uOt2cFGhGr=running
[[17:09:34]] [INFO] Executing action 217/482: Tap on element with xpath: //android.widget.Button[@text="Move to Wishlist"]
[[17:09:34]] [SUCCESS] Screenshot refreshed
[[17:09:34]] [INFO] Refreshing screenshot...
[[17:09:27]] [SUCCESS] Screenshot refreshed successfully
[[17:09:26]] [INFO] Executing action 216/482: Swipe up till element xpath: "//android.widget.Button[@text="Move to Wishlist"]" is visible
[[17:09:26]] [SUCCESS] Screenshot refreshed
[[17:09:26]] [INFO] Refreshing screenshot...
[[17:09:20]] [SUCCESS] Screenshot refreshed successfully
[[17:09:20]] [INFO] Executing action 215/482: Wait for 5 ms
[[17:09:20]] [SUCCESS] Screenshot refreshed
[[17:09:20]] [INFO] Refreshing screenshot...
[[17:09:20]] [INFO] bGqhW1Kciz=pass
[[17:09:19]] [SUCCESS] Screenshot refreshed successfully
[[17:09:18]] [INFO] bGqhW1Kciz=running
[[17:09:18]] [INFO] Executing action 214/482: Tap on element with xpath: //android.widget.Button[@content-desc="Checkout"]
[[17:09:18]] [SUCCESS] Screenshot refreshed
[[17:09:18]] [INFO] Refreshing screenshot...
[[17:09:12]] [SUCCESS] Screenshot refreshed successfully
[[17:09:12]] [INFO] Executing action 213/482: Wait for 5 ms
[[17:09:12]] [SUCCESS] Screenshot refreshed
[[17:09:12]] [INFO] Refreshing screenshot...
[[17:09:12]] [INFO] F1olhgKhUt=pass
[[17:09:10]] [SUCCESS] Screenshot refreshed successfully
[[17:09:10]] [INFO] F1olhgKhUt=running
[[17:09:10]] [INFO] Executing action 212/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[17:09:10]] [SUCCESS] Screenshot refreshed
[[17:09:10]] [INFO] Refreshing screenshot...
[[17:09:10]] [INFO] yhmzeynQyu=pass
[[17:09:07]] [SUCCESS] Screenshot refreshed successfully
[[17:09:06]] [INFO] yhmzeynQyu=running
[[17:09:06]] [INFO] Executing action 211/482: Tap on Text: "Remove"
[[17:09:06]] [SUCCESS] Screenshot refreshed
[[17:09:06]] [INFO] Refreshing screenshot...
[[17:09:06]] [INFO] Q0fomJIDoQ=pass
[[17:09:04]] [SUCCESS] Screenshot refreshed successfully
[[17:09:03]] [INFO] Q0fomJIDoQ=running
[[17:09:03]] [INFO] Executing action 210/482: Tap on element with xpath: (//android.widget.Button[@content-desc="txtAdd to Bag"])[1]/preceding-sibling::android.widget.ImageView[1]
[[17:09:03]] [SUCCESS] Screenshot refreshed
[[17:09:03]] [INFO] Refreshing screenshot...
[[17:09:03]] [INFO] y4i304JeJj=pass
[[17:09:00]] [SUCCESS] Screenshot refreshed successfully
[[17:08:59]] [INFO] y4i304JeJj=running
[[17:08:59]] [INFO] Executing action 209/482: Tap on Text: "Move"
[[17:08:59]] [SUCCESS] Screenshot refreshed
[[17:08:59]] [INFO] Refreshing screenshot...
[[17:08:59]] [INFO] Q0fomJIDoQ=pass
[[17:08:58]] [SUCCESS] Screenshot refreshed successfully
[[17:08:57]] [INFO] Q0fomJIDoQ=running
[[17:08:57]] [INFO] Executing action 208/482: Tap on element with xpath: (//android.widget.Button[@content-desc="txtAdd to Bag"])[1]/preceding-sibling::android.widget.ImageView[1]
[[17:08:57]] [SUCCESS] Screenshot refreshed
[[17:08:57]] [INFO] Refreshing screenshot...
[[17:08:57]] [INFO] F1olhgKhUt=pass
[[17:08:55]] [SUCCESS] Screenshot refreshed successfully
[[17:08:54]] [INFO] F1olhgKhUt=running
[[17:08:54]] [INFO] Executing action 207/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 3 of 5")]
[[17:08:54]] [SUCCESS] Screenshot refreshed
[[17:08:54]] [INFO] Refreshing screenshot...
[[17:08:54]] [INFO] WbxRVpWtjw=pass
[[17:08:53]] [SUCCESS] Screenshot refreshed successfully
[[17:08:52]] [INFO] WbxRVpWtjw=running
[[17:08:52]] [INFO] Executing action 206/482: Tap on element with xpath: //android.widget.Button[@resource-id="wishlist-button"]
[[17:08:52]] [SUCCESS] Screenshot refreshed
[[17:08:52]] [INFO] Refreshing screenshot...
[[17:08:52]] [INFO] H3IAmq3r3i=pass
[[17:08:48]] [SUCCESS] Screenshot refreshed successfully
[[17:08:47]] [INFO] H3IAmq3r3i=running
[[17:08:47]] [INFO] Executing action 205/482: Swipe up till element xpath: "//android.widget.Button[@resource-id="wishlist-button"]" is visible
[[17:08:47]] [SUCCESS] Screenshot refreshed
[[17:08:47]] [INFO] Refreshing screenshot...
[[17:08:47]] [INFO] ITHvSyXXmu=pass
[[17:08:42]] [SUCCESS] Screenshot refreshed successfully
[[17:08:41]] [INFO] ITHvSyXXmu=running
[[17:08:41]] [INFO] Executing action 204/482: Wait till xpath=//android.widget.TextView[contains(@text,"SKU")]
[[17:08:41]] [SUCCESS] Screenshot refreshed
[[17:08:41]] [INFO] Refreshing screenshot...
[[17:08:41]] [INFO] eLxHVWKeDQ=pass
[[17:08:39]] [SUCCESS] Screenshot refreshed successfully
[[17:08:38]] [INFO] eLxHVWKeDQ=running
[[17:08:38]] [INFO] Executing action 203/482: Tap on element with xpath: ((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1]
[[17:08:38]] [SUCCESS] Screenshot refreshed
[[17:08:38]] [INFO] Refreshing screenshot...
[[17:08:38]] [INFO] nAB6Q8LAdv=pass
[[17:08:30]] [SUCCESS] Screenshot refreshed successfully
[[17:08:29]] [INFO] nAB6Q8LAdv=running
[[17:08:29]] [INFO] Executing action 202/482: Wait till xpath=//android.widget.Button[@text="Filter"]
[[17:08:29]] [SUCCESS] Screenshot refreshed
[[17:08:29]] [INFO] Refreshing screenshot...
[[17:08:29]] [INFO] wHCn7sSilS=pass
[[17:08:28]] [SUCCESS] Screenshot refreshed successfully
[[17:08:27]] [INFO] wHCn7sSilS=running
[[17:08:27]] [INFO] Executing action 201/482: Android Function: send_key_event - Key Event: ENTER
[[17:08:27]] [SUCCESS] Screenshot refreshed
[[17:08:27]] [INFO] Refreshing screenshot...
[[17:08:27]] [INFO] H3IAmq3r3i=pass
[[17:08:26]] [SUCCESS] Screenshot refreshed successfully
[[17:08:25]] [INFO] H3IAmq3r3i=running
[[17:08:25]] [INFO] Executing action 200/482: Input text: "notepads"
[[17:08:25]] [SUCCESS] Screenshot refreshed
[[17:08:25]] [INFO] Refreshing screenshot...
[[17:08:25]] [INFO] wzxrm7WwXv=pass
[[17:08:17]] [SUCCESS] Screenshot refreshed successfully
[[17:08:17]] [INFO] wzxrm7WwXv=running
[[17:08:17]] [INFO] Executing action 199/482: Tap on image: search-glassimage-android.png
[[17:08:17]] [SUCCESS] Screenshot refreshed
[[17:08:17]] [INFO] Refreshing screenshot...
[[17:08:17]] [INFO] F1olhgKhUt=pass
[[17:08:15]] [SUCCESS] Screenshot refreshed successfully
[[17:08:15]] [INFO] F1olhgKhUt=running
[[17:08:15]] [INFO] Executing action 198/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 2 of 5")]
[[17:08:15]] [SUCCESS] Screenshot refreshed
[[17:08:15]] [INFO] Refreshing screenshot...
[[17:08:15]] [INFO] WbxRVpWtjw=pass
[[17:08:13]] [SUCCESS] Screenshot refreshed successfully
[[17:08:13]] [INFO] WbxRVpWtjw=running
[[17:08:13]] [INFO] Executing action 197/482: Tap on element with xpath: //android.widget.Button[@resource-id="wishlist-button"]
[[17:08:13]] [SUCCESS] Screenshot refreshed
[[17:08:13]] [INFO] Refreshing screenshot...
[[17:08:13]] [INFO] H3IAmq3r3i=pass
[[17:08:10]] [SUCCESS] Screenshot refreshed successfully
[[17:08:09]] [INFO] H3IAmq3r3i=running
[[17:08:09]] [INFO] Executing action 196/482: Swipe up till element xpath: "//android.widget.Button[@resource-id="wishlist-button"]" is visible
[[17:08:09]] [SUCCESS] Screenshot refreshed
[[17:08:09]] [INFO] Refreshing screenshot...
[[17:08:09]] [INFO] ITHvSyXXmu=pass
[[17:08:05]] [SUCCESS] Screenshot refreshed successfully
[[17:08:04]] [INFO] ITHvSyXXmu=running
[[17:08:04]] [INFO] Executing action 195/482: Wait till xpath=//android.widget.TextView[contains(@text,"SKU")]
[[17:08:04]] [SUCCESS] Screenshot refreshed
[[17:08:04]] [INFO] Refreshing screenshot...
[[17:08:04]] [INFO] eLxHVWKeDQ=pass
[[17:08:03]] [SUCCESS] Screenshot refreshed successfully
[[17:08:02]] [INFO] eLxHVWKeDQ=running
[[17:08:02]] [INFO] Executing action 194/482: Tap on element with xpath: ((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1]
[[17:08:02]] [SUCCESS] Screenshot refreshed
[[17:08:02]] [INFO] Refreshing screenshot...
[[17:08:02]] [INFO] nAB6Q8LAdv=pass
[[17:07:48]] [SUCCESS] Screenshot refreshed successfully
[[17:07:47]] [INFO] nAB6Q8LAdv=running
[[17:07:47]] [INFO] Executing action 193/482: Wait till xpath=//android.widget.Button[@text="Filter"]
[[17:07:47]] [SUCCESS] Screenshot refreshed
[[17:07:47]] [INFO] Refreshing screenshot...
[[17:07:47]] [INFO] wHCn7sSilS=pass
[[17:07:46]] [SUCCESS] Screenshot refreshed successfully
[[17:07:45]] [INFO] wHCn7sSilS=running
[[17:07:45]] [INFO] Executing action 192/482: Android Function: send_key_event - Key Event: ENTER
[[17:07:45]] [SUCCESS] Screenshot refreshed
[[17:07:45]] [INFO] Refreshing screenshot...
[[17:07:45]] [INFO] H3IAmq3r3i=pass
[[17:07:44]] [SUCCESS] Screenshot refreshed successfully
[[17:07:43]] [INFO] H3IAmq3r3i=running
[[17:07:43]] [INFO] Executing action 191/482: Input text: "mat"
[[17:07:43]] [SUCCESS] Screenshot refreshed
[[17:07:43]] [INFO] Refreshing screenshot...
[[17:07:43]] [INFO] wzxrm7WwXv=pass
[[17:07:35]] [SUCCESS] Screenshot refreshed successfully
[[17:07:35]] [INFO] wzxrm7WwXv=running
[[17:07:35]] [INFO] Executing action 190/482: Tap on image: search-glassimage-android.png
[[17:07:35]] [SUCCESS] Screenshot refreshed
[[17:07:35]] [INFO] Refreshing screenshot...
[[17:07:35]] [INFO] F1olhgKhUt=pass
[[17:07:33]] [SUCCESS] Screenshot refreshed successfully
[[17:07:32]] [INFO] F1olhgKhUt=running
[[17:07:32]] [INFO] Executing action 189/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 2 of 5")]
[[17:07:32]] [SUCCESS] Screenshot refreshed
[[17:07:32]] [INFO] Refreshing screenshot...
[[17:07:32]] [INFO] WbxRVpWtjw=pass
[[17:07:30]] [SUCCESS] Screenshot refreshed successfully
[[17:07:29]] [INFO] WbxRVpWtjw=running
[[17:07:29]] [INFO] Executing action 188/482: Tap on element with xpath: //android.widget.Button[@resource-id="wishlist-button"]
[[17:07:29]] [SUCCESS] Screenshot refreshed
[[17:07:29]] [INFO] Refreshing screenshot...
[[17:07:29]] [INFO] H3IAmq3r3i=pass
[[17:07:26]] [SUCCESS] Screenshot refreshed successfully
[[17:07:25]] [INFO] H3IAmq3r3i=running
[[17:07:25]] [INFO] Executing action 187/482: Swipe up till element xpath: "//android.widget.Button[@resource-id="wishlist-button"]" is visible
[[17:07:25]] [SUCCESS] Screenshot refreshed
[[17:07:25]] [INFO] Refreshing screenshot...
[[17:07:25]] [INFO] ITHvSyXXmu=pass
[[17:07:22]] [SUCCESS] Screenshot refreshed successfully
[[17:07:21]] [INFO] ITHvSyXXmu=running
[[17:07:21]] [INFO] Executing action 186/482: Wait till xpath=//android.widget.TextView[contains(@text,"SKU")]
[[17:07:21]] [SUCCESS] Screenshot refreshed
[[17:07:21]] [INFO] Refreshing screenshot...
[[17:07:21]] [INFO] eLxHVWKeDQ=pass
[[17:07:20]] [SUCCESS] Screenshot refreshed successfully
[[17:07:19]] [INFO] eLxHVWKeDQ=running
[[17:07:19]] [INFO] Executing action 185/482: Tap on element with xpath: ((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1]
[[17:07:19]] [SUCCESS] Screenshot refreshed
[[17:07:19]] [INFO] Refreshing screenshot...
[[17:07:19]] [INFO] nAB6Q8LAdv=pass
[[17:07:13]] [SUCCESS] Screenshot refreshed successfully
[[17:07:12]] [INFO] nAB6Q8LAdv=running
[[17:07:12]] [INFO] Executing action 184/482: Wait till xpath=//android.widget.Button[@text="Filter"]
[[17:07:12]] [SUCCESS] Screenshot refreshed
[[17:07:12]] [INFO] Refreshing screenshot...
[[17:07:12]] [INFO] oy4DKDdbeL=pass
[[17:07:11]] [SUCCESS] Screenshot refreshed successfully
[[17:07:10]] [INFO] oy4DKDdbeL=running
[[17:07:10]] [INFO] Executing action 183/482: Android Function: send_key_event - Key Event: ENTER
[[17:07:10]] [SUCCESS] Screenshot refreshed
[[17:07:10]] [INFO] Refreshing screenshot...
[[17:07:10]] [INFO] sc2KH9bG6H=pass
[[17:07:08]] [SUCCESS] Screenshot refreshed successfully
[[17:07:07]] [INFO] sc2KH9bG6H=running
[[17:07:07]] [INFO] Executing action 182/482: Input text: "Uno card"
[[17:07:07]] [SUCCESS] Screenshot refreshed
[[17:07:07]] [INFO] Refreshing screenshot...
[[17:07:07]] [INFO] rqLJpAP0mA=pass
[[17:07:04]] [SUCCESS] Screenshot refreshed successfully
[[17:07:03]] [INFO] rqLJpAP0mA=running
[[17:07:03]] [INFO] Executing action 181/482: Tap on Text: "Find"
[[17:07:03]] [SUCCESS] Screenshot refreshed
[[17:07:03]] [INFO] Refreshing screenshot...
[[17:07:03]] [INFO] yiKyF5FJwN=pass
[[17:06:57]] [SUCCESS] Screenshot refreshed successfully
[[17:06:56]] [INFO] yiKyF5FJwN=running
[[17:06:56]] [INFO] Executing action 180/482: Check if element with xpath="//android.view.View[@content-desc="txtHomeGreetingText"]" exists
[[17:06:56]] [SUCCESS] Screenshot refreshed
[[17:06:56]] [INFO] Refreshing screenshot...
[[17:06:56]] [SUCCESS] Screenshot refreshed
[[17:06:56]] [INFO] Refreshing screenshot...
[[17:06:55]] [SUCCESS] Screenshot refreshed successfully
[[17:06:54]] [INFO] Executing Multi Step action step 7/7: Tap on element with xpath: //android.widget.Button[@text="Sign in"]
[[17:06:54]] [SUCCESS] Screenshot refreshed
[[17:06:54]] [INFO] Refreshing screenshot...
[[17:06:53]] [SUCCESS] Screenshot refreshed successfully
[[17:06:52]] [INFO] Executing Multi Step action step 6/7: Input text: "Wonderbaby@6"
[[17:06:52]] [SUCCESS] Screenshot refreshed
[[17:06:52]] [INFO] Refreshing screenshot...
[[17:06:51]] [SUCCESS] Screenshot refreshed successfully
[[17:06:50]] [INFO] Executing Multi Step action step 5/7: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[17:06:50]] [SUCCESS] Screenshot refreshed
[[17:06:50]] [INFO] Refreshing screenshot...
[[17:06:49]] [SUCCESS] Screenshot refreshed successfully
[[17:06:48]] [INFO] Executing Multi Step action step 4/7: Tap on element with xpath: //android.widget.Button[@text="Continue to password"]
[[17:06:48]] [SUCCESS] Screenshot refreshed
[[17:06:48]] [INFO] Refreshing screenshot...
[[17:06:47]] [SUCCESS] Screenshot refreshed successfully
[[17:06:46]] [INFO] Executing Multi Step action step 3/7: Input text: "<EMAIL>"
[[17:06:46]] [SUCCESS] Screenshot refreshed
[[17:06:46]] [INFO] Refreshing screenshot...
[[17:06:45]] [SUCCESS] Screenshot refreshed successfully
[[17:06:44]] [INFO] Executing Multi Step action step 2/7: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[17:06:44]] [SUCCESS] Screenshot refreshed
[[17:06:44]] [INFO] Refreshing screenshot...
[[17:06:41]] [SUCCESS] Screenshot refreshed successfully
[[17:06:41]] [INFO] Executing Multi Step action step 1/7: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[17:06:41]] [INFO] Loaded 7 steps from test case: Kmart-Signin-AU-ANDROID
[[17:06:41]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[17:06:41]] [INFO] rUH3kvaEH9=running
[[17:06:41]] [INFO] Executing action 179/482: Execute Test Case: Kmart-Signin-AU-ANDROID (7 steps)
[[17:06:41]] [SUCCESS] Screenshot refreshed
[[17:06:41]] [INFO] Refreshing screenshot...
[[17:06:41]] [INFO] rkL0oz4kiL=pass
[[17:06:37]] [SUCCESS] Screenshot refreshed successfully
[[17:06:36]] [INFO] rkL0oz4kiL=running
[[17:06:36]] [INFO] Executing action 178/482: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[17:06:36]] [SUCCESS] Screenshot refreshed
[[17:06:36]] [INFO] Refreshing screenshot...
[[17:06:36]] [SUCCESS] Screenshot refreshed
[[17:06:36]] [INFO] Refreshing screenshot...
[[17:06:32]] [SUCCESS] Screenshot refreshed successfully
[[17:06:31]] [INFO] Executing Multi Step action step 6/6: Tap on element with accessibility_id: txtOnePassOnboardingSkipForNow
[[17:06:31]] [SUCCESS] Screenshot refreshed
[[17:06:31]] [INFO] Refreshing screenshot...
[[17:06:30]] [SUCCESS] Screenshot refreshed successfully
[[17:06:29]] [INFO] Executing Multi Step action step 5/6: Tap on element with accessibility_id: btnMayBeLater
[[17:06:29]] [SUCCESS] Screenshot refreshed
[[17:06:29]] [INFO] Refreshing screenshot...
[[17:06:28]] [SUCCESS] Screenshot refreshed successfully
[[17:06:27]] [INFO] Executing Multi Step action step 4/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[17:06:27]] [SUCCESS] Screenshot refreshed
[[17:06:27]] [INFO] Refreshing screenshot...
[[17:06:22]] [SUCCESS] Screenshot refreshed successfully
[[17:06:22]] [INFO] Executing Multi Step action step 3/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[17:06:22]] [SUCCESS] Screenshot refreshed
[[17:06:22]] [INFO] Refreshing screenshot...
[[17:06:21]] [SUCCESS] Screenshot refreshed successfully
[[17:06:19]] [INFO] Executing Multi Step action step 2/6: Launch app: au.com.kmart
[[17:06:19]] [SUCCESS] Screenshot refreshed
[[17:06:19]] [INFO] Refreshing screenshot...
[[17:06:16]] [SUCCESS] Screenshot refreshed successfully
[[17:06:15]] [INFO] Executing Multi Step action step 1/6: Android Function: clear_app - Package: au.com.kmart
[[17:06:15]] [INFO] Loaded 6 steps from test case: Onboarding-Start-AU
[[17:06:15]] [INFO] Loading steps for Multi Step action: Onboarding-Start-AU
[[17:06:15]] [INFO] HotUJOd6oB=running
[[17:06:15]] [INFO] Executing action 177/482: Execute Test Case: Onboarding-Start-AU (6 steps)
[[17:06:15]] [SUCCESS] Screenshot refreshed
[[17:06:15]] [INFO] Refreshing screenshot...
[[17:06:15]] [INFO] xyHVihJMBi=pass
[[17:06:13]] [SUCCESS] Screenshot refreshed successfully
[[17:06:12]] [INFO] xyHVihJMBi=running
[[17:06:12]] [INFO] Executing action 176/482: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[17:06:12]] [SUCCESS] Screenshot refreshed
[[17:06:12]] [INFO] Refreshing screenshot...
[[17:06:09]] [SUCCESS] Screenshot refreshed successfully
[[17:06:08]] [INFO] Executing action 175/482: Swipe from (50%, 70%) to (50%, 30%)
[[17:06:08]] [SUCCESS] Screenshot refreshed
[[17:06:08]] [INFO] Refreshing screenshot...
[[17:06:08]] [INFO] rkwVoJGZG4=pass
[[17:06:07]] [SUCCESS] Screenshot refreshed successfully
[[17:06:06]] [INFO] rkwVoJGZG4=running
[[17:06:06]] [INFO] Executing action 174/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[17:06:06]] [SUCCESS] Screenshot refreshed
[[17:06:06]] [INFO] Refreshing screenshot...
[[17:06:06]] [INFO] 0f2FSZYjWq=pass
[[17:05:59]] [SUCCESS] Screenshot refreshed successfully
[[17:05:58]] [INFO] 0f2FSZYjWq=running
[[17:05:58]] [INFO] Executing action 173/482: Check if element with text="3000" exists
[[17:05:58]] [SUCCESS] Screenshot refreshed
[[17:05:58]] [INFO] Refreshing screenshot...
[[17:05:58]] [INFO] Tebej51pT2=pass
[[17:05:57]] [SUCCESS] Screenshot refreshed successfully
[[17:05:56]] [INFO] Tebej51pT2=running
[[17:05:56]] [INFO] Executing action 172/482: Tap on element with xpath: //android.widget.TextView[@text="Continue shopping"]
[[17:05:56]] [SUCCESS] Screenshot refreshed
[[17:05:56]] [INFO] Refreshing screenshot...
[[17:05:56]] [INFO] JrPVGdts3J=pass
[[17:05:48]] [SUCCESS] Screenshot refreshed successfully
[[17:05:47]] [INFO] JrPVGdts3J=running
[[17:05:47]] [INFO] Executing action 171/482: Tap on image: bag-remove-btn-android.png
[[17:05:47]] [SUCCESS] Screenshot refreshed
[[17:05:47]] [INFO] Refreshing screenshot...
[[17:05:47]] [INFO] mHyK7BTEWp=pass
[[17:05:44]] [SUCCESS] Screenshot refreshed successfully
[[17:05:43]] [INFO] mHyK7BTEWp=running
[[17:05:43]] [INFO] Executing action 170/482: Wait for 3 ms
[[17:05:43]] [SUCCESS] Screenshot refreshed
[[17:05:43]] [INFO] Refreshing screenshot...
[[17:05:41]] [SUCCESS] Screenshot refreshed successfully
[[17:05:41]] [INFO] Executing action 169/482: Swipe from (50%, 70%) to (50%, 30%)
[[17:05:41]] [SUCCESS] Screenshot refreshed
[[17:05:41]] [INFO] Refreshing screenshot...
[[17:05:41]] [INFO] GYK47u1y3A=pass
[[17:05:40]] [SUCCESS] Screenshot refreshed successfully
[[17:05:39]] [INFO] GYK47u1y3A=running
[[17:05:39]] [INFO] Executing action 168/482: Android Function: send_key_event - Key Event: TAB
[[17:05:39]] [SUCCESS] Screenshot refreshed
[[17:05:39]] [INFO] Refreshing screenshot...
[[17:05:39]] [INFO] ZWpYNcpbFA=pass
[[17:05:36]] [SUCCESS] Screenshot refreshed successfully
[[17:05:35]] [INFO] ZWpYNcpbFA=running
[[17:05:35]] [INFO] Executing action 167/482: Tap on Text: "VIC"
[[17:05:35]] [SUCCESS] Screenshot refreshed
[[17:05:35]] [INFO] Refreshing screenshot...
[[17:05:35]] [INFO] QpBLC6BStn=pass
[[17:05:33]] [SUCCESS] Screenshot refreshed successfully
[[17:05:32]] [INFO] QpBLC6BStn=running
[[17:05:32]] [INFO] Executing action 166/482: textClear action
[[17:05:32]] [SUCCESS] Screenshot refreshed
[[17:05:32]] [INFO] Refreshing screenshot...
[[17:05:32]] [INFO] G4A3KBlXHq=pass
[[17:05:26]] [SUCCESS] Screenshot refreshed successfully
[[17:05:25]] [INFO] G4A3KBlXHq=running
[[17:05:25]] [INFO] Executing action 165/482: Tap on Text: "Nearby"
[[17:05:25]] [SUCCESS] Screenshot refreshed
[[17:05:25]] [INFO] Refreshing screenshot...
[[17:05:25]] [INFO] BkQtEmZCrY=pass
[[17:05:18]] [SUCCESS] Screenshot refreshed successfully
[[17:05:17]] [INFO] BkQtEmZCrY=running
[[17:05:17]] [INFO] Executing action 164/482: Tap on image: cnc-tab-android.png
[[17:05:17]] [SUCCESS] Screenshot refreshed
[[17:05:17]] [INFO] Refreshing screenshot...
[[17:05:17]] [INFO] 0QetCcQWOH=pass
[[17:05:14]] [SUCCESS] Screenshot refreshed successfully
[[17:05:13]] [INFO] 0QetCcQWOH=running
[[17:05:13]] [INFO] Executing action 163/482: Wait till image appears: delivery-tab-android.png
[[17:05:13]] [SUCCESS] Screenshot refreshed
[[17:05:13]] [INFO] Refreshing screenshot...
[[17:05:13]] [INFO] wLxl79OeKO=pass
[[17:05:12]] [SUCCESS] Screenshot refreshed successfully
[[17:05:11]] [INFO] wLxl79OeKO=running
[[17:05:11]] [INFO] Executing action 162/482: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[17:05:11]] [SUCCESS] Screenshot refreshed
[[17:05:11]] [INFO] Refreshing screenshot...
[[17:05:11]] [INFO] rkwVoJGZG4=pass
[[17:05:10]] [SUCCESS] Screenshot refreshed successfully
[[17:05:09]] [INFO] rkwVoJGZG4=running
[[17:05:09]] [INFO] Executing action 161/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[17:05:09]] [SUCCESS] Screenshot refreshed
[[17:05:09]] [INFO] Refreshing screenshot...
[[17:05:09]] [INFO] 94ikwhIEE2=pass
[[17:05:05]] [SUCCESS] Screenshot refreshed successfully
[[17:05:04]] [INFO] 94ikwhIEE2=running
[[17:05:04]] [INFO] Executing action 160/482: Tap on Text: "bag"
[[17:05:04]] [SUCCESS] Screenshot refreshed
[[17:05:04]] [INFO] Refreshing screenshot...
[[17:05:04]] [INFO] DfwaiVZ8Z9=pass
[[17:05:02]] [SUCCESS] Screenshot refreshed successfully
[[17:05:01]] [INFO] DfwaiVZ8Z9=running
[[17:05:01]] [INFO] Executing action 159/482: Swipe from (50%, 70%) to (50%, 50%)
[[17:05:01]] [SUCCESS] Screenshot refreshed
[[17:05:01]] [INFO] Refreshing screenshot...
[[17:05:01]] [INFO] eRCmRhc3re=pass
[[17:04:52]] [SUCCESS] Screenshot refreshed successfully
[[17:04:52]] [INFO] eRCmRhc3re=running
[[17:04:52]] [INFO] Executing action 158/482: Check if element with text="Broadway" exists
[[17:04:52]] [SUCCESS] Screenshot refreshed
[[17:04:52]] [INFO] Refreshing screenshot...
[[17:04:52]] [INFO] E2jpN7BioW=pass
[[17:04:50]] [SUCCESS] Screenshot refreshed successfully
[[17:04:49]] [INFO] E2jpN7BioW=running
[[17:04:49]] [INFO] Executing action 157/482: Tap on element with accessibility_id: btnSaveOrContinue
[[17:04:49]] [SUCCESS] Screenshot refreshed
[[17:04:49]] [INFO] Refreshing screenshot...
[[17:04:49]] [INFO] kDnmoQJG4o=pass
[[17:04:48]] [SUCCESS] Screenshot refreshed successfully
[[17:04:47]] [INFO] kDnmoQJG4o=running
[[17:04:47]] [INFO] Executing action 156/482: Wait till accessibility_id=btnSaveOrContinue
[[17:04:47]] [SUCCESS] Screenshot refreshed
[[17:04:47]] [INFO] Refreshing screenshot...
[[17:04:47]] [INFO] H0ODFz7sWJ=pass
[[17:04:44]] [SUCCESS] Screenshot refreshed successfully
[[17:04:43]] [INFO] H0ODFz7sWJ=running
[[17:04:43]] [INFO] Executing action 155/482: Tap on Text: "2000"
[[17:04:43]] [SUCCESS] Screenshot refreshed
[[17:04:43]] [INFO] Refreshing screenshot...
[[17:04:43]] [INFO] pldheRUBVi=pass
[[17:04:42]] [SUCCESS] Screenshot refreshed successfully
[[17:04:41]] [INFO] pldheRUBVi=running
[[17:04:41]] [INFO] Executing action 154/482: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[17:04:41]] [SUCCESS] Screenshot refreshed
[[17:04:41]] [INFO] Refreshing screenshot...
[[17:04:41]] [INFO] uZHvvAzVfx=pass
[[17:04:38]] [SUCCESS] Screenshot refreshed successfully
[[17:04:37]] [INFO] uZHvvAzVfx=running
[[17:04:37]] [INFO] Executing action 153/482: textClear action
[[17:04:37]] [SUCCESS] Screenshot refreshed
[[17:04:37]] [INFO] Refreshing screenshot...
[[17:04:37]] [INFO] pldheRUBVi=pass
[[17:04:36]] [SUCCESS] Screenshot refreshed successfully
[[17:04:35]] [INFO] pldheRUBVi=running
[[17:04:35]] [INFO] Executing action 152/482: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[17:04:35]] [SUCCESS] Screenshot refreshed
[[17:04:35]] [INFO] Refreshing screenshot...
[[17:04:35]] [INFO] pldheRUBVi=pass
[[17:04:34]] [SUCCESS] Screenshot refreshed successfully
[[17:04:33]] [INFO] pldheRUBVi=running
[[17:04:33]] [INFO] Executing action 151/482: Wait till xpath=//android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[17:04:33]] [SUCCESS] Screenshot refreshed
[[17:04:33]] [INFO] Refreshing screenshot...
[[17:04:33]] [INFO] WmNWcsWVHv=pass
[[17:04:30]] [SUCCESS] Screenshot refreshed successfully
[[17:04:29]] [INFO] WmNWcsWVHv=running
[[17:04:29]] [INFO] Executing action 150/482: Tap on Text: "4000"
[[17:04:29]] [SUCCESS] Screenshot refreshed
[[17:04:29]] [INFO] Refreshing screenshot...
[[17:04:29]] [INFO] lnjoz8hHUU=pass
[[17:04:24]] [SUCCESS] Screenshot refreshed successfully
[[17:04:23]] [INFO] lnjoz8hHUU=running
[[17:04:23]] [INFO] Executing action 149/482: Wait till xpath=//android.widget.TextView[contains(@text,"SKU")]
[[17:04:23]] [SUCCESS] Screenshot refreshed
[[17:04:23]] [INFO] Refreshing screenshot...
[[17:04:23]] [INFO] VkUKQbf1Qt=pass
[[17:04:20]] [SUCCESS] Screenshot refreshed successfully
[[17:04:19]] [INFO] VkUKQbf1Qt=running
[[17:04:19]] [INFO] Executing action 148/482: Tap on Text: "UNO"
[[17:04:19]] [SUCCESS] Screenshot refreshed
[[17:04:19]] [INFO] Refreshing screenshot...
[[17:04:19]] [INFO] 73NABkfWyY=pass
[[17:04:12]] [SUCCESS] Screenshot refreshed successfully
[[17:04:11]] [INFO] 73NABkfWyY=running
[[17:04:11]] [INFO] Executing action 147/482: Check if element with text="Toowong" exists
[[17:04:11]] [SUCCESS] Screenshot refreshed
[[17:04:11]] [INFO] Refreshing screenshot...
[[17:04:11]] [INFO] E2jpN7BioW=pass
[[17:04:10]] [SUCCESS] Screenshot refreshed successfully
[[17:04:09]] [INFO] E2jpN7BioW=running
[[17:04:09]] [INFO] Executing action 146/482: Tap on element with accessibility_id: btnSaveOrContinue
[[17:04:09]] [SUCCESS] Screenshot refreshed
[[17:04:09]] [INFO] Refreshing screenshot...
[[17:04:09]] [INFO] kDnmoQJG4o=pass
[[17:04:08]] [SUCCESS] Screenshot refreshed successfully
[[17:04:06]] [INFO] kDnmoQJG4o=running
[[17:04:06]] [INFO] Executing action 145/482: Wait till accessibility_id=btnSaveOrContinue
[[17:04:06]] [SUCCESS] Screenshot refreshed
[[17:04:06]] [INFO] Refreshing screenshot...
[[17:04:06]] [INFO] VkUKQbf1Qt=pass
[[17:04:03]] [SUCCESS] Screenshot refreshed successfully
[[17:04:02]] [INFO] VkUKQbf1Qt=running
[[17:04:02]] [INFO] Executing action 144/482: Tap on Text: "CITY"
[[17:04:02]] [SUCCESS] Screenshot refreshed
[[17:04:02]] [INFO] Refreshing screenshot...
[[17:04:02]] [INFO] pldheRUBVi=pass
[[17:04:00]] [SUCCESS] Screenshot refreshed successfully
[[17:03:50]] [INFO] pldheRUBVi=running
[[17:03:50]] [INFO] Executing action 143/482: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[17:03:50]] [SUCCESS] Screenshot refreshed
[[17:03:50]] [INFO] Refreshing screenshot...
[[17:03:50]] [INFO] kbdEPCPYod=pass
[[17:03:47]] [SUCCESS] Screenshot refreshed successfully
[[17:03:46]] [INFO] kbdEPCPYod=running
[[17:03:46]] [INFO] Executing action 142/482: textClear action
[[17:03:46]] [SUCCESS] Screenshot refreshed
[[17:03:46]] [INFO] Refreshing screenshot...
[[17:03:46]] [INFO] pldheRUBVi=pass
[[17:03:45]] [SUCCESS] Screenshot refreshed successfully
[[17:03:44]] [INFO] pldheRUBVi=running
[[17:03:44]] [INFO] Executing action 141/482: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[17:03:44]] [SUCCESS] Screenshot refreshed
[[17:03:44]] [INFO] Refreshing screenshot...
[[17:03:44]] [INFO] YhLhTn3Wtm=pass
[[17:03:39]] [SUCCESS] Screenshot refreshed successfully
[[17:03:38]] [INFO] YhLhTn3Wtm=running
[[17:03:38]] [INFO] Executing action 140/482: Wait for 5 ms
[[17:03:38]] [SUCCESS] Screenshot refreshed
[[17:03:38]] [INFO] Refreshing screenshot...
[[17:03:38]] [INFO] VkUKQbf1Qt=pass
[[17:03:35]] [SUCCESS] Screenshot refreshed successfully
[[17:03:34]] [INFO] VkUKQbf1Qt=running
[[17:03:34]] [INFO] Executing action 139/482: Tap on Text: "Edit"
[[17:03:34]] [SUCCESS] Screenshot refreshed
[[17:03:34]] [INFO] Refreshing screenshot...
[[17:03:34]] [INFO] MpdUKUazHa=pass
[[17:03:32]] [SUCCESS] Screenshot refreshed successfully
[[17:03:31]] [INFO] MpdUKUazHa=running
[[17:03:31]] [INFO] Executing action 138/482: Wait till image appears: sort-by-relevance-android.png
[[17:03:31]] [SUCCESS] Screenshot refreshed
[[17:03:31]] [INFO] Refreshing screenshot...
[[17:03:31]] [INFO] RUizUoLMuO=pass
[[17:03:30]] [SUCCESS] Screenshot refreshed successfully
[[17:03:29]] [INFO] RUizUoLMuO=running
[[17:03:29]] [INFO] Executing action 137/482: Android Function: send_key_event - Key Event: ENTER
[[17:03:29]] [SUCCESS] Screenshot refreshed
[[17:03:29]] [INFO] Refreshing screenshot...
[[17:03:29]] [INFO] IupxLP2Jsr=pass
[[17:03:27]] [SUCCESS] Screenshot refreshed successfully
[[17:03:26]] [INFO] IupxLP2Jsr=running
[[17:03:26]] [INFO] Executing action 136/482: Input text: "P_6225544"
[[17:03:26]] [SUCCESS] Screenshot refreshed
[[17:03:26]] [INFO] Refreshing screenshot...
[[17:03:26]] [INFO] 70iOOakiG7=pass
[[17:03:22]] [SUCCESS] Screenshot refreshed successfully
[[17:03:21]] [INFO] 70iOOakiG7=running
[[17:03:21]] [INFO] Executing action 135/482: Tap on Text: "Find"
[[17:03:21]] [SUCCESS] Screenshot refreshed
[[17:03:21]] [INFO] Refreshing screenshot...
[[17:03:21]] [INFO] E2jpN7BioW=pass
[[17:03:19]] [SUCCESS] Screenshot refreshed successfully
[[17:03:19]] [INFO] E2jpN7BioW=running
[[17:03:19]] [INFO] Executing action 134/482: Tap on element with accessibility_id: btnSaveOrContinue
[[17:03:19]] [SUCCESS] Screenshot refreshed
[[17:03:19]] [INFO] Refreshing screenshot...
[[17:03:19]] [INFO] kDnmoQJG4o=pass
[[17:03:18]] [SUCCESS] Screenshot refreshed successfully
[[17:03:17]] [INFO] kDnmoQJG4o=running
[[17:03:17]] [INFO] Executing action 133/482: Wait till accessibility_id=btnSaveOrContinue
[[17:03:17]] [SUCCESS] Screenshot refreshed
[[17:03:17]] [INFO] Refreshing screenshot...
[[17:03:17]] [INFO] mw9GQ4mzRE=pass
[[17:03:13]] [SUCCESS] Screenshot refreshed successfully
[[17:03:12]] [INFO] mw9GQ4mzRE=running
[[17:03:12]] [INFO] Executing action 132/482: Tap on Text: "BC"
[[17:03:12]] [SUCCESS] Screenshot refreshed
[[17:03:12]] [INFO] Refreshing screenshot...
[[17:03:12]] [INFO] pldheRUBVi=pass
[[17:03:10]] [SUCCESS] Screenshot refreshed successfully
[[17:03:09]] [INFO] pldheRUBVi=running
[[17:03:09]] [INFO] Executing action 131/482: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[17:03:09]] [SUCCESS] Screenshot refreshed
[[17:03:09]] [INFO] Refreshing screenshot...
[[17:03:09]] [INFO] kbdEPCPYod=pass
[[17:03:07]] [SUCCESS] Screenshot refreshed successfully
[[17:03:06]] [INFO] kbdEPCPYod=running
[[17:03:06]] [INFO] Executing action 130/482: textClear action
[[17:03:06]] [SUCCESS] Screenshot refreshed
[[17:03:06]] [INFO] Refreshing screenshot...
[[17:03:06]] [INFO] pldheRUBVi=pass
[[17:03:04]] [SUCCESS] Screenshot refreshed successfully
[[17:03:03]] [INFO] pldheRUBVi=running
[[17:03:03]] [INFO] Executing action 129/482: Tap on element with xpath: //android.view.View[@content-desc="txtPostCodeSelectionScreenHeader"]/following-sibling::android.widget.ImageView[1]
[[17:03:03]] [SUCCESS] Screenshot refreshed
[[17:03:03]] [INFO] Refreshing screenshot...
[[17:02:54]] [SUCCESS] Screenshot refreshed successfully
[[17:02:53]] [INFO] Executing action 128/482: Tap on image: postcode_edit.png
[[17:02:53]] [SUCCESS] Screenshot refreshed
[[17:02:53]] [INFO] Refreshing screenshot...
[[17:02:53]] [INFO] RLz6vQo3ag=pass
[[17:02:48]] [SUCCESS] Screenshot refreshed successfully
[[17:02:47]] [INFO] RLz6vQo3ag=running
[[17:02:47]] [INFO] Executing action 127/482: Wait till xpath=//android.view.View[@content-desc="txtHomeGreetingText"]
[[17:02:47]] [SUCCESS] Screenshot refreshed
[[17:02:47]] [INFO] Refreshing screenshot...
[[17:02:47]] [SUCCESS] Screenshot refreshed
[[17:02:47]] [INFO] Refreshing screenshot...
[[17:02:46]] [SUCCESS] Screenshot refreshed successfully
[[17:02:45]] [INFO] Executing Multi Step action step 7/7: Tap on element with xpath: //android.widget.Button[@text="Sign in"]
[[17:02:45]] [SUCCESS] Screenshot refreshed
[[17:02:45]] [INFO] Refreshing screenshot...
[[17:02:44]] [SUCCESS] Screenshot refreshed successfully
[[17:02:43]] [INFO] Executing Multi Step action step 6/7: Input text: "Wonderbaby@6"
[[17:02:43]] [SUCCESS] Screenshot refreshed
[[17:02:43]] [INFO] Refreshing screenshot...
[[17:02:42]] [SUCCESS] Screenshot refreshed successfully
[[17:02:41]] [INFO] Executing Multi Step action step 5/7: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[17:02:41]] [SUCCESS] Screenshot refreshed
[[17:02:41]] [INFO] Refreshing screenshot...
[[17:02:40]] [SUCCESS] Screenshot refreshed successfully
[[17:02:40]] [INFO] Executing Multi Step action step 4/7: Tap on element with xpath: //android.widget.Button[@text="Continue to password"]
[[17:02:40]] [SUCCESS] Screenshot refreshed
[[17:02:40]] [INFO] Refreshing screenshot...
[[17:02:38]] [SUCCESS] Screenshot refreshed successfully
[[17:02:37]] [INFO] Executing Multi Step action step 3/7: Input text: "<EMAIL>"
[[17:02:37]] [SUCCESS] Screenshot refreshed
[[17:02:37]] [INFO] Refreshing screenshot...
[[17:02:36]] [SUCCESS] Screenshot refreshed successfully
[[17:02:36]] [INFO] Executing Multi Step action step 2/7: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[17:02:35]] [SUCCESS] Screenshot refreshed
[[17:02:35]] [INFO] Refreshing screenshot...
[[17:02:33]] [SUCCESS] Screenshot refreshed successfully
[[17:02:32]] [INFO] Executing Multi Step action step 1/7: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[17:02:32]] [INFO] Loaded 7 steps from test case: Kmart-Signin-AU-ANDROID
[[17:02:32]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[17:02:32]] [INFO] xz8njynjpZ=running
[[17:02:32]] [INFO] Executing action 126/482: Execute Test Case: Kmart-Signin-AU-ANDROID (7 steps)
[[17:02:32]] [SUCCESS] Screenshot refreshed
[[17:02:32]] [INFO] Refreshing screenshot...
[[17:02:32]] [INFO] J9loj6Zl5K=pass
[[17:02:30]] [SUCCESS] Screenshot refreshed successfully
[[17:02:30]] [INFO] J9loj6Zl5K=running
[[17:02:30]] [INFO] Executing action 125/482: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[17:02:30]] [SUCCESS] Screenshot refreshed
[[17:02:30]] [INFO] Refreshing screenshot...
[[17:02:30]] [INFO] Y8vz7AJD1i=pass
[[17:02:27]] [SUCCESS] Screenshot refreshed successfully
[[17:02:25]] [INFO] Y8vz7AJD1i=running
[[17:02:25]] [INFO] Executing action 124/482: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[17:02:25]] [SUCCESS] Screenshot refreshed
[[17:02:25]] [INFO] Refreshing screenshot...
[[17:02:25]] [SUCCESS] Screenshot refreshed
[[17:02:25]] [INFO] Refreshing screenshot...
[[17:02:23]] [SUCCESS] Screenshot refreshed successfully
[[17:02:22]] [INFO] Executing Multi Step action step 6/6: Tap on element with accessibility_id: txtOnePassOnboardingSkipForNow
[[17:02:22]] [SUCCESS] Screenshot refreshed
[[17:02:22]] [INFO] Refreshing screenshot...
[[17:02:21]] [SUCCESS] Screenshot refreshed successfully
[[17:02:20]] [INFO] Executing Multi Step action step 5/6: Tap on element with accessibility_id: btnMayBeLater
[[17:02:20]] [SUCCESS] Screenshot refreshed
[[17:02:20]] [INFO] Refreshing screenshot...
[[17:02:19]] [SUCCESS] Screenshot refreshed successfully
[[17:02:17]] [INFO] Executing Multi Step action step 4/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[17:02:17]] [SUCCESS] Screenshot refreshed
[[17:02:17]] [INFO] Refreshing screenshot...
[[17:02:13]] [SUCCESS] Screenshot refreshed successfully
[[17:02:13]] [INFO] Executing Multi Step action step 3/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[17:02:13]] [SUCCESS] Screenshot refreshed
[[17:02:13]] [INFO] Refreshing screenshot...
[[17:02:12]] [SUCCESS] Screenshot refreshed successfully
[[17:02:10]] [INFO] Executing Multi Step action step 2/6: Launch app: au.com.kmart
[[17:02:10]] [SUCCESS] Screenshot refreshed
[[17:02:10]] [INFO] Refreshing screenshot...
[[17:02:07]] [SUCCESS] Screenshot refreshed successfully
[[17:02:06]] [INFO] Executing Multi Step action step 1/6: Android Function: clear_app - Package: au.com.kmart
[[17:02:06]] [INFO] Loaded 6 steps from test case: Onboarding-Start-AU
[[17:02:06]] [INFO] Loading steps for Multi Step action: Onboarding-Start-AU
[[17:02:06]] [INFO] Executing action 123/482: Execute Test Case: Onboarding-Start-AU (6 steps)
[[17:02:06]] [SUCCESS] Screenshot refreshed
[[17:02:06]] [INFO] Refreshing screenshot...
[[17:02:06]] [INFO] F0gZF1jEnT=pass
[[17:02:04]] [SUCCESS] Screenshot refreshed successfully
[[17:02:03]] [INFO] F0gZF1jEnT=running
[[17:02:03]] [INFO] Executing action 122/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 1 of 5")]
[[17:02:03]] [SUCCESS] Screenshot refreshed
[[17:02:03]] [INFO] Refreshing screenshot...
[[17:02:03]] [INFO] bGo3feCwBQ=pass
[[17:02:01]] [SUCCESS] Screenshot refreshed successfully
[[17:01:40]] [INFO] bGo3feCwBQ=running
[[17:01:40]] [INFO] Executing action 121/482: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[17:01:40]] [SUCCESS] Screenshot refreshed
[[17:01:40]] [INFO] Refreshing screenshot...
[[17:01:38]] [SUCCESS] Screenshot refreshed successfully
[[17:01:37]] [INFO] Executing action 120/482: Swipe from (50%, 70%) to (50%, 30%)
[[17:01:37]] [SUCCESS] Screenshot refreshed
[[17:01:37]] [INFO] Refreshing screenshot...
[[17:01:37]] [INFO] F0gZF1jEnT=pass
[[17:01:36]] [SUCCESS] Screenshot refreshed successfully
[[17:01:34]] [INFO] F0gZF1jEnT=running
[[17:01:34]] [INFO] Executing action 119/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[17:01:34]] [SUCCESS] Screenshot refreshed
[[17:01:34]] [INFO] Refreshing screenshot...
[[17:01:34]] [INFO] CkfAScJNq8=pass
[[17:01:07]] [SUCCESS] Screenshot refreshed successfully
[[17:01:06]] [INFO] CkfAScJNq8=running
[[17:01:06]] [INFO] Executing action 118/482: Tap on image: bag-close-android.png
[[17:01:06]] [SUCCESS] Screenshot refreshed
[[17:01:06]] [INFO] Refreshing screenshot...
[[17:01:06]] [INFO] 1NWfFsDiTQ=pass
[[17:01:05]] [SUCCESS] Screenshot refreshed successfully
[[17:01:04]] [INFO] 1NWfFsDiTQ=running
[[17:01:04]] [INFO] Executing action 117/482: Tap on element with xpath: //android.widget.Button[contains(@text,"Remove")]
[[17:01:04]] [SUCCESS] Screenshot refreshed
[[17:01:04]] [INFO] Refreshing screenshot...
[[17:01:04]] [INFO] ZZ1yenkiIl=pass
[[17:01:03]] [SUCCESS] Screenshot refreshed successfully
[[17:01:02]] [INFO] ZZ1yenkiIl=running
[[17:01:02]] [INFO] Executing action 116/482: Tap on element with xpath: //android.view.View[@text="Delivery"]
[[17:01:02]] [SUCCESS] Screenshot refreshed
[[17:01:02]] [INFO] Refreshing screenshot...
[[17:01:02]] [INFO] g8u66qfKkX=pass
[[17:00:39]] [SUCCESS] Screenshot refreshed successfully
[[17:00:38]] [INFO] g8u66qfKkX=running
[[17:00:38]] [INFO] Executing action 115/482: Wait till xpath=//android.view.View[@text="Delivery"]
[[17:00:38]] [SUCCESS] Screenshot refreshed
[[17:00:38]] [INFO] Refreshing screenshot...
[[17:00:38]] [INFO] V9ldRojdyD=pass
[[17:00:33]] [SUCCESS] Screenshot refreshed successfully
[[17:00:32]] [INFO] V9ldRojdyD=running
[[17:00:32]] [INFO] Executing action 114/482: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[17:00:32]] [SUCCESS] Screenshot refreshed
[[17:00:32]] [INFO] Refreshing screenshot...
[[17:00:32]] [SUCCESS] Screenshot refreshed
[[17:00:32]] [INFO] Refreshing screenshot...
[[17:00:31]] [SUCCESS] Screenshot refreshed successfully
[[17:00:21]] [INFO] Executing Multi Step action step 7/7: Tap on element with xpath: //android.widget.Button[@text="Sign in"]
[[17:00:21]] [SUCCESS] Screenshot refreshed
[[17:00:21]] [INFO] Refreshing screenshot...
[[17:00:20]] [SUCCESS] Screenshot refreshed successfully
[[17:00:19]] [INFO] Executing Multi Step action step 6/7: Input text: "Wonderbaby@6"
[[17:00:19]] [SUCCESS] Screenshot refreshed
[[17:00:19]] [INFO] Refreshing screenshot...
[[17:00:18]] [SUCCESS] Screenshot refreshed successfully
[[17:00:17]] [INFO] Executing Multi Step action step 5/7: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[17:00:17]] [SUCCESS] Screenshot refreshed
[[17:00:17]] [INFO] Refreshing screenshot...
[[17:00:16]] [SUCCESS] Screenshot refreshed successfully
[[17:00:16]] [INFO] Executing Multi Step action step 4/7: Tap on element with xpath: //android.widget.Button[@text="Continue to password"]
[[17:00:16]] [SUCCESS] Screenshot refreshed
[[17:00:16]] [INFO] Refreshing screenshot...
[[17:00:14]] [SUCCESS] Screenshot refreshed successfully
[[17:00:14]] [INFO] Executing Multi Step action step 3/7: Input text: "<EMAIL>"
[[17:00:14]] [SUCCESS] Screenshot refreshed
[[17:00:14]] [INFO] Refreshing screenshot...
[[17:00:12]] [SUCCESS] Screenshot refreshed successfully
[[17:00:12]] [INFO] Executing Multi Step action step 2/7: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[17:00:12]] [SUCCESS] Screenshot refreshed
[[17:00:12]] [INFO] Refreshing screenshot...
[[17:00:08]] [SUCCESS] Screenshot refreshed successfully
[[17:00:08]] [INFO] Executing Multi Step action step 1/7: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[17:00:08]] [INFO] Loaded 7 steps from test case: Kmart-Signin-AU-ANDROID
[[17:00:08]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[17:00:08]] [INFO] kR0cfY8jim=running
[[17:00:08]] [INFO] Executing action 113/482: Execute Test Case: Kmart-Signin-AU-ANDROID (7 steps)
[[17:00:08]] [SUCCESS] Screenshot refreshed
[[17:00:08]] [INFO] Refreshing screenshot...
[[17:00:08]] [INFO] 6PL8P3rT57=pass
[[17:00:04]] [SUCCESS] Screenshot refreshed successfully
[[17:00:04]] [INFO] 6PL8P3rT57=running
[[17:00:04]] [INFO] Executing action 112/482: Tap on Text: "Sign"
[[17:00:04]] [SUCCESS] Screenshot refreshed
[[17:00:04]] [INFO] Refreshing screenshot...
[[17:00:04]] [INFO] iWRZoQx4qd=pass
[[17:00:03]] [SUCCESS] Screenshot refreshed successfully
[[17:00:02]] [INFO] iWRZoQx4qd=running
[[17:00:02]] [INFO] Executing action 111/482: Tap on element with xpath: //android.widget.Button[contains(@text,"Continue")]
[[17:00:02]] [SUCCESS] Screenshot refreshed
[[17:00:02]] [INFO] Refreshing screenshot...
[[16:59:50]] [SUCCESS] Screenshot refreshed successfully
[[16:59:49]] [INFO] Executing action 110/482: Swipe up till element xpath: "//android.widget.Button[contains(@text,"Continue")]" is visible
[[16:59:49]] [SUCCESS] Screenshot refreshed
[[16:59:49]] [INFO] Refreshing screenshot...
[[16:59:49]] [INFO] ZZ1yenkiIl=pass
[[16:59:48]] [SUCCESS] Screenshot refreshed successfully
[[16:59:47]] [INFO] ZZ1yenkiIl=running
[[16:59:47]] [INFO] Executing action 109/482: Tap on element with xpath: //android.view.View[@text="Delivery"]
[[16:59:47]] [SUCCESS] Screenshot refreshed
[[16:59:47]] [INFO] Refreshing screenshot...
[[16:59:47]] [INFO] g8u66qfKkX=pass
[[16:59:40]] [SUCCESS] Screenshot refreshed successfully
[[16:59:39]] [INFO] g8u66qfKkX=running
[[16:59:39]] [INFO] Executing action 108/482: Wait till xpath=//android.view.View[@text="Delivery"]
[[16:59:39]] [SUCCESS] Screenshot refreshed
[[16:59:39]] [INFO] Refreshing screenshot...
[[16:59:39]] [INFO] V9ldRojdyD=pass
[[16:59:37]] [SUCCESS] Screenshot refreshed successfully
[[16:59:37]] [INFO] V9ldRojdyD=running
[[16:59:37]] [INFO] Executing action 107/482: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[16:59:37]] [SUCCESS] Screenshot refreshed
[[16:59:37]] [INFO] Refreshing screenshot...
[[16:59:37]] [INFO] F0gZF1jEnT=pass
[[16:59:35]] [SUCCESS] Screenshot refreshed successfully
[[16:59:34]] [INFO] F0gZF1jEnT=running
[[16:59:34]] [INFO] Executing action 106/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[16:59:34]] [SUCCESS] Screenshot refreshed
[[16:59:34]] [INFO] Refreshing screenshot...
[[16:59:34]] [INFO] XcWXIMtv1E=pass
[[16:59:29]] [SUCCESS] Screenshot refreshed successfully
[[16:59:28]] [INFO] XcWXIMtv1E=running
[[16:59:28]] [INFO] Executing action 105/482: Wait for 5 ms
[[16:59:28]] [SUCCESS] Screenshot refreshed
[[16:59:28]] [INFO] Refreshing screenshot...
[[16:59:28]] [INFO] S1cQQxksEj=pass
[[16:59:27]] [SUCCESS] Screenshot refreshed successfully
[[16:59:26]] [INFO] S1cQQxksEj=running
[[16:59:26]] [INFO] Executing action 104/482: Tap on element with xpath: //android.widget.Button[@text="Add to bag"]
[[16:59:26]] [SUCCESS] Screenshot refreshed
[[16:59:26]] [INFO] Refreshing screenshot...
[[16:59:26]] [INFO] K2w9XUGwnb=pass
[[16:59:22]] [SUCCESS] Screenshot refreshed successfully
[[16:59:22]] [INFO] K2w9XUGwnb=running
[[16:59:22]] [INFO] Executing action 103/482: Swipe up till element xpath: "//android.widget.Button[@text="Add to bag"]" is visible
[[16:59:22]] [SUCCESS] Screenshot refreshed
[[16:59:22]] [INFO] Refreshing screenshot...
[[16:59:22]] [INFO] BTYxjEaZEk=pass
[[16:59:20]] [SUCCESS] Screenshot refreshed successfully
[[16:59:19]] [INFO] BTYxjEaZEk=running
[[16:59:19]] [INFO] Executing action 102/482: Tap on element with xpath: ((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1]
[[16:59:19]] [SUCCESS] Screenshot refreshed
[[16:59:19]] [INFO] Refreshing screenshot...
[[16:59:19]] [INFO] YC6bBrKQgq=pass
[[16:59:13]] [SUCCESS] Screenshot refreshed successfully
[[16:59:12]] [INFO] YC6bBrKQgq=running
[[16:59:12]] [INFO] Executing action 101/482: Wait till xpath=//android.widget.Button[@text="Filter"]
[[16:59:12]] [SUCCESS] Screenshot refreshed
[[16:59:12]] [INFO] Refreshing screenshot...
[[16:59:12]] [INFO] GMdwqXyeMs=pass
[[16:59:11]] [SUCCESS] Screenshot refreshed successfully
[[16:59:10]] [INFO] GMdwqXyeMs=running
[[16:59:10]] [INFO] Executing action 100/482: Android Function: send_key_event - Key Event: ENTER
[[16:59:10]] [SUCCESS] Screenshot refreshed
[[16:59:10]] [INFO] Refreshing screenshot...
[[16:59:10]] [INFO] aRgHcQcLDP=pass
[[16:59:09]] [SUCCESS] Screenshot refreshed successfully
[[16:59:08]] [INFO] aRgHcQcLDP=running
[[16:59:08]] [INFO] Executing action 99/482: Input text: "Uno card"
[[16:59:08]] [SUCCESS] Screenshot refreshed
[[16:59:08]] [INFO] Refreshing screenshot...
[[16:59:08]] [INFO] 4PZC1vVWJW=pass
[[16:59:04]] [SUCCESS] Screenshot refreshed successfully
[[16:59:02]] [INFO] 4PZC1vVWJW=running
[[16:59:02]] [INFO] Executing action 98/482: Tap on Text: "Find"
[[16:59:02]] [SUCCESS] Screenshot refreshed
[[16:59:02]] [INFO] Refreshing screenshot...
[[16:59:02]] [INFO] F0gZF1jEnT=pass
[[16:59:01]] [SUCCESS] Screenshot refreshed successfully
[[16:58:42]] [INFO] F0gZF1jEnT=running
[[16:58:42]] [INFO] Executing action 97/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 1 of 5")]
[[16:58:42]] [SUCCESS] Screenshot refreshed
[[16:58:42]] [INFO] Refreshing screenshot...
[[16:58:42]] [INFO] bGo3feCwBQ=pass
[[16:58:40]] [SUCCESS] Screenshot refreshed successfully
[[16:58:39]] [INFO] bGo3feCwBQ=running
[[16:58:39]] [INFO] Executing action 96/482: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[16:58:39]] [SUCCESS] Screenshot refreshed
[[16:58:39]] [INFO] Refreshing screenshot...
[[16:58:36]] [SUCCESS] Screenshot refreshed successfully
[[16:58:35]] [INFO] Executing action 95/482: Swipe from (50%, 70%) to (50%, 30%)
[[16:58:35]] [SUCCESS] Screenshot refreshed
[[16:58:35]] [INFO] Refreshing screenshot...
[[16:58:35]] [SUCCESS] Screenshot refreshed
[[16:58:35]] [INFO] Refreshing screenshot...
[[16:58:34]] [SUCCESS] Screenshot refreshed successfully
[[16:58:33]] [INFO] Executing Multi Step action step 7/7: Tap on element with xpath: //android.widget.Button[@text="Sign in"]
[[16:58:33]] [SUCCESS] Screenshot refreshed
[[16:58:33]] [INFO] Refreshing screenshot...
[[16:58:31]] [SUCCESS] Screenshot refreshed successfully
[[16:58:30]] [INFO] Executing Multi Step action step 6/7: Input text: "Wonderbaby@6"
[[16:58:30]] [SUCCESS] Screenshot refreshed
[[16:58:30]] [INFO] Refreshing screenshot...
[[16:58:29]] [SUCCESS] Screenshot refreshed successfully
[[16:58:29]] [INFO] Executing Multi Step action step 5/7: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[16:58:29]] [SUCCESS] Screenshot refreshed
[[16:58:29]] [INFO] Refreshing screenshot...
[[16:58:27]] [SUCCESS] Screenshot refreshed successfully
[[16:58:27]] [INFO] Executing Multi Step action step 4/7: Tap on element with xpath: //android.widget.Button[@text="Continue to password"]
[[16:58:27]] [SUCCESS] Screenshot refreshed
[[16:58:27]] [INFO] Refreshing screenshot...
[[16:58:25]] [SUCCESS] Screenshot refreshed successfully
[[16:58:25]] [INFO] Executing Multi Step action step 3/7: Input text: "<EMAIL>"
[[16:58:25]] [SUCCESS] Screenshot refreshed
[[16:58:25]] [INFO] Refreshing screenshot...
[[16:58:23]] [SUCCESS] Screenshot refreshed successfully
[[16:58:10]] [INFO] Executing Multi Step action step 2/7: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[16:58:10]] [SUCCESS] Screenshot refreshed
[[16:58:10]] [INFO] Refreshing screenshot...
[[16:58:07]] [SUCCESS] Screenshot refreshed successfully
[[16:58:07]] [INFO] Executing Multi Step action step 1/7: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[16:58:06]] [INFO] Loaded 7 steps from test case: Kmart-Signin-AU-ANDROID
[[16:58:06]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[16:58:06]] [INFO] kR0cfY8jim=running
[[16:58:06]] [INFO] Executing action 94/482: Execute Test Case: Kmart-Signin-AU-ANDROID (7 steps)
[[16:58:06]] [SUCCESS] Screenshot refreshed
[[16:58:06]] [INFO] Refreshing screenshot...
[[16:58:06]] [INFO] L6wTorOX8B=pass
[[16:58:03]] [SUCCESS] Screenshot refreshed successfully
[[16:58:02]] [INFO] L6wTorOX8B=running
[[16:58:02]] [INFO] Executing action 93/482: Tap on element with xpath: //android.widget.Button[@content-desc="txtMoreAccountCtaSignIn"]
[[16:58:02]] [SUCCESS] Screenshot refreshed
[[16:58:02]] [INFO] Refreshing screenshot...
[[16:58:02]] [INFO] F0gZF1jEnT=pass
[[16:58:01]] [SUCCESS] Screenshot refreshed successfully
[[16:57:53]] [INFO] F0gZF1jEnT=running
[[16:57:53]] [INFO] Executing action 92/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[16:57:53]] [SUCCESS] Screenshot refreshed
[[16:57:53]] [INFO] Refreshing screenshot...
[[16:57:53]] [INFO] bGo3feCwBQ=pass
[[16:57:49]] [SUCCESS] Screenshot refreshed successfully
[[16:57:49]] [INFO] bGo3feCwBQ=running
[[16:57:49]] [INFO] Executing action 91/482: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[16:57:49]] [SUCCESS] Screenshot refreshed
[[16:57:49]] [INFO] Refreshing screenshot...
[[16:57:46]] [SUCCESS] Screenshot refreshed successfully
[[16:57:45]] [INFO] Executing action 90/482: Swipe from (50%, 70%) to (50%, 30%)
[[16:57:45]] [SUCCESS] Screenshot refreshed
[[16:57:45]] [INFO] Refreshing screenshot...
[[16:57:45]] [INFO] F0gZF1jEnT=pass
[[16:57:44]] [SUCCESS] Screenshot refreshed successfully
[[16:57:44]] [INFO] F0gZF1jEnT=running
[[16:57:44]] [INFO] Executing action 89/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[16:57:44]] [SUCCESS] Screenshot refreshed
[[16:57:44]] [INFO] Refreshing screenshot...
[[16:57:44]] [INFO] XcWXIMtv1E=pass
[[16:57:38]] [SUCCESS] Screenshot refreshed successfully
[[16:57:37]] [INFO] XcWXIMtv1E=running
[[16:57:37]] [INFO] Executing action 88/482: Wait for 5 ms
[[16:57:37]] [SUCCESS] Screenshot refreshed
[[16:57:37]] [INFO] Refreshing screenshot...
[[16:57:37]] [INFO] g6SvrkfA9z=pass
[[16:57:36]] [SUCCESS] Screenshot refreshed successfully
[[16:57:36]] [INFO] g6SvrkfA9z=running
[[16:57:36]] [INFO] Executing action 87/482: Android Function: send_key_event - Key Event: ENTER
[[16:57:36]] [SUCCESS] Screenshot refreshed
[[16:57:36]] [INFO] Refreshing screenshot...
[[16:57:36]] [INFO] N2yjynioko=pass
[[16:57:34]] [SUCCESS] Screenshot refreshed successfully
[[16:57:33]] [INFO] N2yjynioko=running
[[16:57:33]] [INFO] Executing action 86/482: Input text: "Wonderbaby@5"
[[16:57:33]] [SUCCESS] Screenshot refreshed
[[16:57:33]] [INFO] Refreshing screenshot...
[[16:57:33]] [INFO] SHaIduBnay=pass
[[16:57:32]] [SUCCESS] Screenshot refreshed successfully
[[16:57:31]] [INFO] SHaIduBnay=running
[[16:57:31]] [INFO] Executing action 85/482: Tap on element with xpath: //android.widget.EditText[@resource-id="password-input"]
[[16:57:31]] [SUCCESS] Screenshot refreshed
[[16:57:31]] [INFO] Refreshing screenshot...
[[16:57:31]] [INFO] F4PAASAS6q=pass
[[16:57:01]] [SUCCESS] Screenshot refreshed successfully
[[16:57:00]] [INFO] F4PAASAS6q=running
[[16:57:00]] [INFO] Executing action 84/482: Tap if locator exists: xpath="//android.widget.CheckBox[@resource-id="recaptcha-anchor"]"
[[16:57:00]] [SUCCESS] Screenshot refreshed
[[16:57:00]] [INFO] Refreshing screenshot...
[[16:57:00]] [INFO] KGagCNh4k8=pass
[[16:56:59]] [SUCCESS] Screenshot refreshed successfully
[[16:56:58]] [INFO] KGagCNh4k8=running
[[16:56:58]] [INFO] Executing action 83/482: Android Function: send_key_event - Key Event: ENTER
[[16:56:58]] [SUCCESS] Screenshot refreshed
[[16:56:58]] [INFO] Refreshing screenshot...
[[16:56:58]] [INFO] wuIMlAwYVA=pass
[[16:56:57]] [SUCCESS] Screenshot refreshed successfully
[[16:56:56]] [INFO] wuIMlAwYVA=running
[[16:56:56]] [INFO] Executing action 82/482: Input text: "<EMAIL>"
[[16:56:56]] [SUCCESS] Screenshot refreshed
[[16:56:56]] [INFO] Refreshing screenshot...
[[16:56:56]] [INFO] 50Z2jrodNd=pass
[[16:56:54]] [SUCCESS] Screenshot refreshed successfully
[[16:56:54]] [INFO] 50Z2jrodNd=running
[[16:56:54]] [INFO] Executing action 81/482: Tap on element with xpath: //android.widget.EditText[@resource-id="email-input"]
[[16:56:54]] [SUCCESS] Screenshot refreshed
[[16:56:54]] [INFO] Refreshing screenshot...
[[16:56:54]] [INFO] VK2oI6mXSB=pass
[[16:56:50]] [SUCCESS] Screenshot refreshed successfully
[[16:56:49]] [INFO] VK2oI6mXSB=running
[[16:56:49]] [INFO] Executing action 80/482: Wait till xpath=//android.widget.EditText[@resource-id="email-input"]
[[16:56:49]] [SUCCESS] Screenshot refreshed
[[16:56:49]] [INFO] Refreshing screenshot...
[[16:56:49]] [INFO] 4PZC1vVWJW=pass
[[16:56:46]] [SUCCESS] Screenshot refreshed successfully
[[16:56:45]] [INFO] 4PZC1vVWJW=running
[[16:56:45]] [INFO] Executing action 79/482: Tap on Text: "Sign"
[[16:56:45]] [SUCCESS] Screenshot refreshed
[[16:56:45]] [INFO] Refreshing screenshot...
[[16:56:45]] [INFO] mcscWdhpn2=pass
[[16:56:34]] [SUCCESS] Screenshot refreshed successfully
[[16:56:33]] [INFO] mcscWdhpn2=running
[[16:56:33]] [INFO] Executing action 78/482: Swipe up till element xpath: "//android.widget.TextView[contains(@text,"Already a member")]" is visible
[[16:56:33]] [SUCCESS] Screenshot refreshed
[[16:56:33]] [INFO] Refreshing screenshot...
[[16:56:33]] [INFO] 6zUBxjSFym=pass
[[16:56:04]] [SUCCESS] Screenshot refreshed successfully
[[16:56:04]] [INFO] 6zUBxjSFym=running
[[16:56:04]] [INFO] Executing action 77/482: Wait till xpath=//android.widget.TextView[contains(@text,"SKU")]
[[16:56:04]] [SUCCESS] Screenshot refreshed
[[16:56:04]] [INFO] Refreshing screenshot...
[[16:56:04]] [INFO] BTYxjEaZEk=pass
[[16:56:02]] [SUCCESS] Screenshot refreshed successfully
[[16:56:01]] [INFO] BTYxjEaZEk=running
[[16:56:01]] [INFO] Executing action 76/482: Tap on element with xpath: ((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1]
[[16:56:01]] [SUCCESS] Screenshot refreshed
[[16:56:01]] [INFO] Refreshing screenshot...
[[16:56:01]] [INFO] YC6bBrKQgq=pass
[[16:55:53]] [SUCCESS] Screenshot refreshed successfully
[[16:55:53]] [INFO] YC6bBrKQgq=running
[[16:55:53]] [INFO] Executing action 75/482: Wait till xpath=//android.widget.Button[@text="Filter"]
[[16:55:53]] [SUCCESS] Screenshot refreshed
[[16:55:53]] [INFO] Refreshing screenshot...
[[16:55:53]] [INFO] FLVgc6jpIu=pass
[[16:55:52]] [SUCCESS] Screenshot refreshed successfully
[[16:55:51]] [INFO] FLVgc6jpIu=running
[[16:55:51]] [INFO] Executing action 74/482: Android Function: send_key_event - Key Event: ENTER
[[16:55:51]] [SUCCESS] Screenshot refreshed
[[16:55:51]] [INFO] Refreshing screenshot...
[[16:55:51]] [INFO] aRgHcQcLDP=pass
[[16:55:49]] [SUCCESS] Screenshot refreshed successfully
[[16:55:48]] [INFO] aRgHcQcLDP=running
[[16:55:48]] [INFO] Executing action 73/482: Input text: "Uno card"
[[16:55:48]] [SUCCESS] Screenshot refreshed
[[16:55:48]] [INFO] Refreshing screenshot...
[[16:55:48]] [INFO] 4PZC1vVWJW=pass
[[16:55:44]] [SUCCESS] Screenshot refreshed successfully
[[16:55:43]] [INFO] 4PZC1vVWJW=running
[[16:55:43]] [INFO] Executing action 72/482: Tap on Text: "Find"
[[16:55:43]] [SUCCESS] Screenshot refreshed
[[16:55:43]] [INFO] Refreshing screenshot...
[[16:55:43]] [INFO] bGo3feCwBQ=pass
[[16:55:41]] [SUCCESS] Screenshot refreshed successfully
[[16:55:39]] [INFO] bGo3feCwBQ=running
[[16:55:39]] [INFO] Executing action 71/482: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[16:55:39]] [SUCCESS] Screenshot refreshed
[[16:55:39]] [INFO] Refreshing screenshot...
[[16:55:36]] [SUCCESS] Screenshot refreshed successfully
[[16:55:35]] [INFO] Executing action 70/482: Swipe from (50%, 70%) to (50%, 30%)
[[16:55:35]] [SUCCESS] Screenshot refreshed
[[16:55:35]] [INFO] Refreshing screenshot...
[[16:55:35]] [INFO] F0gZF1jEnT=pass
[[16:55:33]] [SUCCESS] Screenshot refreshed successfully
[[16:55:32]] [INFO] F0gZF1jEnT=running
[[16:55:32]] [INFO] Executing action 69/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[16:55:32]] [SUCCESS] Screenshot refreshed
[[16:55:32]] [INFO] Refreshing screenshot...
[[16:55:32]] [INFO] EDHl0X27Wi=pass
[[16:55:31]] [SUCCESS] Screenshot refreshed successfully
[[16:55:00]] [INFO] EDHl0X27Wi=running
[[16:55:00]] [INFO] Executing action 68/482: Wait till xpath=//android.view.View[@content-desc="txtHomeGreetingText"]
[[16:55:00]] [SUCCESS] Screenshot refreshed
[[16:55:00]] [INFO] Refreshing screenshot...
[[16:55:00]] [INFO] IvqPpScAJa=pass
[[16:54:56]] [SUCCESS] Screenshot refreshed successfully
[[16:54:55]] [INFO] IvqPpScAJa=running
[[16:54:55]] [INFO] Executing action 67/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 1 of 5")]
[[16:54:55]] [SUCCESS] Screenshot refreshed
[[16:54:55]] [INFO] Refreshing screenshot...
[[16:54:55]] [SUCCESS] Screenshot refreshed
[[16:54:55]] [INFO] Refreshing screenshot...
[[16:54:54]] [SUCCESS] Screenshot refreshed successfully
[[16:54:53]] [INFO] Executing Multi Step action step 7/7: Tap on element with xpath: //android.widget.Button[@text="Sign in"]
[[16:54:53]] [SUCCESS] Screenshot refreshed
[[16:54:53]] [INFO] Refreshing screenshot...
[[16:54:52]] [SUCCESS] Screenshot refreshed successfully
[[16:54:51]] [INFO] Executing Multi Step action step 6/7: Input text: "Wonderbaby@6"
[[16:54:51]] [SUCCESS] Screenshot refreshed
[[16:54:51]] [INFO] Refreshing screenshot...
[[16:54:50]] [SUCCESS] Screenshot refreshed successfully
[[16:54:49]] [INFO] Executing Multi Step action step 5/7: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[16:54:49]] [SUCCESS] Screenshot refreshed
[[16:54:49]] [INFO] Refreshing screenshot...
[[16:54:48]] [SUCCESS] Screenshot refreshed successfully
[[16:54:47]] [INFO] Executing Multi Step action step 4/7: Tap on element with xpath: //android.widget.Button[@text="Continue to password"]
[[16:54:47]] [SUCCESS] Screenshot refreshed
[[16:54:47]] [INFO] Refreshing screenshot...
[[16:54:46]] [SUCCESS] Screenshot refreshed successfully
[[16:54:45]] [INFO] Executing Multi Step action step 3/7: Input text: "<EMAIL>"
[[16:54:45]] [SUCCESS] Screenshot refreshed
[[16:54:45]] [INFO] Refreshing screenshot...
[[16:54:44]] [SUCCESS] Screenshot refreshed successfully
[[16:54:43]] [INFO] Executing Multi Step action step 2/7: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[16:54:43]] [SUCCESS] Screenshot refreshed
[[16:54:43]] [INFO] Refreshing screenshot...
[[16:54:40]] [SUCCESS] Screenshot refreshed successfully
[[16:54:40]] [INFO] Executing Multi Step action step 1/7: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[16:54:40]] [INFO] Loaded 7 steps from test case: Kmart-Signin-AU-ANDROID
[[16:54:40]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[16:54:40]] [INFO] ZGVncEc5o1=running
[[16:54:40]] [INFO] Executing action 66/482: Execute Test Case: Kmart-Signin-AU-ANDROID (7 steps)
[[16:54:40]] [SUCCESS] Screenshot refreshed
[[16:54:40]] [INFO] Refreshing screenshot...
[[16:54:40]] [INFO] WlISsMf9QA=pass
[[16:54:36]] [SUCCESS] Screenshot refreshed successfully
[[16:54:35]] [INFO] WlISsMf9QA=running
[[16:54:35]] [INFO] Executing action 65/482: Tap on element with xpath: //android.widget.Button[@content-desc="txtLog in"]
[[16:54:35]] [SUCCESS] Screenshot refreshed
[[16:54:35]] [INFO] Refreshing screenshot...
[[16:54:35]] [INFO] IvqPpScAJa=pass
[[16:54:34]] [SUCCESS] Screenshot refreshed successfully
[[16:54:33]] [INFO] IvqPpScAJa=running
[[16:54:33]] [INFO] Executing action 64/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 3 of 5")]
[[16:54:33]] [SUCCESS] Screenshot refreshed
[[16:54:33]] [INFO] Refreshing screenshot...
[[16:54:33]] [INFO] bGo3feCwBQ=pass
[[16:54:30]] [SUCCESS] Screenshot refreshed successfully
[[16:54:29]] [INFO] bGo3feCwBQ=running
[[16:54:29]] [INFO] Executing action 63/482: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[16:54:29]] [SUCCESS] Screenshot refreshed
[[16:54:29]] [INFO] Refreshing screenshot...
[[16:54:27]] [SUCCESS] Screenshot refreshed successfully
[[16:54:26]] [INFO] Executing action 62/482: Swipe from (50%, 70%) to (50%, 30%)
[[16:54:26]] [SUCCESS] Screenshot refreshed
[[16:54:26]] [INFO] Refreshing screenshot...
[[16:54:26]] [INFO] F0gZF1jEnT=pass
[[16:54:25]] [SUCCESS] Screenshot refreshed successfully
[[16:54:24]] [INFO] F0gZF1jEnT=running
[[16:54:24]] [INFO] Executing action 61/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[16:54:24]] [SUCCESS] Screenshot refreshed
[[16:54:24]] [INFO] Refreshing screenshot...
[[16:54:24]] [INFO] EDHl0X27Wi=pass
[[16:54:19]] [SUCCESS] Screenshot refreshed successfully
[[16:54:17]] [INFO] EDHl0X27Wi=running
[[16:54:17]] [INFO] Executing action 60/482: Wait till xpath=//android.view.View[@content-desc="txtHomeGreetingText"]
[[16:54:17]] [SUCCESS] Screenshot refreshed
[[16:54:17]] [INFO] Refreshing screenshot...
[[16:54:17]] [SUCCESS] Screenshot refreshed
[[16:54:17]] [INFO] Refreshing screenshot...
[[16:54:16]] [SUCCESS] Screenshot refreshed successfully
[[16:54:15]] [INFO] Executing Multi Step action step 7/7: Tap on element with xpath: //android.widget.Button[@text="Sign in"]
[[16:54:15]] [SUCCESS] Screenshot refreshed
[[16:54:15]] [INFO] Refreshing screenshot...
[[16:54:14]] [SUCCESS] Screenshot refreshed successfully
[[16:54:13]] [INFO] Executing Multi Step action step 6/7: Input text: "Wonderbaby@6"
[[16:54:13]] [SUCCESS] Screenshot refreshed
[[16:54:13]] [INFO] Refreshing screenshot...
[[16:54:12]] [SUCCESS] Screenshot refreshed successfully
[[16:54:11]] [INFO] Executing Multi Step action step 5/7: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[16:54:11]] [SUCCESS] Screenshot refreshed
[[16:54:11]] [INFO] Refreshing screenshot...
[[16:54:10]] [SUCCESS] Screenshot refreshed successfully
[[16:54:09]] [INFO] Executing Multi Step action step 4/7: Tap on element with xpath: //android.widget.Button[@text="Continue to password"]
[[16:54:09]] [SUCCESS] Screenshot refreshed
[[16:54:09]] [INFO] Refreshing screenshot...
[[16:54:07]] [SUCCESS] Screenshot refreshed successfully
[[16:54:07]] [INFO] Executing Multi Step action step 3/7: Input text: "<EMAIL>"
[[16:54:07]] [SUCCESS] Screenshot refreshed
[[16:54:07]] [INFO] Refreshing screenshot...
[[16:54:06]] [SUCCESS] Screenshot refreshed successfully
[[16:54:05]] [INFO] Executing Multi Step action step 2/7: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[16:54:05]] [SUCCESS] Screenshot refreshed
[[16:54:05]] [INFO] Refreshing screenshot...
[[16:54:02]] [SUCCESS] Screenshot refreshed successfully
[[16:54:02]] [INFO] Executing Multi Step action step 1/7: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[16:54:02]] [INFO] Loaded 7 steps from test case: Kmart-Signin-AU-ANDROID
[[16:54:02]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[16:54:02]] [INFO] nshEZeNkzs=running
[[16:54:02]] [INFO] Executing action 59/482: Execute Test Case: Kmart-Signin-AU-ANDROID (7 steps)
[[16:54:02]] [SUCCESS] Screenshot refreshed
[[16:54:02]] [INFO] Refreshing screenshot...
[[16:54:02]] [INFO] qA1ap4n1m4=pass
[[16:53:58]] [SUCCESS] Screenshot refreshed successfully
[[16:53:57]] [INFO] qA1ap4n1m4=running
[[16:53:57]] [INFO] Executing action 58/482: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[16:53:57]] [SUCCESS] Screenshot refreshed
[[16:53:57]] [INFO] Refreshing screenshot...
[[16:53:57]] [SUCCESS] Screenshot refreshed
[[16:53:57]] [INFO] Refreshing screenshot...
[[16:53:53]] [SUCCESS] Screenshot refreshed successfully
[[16:53:53]] [INFO] Executing Multi Step action step 6/6: Tap on element with accessibility_id: txtOnePassOnboardingSkipForNow
[[16:53:53]] [SUCCESS] Screenshot refreshed
[[16:53:53]] [INFO] Refreshing screenshot...
[[16:53:52]] [SUCCESS] Screenshot refreshed successfully
[[16:53:51]] [INFO] Executing Multi Step action step 5/6: Tap on element with accessibility_id: btnMayBeLater
[[16:53:51]] [SUCCESS] Screenshot refreshed
[[16:53:51]] [INFO] Refreshing screenshot...
[[16:53:49]] [SUCCESS] Screenshot refreshed successfully
[[16:53:48]] [INFO] Executing Multi Step action step 4/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[16:53:48]] [SUCCESS] Screenshot refreshed
[[16:53:48]] [INFO] Refreshing screenshot...
[[16:53:44]] [SUCCESS] Screenshot refreshed successfully
[[16:53:43]] [INFO] Executing Multi Step action step 3/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[16:53:43]] [SUCCESS] Screenshot refreshed
[[16:53:43]] [INFO] Refreshing screenshot...
[[16:53:42]] [SUCCESS] Screenshot refreshed successfully
[[16:53:40]] [INFO] Executing Multi Step action step 2/6: Launch app: au.com.kmart
[[16:53:40]] [SUCCESS] Screenshot refreshed
[[16:53:40]] [INFO] Refreshing screenshot...
[[16:53:35]] [INFO] Executing Multi Step action step 1/6: Android Function: clear_app - Package: au.com.kmart
[[16:53:35]] [INFO] Loaded 6 steps from test case: Onboarding-Start-AU
[[16:53:35]] [INFO] Loading steps for Multi Step action: Onboarding-Start-AU
[[16:53:35]] [INFO] Executing action 57/482: Execute Test Case: Onboarding-Start-AU (6 steps)
[[16:53:33]] [INFO] === RETRYING TEST CASE: All_Sign_ins_AUANDROID_20250715202114.json (Attempt 2 of 3) ===
[[16:53:33]] [INFO] g8u66qfKkX=fail
[[16:53:33]] [ERROR] Action 108 failed: Element with xpath '//android.view.View[@text="Delivery"]' not found within timeout of 25.0 seconds
[[16:53:02]] [SUCCESS] Screenshot refreshed successfully
[[16:53:01]] [INFO] g8u66qfKkX=running
[[16:53:01]] [INFO] Executing action 108/482: Wait till xpath=//android.view.View[@text="Delivery"]
[[16:53:01]] [SUCCESS] Screenshot refreshed
[[16:53:01]] [INFO] Refreshing screenshot...
[[16:53:01]] [INFO] V9ldRojdyD=pass
[[16:52:16]] [SUCCESS] Screenshot refreshed successfully
[[16:52:15]] [INFO] V9ldRojdyD=running
[[16:52:15]] [INFO] Executing action 107/482: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[16:52:15]] [SUCCESS] Screenshot refreshed
[[16:52:15]] [INFO] Refreshing screenshot...
[[16:52:15]] [INFO] F0gZF1jEnT=pass
[[16:52:14]] [SUCCESS] Screenshot refreshed successfully
[[16:52:13]] [INFO] F0gZF1jEnT=running
[[16:52:13]] [INFO] Executing action 106/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[16:52:13]] [SUCCESS] Screenshot refreshed
[[16:52:13]] [INFO] Refreshing screenshot...
[[16:52:13]] [INFO] XcWXIMtv1E=pass
[[16:52:07]] [SUCCESS] Screenshot refreshed successfully
[[16:52:07]] [INFO] XcWXIMtv1E=running
[[16:52:07]] [INFO] Executing action 105/482: Wait for 5 ms
[[16:52:07]] [SUCCESS] Screenshot refreshed
[[16:52:07]] [INFO] Refreshing screenshot...
[[16:52:07]] [INFO] S1cQQxksEj=pass
[[16:52:05]] [SUCCESS] Screenshot refreshed successfully
[[16:52:05]] [INFO] S1cQQxksEj=running
[[16:52:05]] [INFO] Executing action 104/482: Tap on element with xpath: //android.widget.Button[@text="Add to bag"]
[[16:52:05]] [SUCCESS] Screenshot refreshed
[[16:52:05]] [INFO] Refreshing screenshot...
[[16:52:05]] [INFO] K2w9XUGwnb=pass
[[16:52:01]] [SUCCESS] Screenshot refreshed successfully
[[16:52:01]] [INFO] K2w9XUGwnb=running
[[16:52:01]] [INFO] Executing action 103/482: Swipe up till element xpath: "//android.widget.Button[@text="Add to bag"]" is visible
[[16:52:01]] [SUCCESS] Screenshot refreshed
[[16:52:01]] [INFO] Refreshing screenshot...
[[16:52:01]] [INFO] BTYxjEaZEk=pass
[[16:51:59]] [SUCCESS] Screenshot refreshed successfully
[[16:51:58]] [INFO] BTYxjEaZEk=running
[[16:51:58]] [INFO] Executing action 102/482: Tap on element with xpath: ((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1]
[[16:51:58]] [SUCCESS] Screenshot refreshed
[[16:51:58]] [INFO] Refreshing screenshot...
[[16:51:58]] [INFO] YC6bBrKQgq=pass
[[16:51:52]] [SUCCESS] Screenshot refreshed successfully
[[16:51:51]] [INFO] YC6bBrKQgq=running
[[16:51:51]] [INFO] Executing action 101/482: Wait till xpath=//android.widget.Button[@text="Filter"]
[[16:51:51]] [SUCCESS] Screenshot refreshed
[[16:51:51]] [INFO] Refreshing screenshot...
[[16:51:51]] [INFO] GMdwqXyeMs=pass
[[16:51:50]] [SUCCESS] Screenshot refreshed successfully
[[16:51:49]] [INFO] GMdwqXyeMs=running
[[16:51:49]] [INFO] Executing action 100/482: Android Function: send_key_event - Key Event: ENTER
[[16:51:49]] [SUCCESS] Screenshot refreshed
[[16:51:49]] [INFO] Refreshing screenshot...
[[16:51:49]] [INFO] aRgHcQcLDP=pass
[[16:51:48]] [SUCCESS] Screenshot refreshed successfully
[[16:51:47]] [INFO] aRgHcQcLDP=running
[[16:51:47]] [INFO] Executing action 99/482: Input text: "Uno card"
[[16:51:47]] [SUCCESS] Screenshot refreshed
[[16:51:47]] [INFO] Refreshing screenshot...
[[16:51:47]] [INFO] 4PZC1vVWJW=pass
[[16:51:43]] [SUCCESS] Screenshot refreshed successfully
[[16:51:42]] [INFO] 4PZC1vVWJW=running
[[16:51:42]] [INFO] Executing action 98/482: Tap on Text: "Find"
[[16:51:42]] [SUCCESS] Screenshot refreshed
[[16:51:42]] [INFO] Refreshing screenshot...
[[16:51:42]] [INFO] F0gZF1jEnT=pass
[[16:51:40]] [SUCCESS] Screenshot refreshed successfully
[[16:51:39]] [INFO] F0gZF1jEnT=running
[[16:51:39]] [INFO] Executing action 97/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 1 of 5")]
[[16:51:39]] [SUCCESS] Screenshot refreshed
[[16:51:39]] [INFO] Refreshing screenshot...
[[16:51:39]] [INFO] bGo3feCwBQ=pass
[[16:51:37]] [SUCCESS] Screenshot refreshed successfully
[[16:51:36]] [INFO] bGo3feCwBQ=running
[[16:51:36]] [INFO] Executing action 96/482: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[16:51:36]] [SUCCESS] Screenshot refreshed
[[16:51:36]] [INFO] Refreshing screenshot...
[[16:51:34]] [SUCCESS] Screenshot refreshed successfully
[[16:51:32]] [INFO] Executing action 95/482: Swipe from (50%, 70%) to (50%, 30%)
[[16:51:32]] [SUCCESS] Screenshot refreshed
[[16:51:32]] [INFO] Refreshing screenshot...
[[16:51:32]] [SUCCESS] Screenshot refreshed
[[16:51:32]] [INFO] Refreshing screenshot...
[[16:51:31]] [SUCCESS] Screenshot refreshed successfully
[[16:51:10]] [INFO] Executing Multi Step action step 7/7: Tap on element with xpath: //android.widget.Button[@text="Sign in"]
[[16:51:10]] [SUCCESS] Screenshot refreshed
[[16:51:10]] [INFO] Refreshing screenshot...
[[16:51:08]] [SUCCESS] Screenshot refreshed successfully
[[16:51:08]] [INFO] Executing Multi Step action step 6/7: Input text: "Wonderbaby@6"
[[16:51:08]] [SUCCESS] Screenshot refreshed
[[16:51:08]] [INFO] Refreshing screenshot...
[[16:51:07]] [SUCCESS] Screenshot refreshed successfully
[[16:51:06]] [INFO] Executing Multi Step action step 5/7: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[16:51:06]] [SUCCESS] Screenshot refreshed
[[16:51:06]] [INFO] Refreshing screenshot...
[[16:51:05]] [SUCCESS] Screenshot refreshed successfully
[[16:51:04]] [INFO] Executing Multi Step action step 4/7: Tap on element with xpath: //android.widget.Button[@text="Continue to password"]
[[16:51:04]] [SUCCESS] Screenshot refreshed
[[16:51:04]] [INFO] Refreshing screenshot...
[[16:51:02]] [SUCCESS] Screenshot refreshed successfully
[[16:51:02]] [INFO] Executing Multi Step action step 3/7: Input text: "<EMAIL>"
[[16:51:02]] [SUCCESS] Screenshot refreshed
[[16:51:02]] [INFO] Refreshing screenshot...
[[16:51:00]] [SUCCESS] Screenshot refreshed successfully
[[16:50:40]] [INFO] Executing Multi Step action step 2/7: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[16:50:40]] [SUCCESS] Screenshot refreshed
[[16:50:40]] [INFO] Refreshing screenshot...
[[16:50:37]] [SUCCESS] Screenshot refreshed successfully
[[16:50:37]] [INFO] Executing Multi Step action step 1/7: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[16:50:37]] [INFO] Loaded 7 steps from test case: Kmart-Signin-AU-ANDROID
[[16:50:37]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[16:50:37]] [INFO] kR0cfY8jim=running
[[16:50:37]] [INFO] Executing action 94/482: Execute Test Case: Kmart-Signin-AU-ANDROID (7 steps)
[[16:50:37]] [SUCCESS] Screenshot refreshed
[[16:50:37]] [INFO] Refreshing screenshot...
[[16:50:37]] [INFO] L6wTorOX8B=pass
[[16:50:33]] [SUCCESS] Screenshot refreshed successfully
[[16:50:33]] [INFO] L6wTorOX8B=running
[[16:50:33]] [INFO] Executing action 93/482: Tap on element with xpath: //android.widget.Button[@content-desc="txtMoreAccountCtaSignIn"]
[[16:50:33]] [SUCCESS] Screenshot refreshed
[[16:50:33]] [INFO] Refreshing screenshot...
[[16:50:33]] [INFO] F0gZF1jEnT=pass
[[16:50:31]] [SUCCESS] Screenshot refreshed successfully
[[16:50:23]] [INFO] F0gZF1jEnT=running
[[16:50:23]] [INFO] Executing action 92/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[16:50:23]] [SUCCESS] Screenshot refreshed
[[16:50:23]] [INFO] Refreshing screenshot...
[[16:50:23]] [INFO] bGo3feCwBQ=pass
[[16:50:20]] [SUCCESS] Screenshot refreshed successfully
[[16:50:20]] [INFO] bGo3feCwBQ=running
[[16:50:20]] [INFO] Executing action 91/482: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[16:50:20]] [SUCCESS] Screenshot refreshed
[[16:50:20]] [INFO] Refreshing screenshot...
[[16:50:17]] [SUCCESS] Screenshot refreshed successfully
[[16:50:16]] [INFO] Executing action 90/482: Swipe from (50%, 70%) to (50%, 30%)
[[16:50:16]] [SUCCESS] Screenshot refreshed
[[16:50:16]] [INFO] Refreshing screenshot...
[[16:50:16]] [INFO] F0gZF1jEnT=pass
[[16:50:15]] [SUCCESS] Screenshot refreshed successfully
[[16:50:14]] [INFO] F0gZF1jEnT=running
[[16:50:14]] [INFO] Executing action 89/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[16:50:14]] [SUCCESS] Screenshot refreshed
[[16:50:14]] [INFO] Refreshing screenshot...
[[16:50:14]] [INFO] XcWXIMtv1E=pass
[[16:50:09]] [SUCCESS] Screenshot refreshed successfully
[[16:50:08]] [INFO] XcWXIMtv1E=running
[[16:50:08]] [INFO] Executing action 88/482: Wait for 5 ms
[[16:50:08]] [SUCCESS] Screenshot refreshed
[[16:50:08]] [INFO] Refreshing screenshot...
[[16:50:08]] [INFO] g6SvrkfA9z=pass
[[16:50:07]] [SUCCESS] Screenshot refreshed successfully
[[16:50:06]] [INFO] g6SvrkfA9z=running
[[16:50:06]] [INFO] Executing action 87/482: Android Function: send_key_event - Key Event: ENTER
[[16:50:06]] [SUCCESS] Screenshot refreshed
[[16:50:06]] [INFO] Refreshing screenshot...
[[16:50:06]] [INFO] N2yjynioko=pass
[[16:50:05]] [SUCCESS] Screenshot refreshed successfully
[[16:50:04]] [INFO] N2yjynioko=running
[[16:50:04]] [INFO] Executing action 86/482: Input text: "Wonderbaby@5"
[[16:50:04]] [SUCCESS] Screenshot refreshed
[[16:50:04]] [INFO] Refreshing screenshot...
[[16:50:04]] [INFO] SHaIduBnay=pass
[[16:50:02]] [SUCCESS] Screenshot refreshed successfully
[[16:50:01]] [INFO] SHaIduBnay=running
[[16:50:01]] [INFO] Executing action 85/482: Tap on element with xpath: //android.widget.EditText[@resource-id="password-input"]
[[16:50:01]] [SUCCESS] Screenshot refreshed
[[16:50:01]] [INFO] Refreshing screenshot...
[[16:50:01]] [INFO] F4PAASAS6q=pass
[[16:49:31]] [SUCCESS] Screenshot refreshed successfully
[[16:49:30]] [INFO] F4PAASAS6q=running
[[16:49:30]] [INFO] Executing action 84/482: Tap if locator exists: xpath="//android.widget.CheckBox[@resource-id="recaptcha-anchor"]"
[[16:49:30]] [SUCCESS] Screenshot refreshed
[[16:49:30]] [INFO] Refreshing screenshot...
[[16:49:30]] [INFO] KGagCNh4k8=pass
[[16:49:29]] [SUCCESS] Screenshot refreshed successfully
[[16:49:29]] [INFO] KGagCNh4k8=running
[[16:49:29]] [INFO] Executing action 83/482: Android Function: send_key_event - Key Event: ENTER
[[16:49:29]] [SUCCESS] Screenshot refreshed
[[16:49:29]] [INFO] Refreshing screenshot...
[[16:49:29]] [INFO] wuIMlAwYVA=pass
[[16:49:27]] [SUCCESS] Screenshot refreshed successfully
[[16:49:26]] [INFO] wuIMlAwYVA=running
[[16:49:26]] [INFO] Executing action 82/482: Input text: "<EMAIL>"
[[16:49:26]] [SUCCESS] Screenshot refreshed
[[16:49:26]] [INFO] Refreshing screenshot...
[[16:49:26]] [INFO] 50Z2jrodNd=pass
[[16:49:25]] [SUCCESS] Screenshot refreshed successfully
[[16:49:25]] [INFO] 50Z2jrodNd=running
[[16:49:25]] [INFO] Executing action 81/482: Tap on element with xpath: //android.widget.EditText[@resource-id="email-input"]
[[16:49:25]] [SUCCESS] Screenshot refreshed
[[16:49:25]] [INFO] Refreshing screenshot...
[[16:49:25]] [INFO] VK2oI6mXSB=pass
[[16:49:20]] [SUCCESS] Screenshot refreshed successfully
[[16:49:19]] [INFO] VK2oI6mXSB=running
[[16:49:19]] [INFO] Executing action 80/482: Wait till xpath=//android.widget.EditText[@resource-id="email-input"]
[[16:49:19]] [SUCCESS] Screenshot refreshed
[[16:49:19]] [INFO] Refreshing screenshot...
[[16:49:19]] [INFO] 4PZC1vVWJW=pass
[[16:49:16]] [SUCCESS] Screenshot refreshed successfully
[[16:49:15]] [INFO] 4PZC1vVWJW=running
[[16:49:15]] [INFO] Executing action 79/482: Tap on Text: "Sign"
[[16:49:15]] [SUCCESS] Screenshot refreshed
[[16:49:15]] [INFO] Refreshing screenshot...
[[16:49:15]] [INFO] mcscWdhpn2=pass
[[16:49:04]] [SUCCESS] Screenshot refreshed successfully
[[16:49:03]] [INFO] mcscWdhpn2=running
[[16:49:03]] [INFO] Executing action 78/482: Swipe up till element xpath: "//android.widget.TextView[contains(@text,"Already a member")]" is visible
[[16:49:03]] [SUCCESS] Screenshot refreshed
[[16:49:03]] [INFO] Refreshing screenshot...
[[16:49:03]] [INFO] 6zUBxjSFym=pass
[[16:48:26]] [SUCCESS] Screenshot refreshed successfully
[[16:48:26]] [INFO] 6zUBxjSFym=running
[[16:48:26]] [INFO] Executing action 77/482: Wait till xpath=//android.widget.TextView[contains(@text,"SKU")]
[[16:48:26]] [SUCCESS] Screenshot refreshed
[[16:48:26]] [INFO] Refreshing screenshot...
[[16:48:26]] [INFO] BTYxjEaZEk=pass
[[16:48:24]] [SUCCESS] Screenshot refreshed successfully
[[16:48:23]] [INFO] BTYxjEaZEk=running
[[16:48:23]] [INFO] Executing action 76/482: Tap on element with xpath: ((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1]
[[16:48:23]] [SUCCESS] Screenshot refreshed
[[16:48:23]] [INFO] Refreshing screenshot...
[[16:48:23]] [INFO] YC6bBrKQgq=pass
[[16:48:16]] [SUCCESS] Screenshot refreshed successfully
[[16:48:15]] [INFO] YC6bBrKQgq=running
[[16:48:15]] [INFO] Executing action 75/482: Wait till xpath=//android.widget.Button[@text="Filter"]
[[16:48:15]] [SUCCESS] Screenshot refreshed
[[16:48:15]] [INFO] Refreshing screenshot...
[[16:48:15]] [INFO] FLVgc6jpIu=pass
[[16:48:14]] [SUCCESS] Screenshot refreshed successfully
[[16:48:13]] [INFO] FLVgc6jpIu=running
[[16:48:13]] [INFO] Executing action 74/482: Android Function: send_key_event - Key Event: ENTER
[[16:48:13]] [SUCCESS] Screenshot refreshed
[[16:48:13]] [INFO] Refreshing screenshot...
[[16:48:13]] [INFO] aRgHcQcLDP=pass
[[16:48:12]] [SUCCESS] Screenshot refreshed successfully
[[16:48:11]] [INFO] aRgHcQcLDP=running
[[16:48:11]] [INFO] Executing action 73/482: Input text: "Uno card"
[[16:48:11]] [SUCCESS] Screenshot refreshed
[[16:48:11]] [INFO] Refreshing screenshot...
[[16:48:11]] [INFO] 4PZC1vVWJW=pass
[[16:48:07]] [SUCCESS] Screenshot refreshed successfully
[[16:48:05]] [INFO] 4PZC1vVWJW=running
[[16:48:05]] [INFO] Executing action 72/482: Tap on Text: "Find"
[[16:48:05]] [SUCCESS] Screenshot refreshed
[[16:48:05]] [INFO] Refreshing screenshot...
[[16:48:05]] [INFO] bGo3feCwBQ=pass
[[16:48:03]] [SUCCESS] Screenshot refreshed successfully
[[16:48:02]] [INFO] bGo3feCwBQ=running
[[16:48:02]] [INFO] Executing action 71/482: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[16:48:02]] [SUCCESS] Screenshot refreshed
[[16:48:02]] [INFO] Refreshing screenshot...
[[16:47:59]] [SUCCESS] Screenshot refreshed successfully
[[16:47:58]] [INFO] Executing action 70/482: Swipe from (50%, 70%) to (50%, 30%)
[[16:47:58]] [SUCCESS] Screenshot refreshed
[[16:47:58]] [INFO] Refreshing screenshot...
[[16:47:58]] [INFO] F0gZF1jEnT=pass
[[16:47:57]] [SUCCESS] Screenshot refreshed successfully
[[16:47:56]] [INFO] F0gZF1jEnT=running
[[16:47:56]] [INFO] Executing action 69/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[16:47:56]] [SUCCESS] Screenshot refreshed
[[16:47:56]] [INFO] Refreshing screenshot...
[[16:47:56]] [INFO] EDHl0X27Wi=pass
[[16:47:54]] [SUCCESS] Screenshot refreshed successfully
[[16:47:53]] [INFO] EDHl0X27Wi=running
[[16:47:53]] [INFO] Executing action 68/482: Wait till xpath=//android.view.View[@content-desc="txtHomeGreetingText"]
[[16:47:53]] [SUCCESS] Screenshot refreshed
[[16:47:53]] [INFO] Refreshing screenshot...
[[16:47:53]] [INFO] IvqPpScAJa=pass
[[16:47:50]] [SUCCESS] Screenshot refreshed successfully
[[16:47:49]] [INFO] IvqPpScAJa=running
[[16:47:49]] [INFO] Executing action 67/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 1 of 5")]
[[16:47:49]] [SUCCESS] Screenshot refreshed
[[16:47:49]] [INFO] Refreshing screenshot...
[[16:47:49]] [SUCCESS] Screenshot refreshed
[[16:47:49]] [INFO] Refreshing screenshot...
[[16:47:48]] [SUCCESS] Screenshot refreshed successfully
[[16:47:47]] [INFO] Executing Multi Step action step 7/7: Tap on element with xpath: //android.widget.Button[@text="Sign in"]
[[16:47:47]] [SUCCESS] Screenshot refreshed
[[16:47:47]] [INFO] Refreshing screenshot...
[[16:47:45]] [SUCCESS] Screenshot refreshed successfully
[[16:47:45]] [INFO] Executing Multi Step action step 6/7: Input text: "Wonderbaby@6"
[[16:47:45]] [SUCCESS] Screenshot refreshed
[[16:47:45]] [INFO] Refreshing screenshot...
[[16:47:43]] [SUCCESS] Screenshot refreshed successfully
[[16:47:43]] [INFO] Executing Multi Step action step 5/7: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[16:47:43]] [SUCCESS] Screenshot refreshed
[[16:47:43]] [INFO] Refreshing screenshot...
[[16:47:41]] [SUCCESS] Screenshot refreshed successfully
[[16:47:41]] [INFO] Executing Multi Step action step 4/7: Tap on element with xpath: //android.widget.Button[@text="Continue to password"]
[[16:47:41]] [SUCCESS] Screenshot refreshed
[[16:47:41]] [INFO] Refreshing screenshot...
[[16:47:39]] [SUCCESS] Screenshot refreshed successfully
[[16:47:38]] [INFO] Executing Multi Step action step 3/7: Input text: "<EMAIL>"
[[16:47:38]] [SUCCESS] Screenshot refreshed
[[16:47:38]] [INFO] Refreshing screenshot...
[[16:47:37]] [SUCCESS] Screenshot refreshed successfully
[[16:47:36]] [INFO] Executing Multi Step action step 2/7: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[16:47:36]] [SUCCESS] Screenshot refreshed
[[16:47:36]] [INFO] Refreshing screenshot...
[[16:47:34]] [SUCCESS] Screenshot refreshed successfully
[[16:47:33]] [INFO] Executing Multi Step action step 1/7: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[16:47:33]] [INFO] Loaded 7 steps from test case: Kmart-Signin-AU-ANDROID
[[16:47:33]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[16:47:33]] [INFO] ZGVncEc5o1=running
[[16:47:33]] [INFO] Executing action 66/482: Execute Test Case: Kmart-Signin-AU-ANDROID (7 steps)
[[16:47:33]] [SUCCESS] Screenshot refreshed
[[16:47:33]] [INFO] Refreshing screenshot...
[[16:47:33]] [INFO] WlISsMf9QA=pass
[[16:47:30]] [SUCCESS] Screenshot refreshed successfully
[[16:47:29]] [INFO] WlISsMf9QA=running
[[16:47:29]] [INFO] Executing action 65/482: Tap on element with xpath: //android.widget.Button[@content-desc="txtLog in"]
[[16:47:29]] [SUCCESS] Screenshot refreshed
[[16:47:29]] [INFO] Refreshing screenshot...
[[16:47:29]] [INFO] IvqPpScAJa=pass
[[16:47:28]] [SUCCESS] Screenshot refreshed successfully
[[16:47:27]] [INFO] IvqPpScAJa=running
[[16:47:27]] [INFO] Executing action 64/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 3 of 5")]
[[16:47:27]] [SUCCESS] Screenshot refreshed
[[16:47:27]] [INFO] Refreshing screenshot...
[[16:47:27]] [INFO] bGo3feCwBQ=pass
[[16:47:24]] [SUCCESS] Screenshot refreshed successfully
[[16:47:23]] [INFO] bGo3feCwBQ=running
[[16:47:23]] [INFO] Executing action 63/482: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[16:47:23]] [SUCCESS] Screenshot refreshed
[[16:47:23]] [INFO] Refreshing screenshot...
[[16:47:20]] [SUCCESS] Screenshot refreshed successfully
[[16:47:19]] [INFO] Executing action 62/482: Swipe from (50%, 70%) to (50%, 30%)
[[16:47:19]] [SUCCESS] Screenshot refreshed
[[16:47:19]] [INFO] Refreshing screenshot...
[[16:47:19]] [INFO] F0gZF1jEnT=pass
[[16:47:18]] [SUCCESS] Screenshot refreshed successfully
[[16:47:17]] [INFO] F0gZF1jEnT=running
[[16:47:17]] [INFO] Executing action 61/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[16:47:17]] [SUCCESS] Screenshot refreshed
[[16:47:17]] [INFO] Refreshing screenshot...
[[16:47:17]] [INFO] EDHl0X27Wi=pass
[[16:47:13]] [SUCCESS] Screenshot refreshed successfully
[[16:47:11]] [INFO] EDHl0X27Wi=running
[[16:47:11]] [INFO] Executing action 60/482: Wait till xpath=//android.view.View[@content-desc="txtHomeGreetingText"]
[[16:47:11]] [SUCCESS] Screenshot refreshed
[[16:47:11]] [INFO] Refreshing screenshot...
[[16:47:11]] [SUCCESS] Screenshot refreshed
[[16:47:11]] [INFO] Refreshing screenshot...
[[16:47:10]] [SUCCESS] Screenshot refreshed successfully
[[16:47:09]] [INFO] Executing Multi Step action step 7/7: Tap on element with xpath: //android.widget.Button[@text="Sign in"]
[[16:47:09]] [SUCCESS] Screenshot refreshed
[[16:47:09]] [INFO] Refreshing screenshot...
[[16:47:08]] [SUCCESS] Screenshot refreshed successfully
[[16:47:07]] [INFO] Executing Multi Step action step 6/7: Input text: "Wonderbaby@6"
[[16:47:07]] [SUCCESS] Screenshot refreshed
[[16:47:07]] [INFO] Refreshing screenshot...
[[16:47:06]] [SUCCESS] Screenshot refreshed successfully
[[16:47:05]] [INFO] Executing Multi Step action step 5/7: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[16:47:05]] [SUCCESS] Screenshot refreshed
[[16:47:05]] [INFO] Refreshing screenshot...
[[16:47:04]] [SUCCESS] Screenshot refreshed successfully
[[16:47:03]] [INFO] Executing Multi Step action step 4/7: Tap on element with xpath: //android.widget.Button[@text="Continue to password"]
[[16:47:03]] [SUCCESS] Screenshot refreshed
[[16:47:03]] [INFO] Refreshing screenshot...
[[16:47:02]] [SUCCESS] Screenshot refreshed successfully
[[16:47:01]] [INFO] Executing Multi Step action step 3/7: Input text: "<EMAIL>"
[[16:47:01]] [SUCCESS] Screenshot refreshed
[[16:47:01]] [INFO] Refreshing screenshot...
[[16:47:00]] [SUCCESS] Screenshot refreshed successfully
[[16:46:59]] [INFO] Executing Multi Step action step 2/7: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[16:46:59]] [SUCCESS] Screenshot refreshed
[[16:46:59]] [INFO] Refreshing screenshot...
[[16:46:56]] [SUCCESS] Screenshot refreshed successfully
[[16:46:55]] [INFO] Executing Multi Step action step 1/7: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[16:46:55]] [INFO] Loaded 7 steps from test case: Kmart-Signin-AU-ANDROID
[[16:46:55]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[16:46:55]] [INFO] nshEZeNkzs=running
[[16:46:55]] [INFO] Executing action 59/482: Execute Test Case: Kmart-Signin-AU-ANDROID (7 steps)
[[16:46:55]] [SUCCESS] Screenshot refreshed
[[16:46:55]] [INFO] Refreshing screenshot...
[[16:46:55]] [INFO] qA1ap4n1m4=pass
[[16:46:52]] [SUCCESS] Screenshot refreshed successfully
[[16:46:50]] [INFO] qA1ap4n1m4=running
[[16:46:50]] [INFO] Executing action 58/482: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[16:46:50]] [SUCCESS] Screenshot refreshed
[[16:46:50]] [INFO] Refreshing screenshot...
[[16:46:50]] [SUCCESS] Screenshot refreshed
[[16:46:50]] [INFO] Refreshing screenshot...
[[16:46:46]] [SUCCESS] Screenshot refreshed successfully
[[16:46:46]] [INFO] Executing Multi Step action step 6/6: Tap on element with accessibility_id: txtOnePassOnboardingSkipForNow
[[16:46:46]] [SUCCESS] Screenshot refreshed
[[16:46:46]] [INFO] Refreshing screenshot...
[[16:46:45]] [SUCCESS] Screenshot refreshed successfully
[[16:46:44]] [INFO] Executing Multi Step action step 5/6: Tap on element with accessibility_id: btnMayBeLater
[[16:46:44]] [SUCCESS] Screenshot refreshed
[[16:46:44]] [INFO] Refreshing screenshot...
[[16:46:42]] [SUCCESS] Screenshot refreshed successfully
[[16:46:41]] [INFO] Executing Multi Step action step 4/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[16:46:41]] [SUCCESS] Screenshot refreshed
[[16:46:41]] [INFO] Refreshing screenshot...
[[16:46:37]] [SUCCESS] Screenshot refreshed successfully
[[16:46:36]] [INFO] Executing Multi Step action step 3/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[16:46:36]] [SUCCESS] Screenshot refreshed
[[16:46:36]] [INFO] Refreshing screenshot...
[[16:46:35]] [SUCCESS] Screenshot refreshed successfully
[[16:46:33]] [INFO] Executing Multi Step action step 2/6: Launch app: au.com.kmart
[[16:46:33]] [SUCCESS] Screenshot refreshed
[[16:46:33]] [INFO] Refreshing screenshot...
[[16:46:31]] [SUCCESS] Screenshot refreshed successfully
[[16:46:10]] [INFO] Executing Multi Step action step 1/6: Android Function: clear_app - Package: au.com.kmart
[[16:46:10]] [INFO] Loaded 6 steps from test case: Onboarding-Start-AU
[[16:46:10]] [INFO] Loading steps for Multi Step action: Onboarding-Start-AU
[[16:46:10]] [INFO] Executing action 57/482: Execute Test Case: Onboarding-Start-AU (6 steps)
[[16:46:10]] [SUCCESS] Screenshot refreshed
[[16:46:10]] [INFO] Refreshing screenshot...
[[16:46:10]] [INFO] BracBsfa3Y=pass
[[16:46:08]] [SUCCESS] Screenshot refreshed successfully
[[16:46:07]] [INFO] BracBsfa3Y=running
[[16:46:07]] [INFO] Executing action 56/482: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[16:46:07]] [SUCCESS] Screenshot refreshed
[[16:46:07]] [INFO] Refreshing screenshot...
[[16:46:07]] [INFO] s6tWdQ5URW=pass
[[16:46:02]] [SUCCESS] Screenshot refreshed successfully
[[16:46:02]] [INFO] s6tWdQ5URW=running
[[16:46:02]] [INFO] Executing action 55/482: Swipe from (50%, 70%) to (50%, 30%)
[[16:46:02]] [SUCCESS] Screenshot refreshed
[[16:46:02]] [INFO] Refreshing screenshot...
[[16:46:02]] [INFO] YuuQe2KupX=pass
[[16:46:00]] [SUCCESS] Screenshot refreshed successfully
[[16:45:39]] [INFO] YuuQe2KupX=running
[[16:45:39]] [INFO] Executing action 54/482: Android Function: send_key_event - Key Event: BACK
[[16:45:39]] [SUCCESS] Screenshot refreshed
[[16:45:39]] [INFO] Refreshing screenshot...
[[16:45:39]] [INFO] BracBsfa3Y=pass
[[16:45:38]] [SUCCESS] Screenshot refreshed successfully
[[16:45:37]] [INFO] BracBsfa3Y=running
[[16:45:37]] [INFO] Executing action 53/482: Tap on element with xpath: //android.widget.Button[@content-desc="txtCustomer Help"]
[[16:45:37]] [SUCCESS] Screenshot refreshed
[[16:45:37]] [INFO] Refreshing screenshot...
[[16:45:37]] [INFO] YuuQe2KupX=pass
[[16:45:36]] [SUCCESS] Screenshot refreshed successfully
[[16:45:36]] [INFO] YuuQe2KupX=running
[[16:45:36]] [INFO] Executing action 52/482: Android Function: send_key_event - Key Event: BACK
[[16:45:36]] [SUCCESS] Screenshot refreshed
[[16:45:36]] [INFO] Refreshing screenshot...
[[16:45:36]] [INFO] ePyaYpttQA=pass
[[16:45:34]] [SUCCESS] Screenshot refreshed successfully
[[16:45:34]] [INFO] ePyaYpttQA=running
[[16:45:34]] [INFO] Executing action 51/482: Check if element with xpath="//android.widget.TextView[@resource-id="android:id/content_preview_text"]" exists
[[16:45:34]] [SUCCESS] Screenshot refreshed
[[16:45:34]] [INFO] Refreshing screenshot...
[[16:45:34]] [INFO] BracBsfa3Y=pass
[[16:45:33]] [SUCCESS] Screenshot refreshed successfully
[[16:45:32]] [INFO] BracBsfa3Y=running
[[16:45:32]] [INFO] Executing action 50/482: Tap on element with xpath: //android.widget.Button[@content-desc="txtInvite a friend"]
[[16:45:32]] [SUCCESS] Screenshot refreshed
[[16:45:32]] [INFO] Refreshing screenshot...
[[16:45:32]] [INFO] YuuQe2KupX=pass
[[16:45:30]] [SUCCESS] Screenshot refreshed successfully
[[16:45:26]] [INFO] YuuQe2KupX=running
[[16:45:26]] [INFO] Executing action 49/482: Android Function: send_key_event - Key Event: BACK
[[16:45:26]] [SUCCESS] Screenshot refreshed
[[16:45:26]] [INFO] Refreshing screenshot...
[[16:45:26]] [INFO] 2BfJyzwQFx=pass
[[16:45:21]] [SUCCESS] Screenshot refreshed successfully
[[16:45:20]] [INFO] 2BfJyzwQFx=running
[[16:45:20]] [INFO] Executing action 48/482: Check if element with text="Cbd" exists
[[16:45:20]] [SUCCESS] Screenshot refreshed
[[16:45:20]] [INFO] Refreshing screenshot...
[[16:45:20]] [INFO] PgjJCrKFYo=pass
[[16:45:17]] [SUCCESS] Screenshot refreshed successfully
[[16:45:13]] [INFO] PgjJCrKFYo=running
[[16:45:13]] [INFO] Executing action 47/482: Tap on Text: "VIC"
[[16:45:13]] [SUCCESS] Screenshot refreshed
[[16:45:13]] [INFO] Refreshing screenshot...
[[16:45:13]] [INFO] YuuQe2KupX=pass
[[16:45:12]] [SUCCESS] Screenshot refreshed successfully
[[16:45:11]] [INFO] YuuQe2KupX=running
[[16:45:11]] [INFO] Executing action 46/482: Android Function: send_key_event - Key Event: ENTER
[[16:45:11]] [SUCCESS] Screenshot refreshed
[[16:45:11]] [INFO] Refreshing screenshot...
[[16:45:11]] [INFO] 3Si0csRNaw=pass
[[16:45:08]] [SUCCESS] Screenshot refreshed successfully
[[16:45:07]] [INFO] 3Si0csRNaw=running
[[16:45:07]] [INFO] Executing action 45/482: Input text: "3000"
[[16:45:07]] [SUCCESS] Screenshot refreshed
[[16:45:07]] [INFO] Refreshing screenshot...
[[16:45:07]] [INFO] BracBsfa3Y=pass
[[16:45:04]] [SUCCESS] Screenshot refreshed successfully
[[16:45:03]] [INFO] BracBsfa3Y=running
[[16:45:03]] [INFO] Executing action 44/482: Tap on Text: "Nearby"
[[16:45:03]] [SUCCESS] Screenshot refreshed
[[16:45:03]] [INFO] Refreshing screenshot...
[[16:45:01]] [SUCCESS] Screenshot refreshed successfully
[[16:45:01]] [INFO] Executing action 43/482: Tap if locator exists: xpath="//android.widget.Button[@resource-id="com.android.permissioncontroller:id/permission_allow_foreground_only_button"]"
[[16:45:01]] [SUCCESS] Screenshot refreshed
[[16:45:01]] [INFO] Refreshing screenshot...
[[16:45:01]] [INFO] BracBsfa3Y=pass
[[16:44:59]] [SUCCESS] Screenshot refreshed successfully
[[16:44:58]] [INFO] BracBsfa3Y=running
[[16:44:58]] [INFO] Executing action 42/482: Tap on element with xpath: //android.widget.Button[@content-desc="txtStore locator"]
[[16:44:58]] [SUCCESS] Screenshot refreshed
[[16:44:58]] [INFO] Refreshing screenshot...
[[16:44:58]] [INFO] s6tWdQ5URW=pass
[[16:44:54]] [SUCCESS] Screenshot refreshed successfully
[[16:44:50]] [INFO] s6tWdQ5URW=running
[[16:44:50]] [INFO] Executing action 41/482: Swipe from (50%, 70%) to (50%, 30%)
[[16:44:50]] [SUCCESS] Screenshot refreshed
[[16:44:50]] [INFO] Refreshing screenshot...
[[16:44:43]] [INFO] Executing action 40/482: Wait for 6 ms
[[16:44:43]] [SUCCESS] Screenshot refreshed
[[16:44:43]] [INFO] Refreshing screenshot...
[[16:44:43]] [INFO] Ds5GfNVb3x=pass
[[16:44:42]] [SUCCESS] Screenshot refreshed successfully
[[16:44:41]] [INFO] Ds5GfNVb3x=running
[[16:44:41]] [INFO] Executing action 39/482: Tap on element with accessibility_id: btnRemove
[[16:44:41]] [SUCCESS] Screenshot refreshed
[[16:44:41]] [INFO] Refreshing screenshot...
[[16:44:41]] [INFO] 3ZFgwFaiXp=pass
[[16:44:39]] [SUCCESS] Screenshot refreshed successfully
[[16:44:38]] [INFO] 3ZFgwFaiXp=running
[[16:44:38]] [INFO] Executing action 38/482: Tap on element with accessibility_id: Remove card
[[16:44:38]] [SUCCESS] Screenshot refreshed
[[16:44:38]] [INFO] Refreshing screenshot...
[[16:44:38]] [INFO] 40hnWPsQ9P=pass
[[16:44:37]] [SUCCESS] Screenshot refreshed successfully
[[16:44:36]] [INFO] 40hnWPsQ9P=running
[[16:44:36]] [INFO] Executing action 37/482: Tap on element with accessibility_id: btneditFlybuysCard
[[16:44:36]] [SUCCESS] Screenshot refreshed
[[16:44:36]] [INFO] Refreshing screenshot...
[[16:44:36]] [INFO] 40hnWPsQ9P=pass
[[16:44:34]] [SUCCESS] Screenshot refreshed successfully
[[16:44:32]] [INFO] 40hnWPsQ9P=running
[[16:44:32]] [INFO] Executing action 36/482: Wait till accessibility_id=btneditFlybuysCard
[[16:44:32]] [SUCCESS] Screenshot refreshed
[[16:44:32]] [INFO] Refreshing screenshot...
[[16:44:32]] [INFO] BracBsfa3Y=pass
[[16:44:29]] [SUCCESS] Screenshot refreshed successfully
[[16:44:28]] [INFO] BracBsfa3Y=running
[[16:44:28]] [INFO] Executing action 35/482: Tap on Text: "Flybuys"
[[16:44:28]] [SUCCESS] Screenshot refreshed
[[16:44:28]] [INFO] Refreshing screenshot...
[[16:44:28]] [INFO] 2M0KHOVecv=pass
[[16:44:27]] [SUCCESS] Screenshot refreshed successfully
[[16:44:26]] [INFO] 2M0KHOVecv=running
[[16:44:26]] [INFO] Executing action 34/482: Check if element with accessibility_id="txtMy Flybuys card" exists
[[16:44:26]] [SUCCESS] Screenshot refreshed
[[16:44:26]] [INFO] Refreshing screenshot...
[[16:44:26]] [INFO] YuuQe2KupX=pass
[[16:44:25]] [SUCCESS] Screenshot refreshed successfully
[[16:44:24]] [INFO] YuuQe2KupX=running
[[16:44:24]] [INFO] Executing action 33/482: Android Function: send_key_event - Key Event: BACK
[[16:44:24]] [SUCCESS] Screenshot refreshed
[[16:44:24]] [INFO] Refreshing screenshot...
[[16:44:24]] [INFO] biRyWs3nSs=pass
[[16:44:22]] [SUCCESS] Screenshot refreshed successfully
[[16:44:10]] [INFO] biRyWs3nSs=running
[[16:44:10]] [INFO] Executing action 32/482: Tap on element with accessibility_id: btnSaveFlybuysCard
[[16:44:10]] [SUCCESS] Screenshot refreshed
[[16:44:10]] [INFO] Refreshing screenshot...
[[16:44:10]] [INFO] sLe0Wurhgm=pass
[[16:44:08]] [SUCCESS] Screenshot refreshed successfully
[[16:44:07]] [INFO] sLe0Wurhgm=running
[[16:44:07]] [INFO] Executing action 31/482: Input text: "2791234567890"
[[16:44:07]] [SUCCESS] Screenshot refreshed
[[16:44:07]] [INFO] Refreshing screenshot...
[[16:44:07]] [INFO] Ey86YRVRzU=pass
[[16:44:06]] [SUCCESS] Screenshot refreshed successfully
[[16:44:05]] [INFO] Ey86YRVRzU=running
[[16:44:05]] [INFO] Executing action 30/482: Tap on element with xpath: //android.widget.EditText
[[16:44:05]] [SUCCESS] Screenshot refreshed
[[16:44:05]] [INFO] Refreshing screenshot...
[[16:44:05]] [INFO] Gxhf3XGc6e=pass
[[16:44:03]] [SUCCESS] Screenshot refreshed successfully
[[16:44:02]] [INFO] Gxhf3XGc6e=running
[[16:44:02]] [INFO] Executing action 29/482: Tap on element with accessibility_id: btnLinkFlyBuys
[[16:44:02]] [SUCCESS] Screenshot refreshed
[[16:44:02]] [INFO] Refreshing screenshot...
[[16:44:02]] [INFO] BracBsfa3Y=pass
[[16:43:40]] [SUCCESS] Screenshot refreshed successfully
[[16:43:39]] [INFO] BracBsfa3Y=running
[[16:43:39]] [INFO] Executing action 28/482: Tap on Text: "Flybuys"
[[16:43:39]] [SUCCESS] Screenshot refreshed
[[16:43:39]] [INFO] Refreshing screenshot...
[[16:43:39]] [INFO] YuuQe2KupX=pass
[[16:43:38]] [SUCCESS] Screenshot refreshed successfully
[[16:43:38]] [INFO] YuuQe2KupX=running
[[16:43:38]] [INFO] Executing action 27/482: Android Function: send_key_event - Key Event: BACK
[[16:43:38]] [SUCCESS] Screenshot refreshed
[[16:43:38]] [INFO] Refreshing screenshot...
[[16:43:38]] [INFO] napKDohf3Z=pass
[[16:43:37]] [SUCCESS] Screenshot refreshed successfully
[[16:43:36]] [INFO] napKDohf3Z=running
[[16:43:36]] [INFO] Executing action 26/482: Tap on element with xpath: //android.widget.Button[contains(@content-desc,"payment methods")]
[[16:43:36]] [SUCCESS] Screenshot refreshed
[[16:43:36]] [INFO] Refreshing screenshot...
[[16:43:36]] [INFO] YuuQe2KupX=pass
[[16:43:35]] [SUCCESS] Screenshot refreshed successfully
[[16:43:34]] [INFO] YuuQe2KupX=running
[[16:43:34]] [INFO] Executing action 25/482: Android Function: send_key_event - Key Event: BACK
[[16:43:34]] [SUCCESS] Screenshot refreshed
[[16:43:34]] [INFO] Refreshing screenshot...
[[16:43:34]] [INFO] 20qUCJgpE9=pass
[[16:43:33]] [SUCCESS] Screenshot refreshed successfully
[[16:43:32]] [INFO] 20qUCJgpE9=running
[[16:43:32]] [INFO] Executing action 24/482: Tap on element with xpath: //android.widget.Button[@content-desc="txtMy addresses"]
[[16:43:32]] [SUCCESS] Screenshot refreshed
[[16:43:32]] [INFO] Refreshing screenshot...
[[16:43:32]] [INFO] YuuQe2KupX=pass
[[16:43:31]] [SUCCESS] Screenshot refreshed successfully
[[16:43:10]] [INFO] YuuQe2KupX=running
[[16:43:10]] [INFO] Executing action 23/482: Android Function: send_key_event - Key Event: BACK
[[16:43:10]] [SUCCESS] Screenshot refreshed
[[16:43:10]] [INFO] Refreshing screenshot...
[[16:43:10]] [INFO] 3hOTINBVMf=pass
[[16:43:09]] [SUCCESS] Screenshot refreshed successfully
[[16:43:08]] [INFO] 3hOTINBVMf=running
[[16:43:08]] [INFO] Executing action 22/482: Tap on element with xpath: //android.widget.Button[@content-desc="txtMy details"]
[[16:43:08]] [SUCCESS] Screenshot refreshed
[[16:43:08]] [INFO] Refreshing screenshot...
[[16:43:08]] [INFO] zNwyPagPE1=pass
[[16:43:03]] [SUCCESS] Screenshot refreshed successfully
[[16:43:02]] [INFO] zNwyPagPE1=running
[[16:43:02]] [INFO] Executing action 21/482: Wait for 5 ms
[[16:43:02]] [SUCCESS] Screenshot refreshed
[[16:43:02]] [INFO] Refreshing screenshot...
[[16:43:02]] [INFO] YuuQe2KupX=pass
[[16:43:00]] [SUCCESS] Screenshot refreshed successfully
[[16:42:55]] [INFO] YuuQe2KupX=running
[[16:42:55]] [INFO] Executing action 20/482: Android Function: send_key_event - Key Event: BACK
[[16:42:55]] [SUCCESS] Screenshot refreshed
[[16:42:55]] [INFO] Refreshing screenshot...
[[16:42:55]] [INFO] zNwyPagPE1=pass
[[16:42:49]] [SUCCESS] Screenshot refreshed successfully
[[16:42:48]] [INFO] zNwyPagPE1=running
[[16:42:48]] [INFO] Executing action 19/482: Wait for 6 ms
[[16:42:48]] [SUCCESS] Screenshot refreshed
[[16:42:48]] [INFO] Refreshing screenshot...
[[16:42:48]] [INFO] g0PE7Mofye=pass
[[16:42:43]] [SUCCESS] Screenshot refreshed successfully
[[16:42:42]] [INFO] g0PE7Mofye=running
[[16:42:42]] [INFO] Executing action 18/482: Check if element with xpath="//android.widget.TextView[@text="My details"]" exists
[[16:42:42]] [SUCCESS] Screenshot refreshed
[[16:42:42]] [INFO] Refreshing screenshot...
[[16:42:42]] [INFO] zNwyPagPE1=pass
[[16:42:41]] [SUCCESS] Screenshot refreshed successfully
[[16:42:40]] [INFO] zNwyPagPE1=running
[[16:42:40]] [INFO] Executing action 17/482: Tap on element with xpath: //android.widget.TextView[@text="My details"]
[[16:42:40]] [SUCCESS] Screenshot refreshed
[[16:42:40]] [INFO] Refreshing screenshot...
[[16:42:40]] [INFO] GgQaBLWYkb=pass
[[16:42:36]] [SUCCESS] Screenshot refreshed successfully
[[16:42:35]] [INFO] GgQaBLWYkb=running
[[16:42:35]] [INFO] Executing action 16/482: Swipe up till element xpath: "//android.widget.TextView[@text="My details"]" is visible
[[16:42:35]] [SUCCESS] Screenshot refreshed
[[16:42:35]] [INFO] Refreshing screenshot...
[[16:42:35]] [INFO] Rl6s389Qsd=pass
[[16:42:29]] [SUCCESS] Screenshot refreshed successfully
[[16:42:28]] [INFO] Rl6s389Qsd=running
[[16:42:28]] [INFO] Executing action 15/482: Tap on Text: "Store"
[[16:42:28]] [SUCCESS] Screenshot refreshed
[[16:42:28]] [INFO] Refreshing screenshot...
[[16:42:28]] [INFO] pFlYwTS53v=pass
[[16:42:26]] [SUCCESS] Screenshot refreshed successfully
[[16:42:25]] [INFO] pFlYwTS53v=running
[[16:42:25]] [INFO] Executing action 14/482: Tap on element with xpath: //android.widget.Button[@content-desc="txtMy orders & receipts"]
[[16:42:25]] [SUCCESS] Screenshot refreshed
[[16:42:25]] [INFO] Refreshing screenshot...
[[16:42:25]] [INFO] sl3Wk1gK8X=pass
[[16:42:24]] [SUCCESS] Screenshot refreshed successfully
[[16:42:23]] [INFO] sl3Wk1gK8X=running
[[16:42:23]] [INFO] Executing action 13/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[16:42:23]] [SUCCESS] Screenshot refreshed
[[16:42:23]] [INFO] Refreshing screenshot...
[[16:42:23]] [INFO] ADHRFCY0LX=pass
[[16:42:20]] [SUCCESS] Screenshot refreshed successfully
[[16:42:18]] [INFO] ADHRFCY0LX=running
[[16:42:18]] [INFO] Executing action 12/482: Wait for 3 ms
[[16:42:18]] [SUCCESS] Screenshot refreshed
[[16:42:18]] [INFO] Refreshing screenshot...
[[16:42:18]] [INFO] Z6g3sGuHTp=pass
[[16:42:17]] [SUCCESS] Screenshot refreshed successfully
[[16:42:16]] [INFO] Z6g3sGuHTp=running
[[16:42:16]] [INFO] Executing action 11/482: Check if element with xpath="//android.view.View[@content-desc="txtHomeGreetingText"]" exists
[[16:42:16]] [SUCCESS] Screenshot refreshed
[[16:42:16]] [INFO] Refreshing screenshot...
[[16:42:16]] [INFO] 7g6MFJSGIO=pass
[[16:42:11]] [SUCCESS] Screenshot refreshed successfully
[[16:42:10]] [INFO] 7g6MFJSGIO=running
[[16:42:10]] [INFO] Executing action 10/482: Tap on element with xpath: //android.widget.Button[@text="Start shopping"]
[[16:42:10]] [SUCCESS] Screenshot refreshed
[[16:42:10]] [INFO] Refreshing screenshot...
[[16:42:10]] [INFO] pFlYwTS53v=pass
[[16:42:08]] [SUCCESS] Screenshot refreshed successfully
[[16:42:07]] [INFO] pFlYwTS53v=running
[[16:42:07]] [INFO] Executing action 9/482: Tap on element with xpath: //android.widget.Button[@content-desc="txtMy orders & receipts"]
[[16:42:07]] [SUCCESS] Screenshot refreshed
[[16:42:07]] [INFO] Refreshing screenshot...
[[16:42:07]] [INFO] RIFpYdpN3M=pass
[[16:42:05]] [SUCCESS] Screenshot refreshed successfully
[[16:42:04]] [INFO] RIFpYdpN3M=running
[[16:42:04]] [INFO] Executing action 8/482: Tap if locator exists: xpath="//android.view.View[contains(@content-desc,"live chat now")]/android.widget.ImageView[2]"
[[16:42:04]] [SUCCESS] Screenshot refreshed
[[16:42:04]] [INFO] Refreshing screenshot...
[[16:42:04]] [INFO] V59u3l1wkM=pass
[[16:42:03]] [SUCCESS] Screenshot refreshed successfully
[[16:42:02]] [INFO] V59u3l1wkM=running
[[16:42:02]] [INFO] Executing action 7/482: Wait till xpath=//android.view.View[contains(@content-desc,"Manage your account")]
[[16:42:02]] [SUCCESS] Screenshot refreshed
[[16:42:02]] [INFO] Refreshing screenshot...
[[16:42:02]] [INFO] sl3Wk1gK8X=pass
[[16:41:57]] [SUCCESS] Screenshot refreshed successfully
[[16:41:56]] [INFO] sl3Wk1gK8X=running
[[16:41:56]] [INFO] Executing action 6/482: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[16:41:56]] [SUCCESS] Screenshot refreshed
[[16:41:56]] [INFO] Refreshing screenshot...
[[16:41:54]] [SUCCESS] Screenshot refreshed successfully
[[16:41:53]] [INFO] Executing action 5/482: Tap if locator exists: xpath="//android.widget.Button[@text="Skip for now"]"
[[16:41:53]] [SUCCESS] Screenshot refreshed
[[16:41:53]] [INFO] Refreshing screenshot...
[[16:41:53]] [SUCCESS] Screenshot refreshed
[[16:41:53]] [INFO] Refreshing screenshot...
[[16:41:52]] [SUCCESS] Screenshot refreshed successfully
[[16:41:51]] [INFO] Executing Multi Step action step 7/7: Tap on element with xpath: //android.widget.Button[@text="Sign in"]
[[16:41:51]] [SUCCESS] Screenshot refreshed
[[16:41:51]] [INFO] Refreshing screenshot...
[[16:41:50]] [SUCCESS] Screenshot refreshed successfully
[[16:41:49]] [INFO] Executing Multi Step action step 6/7: Input text: "Wonderbaby@5"
[[16:41:49]] [SUCCESS] Screenshot refreshed
[[16:41:49]] [INFO] Refreshing screenshot...
[[16:41:48]] [SUCCESS] Screenshot refreshed successfully
[[16:41:47]] [INFO] Executing Multi Step action step 5/7: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[16:41:47]] [SUCCESS] Screenshot refreshed
[[16:41:47]] [INFO] Refreshing screenshot...
[[16:41:46]] [SUCCESS] Screenshot refreshed successfully
[[16:41:45]] [INFO] Executing Multi Step action step 4/7: Tap on element with xpath: //android.widget.Button[@text="Continue to password"]
[[16:41:45]] [SUCCESS] Screenshot refreshed
[[16:41:45]] [INFO] Refreshing screenshot...
[[16:41:43]] [SUCCESS] Screenshot refreshed successfully
[[16:41:42]] [INFO] Executing Multi Step action step 3/7: Input text: "<EMAIL>"
[[16:41:42]] [SUCCESS] Screenshot refreshed
[[16:41:42]] [INFO] Refreshing screenshot...
[[16:41:41]] [SUCCESS] Screenshot refreshed successfully
[[16:41:40]] [INFO] Executing Multi Step action step 2/7: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[16:41:40]] [SUCCESS] Screenshot refreshed
[[16:41:40]] [INFO] Refreshing screenshot...
[[16:41:37]] [SUCCESS] Screenshot refreshed successfully
[[16:41:37]] [INFO] Executing Multi Step action step 1/7: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[16:41:37]] [INFO] Loaded 7 steps from test case: Kmart-Signin-AU-ANDROID-U1
[[16:41:37]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID-U1
[[16:41:37]] [INFO] Executing action 4/482: Execute Test Case: Kmart-Signin-AU-ANDROID-U1 (7 steps)
[[16:41:37]] [SUCCESS] Screenshot refreshed
[[16:41:37]] [INFO] Refreshing screenshot...
[[16:41:37]] [INFO] xAPeBnVHrT=pass
[[16:41:33]] [SUCCESS] Screenshot refreshed successfully
[[16:41:32]] [INFO] xAPeBnVHrT=running
[[16:41:32]] [INFO] Executing action 3/482: Tap on element with accessibility_id: txtHomeAccountCtaSignIn
[[16:41:32]] [SUCCESS] Screenshot refreshed
[[16:41:32]] [INFO] Refreshing screenshot...
[[16:41:32]] [INFO] u6bRYZZFAv=pass
[[16:41:26]] [SUCCESS] Screenshot refreshed successfully
[[16:41:25]] [INFO] u6bRYZZFAv=running
[[16:41:25]] [INFO] Executing action 2/482: Wait for 5 ms
[[16:41:25]] [SUCCESS] Screenshot refreshed
[[16:41:25]] [INFO] Refreshing screenshot...
[[16:41:25]] [SUCCESS] Screenshot refreshed
[[16:41:25]] [INFO] Refreshing screenshot...
[[16:41:21]] [SUCCESS] Screenshot refreshed successfully
[[16:41:21]] [INFO] Executing Multi Step action step 6/6: Tap on element with accessibility_id: txtOnePassOnboardingSkipForNow
[[16:41:21]] [SUCCESS] Screenshot refreshed
[[16:41:21]] [INFO] Refreshing screenshot...
[[16:41:20]] [SUCCESS] Screenshot refreshed successfully
[[16:41:19]] [INFO] Executing Multi Step action step 5/6: Tap on element with accessibility_id: btnMayBeLater
[[16:41:19]] [SUCCESS] Screenshot refreshed
[[16:41:19]] [INFO] Refreshing screenshot...
[[16:41:17]] [SUCCESS] Screenshot refreshed successfully
[[16:41:16]] [INFO] Executing Multi Step action step 4/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[16:41:16]] [SUCCESS] Screenshot refreshed
[[16:41:16]] [INFO] Refreshing screenshot...
[[16:41:12]] [SUCCESS] Screenshot refreshed successfully
[[16:41:11]] [INFO] Executing Multi Step action step 3/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[16:41:11]] [SUCCESS] Screenshot refreshed
[[16:41:11]] [INFO] Refreshing screenshot...
[[16:41:10]] [SUCCESS] Screenshot refreshed successfully
[[16:41:08]] [INFO] Executing Multi Step action step 2/6: Launch app: au.com.kmart
[[16:41:08]] [SUCCESS] Screenshot refreshed
[[16:41:08]] [INFO] Refreshing screenshot...
[[16:41:02]] [INFO] Executing Multi Step action step 1/6: Android Function: clear_app - Package: au.com.kmart
[[16:41:02]] [INFO] Loaded 6 steps from test case: Onboarding-Start-AU
[[16:41:02]] [INFO] Loading steps for Multi Step action: Onboarding-Start-AU
[[16:41:02]] [INFO] Executing action 1/482: Execute Test Case: Onboarding-Start-AU (6 steps)
[[16:41:02]] [INFO] ExecutionManager: Starting execution of 482 actions...
[[16:41:02]] [SUCCESS] Cleared 0 screenshots from database
[[16:41:02]] [INFO] Clearing screenshots from database before execution...
[[16:41:02]] [SUCCESS] All screenshots deleted successfully
[[16:41:02]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[16:41:02]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250827_164102/screenshots
[[16:41:02]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250827_164102
[[16:41:02]] [SUCCESS] Report directory initialized successfully
[[16:41:02]] [INFO] Initializing report directory and screenshots folder for test suite...
[[16:40:52]] [INFO] Collapsed all test cases
[[16:40:51]] [SUCCESS] All screenshots deleted successfully
[[16:40:51]] [INFO] All actions cleared
[[16:40:51]] [INFO] Cleaning up screenshots...
[[16:40:45]] [SUCCESS] Screenshot refreshed successfully
[[16:40:44]] [SUCCESS] Screenshot refreshed
[[16:40:44]] [INFO] Refreshing screenshot...
[[16:40:43]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8
[[16:40:43]] [INFO] Device info updated: RMX2151
[[16:40:18]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[16:40:16]] [SUCCESS] Found 1 device(s)
[[16:40:16]] [INFO] Refreshing device list...
