{"name": "UI Execution 27/08/2025, 18:16:01", "testCases": [{"name": "AU- MyAccount_Android\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Edit\n                            \n                            \n                                 Remove\n                            \n                            56 actions", "status": "passed", "steps": [{"name": "Execute Test Case: Onboarding-Start-AU (6 steps)", "status": "passed", "duration": "0ms", "action_id": "Onboarding", "screenshot_filename": "Onboarding.png", "report_screenshot": "Onboarding.png", "resolved_screenshot": "screenshots/Onboarding.png", "clean_action_id": "Onboarding", "prefixed_action_id": "al_Onboarding", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/Onboarding.png", "action_id_screenshot": "screenshots/Onboarding.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5010ms", "action_id": "screenshot_20250827_180909", "screenshot_filename": "screenshot_20250827_180909.png", "report_screenshot": "screenshot_20250827_180909.png", "resolved_screenshot": "screenshots/screenshot_20250827_180909.png", "clean_action_id": "screenshot_20250827_180909", "prefixed_action_id": "al_screenshot_20250827_180909", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_180909.png", "action_id_screenshot": "screenshots/screenshot_20250827_180909.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "3146ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png", "clean_action_id": "accessibil", "prefixed_action_id": "al_accessibil", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/accessibil.png", "action_id_screenshot": "screenshots/accessibil.png"}, {"name": "Execute Test Case: Kmart-Signin-AU-ANDROID-U1 (7 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250827_175214", "screenshot_filename": "screenshot_20250827_175214.png", "report_screenshot": "screenshot_20250827_175214.png", "resolved_screenshot": "screenshots/screenshot_20250827_175214.png", "clean_action_id": "screenshot_20250827_175214", "prefixed_action_id": "al_screenshot_20250827_175214", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175214.png", "action_id_screenshot": "screenshots/screenshot_20250827_175214.png"}, {"name": "Tap if locator exists: xpath=\"//android.widget.Button[@text=\"Skip for now\"]\"", "status": "passed", "duration": "1860ms", "action_id": "screenshot_20250827_171933", "screenshot_filename": "screenshot_20250827_171933.png", "report_screenshot": "screenshot_20250827_171933.png", "resolved_screenshot": "screenshots/screenshot_20250827_171933.png", "clean_action_id": "screenshot_20250827_171933", "prefixed_action_id": "al_screenshot_20250827_171933", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171933.png", "action_id_screenshot": "screenshots/screenshot_20250827_171933.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "status": "passed", "duration": "4313ms", "action_id": "XoMyLp2unA", "screenshot_filename": "XoMyLp2unA.png", "report_screenshot": "XoMyLp2unA.png", "resolved_screenshot": "screenshots/XoMyLp2unA.png", "clean_action_id": "XoMyLp2unA", "prefixed_action_id": "al_XoMyLp2unA", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/XoMyLp2unA.png", "action_id_screenshot": "screenshots/XoMyLp2unA.png"}, {"name": "Wait till xpath=//android.view.View[contains(@content-desc,\"Manage your account\")]", "status": "passed", "duration": "1120ms", "action_id": "screenshot_20250827_180538", "screenshot_filename": "screenshot_20250827_180538.png", "report_screenshot": "screenshot_20250827_180538.png", "resolved_screenshot": "screenshots/screenshot_20250827_180538.png", "clean_action_id": "screenshot_20250827_180538", "prefixed_action_id": "al_screenshot_20250827_180538", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_180538.png", "action_id_screenshot": "screenshots/screenshot_20250827_180538.png"}, {"name": "Tap if locator exists: xpath=\"//android.view.View[contains(@content-desc,\"live chat now\")]/android.widget.ImageView[2]\"", "status": "passed", "duration": "1858ms", "action_id": "screenshot_20250827_172631", "screenshot_filename": "screenshot_20250827_172631.png", "report_screenshot": "screenshot_20250827_172631.png", "resolved_screenshot": "screenshots/screenshot_20250827_172631.png", "clean_action_id": "screenshot_20250827_172631", "prefixed_action_id": "al_screenshot_20250827_172631", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_172631.png", "action_id_screenshot": "screenshots/screenshot_20250827_172631.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@content-desc=\"txtMy orders & receipts\"]", "status": "passed", "duration": "2052ms", "action_id": "screenshot_20250827_181008", "screenshot_filename": "screenshot_20250827_181008.png", "report_screenshot": "screenshot_20250827_181008.png", "resolved_screenshot": "screenshots/screenshot_20250827_181008.png", "clean_action_id": "screenshot_20250827_181008", "prefixed_action_id": "al_screenshot_20250827_181008", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181008.png", "action_id_screenshot": "screenshots/screenshot_20250827_181008.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@text=\"Start shopping\"]", "status": "passed", "duration": "4710ms", "action_id": "screenshot_20250827_181034", "screenshot_filename": "screenshot_20250827_181034.png", "report_screenshot": "screenshot_20250827_181034.png", "resolved_screenshot": "screenshots/screenshot_20250827_181034.png", "clean_action_id": "screenshot_20250827_181034", "prefixed_action_id": "al_screenshot_20250827_181034", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181034.png", "action_id_screenshot": "screenshots/screenshot_20250827_181034.png"}, {"name": "Check if element with xpath=\"//android.view.View[@content-desc=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "1060ms", "action_id": "txtHomeGre", "screenshot_filename": "txtHomeGre.png", "report_screenshot": "txtHomeGre.png", "resolved_screenshot": "screenshots/txtHomeGre.png", "clean_action_id": "txtHomeGre", "prefixed_action_id": "al_txtHomeGre", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/txtHomeGre.png", "action_id_screenshot": "screenshots/txtHomeGre.png"}, {"name": "Wait for 3 ms", "status": "passed", "duration": "3016ms", "action_id": "screenshot_20250827_173936", "screenshot_filename": "screenshot_20250827_173936.png", "report_screenshot": "screenshot_20250827_173936.png", "resolved_screenshot": "screenshots/screenshot_20250827_173936.png", "clean_action_id": "screenshot_20250827_173936", "prefixed_action_id": "al_screenshot_20250827_173936", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_173936.png", "action_id_screenshot": "screenshots/screenshot_20250827_173936.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "status": "passed", "duration": "1227ms", "action_id": "VXo5C08UOn", "screenshot_filename": "VXo5C08UOn.png", "report_screenshot": "VXo5C08UOn.png", "resolved_screenshot": "screenshots/VXo5C08UOn.png", "clean_action_id": "VXo5C08UOn", "prefixed_action_id": "al_VXo5C08UOn", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/VXo5C08UOn.png", "action_id_screenshot": "screenshots/VXo5C08UOn.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@content-desc=\"txtMy orders & receipts\"]", "status": "passed", "duration": "1875ms", "action_id": "screenshot_20250827_165600", "screenshot_filename": "screenshot_20250827_165600.png", "report_screenshot": "screenshot_20250827_165600.png", "resolved_screenshot": "screenshots/screenshot_20250827_165600.png", "clean_action_id": "screenshot_20250827_165600", "prefixed_action_id": "al_screenshot_20250827_165600", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_165600.png", "action_id_screenshot": "screenshots/screenshot_20250827_165600.png"}, {"name": "Tap on Text: \"Store\"", "status": "passed", "duration": "5675ms", "action_id": "screenshot_20250827_175349", "screenshot_filename": "screenshot_20250827_175349.png", "report_screenshot": "screenshot_20250827_175349.png", "resolved_screenshot": "screenshots/screenshot_20250827_175349.png", "clean_action_id": "screenshot_20250827_175349", "prefixed_action_id": "al_screenshot_20250827_175349", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175349.png", "action_id_screenshot": "screenshots/screenshot_20250827_175349.png"}, {"name": "Swipe up till element xpath: \"//android.widget.TextView[@text=\"My details\"]\" is visible", "status": "passed", "duration": "3810ms", "action_id": "screenshot_20250827_181021", "screenshot_filename": "screenshot_20250827_181021.png", "report_screenshot": "screenshot_20250827_181021.png", "resolved_screenshot": "screenshots/screenshot_20250827_181021.png", "clean_action_id": "screenshot_20250827_181021", "prefixed_action_id": "al_screenshot_20250827_181021", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181021.png", "action_id_screenshot": "screenshots/screenshot_20250827_181021.png"}, {"name": "Tap on element with xpath: //android.widget.TextView[@text=\"My details\"]", "status": "passed", "duration": "988ms", "action_id": "xblqGQSWzC", "screenshot_filename": "xblqGQSWzC.png", "report_screenshot": "xblqGQSWzC.png", "resolved_screenshot": "screenshots/xblqGQSWzC.png", "clean_action_id": "xblqGQSWzC", "prefixed_action_id": "al_xblqGQSWzC", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/xblqGQSWzC.png", "action_id_screenshot": "screenshots/xblqGQSWzC.png"}, {"name": "Check if element with xpath=\"//android.widget.TextView[@text=\"My details\"]\" exists", "status": "passed", "duration": "4683ms", "action_id": "screenshot_20250827_174241", "screenshot_filename": "screenshot_20250827_174241.png", "report_screenshot": "screenshot_20250827_174241.png", "resolved_screenshot": "screenshots/screenshot_20250827_174241.png", "clean_action_id": "screenshot_20250827_174241", "prefixed_action_id": "al_screenshot_20250827_174241", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174241.png", "action_id_screenshot": "screenshots/screenshot_20250827_174241.png"}, {"name": "Wait for 6 ms", "status": "passed", "duration": "6016ms", "action_id": "FIBbvTJFpg", "screenshot_filename": "FIBbvTJFpg.png", "report_screenshot": "FIBbvTJFpg.png", "resolved_screenshot": "screenshots/FIBbvTJFpg.png", "clean_action_id": "FIBbvTJFpg", "prefixed_action_id": "al_FIBbvTJFpg", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/FIBbvTJFpg.png", "action_id_screenshot": "screenshots/FIBbvTJFpg.png"}, {"name": "Android Function: send_key_event - Key Event: BACK", "status": "passed", "duration": "5308ms", "action_id": "screenshot_20250827_174309", "screenshot_filename": "screenshot_20250827_174309.png", "report_screenshot": "screenshot_20250827_174309.png", "resolved_screenshot": "screenshots/screenshot_20250827_174309.png", "clean_action_id": "screenshot_20250827_174309", "prefixed_action_id": "al_screenshot_20250827_174309", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174309.png", "action_id_screenshot": "screenshots/screenshot_20250827_174309.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5010ms", "action_id": "screenshot_20250827_164132", "screenshot_filename": "screenshot_20250827_164132.png", "report_screenshot": "screenshot_20250827_164132.png", "resolved_screenshot": "screenshots/screenshot_20250827_164132.png", "clean_action_id": "screenshot_20250827_164132", "prefixed_action_id": "al_screenshot_20250827_164132", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_164132.png", "action_id_screenshot": "screenshots/screenshot_20250827_164132.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@content-desc=\"txtMy details\"]", "status": "passed", "duration": "796ms", "action_id": "screenshot_20250827_175229", "screenshot_filename": "screenshot_20250827_175229.png", "report_screenshot": "screenshot_20250827_175229.png", "resolved_screenshot": "screenshots/screenshot_20250827_175229.png", "clean_action_id": "screenshot_20250827_175229", "prefixed_action_id": "al_screenshot_20250827_175229", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175229.png", "action_id_screenshot": "screenshots/screenshot_20250827_175229.png"}, {"name": "Android Function: send_key_event - Key Event: BACK", "status": "passed", "duration": "21022ms", "action_id": "screenshot_20250827_172752", "screenshot_filename": "screenshot_20250827_172752.png", "report_screenshot": "screenshot_20250827_172752.png", "resolved_screenshot": "screenshots/screenshot_20250827_172752.png", "clean_action_id": "screenshot_20250827_172752", "prefixed_action_id": "al_screenshot_20250827_172752", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_172752.png", "action_id_screenshot": "screenshots/screenshot_20250827_172752.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@content-desc=\"txtMy addresses\"]", "status": "passed", "duration": "917ms", "action_id": "screenshot_20250827_175015", "screenshot_filename": "screenshot_20250827_175015.png", "report_screenshot": "screenshot_20250827_175015.png", "resolved_screenshot": "screenshots/screenshot_20250827_175015.png", "clean_action_id": "screenshot_20250827_175015", "prefixed_action_id": "al_screenshot_20250827_175015", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175015.png", "action_id_screenshot": "screenshots/screenshot_20250827_175015.png"}, {"name": "Android Function: send_key_event - Key Event: BACK", "status": "passed", "duration": "507ms", "action_id": "R0xsCnCRFk", "screenshot_filename": "R0xsCnCRFk.png", "report_screenshot": "R0xsCnCRFk.png", "resolved_screenshot": "screenshots/R0xsCnCRFk.png", "clean_action_id": "R0xsCnCRFk", "prefixed_action_id": "al_R0xsCnCRFk", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/R0xsCnCRFk.png", "action_id_screenshot": "screenshots/R0xsCnCRFk.png"}, {"name": "Tap on element with xpath: //android.widget.Button[contains(@content-desc,\"payment methods\")]", "status": "passed", "duration": "766ms", "action_id": "uZEEeTeb7p", "screenshot_filename": "uZEEeTeb7p.png", "report_screenshot": "uZEEeTeb7p.png", "resolved_screenshot": "screenshots/uZEEeTeb7p.png", "clean_action_id": "uZEEeTeb7p", "prefixed_action_id": "al_uZEEeTeb7p", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/uZEEeTeb7p.png", "action_id_screenshot": "screenshots/uZEEeTeb7p.png"}, {"name": "Android Function: send_key_event - Key Event: BACK", "status": "passed", "duration": "532ms", "action_id": "V9ldRojdyD", "screenshot_filename": "V9ldRojdyD.png", "report_screenshot": "V9ldRojdyD.png", "resolved_screenshot": "screenshots/V9ldRojdyD.png", "clean_action_id": "V9ldRojdyD", "prefixed_action_id": "al_V9ldRojdyD", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/V9ldRojdyD.png", "action_id_screenshot": "screenshots/V9ldRojdyD.png"}, {"name": "Tap on Text: \"Flybuys\"", "status": "passed", "duration": "21939ms", "action_id": "screenshot_20250827_171844", "screenshot_filename": "screenshot_20250827_171844.png", "report_screenshot": "screenshot_20250827_171844.png", "resolved_screenshot": "screenshots/screenshot_20250827_171844.png", "clean_action_id": "screenshot_20250827_171844", "prefixed_action_id": "al_screenshot_20250827_171844", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171844.png", "action_id_screenshot": "screenshots/screenshot_20250827_171844.png"}, {"name": "Tap on element with accessibility_id: btnLinkFlyBuys", "status": "passed", "duration": "1561ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png", "clean_action_id": "accessibil", "prefixed_action_id": "al_accessibil", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/accessibil.png", "action_id_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with xpath: //android.widget.EditText", "status": "passed", "duration": "998ms", "action_id": "screenshot_20250827_171138", "screenshot_filename": "screenshot_20250827_171138.png", "report_screenshot": "screenshot_20250827_171138.png", "resolved_screenshot": "screenshots/screenshot_20250827_171138.png", "clean_action_id": "screenshot_20250827_171138", "prefixed_action_id": "al_screenshot_20250827_171138", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171138.png", "action_id_screenshot": "screenshots/screenshot_20250827_171138.png"}, {"name": "Input text: \"2791234567890\"", "status": "passed", "duration": "1077ms", "action_id": "2791234567", "screenshot_filename": "2791234567.png", "report_screenshot": "2791234567.png", "resolved_screenshot": "screenshots/2791234567.png", "clean_action_id": "2791234567", "prefixed_action_id": "al_2791234567", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/2791234567.png", "action_id_screenshot": "screenshots/2791234567.png"}, {"name": "Tap on element with accessibility_id: btnSaveFlybuysCard", "status": "passed", "duration": "12944ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png", "clean_action_id": "accessibil", "prefixed_action_id": "al_accessibil", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/accessibil.png", "action_id_screenshot": "screenshots/accessibil.png"}, {"name": "Android Function: send_key_event - Key Event: BACK", "status": "passed", "duration": "727ms", "action_id": "screenshot_20250827_174732", "screenshot_filename": "screenshot_20250827_174732.png", "report_screenshot": "screenshot_20250827_174732.png", "resolved_screenshot": "screenshots/screenshot_20250827_174732.png", "clean_action_id": "screenshot_20250827_174732", "prefixed_action_id": "al_screenshot_20250827_174732", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174732.png", "action_id_screenshot": "screenshots/screenshot_20250827_174732.png"}, {"name": "Check if element with accessibility_id=\"txtMy Flybuys card\" exists", "status": "passed", "duration": "853ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png", "clean_action_id": "accessibil", "prefixed_action_id": "al_accessibil", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/accessibil.png", "action_id_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Flybuys\"", "status": "passed", "duration": "3037ms", "action_id": "screenshot_20250827_173505", "screenshot_filename": "screenshot_20250827_173505.png", "report_screenshot": "screenshot_20250827_173505.png", "resolved_screenshot": "screenshots/screenshot_20250827_173505.png", "clean_action_id": "screenshot_20250827_173505", "prefixed_action_id": "al_screenshot_20250827_173505", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_173505.png", "action_id_screenshot": "screenshots/screenshot_20250827_173505.png"}, {"name": "Wait till accessibility_id=btneditFlybuysCard", "status": "passed", "duration": "2581ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png", "clean_action_id": "accessibil", "prefixed_action_id": "al_accessibil", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/accessibil.png", "action_id_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: btneditFlybuysCard", "status": "passed", "duration": "1354ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png", "clean_action_id": "accessibil", "prefixed_action_id": "al_accessibil", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/accessibil.png", "action_id_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: Remove card", "status": "passed", "duration": "1585ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png", "clean_action_id": "accessibil", "prefixed_action_id": "al_accessibil", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/accessibil.png", "action_id_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: btnRemove", "status": "passed", "duration": "754ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png", "clean_action_id": "accessibil", "prefixed_action_id": "al_accessibil", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/accessibil.png", "action_id_screenshot": "screenshots/accessibil.png"}, {"name": "Wait for 6 ms", "status": "passed", "duration": "6012ms", "action_id": "y5FboDiRLS", "screenshot_filename": "y5FboDiRLS.png", "report_screenshot": "y5FboDiRLS.png", "resolved_screenshot": "screenshots/y5FboDiRLS.png", "clean_action_id": "y5FboDiRLS", "prefixed_action_id": "al_y5FboDiRLS", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/y5FboDiRLS.png", "action_id_screenshot": "screenshots/y5FboDiRLS.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "3774ms", "action_id": "SeiSGMuR9u", "screenshot_filename": "SeiSGMuR9u.png", "report_screenshot": "SeiSGMuR9u.png", "resolved_screenshot": "screenshots/SeiSGMuR9u.png", "clean_action_id": "SeiSGMuR9u", "prefixed_action_id": "al_SeiSGMuR9u", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/SeiSGMuR9u.png", "action_id_screenshot": "screenshots/SeiSGMuR9u.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@content-desc=\"txtStore locator\"]", "status": "passed", "duration": "1573ms", "action_id": "screenshot_20250827_172021", "screenshot_filename": "screenshot_20250827_172021.png", "report_screenshot": "screenshot_20250827_172021.png", "resolved_screenshot": "screenshots/screenshot_20250827_172021.png", "clean_action_id": "screenshot_20250827_172021", "prefixed_action_id": "al_screenshot_20250827_172021", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_172021.png", "action_id_screenshot": "screenshots/screenshot_20250827_172021.png"}, {"name": "Tap if locator exists: xpath=\"//android.widget.Button[@resource-id=\"com.android.permissioncontroller:id/permission_allow_foreground_only_button\"]\"", "status": "passed", "duration": "1756ms", "action_id": "permission", "screenshot_filename": "permission.png", "report_screenshot": "permission.png", "resolved_screenshot": "screenshots/permission.png", "clean_action_id": "permission", "prefixed_action_id": "al_permission", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/permission.png", "action_id_screenshot": "screenshots/permission.png"}, {"name": "Tap on Text: \"Nearby\"", "status": "passed", "duration": "2613ms", "action_id": "screenshot_20250827_181340", "screenshot_filename": "screenshot_20250827_181340.png", "report_screenshot": "screenshot_20250827_181340.png", "resolved_screenshot": "screenshots/screenshot_20250827_181340.png", "clean_action_id": "screenshot_20250827_181340", "prefixed_action_id": "al_screenshot_20250827_181340", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181340.png", "action_id_screenshot": "screenshots/screenshot_20250827_181340.png"}, {"name": "Input text: \"3000\"", "status": "passed", "duration": "3090ms", "action_id": "screenshot_20250827_170420", "screenshot_filename": "screenshot_20250827_170420.png", "report_screenshot": "screenshot_20250827_170420.png", "resolved_screenshot": "screenshots/screenshot_20250827_170420.png", "clean_action_id": "screenshot_20250827_170420", "prefixed_action_id": "al_screenshot_20250827_170420", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_170420.png", "action_id_screenshot": "screenshots/screenshot_20250827_170420.png"}, {"name": "Android Function: send_key_event - Key Event: ENTER", "status": "passed", "duration": "732ms", "action_id": "g8u66qfKkX", "screenshot_filename": "g8u66qfKkX.png", "report_screenshot": "g8u66qfKkX.png", "resolved_screenshot": "screenshots/g8u66qfKkX.png", "clean_action_id": "g8u66qfKkX", "prefixed_action_id": "al_g8u66qfKkX", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/g8u66qfKkX.png", "action_id_screenshot": "screenshots/g8u66qfKkX.png"}, {"name": "Tap on Text: \"VIC\"", "status": "passed", "duration": "5810ms", "action_id": "screenshot_20250827_165001", "screenshot_filename": "screenshot_20250827_165001.png", "report_screenshot": "screenshot_20250827_165001.png", "resolved_screenshot": "screenshots/screenshot_20250827_165001.png", "clean_action_id": "screenshot_20250827_165001", "prefixed_action_id": "al_screenshot_20250827_165001", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_165001.png", "action_id_screenshot": "screenshots/screenshot_20250827_165001.png"}, {"name": "Check if element with text=\"Cbd\" exists", "status": "passed", "duration": "4408ms", "action_id": "screenshot_20250827_170430", "screenshot_filename": "screenshot_20250827_170430.png", "report_screenshot": "screenshot_20250827_170430.png", "resolved_screenshot": "screenshots/screenshot_20250827_170430.png", "clean_action_id": "screenshot_20250827_170430", "prefixed_action_id": "al_screenshot_20250827_170430", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_170430.png", "action_id_screenshot": "screenshots/screenshot_20250827_170430.png"}, {"name": "Android Function: send_key_event - Key Event: BACK", "status": "passed", "duration": "4749ms", "action_id": "screenshot_20250827_171504", "screenshot_filename": "screenshot_20250827_171504.png", "report_screenshot": "screenshot_20250827_171504.png", "resolved_screenshot": "screenshots/screenshot_20250827_171504.png", "clean_action_id": "screenshot_20250827_171504", "prefixed_action_id": "al_screenshot_20250827_171504", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171504.png", "action_id_screenshot": "screenshots/screenshot_20250827_171504.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@content-desc=\"txtInvite a friend\"]", "status": "passed", "duration": "667ms", "action_id": "screenshot_20250827_181152", "screenshot_filename": "screenshot_20250827_181152.png", "report_screenshot": "screenshot_20250827_181152.png", "resolved_screenshot": "screenshots/screenshot_20250827_181152.png", "clean_action_id": "screenshot_20250827_181152", "prefixed_action_id": "al_screenshot_20250827_181152", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181152.png", "action_id_screenshot": "screenshots/screenshot_20250827_181152.png"}, {"name": "Check if element with xpath=\"//android.widget.TextView[@resource-id=\"android:id/content_preview_text\"]\" exists", "status": "passed", "duration": "686ms", "action_id": "Wb6cwBudqO", "screenshot_filename": "Wb6cwBudqO.png", "report_screenshot": "Wb6cwBudqO.png", "resolved_screenshot": "screenshots/Wb6cwBudqO.png", "clean_action_id": "Wb6cwBudqO", "prefixed_action_id": "al_Wb6cwBudqO", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/Wb6cwBudqO.png", "action_id_screenshot": "screenshots/Wb6cwBudqO.png"}, {"name": "Android Function: send_key_event - Key Event: BACK", "status": "passed", "duration": "652ms", "action_id": "mcscWdhpn2", "screenshot_filename": "mcscWdhpn2.png", "report_screenshot": "mcscWdhpn2.png", "resolved_screenshot": "screenshots/mcscWdhpn2.png", "clean_action_id": "mcscWdhpn2", "prefixed_action_id": "al_mcscWdhpn2", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/mcscWdhpn2.png", "action_id_screenshot": "screenshots/mcscWdhpn2.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@content-desc=\"txtCustomer Help\"]", "status": "passed", "duration": "802ms", "action_id": "txtCustome", "screenshot_filename": "txtCustome.png", "report_screenshot": "txtCustome.png", "resolved_screenshot": "screenshots/txtCustome.png", "clean_action_id": "txtCustome", "prefixed_action_id": "al_txtCustome", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/txtCustome.png", "action_id_screenshot": "screenshots/txtCustome.png"}, {"name": "Android Function: send_key_event - Key Event: BACK", "status": "passed", "duration": "21012ms", "action_id": "screenshot_20250827_164914", "screenshot_filename": "screenshot_20250827_164914.png", "report_screenshot": "screenshot_20250827_164914.png", "resolved_screenshot": "screenshots/screenshot_20250827_164914.png", "clean_action_id": "screenshot_20250827_164914", "prefixed_action_id": "al_screenshot_20250827_164914", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_164914.png", "action_id_screenshot": "screenshots/screenshot_20250827_164914.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "4037ms", "action_id": "GEMv6goQtW", "screenshot_filename": "GEMv6goQtW.png", "report_screenshot": "GEMv6goQtW.png", "resolved_screenshot": "screenshots/GEMv6goQtW.png", "clean_action_id": "GEMv6goQtW", "prefixed_action_id": "al_GEMv6goQtW", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/GEMv6goQtW.png", "action_id_screenshot": "screenshots/GEMv6goQtW.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@content-desc=\"txtSign out\"]", "status": "passed", "duration": "1635ms", "action_id": "MuP17p1Xxx", "screenshot_filename": "MuP17p1Xxx.png", "report_screenshot": "MuP17p1Xxx.png", "resolved_screenshot": "screenshots/MuP17p1Xxx.png", "clean_action_id": "MuP17p1Xxx", "prefixed_action_id": "al_MuP17p1Xxx", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/MuP17p1Xxx.png", "action_id_screenshot": "screenshots/MuP17p1Xxx.png"}]}, {"name": "All Sign ins_AU-ANDROID\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Edit\n                            \n                            \n                                 Remove\n                            \n                            66 actions", "status": "passed", "steps": [{"name": "Execute Test Case: Onboarding-Start-AU (6 steps)", "status": "passed", "duration": "0ms", "action_id": "Onboarding", "screenshot_filename": "Onboarding.png", "report_screenshot": "Onboarding.png", "resolved_screenshot": "screenshots/Onboarding.png", "clean_action_id": "Onboarding", "prefixed_action_id": "al_Onboarding", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/Onboarding.png", "action_id_screenshot": "screenshots/Onboarding.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "3252ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png", "clean_action_id": "accessibil", "prefixed_action_id": "al_accessibil", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/accessibil.png", "action_id_screenshot": "screenshots/accessibil.png"}, {"name": "Execute Test Case: Kmart-Signin-AU-ANDROID (7 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250827_180909", "screenshot_filename": "screenshot_20250827_180909.png", "report_screenshot": "screenshot_20250827_180909.png", "resolved_screenshot": "screenshots/screenshot_20250827_180909.png", "clean_action_id": "screenshot_20250827_180909", "prefixed_action_id": "al_screenshot_20250827_180909", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_180909.png", "action_id_screenshot": "screenshots/screenshot_20250827_180909.png"}, {"name": "Wait till xpath=//android.view.View[@content-desc=\"txtHomeGreetingText\"]", "status": "passed", "duration": "5202ms", "action_id": "txtHomeGre", "screenshot_filename": "txtHomeGre.png", "report_screenshot": "txtHomeGre.png", "resolved_screenshot": "screenshots/txtHomeGre.png", "clean_action_id": "txtHomeGre", "prefixed_action_id": "al_txtHomeGre", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/txtHomeGre.png", "action_id_screenshot": "screenshots/txtHomeGre.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "status": "passed", "duration": "973ms", "action_id": "screenshot_20250827_175214", "screenshot_filename": "screenshot_20250827_175214.png", "report_screenshot": "screenshot_20250827_175214.png", "resolved_screenshot": "screenshots/screenshot_20250827_175214.png", "clean_action_id": "screenshot_20250827_175214", "prefixed_action_id": "al_screenshot_20250827_175214", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175214.png", "action_id_screenshot": "screenshots/screenshot_20250827_175214.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2377ms", "action_id": "screenshot_20250827_171933", "screenshot_filename": "screenshot_20250827_171933.png", "report_screenshot": "screenshot_20250827_171933.png", "resolved_screenshot": "screenshots/screenshot_20250827_171933.png", "clean_action_id": "screenshot_20250827_171933", "prefixed_action_id": "al_screenshot_20250827_171933", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171933.png", "action_id_screenshot": "screenshots/screenshot_20250827_171933.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@content-desc=\"txtSign out\"]", "status": "passed", "duration": "2276ms", "action_id": "XoMyLp2unA", "screenshot_filename": "XoMyLp2unA.png", "report_screenshot": "XoMyLp2unA.png", "resolved_screenshot": "screenshots/XoMyLp2unA.png", "clean_action_id": "XoMyLp2unA", "prefixed_action_id": "al_XoMyLp2unA", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/XoMyLp2unA.png", "action_id_screenshot": "screenshots/XoMyLp2unA.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 3 of 5\")]", "status": "passed", "duration": "1191ms", "action_id": "screenshot_20250827_180538", "screenshot_filename": "screenshot_20250827_180538.png", "report_screenshot": "screenshot_20250827_180538.png", "resolved_screenshot": "screenshots/screenshot_20250827_180538.png", "clean_action_id": "screenshot_20250827_180538", "prefixed_action_id": "al_screenshot_20250827_180538", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_180538.png", "action_id_screenshot": "screenshots/screenshot_20250827_180538.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@content-desc=\"txtLog in\"]", "status": "passed", "duration": "3325ms", "action_id": "screenshot_20250827_172631", "screenshot_filename": "screenshot_20250827_172631.png", "report_screenshot": "screenshot_20250827_172631.png", "resolved_screenshot": "screenshots/screenshot_20250827_172631.png", "clean_action_id": "screenshot_20250827_172631", "prefixed_action_id": "al_screenshot_20250827_172631", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_172631.png", "action_id_screenshot": "screenshots/screenshot_20250827_172631.png"}, {"name": "Execute Test Case: Kmart-Signin-AU-ANDROID (7 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250827_181008", "screenshot_filename": "screenshot_20250827_181008.png", "report_screenshot": "screenshot_20250827_181008.png", "resolved_screenshot": "screenshots/screenshot_20250827_181008.png", "clean_action_id": "screenshot_20250827_181008", "prefixed_action_id": "al_screenshot_20250827_181008", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181008.png", "action_id_screenshot": "screenshots/screenshot_20250827_181008.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 1 of 5\")]", "status": "passed", "duration": "3144ms", "action_id": "screenshot_20250827_181034", "screenshot_filename": "screenshot_20250827_181034.png", "report_screenshot": "screenshot_20250827_181034.png", "resolved_screenshot": "screenshots/screenshot_20250827_181034.png", "clean_action_id": "screenshot_20250827_181034", "prefixed_action_id": "al_screenshot_20250827_181034", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181034.png", "action_id_screenshot": "screenshots/screenshot_20250827_181034.png"}, {"name": "Wait till xpath=//android.view.View[@content-desc=\"txtHomeGreetingText\"]", "status": "passed", "duration": "31379ms", "action_id": "txtHomeGre", "screenshot_filename": "txtHomeGre.png", "report_screenshot": "txtHomeGre.png", "resolved_screenshot": "screenshots/txtHomeGre.png", "clean_action_id": "txtHomeGre", "prefixed_action_id": "al_txtHomeGre", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/txtHomeGre.png", "action_id_screenshot": "screenshots/txtHomeGre.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "status": "passed", "duration": "1837ms", "action_id": "screenshot_20250827_173936", "screenshot_filename": "screenshot_20250827_173936.png", "report_screenshot": "screenshot_20250827_173936.png", "resolved_screenshot": "screenshots/screenshot_20250827_173936.png", "clean_action_id": "screenshot_20250827_173936", "prefixed_action_id": "al_screenshot_20250827_173936", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_173936.png", "action_id_screenshot": "screenshots/screenshot_20250827_173936.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2519ms", "action_id": "VXo5C08UOn", "screenshot_filename": "VXo5C08UOn.png", "report_screenshot": "VXo5C08UOn.png", "resolved_screenshot": "screenshots/VXo5C08UOn.png", "clean_action_id": "VXo5C08UOn", "prefixed_action_id": "al_VXo5C08UOn", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/VXo5C08UOn.png", "action_id_screenshot": "screenshots/VXo5C08UOn.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@content-desc=\"txtSign out\"]", "status": "passed", "duration": "2896ms", "action_id": "screenshot_20250827_165600", "screenshot_filename": "screenshot_20250827_165600.png", "report_screenshot": "screenshot_20250827_165600.png", "resolved_screenshot": "screenshots/screenshot_20250827_165600.png", "clean_action_id": "screenshot_20250827_165600", "prefixed_action_id": "al_screenshot_20250827_165600", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_165600.png", "action_id_screenshot": "screenshots/screenshot_20250827_165600.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "4021ms", "action_id": "screenshot_20250827_175349", "screenshot_filename": "screenshot_20250827_175349.png", "report_screenshot": "screenshot_20250827_175349.png", "resolved_screenshot": "screenshots/screenshot_20250827_175349.png", "clean_action_id": "screenshot_20250827_175349", "prefixed_action_id": "al_screenshot_20250827_175349", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175349.png", "action_id_screenshot": "screenshots/screenshot_20250827_175349.png"}, {"name": "Input text: \"Uno card\"", "status": "passed", "duration": "1223ms", "action_id": "screenshot_20250827_181021", "screenshot_filename": "screenshot_20250827_181021.png", "report_screenshot": "screenshot_20250827_181021.png", "resolved_screenshot": "screenshots/screenshot_20250827_181021.png", "clean_action_id": "screenshot_20250827_181021", "prefixed_action_id": "al_screenshot_20250827_181021", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181021.png", "action_id_screenshot": "screenshots/screenshot_20250827_181021.png"}, {"name": "Android Function: send_key_event - Key Event: ENTER", "status": "passed", "duration": "947ms", "action_id": "xblqGQSWzC", "screenshot_filename": "xblqGQSWzC.png", "report_screenshot": "xblqGQSWzC.png", "resolved_screenshot": "screenshots/xblqGQSWzC.png", "clean_action_id": "xblqGQSWzC", "prefixed_action_id": "al_xblqGQSWzC", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/xblqGQSWzC.png", "action_id_screenshot": "screenshots/xblqGQSWzC.png"}, {"name": "Wait till xpath=//android.widget.Button[@text=\"Filter\"]", "status": "passed", "duration": "6217ms", "action_id": "screenshot_20250827_174241", "screenshot_filename": "screenshot_20250827_174241.png", "report_screenshot": "screenshot_20250827_174241.png", "resolved_screenshot": "screenshots/screenshot_20250827_174241.png", "clean_action_id": "screenshot_20250827_174241", "prefixed_action_id": "al_screenshot_20250827_174241", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174241.png", "action_id_screenshot": "screenshots/screenshot_20250827_174241.png"}, {"name": "Tap on element with xpath: ((//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView[1])[1]", "status": "passed", "duration": "1429ms", "action_id": "FIBbvTJFpg", "screenshot_filename": "FIBbvTJFpg.png", "report_screenshot": "FIBbvTJFpg.png", "resolved_screenshot": "screenshots/FIBbvTJFpg.png", "clean_action_id": "FIBbvTJFpg", "prefixed_action_id": "al_FIBbvTJFpg", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/FIBbvTJFpg.png", "action_id_screenshot": "screenshots/FIBbvTJFpg.png"}, {"name": "Wait till xpath=//android.widget.TextView[contains(@text,\"SKU\")]", "status": "passed", "duration": "27447ms", "action_id": "screenshot_20250827_174309", "screenshot_filename": "screenshot_20250827_174309.png", "report_screenshot": "screenshot_20250827_174309.png", "resolved_screenshot": "screenshots/screenshot_20250827_174309.png", "clean_action_id": "screenshot_20250827_174309", "prefixed_action_id": "al_screenshot_20250827_174309", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174309.png", "action_id_screenshot": "screenshots/screenshot_20250827_174309.png"}, {"name": "Swipe up till element xpath: \"//android.widget.TextView[contains(@text,\"Already a member\")]\" is visible", "status": "passed", "duration": "9896ms", "action_id": "screenshot_20250827_164132", "screenshot_filename": "screenshot_20250827_164132.png", "report_screenshot": "screenshot_20250827_164132.png", "resolved_screenshot": "screenshots/screenshot_20250827_164132.png", "clean_action_id": "screenshot_20250827_164132", "prefixed_action_id": "al_screenshot_20250827_164132", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_164132.png", "action_id_screenshot": "screenshots/screenshot_20250827_164132.png"}, {"name": "Tap on Text: \"Sign\"", "status": "passed", "duration": "3157ms", "action_id": "screenshot_20250827_175229", "screenshot_filename": "screenshot_20250827_175229.png", "report_screenshot": "screenshot_20250827_175229.png", "resolved_screenshot": "screenshots/screenshot_20250827_175229.png", "clean_action_id": "screenshot_20250827_175229", "prefixed_action_id": "al_screenshot_20250827_175229", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175229.png", "action_id_screenshot": "screenshots/screenshot_20250827_175229.png"}, {"name": "Wait till xpath=//android.widget.EditText[@resource-id=\"email-input\"]", "status": "passed", "duration": "3312ms", "action_id": "screenshot_20250827_172752", "screenshot_filename": "screenshot_20250827_172752.png", "report_screenshot": "screenshot_20250827_172752.png", "resolved_screenshot": "screenshots/screenshot_20250827_172752.png", "clean_action_id": "screenshot_20250827_172752", "prefixed_action_id": "al_screenshot_20250827_172752", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_172752.png", "action_id_screenshot": "screenshots/screenshot_20250827_172752.png"}, {"name": "Tap on element with xpath: //android.widget.EditText[@resource-id=\"email-input\"]", "status": "passed", "duration": "709ms", "action_id": "screenshot_20250827_175015", "screenshot_filename": "screenshot_20250827_175015.png", "report_screenshot": "screenshot_20250827_175015.png", "resolved_screenshot": "screenshots/screenshot_20250827_175015.png", "clean_action_id": "screenshot_20250827_175015", "prefixed_action_id": "al_screenshot_20250827_175015", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175015.png", "action_id_screenshot": "screenshots/screenshot_20250827_175015.png"}, {"name": "Input text: \"<EMAIL>\"", "status": "passed", "duration": "1281ms", "action_id": "kmartprod0", "screenshot_filename": "kmartprod0.png", "report_screenshot": "kmartprod0.png", "resolved_screenshot": "screenshots/kmartprod0.png", "clean_action_id": "kmartprod0", "prefixed_action_id": "al_kmartprod0", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/kmartprod0.png", "action_id_screenshot": "screenshots/kmartprod0.png"}, {"name": "Android Function: send_key_event - Key Event: ENTER", "status": "passed", "duration": "418ms", "action_id": "R0xsCnCRFk", "screenshot_filename": "R0xsCnCRFk.png", "report_screenshot": "R0xsCnCRFk.png", "resolved_screenshot": "screenshots/R0xsCnCRFk.png", "clean_action_id": "R0xsCnCRFk", "prefixed_action_id": "al_R0xsCnCRFk", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/R0xsCnCRFk.png", "action_id_screenshot": "screenshots/R0xsCnCRFk.png"}, {"name": "Tap if locator exists: xpath=\"//android.widget.CheckBox[@resource-id=\"recaptcha-anchor\"]\"", "status": "passed", "duration": "10029ms", "action_id": "uZEEeTeb7p", "screenshot_filename": "uZEEeTeb7p.png", "report_screenshot": "uZEEeTeb7p.png", "resolved_screenshot": "screenshots/uZEEeTeb7p.png", "clean_action_id": "uZEEeTeb7p", "prefixed_action_id": "al_uZEEeTeb7p", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/uZEEeTeb7p.png", "action_id_screenshot": "screenshots/uZEEeTeb7p.png"}, {"name": "Tap on element with xpath: //android.widget.EditText[@resource-id=\"password-input\"]", "status": "passed", "duration": "1292ms", "action_id": "V9ldRojdyD", "screenshot_filename": "V9ldRojdyD.png", "report_screenshot": "V9ldRojdyD.png", "resolved_screenshot": "screenshots/V9ldRojdyD.png", "clean_action_id": "V9ldRojdyD", "prefixed_action_id": "al_V9ldRojdyD", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/V9ldRojdyD.png", "action_id_screenshot": "screenshots/V9ldRojdyD.png"}, {"name": "Input text: \"Wonderbaby@5\"", "status": "passed", "duration": "1069ms", "action_id": "Wonderbaby", "screenshot_filename": "Wonderbaby.png", "report_screenshot": "Wonderbaby.png", "resolved_screenshot": "screenshots/Wonderbaby.png", "clean_action_id": "Wonderbaby", "prefixed_action_id": "al_Wonderbaby", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/Wonderbaby.png", "action_id_screenshot": "screenshots/Wonderbaby.png"}, {"name": "Android Function: send_key_event - Key Event: ENTER", "status": "passed", "duration": "534ms", "action_id": "screenshot_20250827_171844", "screenshot_filename": "screenshot_20250827_171844.png", "report_screenshot": "screenshot_20250827_171844.png", "resolved_screenshot": "screenshots/screenshot_20250827_171844.png", "clean_action_id": "screenshot_20250827_171844", "prefixed_action_id": "al_screenshot_20250827_171844", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171844.png", "action_id_screenshot": "screenshots/screenshot_20250827_171844.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5019ms", "action_id": "screenshot_20250827_171138", "screenshot_filename": "screenshot_20250827_171138.png", "report_screenshot": "screenshot_20250827_171138.png", "resolved_screenshot": "screenshots/screenshot_20250827_171138.png", "clean_action_id": "screenshot_20250827_171138", "prefixed_action_id": "al_screenshot_20250827_171138", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171138.png", "action_id_screenshot": "screenshots/screenshot_20250827_171138.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "status": "passed", "duration": "537ms", "action_id": "screenshot_20250827_174732", "screenshot_filename": "screenshot_20250827_174732.png", "report_screenshot": "screenshot_20250827_174732.png", "resolved_screenshot": "screenshots/screenshot_20250827_174732.png", "clean_action_id": "screenshot_20250827_174732", "prefixed_action_id": "al_screenshot_20250827_174732", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174732.png", "action_id_screenshot": "screenshots/screenshot_20250827_174732.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2163ms", "action_id": "screenshot_20250827_173505", "screenshot_filename": "screenshot_20250827_173505.png", "report_screenshot": "screenshot_20250827_173505.png", "resolved_screenshot": "screenshots/screenshot_20250827_173505.png", "clean_action_id": "screenshot_20250827_173505", "prefixed_action_id": "al_screenshot_20250827_173505", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_173505.png", "action_id_screenshot": "screenshots/screenshot_20250827_173505.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@content-desc=\"txtSign out\"]", "status": "passed", "duration": "2741ms", "action_id": "y5FboDiRLS", "screenshot_filename": "y5FboDiRLS.png", "report_screenshot": "y5FboDiRLS.png", "resolved_screenshot": "screenshots/y5FboDiRLS.png", "clean_action_id": "y5FboDiRLS", "prefixed_action_id": "al_y5FboDiRLS", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/y5FboDiRLS.png", "action_id_screenshot": "screenshots/y5FboDiRLS.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "status": "passed", "duration": "8511ms", "action_id": "SeiSGMuR9u", "screenshot_filename": "SeiSGMuR9u.png", "report_screenshot": "SeiSGMuR9u.png", "resolved_screenshot": "screenshots/SeiSGMuR9u.png", "clean_action_id": "SeiSGMuR9u", "prefixed_action_id": "al_SeiSGMuR9u", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/SeiSGMuR9u.png", "action_id_screenshot": "screenshots/SeiSGMuR9u.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@content-desc=\"txtMoreAccountCtaSignIn\"]", "status": "passed", "duration": "3068ms", "action_id": "txtMoreAcc", "screenshot_filename": "txtMoreAcc.png", "report_screenshot": "txtMoreAcc.png", "resolved_screenshot": "screenshots/txtMoreAcc.png", "clean_action_id": "txtMoreAcc", "prefixed_action_id": "al_txtMoreAcc", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/txtMoreAcc.png", "action_id_screenshot": "screenshots/txtMoreAcc.png"}, {"name": "Execute Test Case: Kmart-Signin-AU-ANDROID (7 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250827_172021", "screenshot_filename": "screenshot_20250827_172021.png", "report_screenshot": "screenshot_20250827_172021.png", "resolved_screenshot": "screenshots/screenshot_20250827_172021.png", "clean_action_id": "screenshot_20250827_172021", "prefixed_action_id": "al_screenshot_20250827_172021", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_172021.png", "action_id_screenshot": "screenshots/screenshot_20250827_172021.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2647ms", "action_id": "screenshot_20250827_181340", "screenshot_filename": "screenshot_20250827_181340.png", "report_screenshot": "screenshot_20250827_181340.png", "resolved_screenshot": "screenshots/screenshot_20250827_181340.png", "clean_action_id": "screenshot_20250827_181340", "prefixed_action_id": "al_screenshot_20250827_181340", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181340.png", "action_id_screenshot": "screenshots/screenshot_20250827_181340.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@content-desc=\"txtSign out\"]", "status": "passed", "duration": "2417ms", "action_id": "screenshot_20250827_170420", "screenshot_filename": "screenshot_20250827_170420.png", "report_screenshot": "screenshot_20250827_170420.png", "resolved_screenshot": "screenshots/screenshot_20250827_170420.png", "clean_action_id": "screenshot_20250827_170420", "prefixed_action_id": "al_screenshot_20250827_170420", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_170420.png", "action_id_screenshot": "screenshots/screenshot_20250827_170420.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 1 of 5\")]", "status": "passed", "duration": "18915ms", "action_id": "g8u66qfKkX", "screenshot_filename": "g8u66qfKkX.png", "report_screenshot": "g8u66qfKkX.png", "resolved_screenshot": "screenshots/g8u66qfKkX.png", "clean_action_id": "g8u66qfKkX", "prefixed_action_id": "al_g8u66qfKkX", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/g8u66qfKkX.png", "action_id_screenshot": "screenshots/g8u66qfKkX.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "4049ms", "action_id": "screenshot_20250827_165001", "screenshot_filename": "screenshot_20250827_165001.png", "report_screenshot": "screenshot_20250827_165001.png", "resolved_screenshot": "screenshots/screenshot_20250827_165001.png", "clean_action_id": "screenshot_20250827_165001", "prefixed_action_id": "al_screenshot_20250827_165001", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_165001.png", "action_id_screenshot": "screenshots/screenshot_20250827_165001.png"}, {"name": "Input text: \"Uno card\"", "status": "passed", "duration": "1180ms", "action_id": "screenshot_20250827_170430", "screenshot_filename": "screenshot_20250827_170430.png", "report_screenshot": "screenshot_20250827_170430.png", "resolved_screenshot": "screenshots/screenshot_20250827_170430.png", "clean_action_id": "screenshot_20250827_170430", "prefixed_action_id": "al_screenshot_20250827_170430", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_170430.png", "action_id_screenshot": "screenshots/screenshot_20250827_170430.png"}, {"name": "Android Function: send_key_event - Key Event: ENTER", "status": "passed", "duration": "1274ms", "action_id": "screenshot_20250827_171504", "screenshot_filename": "screenshot_20250827_171504.png", "report_screenshot": "screenshot_20250827_171504.png", "resolved_screenshot": "screenshots/screenshot_20250827_171504.png", "clean_action_id": "screenshot_20250827_171504", "prefixed_action_id": "al_screenshot_20250827_171504", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171504.png", "action_id_screenshot": "screenshots/screenshot_20250827_171504.png"}, {"name": "Wait till xpath=//android.widget.Button[@text=\"Filter\"]", "status": "passed", "duration": "5385ms", "action_id": "screenshot_20250827_181152", "screenshot_filename": "screenshot_20250827_181152.png", "report_screenshot": "screenshot_20250827_181152.png", "resolved_screenshot": "screenshots/screenshot_20250827_181152.png", "clean_action_id": "screenshot_20250827_181152", "prefixed_action_id": "al_screenshot_20250827_181152", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181152.png", "action_id_screenshot": "screenshots/screenshot_20250827_181152.png"}, {"name": "Tap on element with xpath: ((//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView[1])[1]", "status": "passed", "duration": "1661ms", "action_id": "Wb6cwBudqO", "screenshot_filename": "Wb6cwBudqO.png", "report_screenshot": "Wb6cwBudqO.png", "resolved_screenshot": "screenshots/Wb6cwBudqO.png", "clean_action_id": "Wb6cwBudqO", "prefixed_action_id": "al_Wb6cwBudqO", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/Wb6cwBudqO.png", "action_id_screenshot": "screenshots/Wb6cwBudqO.png"}, {"name": "Swipe up till element xpath: \"//android.widget.Button[@text=\"Add to bag\"]\" is visible", "status": "passed", "duration": "3229ms", "action_id": "mcscWdhpn2", "screenshot_filename": "mcscWdhpn2.png", "report_screenshot": "mcscWdhpn2.png", "resolved_screenshot": "screenshots/mcscWdhpn2.png", "clean_action_id": "mcscWdhpn2", "prefixed_action_id": "al_mcscWdhpn2", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/mcscWdhpn2.png", "action_id_screenshot": "screenshots/mcscWdhpn2.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@text=\"Add to bag\"]", "status": "passed", "duration": "909ms", "action_id": "screenshot_20250827_164914", "screenshot_filename": "screenshot_20250827_164914.png", "report_screenshot": "screenshot_20250827_164914.png", "resolved_screenshot": "screenshots/screenshot_20250827_164914.png", "clean_action_id": "screenshot_20250827_164914", "prefixed_action_id": "al_screenshot_20250827_164914", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_164914.png", "action_id_screenshot": "screenshots/screenshot_20250827_164914.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5013ms", "action_id": "GEMv6goQtW", "screenshot_filename": "GEMv6goQtW.png", "report_screenshot": "GEMv6goQtW.png", "resolved_screenshot": "screenshots/GEMv6goQtW.png", "clean_action_id": "GEMv6goQtW", "prefixed_action_id": "al_GEMv6goQtW", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/GEMv6goQtW.png", "action_id_screenshot": "screenshots/GEMv6goQtW.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "status": "passed", "duration": "1005ms", "action_id": "MuP17p1Xxx", "screenshot_filename": "MuP17p1Xxx.png", "report_screenshot": "MuP17p1Xxx.png", "resolved_screenshot": "screenshots/MuP17p1Xxx.png", "clean_action_id": "MuP17p1Xxx", "prefixed_action_id": "al_MuP17p1Xxx", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/MuP17p1Xxx.png", "action_id_screenshot": "screenshots/MuP17p1Xxx.png"}, {"name": "Tap if locator exists: xpath=\"//android.widget.Button[@content-desc=\"Checkout\"]\"", "status": "passed", "duration": "1480ms", "action_id": "screenshot_20250827_174509", "screenshot_filename": "screenshot_20250827_174509.png", "report_screenshot": "screenshot_20250827_174509.png", "resolved_screenshot": "screenshots/screenshot_20250827_174509.png", "clean_action_id": "screenshot_20250827_174509", "prefixed_action_id": "al_screenshot_20250827_174509", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174509.png", "action_id_screenshot": "screenshots/screenshot_20250827_174509.png"}, {"name": "Wait till xpath=//android.view.View[@text=\"Delivery\"]", "status": "passed", "duration": "6342ms", "action_id": "screenshot_20250827_174333", "screenshot_filename": "screenshot_20250827_174333.png", "report_screenshot": "screenshot_20250827_174333.png", "resolved_screenshot": "screenshots/screenshot_20250827_174333.png", "clean_action_id": "screenshot_20250827_174333", "prefixed_action_id": "al_screenshot_20250827_174333", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174333.png", "action_id_screenshot": "screenshots/screenshot_20250827_174333.png"}, {"name": "Tap on element with xpath: //android.view.View[@text=\"Delivery\"]", "status": "passed", "duration": "809ms", "action_id": "screenshot_20250827_174441", "screenshot_filename": "screenshot_20250827_174441.png", "report_screenshot": "screenshot_20250827_174441.png", "resolved_screenshot": "screenshots/screenshot_20250827_174441.png", "clean_action_id": "screenshot_20250827_174441", "prefixed_action_id": "al_screenshot_20250827_174441", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174441.png", "action_id_screenshot": "screenshots/screenshot_20250827_174441.png"}, {"name": "Swipe up till element xpath: \"//android.widget.Button[contains(@text,\"Continue\")]\" is visible", "status": "passed", "duration": "11962ms", "action_id": "screenshot_20250827_171505", "screenshot_filename": "screenshot_20250827_171505.png", "report_screenshot": "screenshot_20250827_171505.png", "resolved_screenshot": "screenshots/screenshot_20250827_171505.png", "clean_action_id": "screenshot_20250827_171505", "prefixed_action_id": "al_screenshot_20250827_171505", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171505.png", "action_id_screenshot": "screenshots/screenshot_20250827_171505.png"}, {"name": "Tap on element with xpath: //android.widget.Button[contains(@text,\"Continue\")]", "status": "passed", "duration": "713ms", "action_id": "screenshot_20250827_173110", "screenshot_filename": "screenshot_20250827_173110.png", "report_screenshot": "screenshot_20250827_173110.png", "resolved_screenshot": "screenshots/screenshot_20250827_173110.png", "clean_action_id": "screenshot_20250827_173110", "prefixed_action_id": "al_screenshot_20250827_173110", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_173110.png", "action_id_screenshot": "screenshots/screenshot_20250827_173110.png"}, {"name": "Tap on Text: \"Sign\"", "status": "passed", "duration": "2851ms", "action_id": "MRkOQpNybH", "screenshot_filename": "MRkOQpNybH.png", "report_screenshot": "MRkOQpNybH.png", "resolved_screenshot": "screenshots/MRkOQpNybH.png", "clean_action_id": "MRkOQpNybH", "prefixed_action_id": "al_MRkOQpNybH", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/MRkOQpNybH.png", "action_id_screenshot": "screenshots/MRkOQpNybH.png"}, {"name": "Execute Test Case: Kmart-Signin-AU-ANDROID (7 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250827_172542", "screenshot_filename": "screenshot_20250827_172542.png", "report_screenshot": "screenshot_20250827_172542.png", "resolved_screenshot": "screenshots/screenshot_20250827_172542.png", "clean_action_id": "screenshot_20250827_172542", "prefixed_action_id": "al_screenshot_20250827_172542", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_172542.png", "action_id_screenshot": "screenshots/screenshot_20250827_172542.png"}, {"name": "Tap if locator exists: xpath=\"//android.widget.Button[@content-desc=\"Checkout\"]\"", "status": "passed", "duration": "4705ms", "action_id": "YC6bBrKQgq", "screenshot_filename": "YC6bBrKQgq.png", "report_screenshot": "YC6bBrKQgq.png", "resolved_screenshot": "screenshots/YC6bBrKQgq.png", "clean_action_id": "YC6bBrKQgq", "prefixed_action_id": "al_YC6bBrKQgq", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/YC6bBrKQgq.png", "action_id_screenshot": "screenshots/YC6bBrKQgq.png"}, {"name": "Wait till xpath=//android.view.View[@text=\"Delivery\"]", "status": "passed", "duration": "22787ms", "action_id": "screenshot_20250827_174537", "screenshot_filename": "screenshot_20250827_174537.png", "report_screenshot": "screenshot_20250827_174537.png", "resolved_screenshot": "screenshots/screenshot_20250827_174537.png", "clean_action_id": "screenshot_20250827_174537", "prefixed_action_id": "al_screenshot_20250827_174537", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174537.png", "action_id_screenshot": "screenshots/screenshot_20250827_174537.png"}, {"name": "Tap on element with xpath: //android.view.View[@text=\"Delivery\"]", "status": "passed", "duration": "854ms", "action_id": "screenshot_20250827_173258", "screenshot_filename": "screenshot_20250827_173258.png", "report_screenshot": "screenshot_20250827_173258.png", "resolved_screenshot": "screenshots/screenshot_20250827_173258.png", "clean_action_id": "screenshot_20250827_173258", "prefixed_action_id": "al_screenshot_20250827_173258", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_173258.png", "action_id_screenshot": "screenshots/screenshot_20250827_173258.png"}, {"name": "Tap on element with xpath: //android.widget.Button[contains(@text,\"Remove\")]", "status": "passed", "duration": "1057ms", "action_id": "screenshot_20250827_171103", "screenshot_filename": "screenshot_20250827_171103.png", "report_screenshot": "screenshot_20250827_171103.png", "resolved_screenshot": "screenshots/screenshot_20250827_171103.png", "clean_action_id": "screenshot_20250827_171103", "prefixed_action_id": "al_screenshot_20250827_171103", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171103.png", "action_id_screenshot": "screenshots/screenshot_20250827_171103.png"}, {"name": "Tap on image: bag-close-android.png", "status": "passed", "duration": "26452ms", "action_id": "screenshot_20250827_171659", "screenshot_filename": "screenshot_20250827_171659.png", "report_screenshot": "screenshot_20250827_171659.png", "resolved_screenshot": "screenshots/screenshot_20250827_171659.png", "clean_action_id": "screenshot_20250827_171659", "prefixed_action_id": "al_screenshot_20250827_171659", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171659.png", "action_id_screenshot": "screenshots/screenshot_20250827_171659.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "status": "passed", "duration": "1224ms", "action_id": "wSHsGWAwPm", "screenshot_filename": "wSHsGWAwPm.png", "report_screenshot": "wSHsGWAwPm.png", "resolved_screenshot": "screenshots/wSHsGWAwPm.png", "clean_action_id": "wSHsGWAwPm", "prefixed_action_id": "al_wSHsGWAwPm", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/wSHsGWAwPm.png", "action_id_screenshot": "screenshots/wSHsGWAwPm.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2325ms", "action_id": "screenshot_20250827_181541", "screenshot_filename": "screenshot_20250827_181541.png", "report_screenshot": "screenshot_20250827_181541.png", "resolved_screenshot": "screenshots/screenshot_20250827_181541.png", "clean_action_id": "screenshot_20250827_181541", "prefixed_action_id": "al_screenshot_20250827_181541", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181541.png", "action_id_screenshot": "screenshots/screenshot_20250827_181541.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@content-desc=\"txtSign out\"]", "status": "passed", "duration": "21570ms", "action_id": "screenshot_20250827_172351", "screenshot_filename": "screenshot_20250827_172351.png", "report_screenshot": "screenshot_20250827_172351.png", "resolved_screenshot": "screenshots/screenshot_20250827_172351.png", "clean_action_id": "screenshot_20250827_172351", "prefixed_action_id": "al_screenshot_20250827_172351", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_172351.png", "action_id_screenshot": "screenshots/screenshot_20250827_172351.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 1 of 5\")]", "status": "passed", "duration": "1815ms", "action_id": "screenshot_20250827_164902", "screenshot_filename": "screenshot_20250827_164902.png", "report_screenshot": "screenshot_20250827_164902.png", "resolved_screenshot": "screenshots/screenshot_20250827_164902.png", "clean_action_id": "screenshot_20250827_164902", "prefixed_action_id": "al_screenshot_20250827_164902", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_164902.png", "action_id_screenshot": "screenshots/screenshot_20250827_164902.png"}]}, {"name": "Postcode Flow_AU_ANDROID\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Edit\n                            \n                            \n                                 Remove\n                            \n                            54 actions", "status": "passed", "steps": [{"name": "Execute Test Case: Onboarding-Start-AU (6 steps)", "status": "passed", "duration": "0ms", "action_id": "Onboarding", "screenshot_filename": "Onboarding.png", "report_screenshot": "Onboarding.png", "resolved_screenshot": "screenshots/Onboarding.png", "clean_action_id": "Onboarding", "prefixed_action_id": "al_Onboarding", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/Onboarding.png", "action_id_screenshot": "screenshots/Onboarding.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "3867ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png", "clean_action_id": "accessibil", "prefixed_action_id": "al_accessibil", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/accessibil.png", "action_id_screenshot": "screenshots/accessibil.png"}, {"name": "Wait till xpath=//android.widget.EditText[@resource-id=\"username\"]", "status": "passed", "duration": "1122ms", "action_id": "screenshot_20250827_180909", "screenshot_filename": "screenshot_20250827_180909.png", "report_screenshot": "screenshot_20250827_180909.png", "resolved_screenshot": "screenshots/screenshot_20250827_180909.png", "clean_action_id": "screenshot_20250827_180909", "prefixed_action_id": "al_screenshot_20250827_180909", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_180909.png", "action_id_screenshot": "screenshots/screenshot_20250827_180909.png"}, {"name": "Execute Test Case: Kmart-Signin-AU-ANDROID (7 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250827_175214", "screenshot_filename": "screenshot_20250827_175214.png", "report_screenshot": "screenshot_20250827_175214.png", "resolved_screenshot": "screenshots/screenshot_20250827_175214.png", "clean_action_id": "screenshot_20250827_175214", "prefixed_action_id": "al_screenshot_20250827_175214", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175214.png", "action_id_screenshot": "screenshots/screenshot_20250827_175214.png"}, {"name": "Wait till xpath=//android.view.View[@content-desc=\"txtHomeGreetingText\"]", "status": "passed", "duration": "4928ms", "action_id": "txtHomeGre", "screenshot_filename": "txtHomeGre.png", "report_screenshot": "txtHomeGre.png", "resolved_screenshot": "screenshots/txtHomeGre.png", "clean_action_id": "txtHomeGre", "prefixed_action_id": "al_txtHomeGre", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/txtHomeGre.png", "action_id_screenshot": "screenshots/txtHomeGre.png"}, {"name": "Tap on image: postcode_edit.png", "status": "passed", "duration": "8374ms", "action_id": "screenshot_20250827_171933", "screenshot_filename": "screenshot_20250827_171933.png", "report_screenshot": "screenshot_20250827_171933.png", "resolved_screenshot": "screenshots/screenshot_20250827_171933.png", "clean_action_id": "screenshot_20250827_171933", "prefixed_action_id": "al_screenshot_20250827_171933", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171933.png", "action_id_screenshot": "screenshots/screenshot_20250827_171933.png"}, {"name": "Tap on element with xpath: //android.view.View[@content-desc=\"txtPostCodeSelectionScreenHeader\"]/following-sibling::android.widget.ImageView[1]", "status": "passed", "duration": "1508ms", "action_id": "txtPostCod", "screenshot_filename": "txtPostCod.png", "report_screenshot": "txtPostCod.png", "resolved_screenshot": "screenshots/txtPostCod.png", "clean_action_id": "txtPostCod", "prefixed_action_id": "al_txtPostCod", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/txtPostCod.png", "action_id_screenshot": "screenshots/txtPostCod.png"}, {"name": "textClear action", "status": "passed", "duration": "2679ms", "action_id": "XoMyLp2unA", "screenshot_filename": "XoMyLp2unA.png", "report_screenshot": "XoMyLp2unA.png", "resolved_screenshot": "screenshots/XoMyLp2unA.png", "clean_action_id": "XoMyLp2unA", "prefixed_action_id": "al_XoMyLp2unA", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/XoMyLp2unA.png", "action_id_screenshot": "screenshots/XoMyLp2unA.png"}, {"name": "Tap on element with xpath: //android.view.View[@content-desc=\"txtPostCodeSelectionScreenHeader\"]/following-sibling::android.widget.ImageView[1]", "status": "passed", "duration": "1560ms", "action_id": "txtPostCod", "screenshot_filename": "txtPostCod.png", "report_screenshot": "txtPostCod.png", "resolved_screenshot": "screenshots/txtPostCod.png", "clean_action_id": "txtPostCod", "prefixed_action_id": "al_txtPostCod", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/txtPostCod.png", "action_id_screenshot": "screenshots/txtPostCod.png"}, {"name": "Tap on Text: \"BC\"", "status": "passed", "duration": "3506ms", "action_id": "screenshot_20250827_180538", "screenshot_filename": "screenshot_20250827_180538.png", "report_screenshot": "screenshot_20250827_180538.png", "resolved_screenshot": "screenshots/screenshot_20250827_180538.png", "clean_action_id": "screenshot_20250827_180538", "prefixed_action_id": "al_screenshot_20250827_180538", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_180538.png", "action_id_screenshot": "screenshots/screenshot_20250827_180538.png"}, {"name": "Wait till accessibility_id=btnSaveOrContinue", "status": "passed", "duration": "723ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png", "clean_action_id": "accessibil", "prefixed_action_id": "al_accessibil", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/accessibil.png", "action_id_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: btnSaveOrContinue", "status": "passed", "duration": "1144ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png", "clean_action_id": "accessibil", "prefixed_action_id": "al_accessibil", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/accessibil.png", "action_id_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3843ms", "action_id": "screenshot_20250827_172631", "screenshot_filename": "screenshot_20250827_172631.png", "report_screenshot": "screenshot_20250827_172631.png", "resolved_screenshot": "screenshots/screenshot_20250827_172631.png", "clean_action_id": "screenshot_20250827_172631", "prefixed_action_id": "al_screenshot_20250827_172631", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_172631.png", "action_id_screenshot": "screenshots/screenshot_20250827_172631.png"}, {"name": "Input text: \"P_6225544\"", "status": "passed", "duration": "1594ms", "action_id": "screenshot_20250827_181008", "screenshot_filename": "screenshot_20250827_181008.png", "report_screenshot": "screenshot_20250827_181008.png", "resolved_screenshot": "screenshots/screenshot_20250827_181008.png", "clean_action_id": "screenshot_20250827_181008", "prefixed_action_id": "al_screenshot_20250827_181008", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181008.png", "action_id_screenshot": "screenshots/screenshot_20250827_181008.png"}, {"name": "Android Function: send_key_event - Key Event: ENTER", "status": "passed", "duration": "879ms", "action_id": "screenshot_20250827_181034", "screenshot_filename": "screenshot_20250827_181034.png", "report_screenshot": "screenshot_20250827_181034.png", "resolved_screenshot": "screenshots/screenshot_20250827_181034.png", "clean_action_id": "screenshot_20250827_181034", "prefixed_action_id": "al_screenshot_20250827_181034", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181034.png", "action_id_screenshot": "screenshots/screenshot_20250827_181034.png"}, {"name": "Wait till image appears: sort-by-relevance-android.png", "status": "passed", "duration": "2065ms", "action_id": "screenshot_20250827_173936", "screenshot_filename": "screenshot_20250827_173936.png", "report_screenshot": "screenshot_20250827_173936.png", "resolved_screenshot": "screenshots/screenshot_20250827_173936.png", "clean_action_id": "screenshot_20250827_173936", "prefixed_action_id": "al_screenshot_20250827_173936", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_173936.png", "action_id_screenshot": "screenshots/screenshot_20250827_173936.png"}, {"name": "Tap on Text: \"Edit\"", "status": "passed", "duration": "2744ms", "action_id": "VXo5C08UOn", "screenshot_filename": "VXo5C08UOn.png", "report_screenshot": "VXo5C08UOn.png", "resolved_screenshot": "screenshots/VXo5C08UOn.png", "clean_action_id": "VXo5C08UOn", "prefixed_action_id": "al_VXo5C08UOn", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/VXo5C08UOn.png", "action_id_screenshot": "screenshots/VXo5C08UOn.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5018ms", "action_id": "screenshot_20250827_165600", "screenshot_filename": "screenshot_20250827_165600.png", "report_screenshot": "screenshot_20250827_165600.png", "resolved_screenshot": "screenshots/screenshot_20250827_165600.png", "clean_action_id": "screenshot_20250827_165600", "prefixed_action_id": "al_screenshot_20250827_165600", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_165600.png", "action_id_screenshot": "screenshots/screenshot_20250827_165600.png"}, {"name": "Tap on element with xpath: //android.view.View[@content-desc=\"txtPostCodeSelectionScreenHeader\"]/following-sibling::android.widget.ImageView[1]", "status": "passed", "duration": "728ms", "action_id": "txtPostCod", "screenshot_filename": "txtPostCod.png", "report_screenshot": "txtPostCod.png", "resolved_screenshot": "screenshots/txtPostCod.png", "clean_action_id": "txtPostCod", "prefixed_action_id": "al_txtPostCod", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/txtPostCod.png", "action_id_screenshot": "screenshots/txtPostCod.png"}, {"name": "textClear action", "status": "passed", "duration": "2452ms", "action_id": "screenshot_20250827_175349", "screenshot_filename": "screenshot_20250827_175349.png", "report_screenshot": "screenshot_20250827_175349.png", "resolved_screenshot": "screenshots/screenshot_20250827_175349.png", "clean_action_id": "screenshot_20250827_175349", "prefixed_action_id": "al_screenshot_20250827_175349", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175349.png", "action_id_screenshot": "screenshots/screenshot_20250827_175349.png"}, {"name": "Tap on element with xpath: //android.view.View[@content-desc=\"txtPostCodeSelectionScreenHeader\"]/following-sibling::android.widget.ImageView[1]", "status": "passed", "duration": "11196ms", "action_id": "txtPostCod", "screenshot_filename": "txtPostCod.png", "report_screenshot": "txtPostCod.png", "resolved_screenshot": "screenshots/txtPostCod.png", "clean_action_id": "txtPostCod", "prefixed_action_id": "al_txtPostCod", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/txtPostCod.png", "action_id_screenshot": "screenshots/txtPostCod.png"}, {"name": "Tap on Text: \"CITY\"", "status": "passed", "duration": "2966ms", "action_id": "screenshot_20250827_181021", "screenshot_filename": "screenshot_20250827_181021.png", "report_screenshot": "screenshot_20250827_181021.png", "resolved_screenshot": "screenshots/screenshot_20250827_181021.png", "clean_action_id": "screenshot_20250827_181021", "prefixed_action_id": "al_screenshot_20250827_181021", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181021.png", "action_id_screenshot": "screenshots/screenshot_20250827_181021.png"}, {"name": "Wait till accessibility_id=btnSaveOrContinue", "status": "passed", "duration": "1305ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png", "clean_action_id": "accessibil", "prefixed_action_id": "al_accessibil", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/accessibil.png", "action_id_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: btnSaveOrContinue", "status": "passed", "duration": "1152ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png", "clean_action_id": "accessibil", "prefixed_action_id": "al_accessibil", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/accessibil.png", "action_id_screenshot": "screenshots/accessibil.png"}, {"name": "Check if element with text=\"Toowong\" exists", "status": "passed", "duration": "6609ms", "action_id": "xblqGQSWzC", "screenshot_filename": "xblqGQSWzC.png", "report_screenshot": "xblqGQSWzC.png", "resolved_screenshot": "screenshots/xblqGQSWzC.png", "clean_action_id": "xblqGQSWzC", "prefixed_action_id": "al_xblqGQSWzC", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/xblqGQSWzC.png", "action_id_screenshot": "screenshots/xblqGQSWzC.png"}, {"name": "Tap on Text: \"UNO\"", "status": "passed", "duration": "3077ms", "action_id": "screenshot_20250827_174241", "screenshot_filename": "screenshot_20250827_174241.png", "report_screenshot": "screenshot_20250827_174241.png", "resolved_screenshot": "screenshots/screenshot_20250827_174241.png", "clean_action_id": "screenshot_20250827_174241", "prefixed_action_id": "al_screenshot_20250827_174241", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174241.png", "action_id_screenshot": "screenshots/screenshot_20250827_174241.png"}, {"name": "Wait till xpath=//android.widget.TextView[contains(@text,\"SKU\")]", "status": "passed", "duration": "4429ms", "action_id": "FIBbvTJFpg", "screenshot_filename": "FIBbvTJFpg.png", "report_screenshot": "FIBbvTJFpg.png", "resolved_screenshot": "screenshots/FIBbvTJFpg.png", "clean_action_id": "FIBbvTJFpg", "prefixed_action_id": "al_FIBbvTJFpg", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/FIBbvTJFpg.png", "action_id_screenshot": "screenshots/FIBbvTJFpg.png"}, {"name": "Tap on Text: \"4000\"", "status": "passed", "duration": "3241ms", "action_id": "screenshot_20250827_174309", "screenshot_filename": "screenshot_20250827_174309.png", "report_screenshot": "screenshot_20250827_174309.png", "resolved_screenshot": "screenshots/screenshot_20250827_174309.png", "clean_action_id": "screenshot_20250827_174309", "prefixed_action_id": "al_screenshot_20250827_174309", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174309.png", "action_id_screenshot": "screenshots/screenshot_20250827_174309.png"}, {"name": "Wait till xpath=//android.view.View[@content-desc=\"txtPostCodeSelectionScreenHeader\"]/following-sibling::android.widget.ImageView[1]", "status": "passed", "duration": "714ms", "action_id": "txtPostCod", "screenshot_filename": "txtPostCod.png", "report_screenshot": "txtPostCod.png", "resolved_screenshot": "screenshots/txtPostCod.png", "clean_action_id": "txtPostCod", "prefixed_action_id": "al_txtPostCod", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/txtPostCod.png", "action_id_screenshot": "screenshots/txtPostCod.png"}, {"name": "Tap on element with xpath: //android.view.View[@content-desc=\"txtPostCodeSelectionScreenHeader\"]/following-sibling::android.widget.ImageView[1]", "status": "passed", "duration": "638ms", "action_id": "txtPostCod", "screenshot_filename": "txtPostCod.png", "report_screenshot": "txtPostCod.png", "resolved_screenshot": "screenshots/txtPostCod.png", "clean_action_id": "txtPostCod", "prefixed_action_id": "al_txtPostCod", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/txtPostCod.png", "action_id_screenshot": "screenshots/txtPostCod.png"}, {"name": "textClear action", "status": "passed", "duration": "2773ms", "action_id": "screenshot_20250827_164132", "screenshot_filename": "screenshot_20250827_164132.png", "report_screenshot": "screenshot_20250827_164132.png", "resolved_screenshot": "screenshots/screenshot_20250827_164132.png", "clean_action_id": "screenshot_20250827_164132", "prefixed_action_id": "al_screenshot_20250827_164132", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_164132.png", "action_id_screenshot": "screenshots/screenshot_20250827_164132.png"}, {"name": "Tap on element with xpath: //android.view.View[@content-desc=\"txtPostCodeSelectionScreenHeader\"]/following-sibling::android.widget.ImageView[1]", "status": "passed", "duration": "698ms", "action_id": "txtPostCod", "screenshot_filename": "txtPostCod.png", "report_screenshot": "txtPostCod.png", "resolved_screenshot": "screenshots/txtPostCod.png", "clean_action_id": "txtPostCod", "prefixed_action_id": "al_txtPostCod", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/txtPostCod.png", "action_id_screenshot": "screenshots/txtPostCod.png"}, {"name": "Tap on Text: \"2000\"", "status": "passed", "duration": "3007ms", "action_id": "screenshot_20250827_175229", "screenshot_filename": "screenshot_20250827_175229.png", "report_screenshot": "screenshot_20250827_175229.png", "resolved_screenshot": "screenshots/screenshot_20250827_175229.png", "clean_action_id": "screenshot_20250827_175229", "prefixed_action_id": "al_screenshot_20250827_175229", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175229.png", "action_id_screenshot": "screenshots/screenshot_20250827_175229.png"}, {"name": "Wait till accessibility_id=btnSaveOrContinue", "status": "passed", "duration": "1167ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png", "clean_action_id": "accessibil", "prefixed_action_id": "al_accessibil", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/accessibil.png", "action_id_screenshot": "screenshots/accessibil.png"}, {"name": "Tap on element with accessibility_id: btnSaveOrContinue", "status": "passed", "duration": "1133ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png", "clean_action_id": "accessibil", "prefixed_action_id": "al_accessibil", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/accessibil.png", "action_id_screenshot": "screenshots/accessibil.png"}, {"name": "Check if element with text=\"Broadway\" exists", "status": "passed", "duration": "7812ms", "action_id": "screenshot_20250827_172752", "screenshot_filename": "screenshot_20250827_172752.png", "report_screenshot": "screenshot_20250827_172752.png", "resolved_screenshot": "screenshots/screenshot_20250827_172752.png", "clean_action_id": "screenshot_20250827_172752", "prefixed_action_id": "al_screenshot_20250827_172752", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_172752.png", "action_id_screenshot": "screenshots/screenshot_20250827_172752.png"}, {"name": "Swipe from (50%, 70%) to (50%, 50%)", "status": "passed", "duration": "2130ms", "action_id": "screenshot_20250827_175015", "screenshot_filename": "screenshot_20250827_175015.png", "report_screenshot": "screenshot_20250827_175015.png", "resolved_screenshot": "screenshots/screenshot_20250827_175015.png", "clean_action_id": "screenshot_20250827_175015", "prefixed_action_id": "al_screenshot_20250827_175015", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175015.png", "action_id_screenshot": "screenshots/screenshot_20250827_175015.png"}, {"name": "Tap on Text: \"bag\"", "status": "passed", "duration": "3371ms", "action_id": "R0xsCnCRFk", "screenshot_filename": "R0xsCnCRFk.png", "report_screenshot": "R0xsCnCRFk.png", "resolved_screenshot": "screenshots/R0xsCnCRFk.png", "clean_action_id": "R0xsCnCRFk", "prefixed_action_id": "al_R0xsCnCRFk", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/R0xsCnCRFk.png", "action_id_screenshot": "screenshots/R0xsCnCRFk.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "status": "passed", "duration": "1144ms", "action_id": "uZEEeTeb7p", "screenshot_filename": "uZEEeTeb7p.png", "report_screenshot": "uZEEeTeb7p.png", "resolved_screenshot": "screenshots/uZEEeTeb7p.png", "clean_action_id": "uZEEeTeb7p", "prefixed_action_id": "al_uZEEeTeb7p", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/uZEEeTeb7p.png", "action_id_screenshot": "screenshots/uZEEeTeb7p.png"}, {"name": "Tap if locator exists: xpath=\"//android.widget.Button[@content-desc=\"Checkout\"]\"", "status": "passed", "duration": "1371ms", "action_id": "V9ldRojdyD", "screenshot_filename": "V9ldRojdyD.png", "report_screenshot": "V9ldRojdyD.png", "resolved_screenshot": "screenshots/V9ldRojdyD.png", "clean_action_id": "V9ldRojdyD", "prefixed_action_id": "al_V9ldRojdyD", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/V9ldRojdyD.png", "action_id_screenshot": "screenshots/V9ldRojdyD.png"}, {"name": "Wait till image appears: delivery-tab-android.png", "status": "passed", "duration": "2342ms", "action_id": "screenshot_20250827_171844", "screenshot_filename": "screenshot_20250827_171844.png", "report_screenshot": "screenshot_20250827_171844.png", "resolved_screenshot": "screenshots/screenshot_20250827_171844.png", "clean_action_id": "screenshot_20250827_171844", "prefixed_action_id": "al_screenshot_20250827_171844", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171844.png", "action_id_screenshot": "screenshots/screenshot_20250827_171844.png"}, {"name": "Tap on image: cnc-tab-android.png", "status": "passed", "duration": "7274ms", "action_id": "screenshot_20250827_171138", "screenshot_filename": "screenshot_20250827_171138.png", "report_screenshot": "screenshot_20250827_171138.png", "resolved_screenshot": "screenshots/screenshot_20250827_171138.png", "clean_action_id": "screenshot_20250827_171138", "prefixed_action_id": "al_screenshot_20250827_171138", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171138.png", "action_id_screenshot": "screenshots/screenshot_20250827_171138.png"}, {"name": "Tap on Text: \"Nearby\"", "status": "passed", "duration": "5378ms", "action_id": "screenshot_20250827_174732", "screenshot_filename": "screenshot_20250827_174732.png", "report_screenshot": "screenshot_20250827_174732.png", "resolved_screenshot": "screenshots/screenshot_20250827_174732.png", "clean_action_id": "screenshot_20250827_174732", "prefixed_action_id": "al_screenshot_20250827_174732", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174732.png", "action_id_screenshot": "screenshots/screenshot_20250827_174732.png"}, {"name": "textClear action", "status": "passed", "duration": "1953ms", "action_id": "screenshot_20250827_173505", "screenshot_filename": "screenshot_20250827_173505.png", "report_screenshot": "screenshot_20250827_173505.png", "resolved_screenshot": "screenshots/screenshot_20250827_173505.png", "clean_action_id": "screenshot_20250827_173505", "prefixed_action_id": "al_screenshot_20250827_173505", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_173505.png", "action_id_screenshot": "screenshots/screenshot_20250827_173505.png"}, {"name": "Tap on Text: \"VIC\"", "status": "passed", "duration": "2722ms", "action_id": "y5FboDiRLS", "screenshot_filename": "y5FboDiRLS.png", "report_screenshot": "y5FboDiRLS.png", "resolved_screenshot": "screenshots/y5FboDiRLS.png", "clean_action_id": "y5FboDiRLS", "prefixed_action_id": "al_y5FboDiRLS", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/y5FboDiRLS.png", "action_id_screenshot": "screenshots/y5FboDiRLS.png"}, {"name": "Android Function: send_key_event - Key Event: TAB", "status": "passed", "duration": "683ms", "action_id": "SeiSGMuR9u", "screenshot_filename": "SeiSGMuR9u.png", "report_screenshot": "SeiSGMuR9u.png", "resolved_screenshot": "screenshots/SeiSGMuR9u.png", "clean_action_id": "SeiSGMuR9u", "prefixed_action_id": "al_SeiSGMuR9u", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/SeiSGMuR9u.png", "action_id_screenshot": "screenshots/SeiSGMuR9u.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "1047ms", "action_id": "screenshot_20250827_172021", "screenshot_filename": "screenshot_20250827_172021.png", "report_screenshot": "screenshot_20250827_172021.png", "resolved_screenshot": "screenshots/screenshot_20250827_172021.png", "clean_action_id": "screenshot_20250827_172021", "prefixed_action_id": "al_screenshot_20250827_172021", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_172021.png", "action_id_screenshot": "screenshots/screenshot_20250827_172021.png"}, {"name": "Wait for 3 ms", "status": "passed", "duration": "3012ms", "action_id": "screenshot_20250827_181340", "screenshot_filename": "screenshot_20250827_181340.png", "report_screenshot": "screenshot_20250827_181340.png", "resolved_screenshot": "screenshots/screenshot_20250827_181340.png", "clean_action_id": "screenshot_20250827_181340", "prefixed_action_id": "al_screenshot_20250827_181340", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181340.png", "action_id_screenshot": "screenshots/screenshot_20250827_181340.png"}, {"name": "Tap on image: bag-remove-btn-android.png", "status": "passed", "duration": "7646ms", "action_id": "screenshot_20250827_170420", "screenshot_filename": "screenshot_20250827_170420.png", "report_screenshot": "screenshot_20250827_170420.png", "resolved_screenshot": "screenshots/screenshot_20250827_170420.png", "clean_action_id": "screenshot_20250827_170420", "prefixed_action_id": "al_screenshot_20250827_170420", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_170420.png", "action_id_screenshot": "screenshots/screenshot_20250827_170420.png"}, {"name": "Tap on element with xpath: //android.widget.TextView[@text=\"Continue shopping\"]", "status": "passed", "duration": "1183ms", "action_id": "g8u66qfKkX", "screenshot_filename": "g8u66qfKkX.png", "report_screenshot": "g8u66qfKkX.png", "resolved_screenshot": "screenshots/g8u66qfKkX.png", "clean_action_id": "g8u66qfKkX", "prefixed_action_id": "al_g8u66qfKkX", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/g8u66qfKkX.png", "action_id_screenshot": "screenshots/g8u66qfKkX.png"}, {"name": "Check if element with text=\"3000\" exists", "status": "passed", "duration": "6340ms", "action_id": "screenshot_20250827_165001", "screenshot_filename": "screenshot_20250827_165001.png", "report_screenshot": "screenshot_20250827_165001.png", "resolved_screenshot": "screenshots/screenshot_20250827_165001.png", "clean_action_id": "screenshot_20250827_165001", "prefixed_action_id": "al_screenshot_20250827_165001", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_165001.png", "action_id_screenshot": "screenshots/screenshot_20250827_165001.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "status": "passed", "duration": "1106ms", "action_id": "screenshot_20250827_170430", "screenshot_filename": "screenshot_20250827_170430.png", "report_screenshot": "screenshot_20250827_170430.png", "resolved_screenshot": "screenshots/screenshot_20250827_170430.png", "clean_action_id": "screenshot_20250827_170430", "prefixed_action_id": "al_screenshot_20250827_170430", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_170430.png", "action_id_screenshot": "screenshots/screenshot_20250827_170430.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2633ms", "action_id": "screenshot_20250827_171504", "screenshot_filename": "screenshot_20250827_171504.png", "report_screenshot": "screenshot_20250827_171504.png", "resolved_screenshot": "screenshots/screenshot_20250827_171504.png", "clean_action_id": "screenshot_20250827_171504", "prefixed_action_id": "al_screenshot_20250827_171504", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171504.png", "action_id_screenshot": "screenshots/screenshot_20250827_171504.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@content-desc=\"txtSign out\"]", "status": "passed", "duration": "1721ms", "action_id": "screenshot_20250827_181152", "screenshot_filename": "screenshot_20250827_181152.png", "report_screenshot": "screenshot_20250827_181152.png", "resolved_screenshot": "screenshots/screenshot_20250827_181152.png", "clean_action_id": "screenshot_20250827_181152", "prefixed_action_id": "al_screenshot_20250827_181152", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181152.png", "action_id_screenshot": "screenshots/screenshot_20250827_181152.png"}]}, {"name": "WishList_AU-Android\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Edit\n                            \n                            \n                                 Remove\n                            \n                            50 actions", "status": "passed", "steps": [{"name": "Execute Test Case: Onboarding-Start-AU (6 steps)", "status": "passed", "duration": "289ms", "action_id": "Onboarding", "screenshot_filename": "Onboarding.png", "report_screenshot": "Onboarding.png", "resolved_screenshot": "screenshots/Onboarding.png", "clean_action_id": "Onboarding", "prefixed_action_id": "al_Onboarding", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/Onboarding.png", "action_id_screenshot": "screenshots/Onboarding.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "3925ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png", "clean_action_id": "accessibil", "prefixed_action_id": "al_accessibil", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/accessibil.png", "action_id_screenshot": "screenshots/accessibil.png"}, {"name": "Execute Test Case: Kmart-Signin-AU-ANDROID (7 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250827_180909", "screenshot_filename": "screenshot_20250827_180909.png", "report_screenshot": "screenshot_20250827_180909.png", "resolved_screenshot": "screenshots/screenshot_20250827_180909.png", "clean_action_id": "screenshot_20250827_180909", "prefixed_action_id": "al_screenshot_20250827_180909", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_180909.png", "action_id_screenshot": "screenshots/screenshot_20250827_180909.png"}, {"name": "Check if element with xpath=\"//android.view.View[@content-desc=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "5463ms", "action_id": "txtHomeGre", "screenshot_filename": "txtHomeGre.png", "report_screenshot": "txtHomeGre.png", "resolved_screenshot": "screenshots/txtHomeGre.png", "clean_action_id": "txtHomeGre", "prefixed_action_id": "al_txtHomeGre", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/txtHomeGre.png", "action_id_screenshot": "screenshots/txtHomeGre.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3571ms", "action_id": "screenshot_20250827_175214", "screenshot_filename": "screenshot_20250827_175214.png", "report_screenshot": "screenshot_20250827_175214.png", "resolved_screenshot": "screenshots/screenshot_20250827_175214.png", "clean_action_id": "screenshot_20250827_175214", "prefixed_action_id": "al_screenshot_20250827_175214", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175214.png", "action_id_screenshot": "screenshots/screenshot_20250827_175214.png"}, {"name": "Input text: \"Uno card\"", "status": "passed", "duration": "1246ms", "action_id": "screenshot_20250827_171933", "screenshot_filename": "screenshot_20250827_171933.png", "report_screenshot": "screenshot_20250827_171933.png", "resolved_screenshot": "screenshots/screenshot_20250827_171933.png", "clean_action_id": "screenshot_20250827_171933", "prefixed_action_id": "al_screenshot_20250827_171933", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171933.png", "action_id_screenshot": "screenshots/screenshot_20250827_171933.png"}, {"name": "Android Function: send_key_event - Key Event: ENTER", "status": "passed", "duration": "1210ms", "action_id": "XoMyLp2unA", "screenshot_filename": "XoMyLp2unA.png", "report_screenshot": "XoMyLp2unA.png", "resolved_screenshot": "screenshots/XoMyLp2unA.png", "clean_action_id": "XoMyLp2unA", "prefixed_action_id": "al_XoMyLp2unA", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/XoMyLp2unA.png", "action_id_screenshot": "screenshots/XoMyLp2unA.png"}, {"name": "Wait till xpath=//android.widget.Button[@text=\"Filter\"]", "status": "passed", "duration": "5480ms", "action_id": "screenshot_20250827_180538", "screenshot_filename": "screenshot_20250827_180538.png", "report_screenshot": "screenshot_20250827_180538.png", "resolved_screenshot": "screenshots/screenshot_20250827_180538.png", "clean_action_id": "screenshot_20250827_180538", "prefixed_action_id": "al_screenshot_20250827_180538", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_180538.png", "action_id_screenshot": "screenshots/screenshot_20250827_180538.png"}, {"name": "Tap on element with xpath: ((//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView[1])[1]", "status": "passed", "duration": "1163ms", "action_id": "screenshot_20250827_172631", "screenshot_filename": "screenshot_20250827_172631.png", "report_screenshot": "screenshot_20250827_172631.png", "resolved_screenshot": "screenshots/screenshot_20250827_172631.png", "clean_action_id": "screenshot_20250827_172631", "prefixed_action_id": "al_screenshot_20250827_172631", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_172631.png", "action_id_screenshot": "screenshots/screenshot_20250827_172631.png"}, {"name": "Wait till xpath=//android.widget.TextView[contains(@text,\"SKU\")]", "status": "passed", "duration": "3026ms", "action_id": "screenshot_20250827_181008", "screenshot_filename": "screenshot_20250827_181008.png", "report_screenshot": "screenshot_20250827_181008.png", "resolved_screenshot": "screenshots/screenshot_20250827_181008.png", "clean_action_id": "screenshot_20250827_181008", "prefixed_action_id": "al_screenshot_20250827_181008", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181008.png", "action_id_screenshot": "screenshots/screenshot_20250827_181008.png"}, {"name": "Swipe up till element xpath: \"//android.widget.Button[@resource-id=\"wishlist-button\"]\" is visible", "status": "passed", "duration": "2871ms", "action_id": "screenshot_20250827_181034", "screenshot_filename": "screenshot_20250827_181034.png", "report_screenshot": "screenshot_20250827_181034.png", "resolved_screenshot": "screenshots/screenshot_20250827_181034.png", "clean_action_id": "screenshot_20250827_181034", "prefixed_action_id": "al_screenshot_20250827_181034", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181034.png", "action_id_screenshot": "screenshots/screenshot_20250827_181034.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@resource-id=\"wishlist-button\"]", "status": "passed", "duration": "1380ms", "action_id": "screenshot_20250827_173936", "screenshot_filename": "screenshot_20250827_173936.png", "report_screenshot": "screenshot_20250827_173936.png", "resolved_screenshot": "screenshots/screenshot_20250827_173936.png", "clean_action_id": "screenshot_20250827_173936", "prefixed_action_id": "al_screenshot_20250827_173936", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_173936.png", "action_id_screenshot": "screenshots/screenshot_20250827_173936.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 2 of 5\")]", "status": "passed", "duration": "1348ms", "action_id": "VXo5C08UOn", "screenshot_filename": "VXo5C08UOn.png", "report_screenshot": "VXo5C08UOn.png", "resolved_screenshot": "screenshots/VXo5C08UOn.png", "clean_action_id": "VXo5C08UOn", "prefixed_action_id": "al_VXo5C08UOn", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/VXo5C08UOn.png", "action_id_screenshot": "screenshots/VXo5C08UOn.png"}, {"name": "Tap on image: search-glassimage-android.png", "status": "passed", "duration": "7238ms", "action_id": "glassimage", "screenshot_filename": "glassimage.png", "report_screenshot": "glassimage.png", "resolved_screenshot": "screenshots/glassimage.png", "clean_action_id": "glassimage", "prefixed_action_id": "al_glassimage", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/glassimage.png", "action_id_screenshot": "screenshots/glassimage.png"}, {"name": "Input text: \"mat\"", "status": "passed", "duration": "947ms", "action_id": "screenshot_20250827_165600", "screenshot_filename": "screenshot_20250827_165600.png", "report_screenshot": "screenshot_20250827_165600.png", "resolved_screenshot": "screenshots/screenshot_20250827_165600.png", "clean_action_id": "screenshot_20250827_165600", "prefixed_action_id": "al_screenshot_20250827_165600", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_165600.png", "action_id_screenshot": "screenshots/screenshot_20250827_165600.png"}, {"name": "Android Function: send_key_event - Key Event: ENTER", "status": "passed", "duration": "830ms", "action_id": "screenshot_20250827_175349", "screenshot_filename": "screenshot_20250827_175349.png", "report_screenshot": "screenshot_20250827_175349.png", "resolved_screenshot": "screenshots/screenshot_20250827_175349.png", "clean_action_id": "screenshot_20250827_175349", "prefixed_action_id": "al_screenshot_20250827_175349", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175349.png", "action_id_screenshot": "screenshots/screenshot_20250827_175349.png"}, {"name": "Wait till xpath=//android.widget.Button[@text=\"Filter\"]", "status": "passed", "duration": "13549ms", "action_id": "screenshot_20250827_181021", "screenshot_filename": "screenshot_20250827_181021.png", "report_screenshot": "screenshot_20250827_181021.png", "resolved_screenshot": "screenshots/screenshot_20250827_181021.png", "clean_action_id": "screenshot_20250827_181021", "prefixed_action_id": "al_screenshot_20250827_181021", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181021.png", "action_id_screenshot": "screenshots/screenshot_20250827_181021.png"}, {"name": "Tap on element with xpath: ((//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView[1])[1]", "status": "passed", "duration": "1061ms", "action_id": "xblqGQSWzC", "screenshot_filename": "xblqGQSWzC.png", "report_screenshot": "xblqGQSWzC.png", "resolved_screenshot": "screenshots/xblqGQSWzC.png", "clean_action_id": "xblqGQSWzC", "prefixed_action_id": "al_xblqGQSWzC", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/xblqGQSWzC.png", "action_id_screenshot": "screenshots/xblqGQSWzC.png"}, {"name": "Wait till xpath=//android.widget.TextView[contains(@text,\"SKU\")]", "status": "passed", "duration": "3553ms", "action_id": "screenshot_20250827_174241", "screenshot_filename": "screenshot_20250827_174241.png", "report_screenshot": "screenshot_20250827_174241.png", "resolved_screenshot": "screenshots/screenshot_20250827_174241.png", "clean_action_id": "screenshot_20250827_174241", "prefixed_action_id": "al_screenshot_20250827_174241", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174241.png", "action_id_screenshot": "screenshots/screenshot_20250827_174241.png"}, {"name": "Swipe up till element xpath: \"//android.widget.Button[@resource-id=\"wishlist-button\"]\" is visible", "status": "passed", "duration": "2650ms", "action_id": "FIBbvTJFpg", "screenshot_filename": "FIBbvTJFpg.png", "report_screenshot": "FIBbvTJFpg.png", "resolved_screenshot": "screenshots/FIBbvTJFpg.png", "clean_action_id": "FIBbvTJFpg", "prefixed_action_id": "al_FIBbvTJFpg", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/FIBbvTJFpg.png", "action_id_screenshot": "screenshots/FIBbvTJFpg.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@resource-id=\"wishlist-button\"]", "status": "passed", "duration": "761ms", "action_id": "screenshot_20250827_174309", "screenshot_filename": "screenshot_20250827_174309.png", "report_screenshot": "screenshot_20250827_174309.png", "resolved_screenshot": "screenshots/screenshot_20250827_174309.png", "clean_action_id": "screenshot_20250827_174309", "prefixed_action_id": "al_screenshot_20250827_174309", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174309.png", "action_id_screenshot": "screenshots/screenshot_20250827_174309.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 2 of 5\")]", "status": "passed", "duration": "711ms", "action_id": "screenshot_20250827_164132", "screenshot_filename": "screenshot_20250827_164132.png", "report_screenshot": "screenshot_20250827_164132.png", "resolved_screenshot": "screenshots/screenshot_20250827_164132.png", "clean_action_id": "screenshot_20250827_164132", "prefixed_action_id": "al_screenshot_20250827_164132", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_164132.png", "action_id_screenshot": "screenshots/screenshot_20250827_164132.png"}, {"name": "Tap on image: search-glassimage-android.png", "status": "passed", "duration": "7488ms", "action_id": "glassimage", "screenshot_filename": "glassimage.png", "report_screenshot": "glassimage.png", "resolved_screenshot": "screenshots/glassimage.png", "clean_action_id": "glassimage", "prefixed_action_id": "al_glassimage", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/glassimage.png", "action_id_screenshot": "screenshots/glassimage.png"}, {"name": "Input text: \"notepads\"", "status": "passed", "duration": "1007ms", "action_id": "screenshot_20250827_175229", "screenshot_filename": "screenshot_20250827_175229.png", "report_screenshot": "screenshot_20250827_175229.png", "resolved_screenshot": "screenshots/screenshot_20250827_175229.png", "clean_action_id": "screenshot_20250827_175229", "prefixed_action_id": "al_screenshot_20250827_175229", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175229.png", "action_id_screenshot": "screenshots/screenshot_20250827_175229.png"}, {"name": "Android Function: send_key_event - Key Event: ENTER", "status": "passed", "duration": "895ms", "action_id": "screenshot_20250827_172752", "screenshot_filename": "screenshot_20250827_172752.png", "report_screenshot": "screenshot_20250827_172752.png", "resolved_screenshot": "screenshots/screenshot_20250827_172752.png", "clean_action_id": "screenshot_20250827_172752", "prefixed_action_id": "al_screenshot_20250827_172752", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_172752.png", "action_id_screenshot": "screenshots/screenshot_20250827_172752.png"}, {"name": "Wait till xpath=//android.widget.Button[@text=\"Filter\"]", "status": "passed", "duration": "7673ms", "action_id": "screenshot_20250827_175015", "screenshot_filename": "screenshot_20250827_175015.png", "report_screenshot": "screenshot_20250827_175015.png", "resolved_screenshot": "screenshots/screenshot_20250827_175015.png", "clean_action_id": "screenshot_20250827_175015", "prefixed_action_id": "al_screenshot_20250827_175015", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175015.png", "action_id_screenshot": "screenshots/screenshot_20250827_175015.png"}, {"name": "Tap on element with xpath: ((//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView[1])[1]", "status": "passed", "duration": "2002ms", "action_id": "R0xsCnCRFk", "screenshot_filename": "R0xsCnCRFk.png", "report_screenshot": "R0xsCnCRFk.png", "resolved_screenshot": "screenshots/R0xsCnCRFk.png", "clean_action_id": "R0xsCnCRFk", "prefixed_action_id": "al_R0xsCnCRFk", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/R0xsCnCRFk.png", "action_id_screenshot": "screenshots/R0xsCnCRFk.png"}, {"name": "Wait till xpath=//android.widget.TextView[contains(@text,\"SKU\")]", "status": "passed", "duration": "4556ms", "action_id": "uZEEeTeb7p", "screenshot_filename": "uZEEeTeb7p.png", "report_screenshot": "uZEEeTeb7p.png", "resolved_screenshot": "screenshots/uZEEeTeb7p.png", "clean_action_id": "uZEEeTeb7p", "prefixed_action_id": "al_uZEEeTeb7p", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/uZEEeTeb7p.png", "action_id_screenshot": "screenshots/uZEEeTeb7p.png"}, {"name": "Swipe up till element xpath: \"//android.widget.Button[@resource-id=\"wishlist-button\"]\" is visible", "status": "passed", "duration": "3802ms", "action_id": "V9ldRojdyD", "screenshot_filename": "V9ldRojdyD.png", "report_screenshot": "V9ldRojdyD.png", "resolved_screenshot": "screenshots/V9ldRojdyD.png", "clean_action_id": "V9ldRojdyD", "prefixed_action_id": "al_V9ldRojdyD", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/V9ldRojdyD.png", "action_id_screenshot": "screenshots/V9ldRojdyD.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@resource-id=\"wishlist-button\"]", "status": "passed", "duration": "1167ms", "action_id": "screenshot_20250827_171844", "screenshot_filename": "screenshot_20250827_171844.png", "report_screenshot": "screenshot_20250827_171844.png", "resolved_screenshot": "screenshots/screenshot_20250827_171844.png", "clean_action_id": "screenshot_20250827_171844", "prefixed_action_id": "al_screenshot_20250827_171844", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171844.png", "action_id_screenshot": "screenshots/screenshot_20250827_171844.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 3 of 5\")]", "status": "passed", "duration": "1487ms", "action_id": "screenshot_20250827_171138", "screenshot_filename": "screenshot_20250827_171138.png", "report_screenshot": "screenshot_20250827_171138.png", "resolved_screenshot": "screenshots/screenshot_20250827_171138.png", "clean_action_id": "screenshot_20250827_171138", "prefixed_action_id": "al_screenshot_20250827_171138", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171138.png", "action_id_screenshot": "screenshots/screenshot_20250827_171138.png"}, {"name": "Tap on element with xpath: (//android.widget.Button[@content-desc=\"txtAdd to Bag\"])[1]/preceding-sibling::android.widget.ImageView[1]", "status": "passed", "duration": "640ms", "action_id": "screenshot_20250827_174732", "screenshot_filename": "screenshot_20250827_174732.png", "report_screenshot": "screenshot_20250827_174732.png", "resolved_screenshot": "screenshots/screenshot_20250827_174732.png", "clean_action_id": "screenshot_20250827_174732", "prefixed_action_id": "al_screenshot_20250827_174732", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174732.png", "action_id_screenshot": "screenshots/screenshot_20250827_174732.png"}, {"name": "Tap on Text: \"Move\"", "status": "passed", "duration": "2947ms", "action_id": "screenshot_20250827_173505", "screenshot_filename": "screenshot_20250827_173505.png", "report_screenshot": "screenshot_20250827_173505.png", "resolved_screenshot": "screenshots/screenshot_20250827_173505.png", "clean_action_id": "screenshot_20250827_173505", "prefixed_action_id": "al_screenshot_20250827_173505", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_173505.png", "action_id_screenshot": "screenshots/screenshot_20250827_173505.png"}, {"name": "Tap on element with xpath: (//android.widget.Button[@content-desc=\"txtAdd to Bag\"])[1]/preceding-sibling::android.widget.ImageView[1]", "status": "passed", "duration": "1867ms", "action_id": "y5FboDiRLS", "screenshot_filename": "y5FboDiRLS.png", "report_screenshot": "y5FboDiRLS.png", "resolved_screenshot": "screenshots/y5FboDiRLS.png", "clean_action_id": "y5FboDiRLS", "prefixed_action_id": "al_y5FboDiRLS", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/y5FboDiRLS.png", "action_id_screenshot": "screenshots/y5FboDiRLS.png"}, {"name": "Tap on Text: \"Remove\"", "status": "passed", "duration": "2386ms", "action_id": "SeiSGMuR9u", "screenshot_filename": "SeiSGMuR9u.png", "report_screenshot": "SeiSGMuR9u.png", "resolved_screenshot": "screenshots/SeiSGMuR9u.png", "clean_action_id": "SeiSGMuR9u", "prefixed_action_id": "al_SeiSGMuR9u", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/SeiSGMuR9u.png", "action_id_screenshot": "screenshots/SeiSGMuR9u.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "status": "passed", "duration": "841ms", "action_id": "screenshot_20250827_172021", "screenshot_filename": "screenshot_20250827_172021.png", "report_screenshot": "screenshot_20250827_172021.png", "resolved_screenshot": "screenshots/screenshot_20250827_172021.png", "clean_action_id": "screenshot_20250827_172021", "prefixed_action_id": "al_screenshot_20250827_172021", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_172021.png", "action_id_screenshot": "screenshots/screenshot_20250827_172021.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5021ms", "action_id": "screenshot_20250827_181340", "screenshot_filename": "screenshot_20250827_181340.png", "report_screenshot": "screenshot_20250827_181340.png", "resolved_screenshot": "screenshots/screenshot_20250827_181340.png", "clean_action_id": "screenshot_20250827_181340", "prefixed_action_id": "al_screenshot_20250827_181340", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181340.png", "action_id_screenshot": "screenshots/screenshot_20250827_181340.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@content-desc=\"Checkout\"]", "status": "passed", "duration": "791ms", "action_id": "screenshot_20250827_170420", "screenshot_filename": "screenshot_20250827_170420.png", "report_screenshot": "screenshot_20250827_170420.png", "resolved_screenshot": "screenshots/screenshot_20250827_170420.png", "clean_action_id": "screenshot_20250827_170420", "prefixed_action_id": "al_screenshot_20250827_170420", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_170420.png", "action_id_screenshot": "screenshots/screenshot_20250827_170420.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5019ms", "action_id": "g8u66qfKkX", "screenshot_filename": "g8u66qfKkX.png", "report_screenshot": "g8u66qfKkX.png", "resolved_screenshot": "screenshots/g8u66qfKkX.png", "clean_action_id": "g8u66qfKkX", "prefixed_action_id": "al_g8u66qfKkX", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/g8u66qfKkX.png", "action_id_screenshot": "screenshots/g8u66qfKkX.png"}, {"name": "Swipe up till element xpath: \"//android.widget.Button[@text=\"Move to Wishlist\"]\" is visible", "status": "passed", "duration": "6463ms", "action_id": "screenshot_20250827_165001", "screenshot_filename": "screenshot_20250827_165001.png", "report_screenshot": "screenshot_20250827_165001.png", "resolved_screenshot": "screenshots/screenshot_20250827_165001.png", "clean_action_id": "screenshot_20250827_165001", "prefixed_action_id": "al_screenshot_20250827_165001", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_165001.png", "action_id_screenshot": "screenshots/screenshot_20250827_165001.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@text=\"Move to Wishlist\"]", "status": "passed", "duration": "927ms", "action_id": "screenshot_20250827_170430", "screenshot_filename": "screenshot_20250827_170430.png", "report_screenshot": "screenshot_20250827_170430.png", "resolved_screenshot": "screenshots/screenshot_20250827_170430.png", "clean_action_id": "screenshot_20250827_170430", "prefixed_action_id": "al_screenshot_20250827_170430", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_170430.png", "action_id_screenshot": "screenshots/screenshot_20250827_170430.png"}, {"name": "Tap on element with xpath: //android.widget.TextView[@text=\"Continue shopping\"]", "status": "passed", "duration": "1752ms", "action_id": "screenshot_20250827_171504", "screenshot_filename": "screenshot_20250827_171504.png", "report_screenshot": "screenshot_20250827_171504.png", "resolved_screenshot": "screenshots/screenshot_20250827_171504.png", "clean_action_id": "screenshot_20250827_171504", "prefixed_action_id": "al_screenshot_20250827_171504", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171504.png", "action_id_screenshot": "screenshots/screenshot_20250827_171504.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 3 of 5\")]", "status": "passed", "duration": "1617ms", "action_id": "screenshot_20250827_181152", "screenshot_filename": "screenshot_20250827_181152.png", "report_screenshot": "screenshot_20250827_181152.png", "resolved_screenshot": "screenshots/screenshot_20250827_181152.png", "clean_action_id": "screenshot_20250827_181152", "prefixed_action_id": "al_screenshot_20250827_181152", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181152.png", "action_id_screenshot": "screenshots/screenshot_20250827_181152.png"}, {"name": "Action: ifThenSteps", "status": "passed", "duration": "1460ms", "action_id": "ifThenStep", "screenshot_filename": "ifThenStep.png", "report_screenshot": "ifThenStep.png", "resolved_screenshot": "screenshots/ifThenStep.png", "clean_action_id": "ifThenStep", "prefixed_action_id": "al_ifThenStep", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/ifThenStep.png", "action_id_screenshot": "screenshots/ifThenStep.png"}, {"name": "Tap on Text: \"Remove\"", "status": "passed", "duration": "2936ms", "action_id": "Wb6cwBudqO", "screenshot_filename": "Wb6cwBudqO.png", "report_screenshot": "Wb6cwBudqO.png", "resolved_screenshot": "screenshots/Wb6cwBudqO.png", "clean_action_id": "Wb6cwBudqO", "prefixed_action_id": "al_Wb6cwBudqO", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/Wb6cwBudqO.png", "action_id_screenshot": "screenshots/Wb6cwBudqO.png"}, {"name": "Action: ifThenSteps", "status": "passed", "duration": "927ms", "action_id": "ifThenStep", "screenshot_filename": "ifThenStep.png", "report_screenshot": "ifThenStep.png", "resolved_screenshot": "screenshots/ifThenStep.png", "clean_action_id": "ifThenStep", "prefixed_action_id": "al_ifThenStep", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/ifThenStep.png", "action_id_screenshot": "screenshots/ifThenStep.png"}, {"name": "Tap on Text: \"Remove\"", "status": "passed", "duration": "2262ms", "action_id": "mcscWdhpn2", "screenshot_filename": "mcscWdhpn2.png", "report_screenshot": "mcscWdhpn2.png", "resolved_screenshot": "screenshots/mcscWdhpn2.png", "clean_action_id": "mcscWdhpn2", "prefixed_action_id": "al_mcscWdhpn2", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/mcscWdhpn2.png", "action_id_screenshot": "screenshots/mcscWdhpn2.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "status": "passed", "duration": "503ms", "action_id": "screenshot_20250827_164914", "screenshot_filename": "screenshot_20250827_164914.png", "report_screenshot": "screenshot_20250827_164914.png", "resolved_screenshot": "screenshots/screenshot_20250827_164914.png", "clean_action_id": "screenshot_20250827_164914", "prefixed_action_id": "al_screenshot_20250827_164914", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_164914.png", "action_id_screenshot": "screenshots/screenshot_20250827_164914.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "1011ms", "action_id": "GEMv6goQtW", "screenshot_filename": "GEMv6goQtW.png", "report_screenshot": "GEMv6goQtW.png", "resolved_screenshot": "screenshots/GEMv6goQtW.png", "clean_action_id": "GEMv6goQtW", "prefixed_action_id": "al_GEMv6goQtW", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/GEMv6goQtW.png", "action_id_screenshot": "screenshots/GEMv6goQtW.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@content-desc=\"txtSign out\"]", "status": "passed", "duration": "550ms", "action_id": "MuP17p1Xxx", "screenshot_filename": "MuP17p1Xxx.png", "report_screenshot": "MuP17p1Xxx.png", "resolved_screenshot": "screenshots/MuP17p1Xxx.png", "clean_action_id": "MuP17p1Xxx", "prefixed_action_id": "al_MuP17p1Xxx", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/MuP17p1Xxx.png", "action_id_screenshot": "screenshots/MuP17p1Xxx.png"}]}, {"name": "Browse & PDP_AU_ANDROID\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Edit\n                            \n                            \n                                 Remove\n                            \n                            68 actions", "status": "failed", "steps": [{"name": "Execute Test Case: Onboarding-Start-AU (6 steps)", "status": "passed", "duration": "307ms", "action_id": "Onboarding", "screenshot_filename": "Onboarding.png", "report_screenshot": "Onboarding.png", "resolved_screenshot": "screenshots/Onboarding.png", "clean_action_id": "Onboarding", "prefixed_action_id": "al_Onboarding", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/Onboarding.png", "action_id_screenshot": "screenshots/Onboarding.png"}, {"name": "Launch app: au.com.kmart", "status": "passed", "duration": "200ms", "action_id": "screenshot_20250827_180909", "screenshot_filename": "screenshot_20250827_180909.png", "report_screenshot": "screenshot_20250827_180909.png", "resolved_screenshot": "screenshots/screenshot_20250827_180909.png", "clean_action_id": "screenshot_20250827_180909", "prefixed_action_id": "al_screenshot_20250827_180909", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_180909.png", "action_id_screenshot": "screenshots/screenshot_20250827_180909.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 2 of 5\")]", "status": "passed", "duration": "133ms", "action_id": "screenshot_20250827_175214", "screenshot_filename": "screenshot_20250827_175214.png", "report_screenshot": "screenshot_20250827_175214.png", "resolved_screenshot": "screenshots/screenshot_20250827_175214.png", "clean_action_id": "screenshot_20250827_175214", "prefixed_action_id": "al_screenshot_20250827_175214", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175214.png", "action_id_screenshot": "screenshots/screenshot_20250827_175214.png"}, {"name": "Tap on image: find-products-browse.png", "status": "passed", "duration": "7225ms", "action_id": "screenshot_20250827_171933", "screenshot_filename": "screenshot_20250827_171933.png", "report_screenshot": "screenshot_20250827_171933.png", "resolved_screenshot": "screenshots/screenshot_20250827_171933.png", "clean_action_id": "screenshot_20250827_171933", "prefixed_action_id": "al_screenshot_20250827_171933", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171933.png", "action_id_screenshot": "screenshots/screenshot_20250827_171933.png"}, {"name": "Check if element with xpath=\"//android.widget.ImageView[@content-desc=\"More\"]\" exists", "status": "passed", "duration": "550ms", "action_id": "XoMyLp2unA", "screenshot_filename": "XoMyLp2unA.png", "report_screenshot": "XoMyLp2unA.png", "resolved_screenshot": "screenshots/XoMyLp2unA.png", "clean_action_id": "XoMyLp2unA", "prefixed_action_id": "al_XoMyLp2unA", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/XoMyLp2unA.png", "action_id_screenshot": "screenshots/XoMyLp2unA.png"}, {"name": "Check if element with xpath=\"//android.widget.ImageView[@content-desc=\"Scan barcode\"]\" exists", "status": "passed", "duration": "85ms", "action_id": "screenshot_20250827_180538", "screenshot_filename": "screenshot_20250827_180538.png", "report_screenshot": "screenshot_20250827_180538.png", "resolved_screenshot": "screenshots/screenshot_20250827_180538.png", "clean_action_id": "screenshot_20250827_180538", "prefixed_action_id": "al_screenshot_20250827_180538", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_180538.png", "action_id_screenshot": "screenshots/screenshot_20250827_180538.png"}, {"name": "Tap on element with xpath: //android.view.View[@content-desc=\"Search\"]/preceding::android.widget.ImageView[1]", "status": "passed", "duration": "1109ms", "action_id": "screenshot_20250827_172631", "screenshot_filename": "screenshot_20250827_172631.png", "report_screenshot": "screenshot_20250827_172631.png", "resolved_screenshot": "screenshots/screenshot_20250827_172631.png", "clean_action_id": "screenshot_20250827_172631", "prefixed_action_id": "al_screenshot_20250827_172631", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_172631.png", "action_id_screenshot": "screenshots/screenshot_20250827_172631.png"}, {"name": "Tap on Text: \"Toys\"", "status": "passed", "duration": "2305ms", "action_id": "screenshot_20250827_181008", "screenshot_filename": "screenshot_20250827_181008.png", "report_screenshot": "screenshot_20250827_181008.png", "resolved_screenshot": "screenshots/screenshot_20250827_181008.png", "clean_action_id": "screenshot_20250827_181008", "prefixed_action_id": "al_screenshot_20250827_181008", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181008.png", "action_id_screenshot": "screenshots/screenshot_20250827_181008.png"}, {"name": "Tap on Text: \"Latest\"", "status": "passed", "duration": "3098ms", "action_id": "screenshot_20250827_181034", "screenshot_filename": "screenshot_20250827_181034.png", "report_screenshot": "screenshot_20250827_181034.png", "resolved_screenshot": "screenshots/screenshot_20250827_181034.png", "clean_action_id": "screenshot_20250827_181034", "prefixed_action_id": "al_screenshot_20250827_181034", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181034.png", "action_id_screenshot": "screenshots/screenshot_20250827_181034.png"}, {"name": "Wait till xpath=//android.widget.Button[@text=\"Filter\"]", "status": "passed", "duration": "22902ms", "action_id": "screenshot_20250827_173936", "screenshot_filename": "screenshot_20250827_173936.png", "report_screenshot": "screenshot_20250827_173936.png", "resolved_screenshot": "screenshots/screenshot_20250827_173936.png", "clean_action_id": "screenshot_20250827_173936", "prefixed_action_id": "al_screenshot_20250827_173936", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_173936.png", "action_id_screenshot": "screenshots/screenshot_20250827_173936.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@text=\"Filter\"]", "status": "passed", "duration": "823ms", "action_id": "VXo5C08UOn", "screenshot_filename": "VXo5C08UOn.png", "report_screenshot": "VXo5C08UOn.png", "resolved_screenshot": "screenshots/VXo5C08UOn.png", "clean_action_id": "VXo5C08UOn", "prefixed_action_id": "al_VXo5C08UOn", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/VXo5C08UOn.png", "action_id_screenshot": "screenshots/VXo5C08UOn.png"}, {"name": "Tap on Text: \"only\"", "status": "passed", "duration": "2540ms", "action_id": "screenshot_20250827_165600", "screenshot_filename": "screenshot_20250827_165600.png", "report_screenshot": "screenshot_20250827_165600.png", "resolved_screenshot": "screenshots/screenshot_20250827_165600.png", "clean_action_id": "screenshot_20250827_165600", "prefixed_action_id": "al_screenshot_20250827_165600", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_165600.png", "action_id_screenshot": "screenshots/screenshot_20250827_165600.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5015ms", "action_id": "screenshot_20250827_175349", "screenshot_filename": "screenshot_20250827_175349.png", "report_screenshot": "screenshot_20250827_175349.png", "resolved_screenshot": "screenshots/screenshot_20250827_175349.png", "clean_action_id": "screenshot_20250827_175349", "prefixed_action_id": "al_screenshot_20250827_175349", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175349.png", "action_id_screenshot": "screenshots/screenshot_20250827_175349.png"}, {"name": "Tap on Text: \"Show\"", "status": "passed", "duration": "2174ms", "action_id": "screenshot_20250827_181021", "screenshot_filename": "screenshot_20250827_181021.png", "report_screenshot": "screenshot_20250827_181021.png", "resolved_screenshot": "screenshots/screenshot_20250827_181021.png", "clean_action_id": "screenshot_20250827_181021", "prefixed_action_id": "al_screenshot_20250827_181021", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181021.png", "action_id_screenshot": "screenshots/screenshot_20250827_181021.png"}, {"name": "Check if element with xpath=\"//android.widget.Button[@text=\"In stock only i... ChipsClose\"]\" exists", "status": "passed", "duration": "1058ms", "action_id": "ChipsClose", "screenshot_filename": "ChipsClose.png", "report_screenshot": "ChipsClose.png", "resolved_screenshot": "screenshots/ChipsClose.png", "clean_action_id": "ChipsClose", "prefixed_action_id": "al_ChipsClose", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/ChipsClose.png", "action_id_screenshot": "screenshots/ChipsClose.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@text=\"In stock only i... ChipsClose\"]", "status": "passed", "duration": "1029ms", "action_id": "ChipsClose", "screenshot_filename": "ChipsClose.png", "report_screenshot": "ChipsClose.png", "resolved_screenshot": "screenshots/ChipsClose.png", "clean_action_id": "ChipsClose", "prefixed_action_id": "al_ChipsClose", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/ChipsClose.png", "action_id_screenshot": "screenshots/ChipsClose.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "1858ms", "action_id": "xblqGQSWzC", "screenshot_filename": "xblqGQSWzC.png", "report_screenshot": "xblqGQSWzC.png", "resolved_screenshot": "screenshots/xblqGQSWzC.png", "clean_action_id": "xblqGQSWzC", "prefixed_action_id": "al_xblqGQSWzC", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/xblqGQSWzC.png", "action_id_screenshot": "screenshots/xblqGQSWzC.png"}, {"name": "Wait for 10 ms", "status": "passed", "duration": "10013ms", "action_id": "screenshot_20250827_174241", "screenshot_filename": "screenshot_20250827_174241.png", "report_screenshot": "screenshot_20250827_174241.png", "resolved_screenshot": "screenshots/screenshot_20250827_174241.png", "clean_action_id": "screenshot_20250827_174241", "prefixed_action_id": "al_screenshot_20250827_174241", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174241.png", "action_id_screenshot": "screenshots/screenshot_20250827_174241.png"}, {"name": "Wait till xpath=((//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView[1])[1]", "status": "passed", "duration": "1340ms", "action_id": "FIBbvTJFpg", "screenshot_filename": "FIBbvTJFpg.png", "report_screenshot": "FIBbvTJFpg.png", "resolved_screenshot": "screenshots/FIBbvTJFpg.png", "clean_action_id": "FIBbvTJFpg", "prefixed_action_id": "al_FIBbvTJFpg", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/FIBbvTJFpg.png", "action_id_screenshot": "screenshots/FIBbvTJFpg.png"}, {"name": "Tap on element with xpath: ((//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView[1])[1] with fallback: Coordinates (98, 308)", "status": "passed", "duration": "913ms", "action_id": "Coordinate", "screenshot_filename": "Coordinate.png", "report_screenshot": "Coordinate.png", "resolved_screenshot": "screenshots/Coordinate.png", "clean_action_id": "Coordinate", "prefixed_action_id": "al_Coordinate", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/Coordinate.png", "action_id_screenshot": "screenshots/Coordinate.png"}, {"name": "Tap on element with xpath: //android.view.View[@content-desc=\"Product Details\"]/following-sibling::android.widget.ImageView[1]", "status": "passed", "duration": "263ms", "action_id": "screenshot_20250827_174309", "screenshot_filename": "screenshot_20250827_174309.png", "report_screenshot": "screenshot_20250827_174309.png", "resolved_screenshot": "screenshots/screenshot_20250827_174309.png", "clean_action_id": "screenshot_20250827_174309", "prefixed_action_id": "al_screenshot_20250827_174309", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174309.png", "action_id_screenshot": "screenshots/screenshot_20250827_174309.png"}, {"name": "Check if element with text=\"Share\" exists", "status": "passed", "duration": "2553ms", "action_id": "screenshot_20250827_164132", "screenshot_filename": "screenshot_20250827_164132.png", "report_screenshot": "screenshot_20250827_164132.png", "resolved_screenshot": "screenshots/screenshot_20250827_164132.png", "clean_action_id": "screenshot_20250827_164132", "prefixed_action_id": "al_screenshot_20250827_164132", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_164132.png", "action_id_screenshot": "screenshots/screenshot_20250827_164132.png"}, {"name": "Tap on image: share-close.png", "status": "passed", "duration": "17632ms", "action_id": "screenshot_20250827_175229", "screenshot_filename": "screenshot_20250827_175229.png", "report_screenshot": "screenshot_20250827_175229.png", "resolved_screenshot": "screenshots/screenshot_20250827_175229.png", "clean_action_id": "screenshot_20250827_175229", "prefixed_action_id": "al_screenshot_20250827_175229", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175229.png", "action_id_screenshot": "screenshots/screenshot_20250827_175229.png"}, {"name": "Swipe up till element xpath: \"//android.widget.TextView[@text=\"Learn moreabout AfterPay\"]\" is visible", "status": "passed", "duration": "2086ms", "action_id": "screenshot_20250827_172752", "screenshot_filename": "screenshot_20250827_172752.png", "report_screenshot": "screenshot_20250827_172752.png", "resolved_screenshot": "screenshots/screenshot_20250827_172752.png", "clean_action_id": "screenshot_20250827_172752", "prefixed_action_id": "al_screenshot_20250827_172752", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_172752.png", "action_id_screenshot": "screenshots/screenshot_20250827_172752.png"}, {"name": "Tap on element with xpath: //android.widget.TextView[@text=\"Learn moreabout AfterPay\"]", "status": "passed", "duration": "1194ms", "action_id": "screenshot_20250827_175015", "screenshot_filename": "screenshot_20250827_175015.png", "report_screenshot": "screenshot_20250827_175015.png", "resolved_screenshot": "screenshots/screenshot_20250827_175015.png", "clean_action_id": "screenshot_20250827_175015", "prefixed_action_id": "al_screenshot_20250827_175015", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175015.png", "action_id_screenshot": "screenshots/screenshot_20250827_175015.png"}, {"name": "Check if element with text=\"Apply\" exists", "status": "passed", "duration": "3291ms", "action_id": "R0xsCnCRFk", "screenshot_filename": "R0xsCnCRFk.png", "report_screenshot": "R0xsCnCRFk.png", "resolved_screenshot": "screenshots/R0xsCnCRFk.png", "clean_action_id": "R0xsCnCRFk", "prefixed_action_id": "al_R0xsCnCRFk", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/R0xsCnCRFk.png", "action_id_screenshot": "screenshots/R0xsCnCRFk.png"}, {"name": "Android Function: send_key_event - Key Event: BACK", "status": "passed", "duration": "109ms", "action_id": "uZEEeTeb7p", "screenshot_filename": "uZEEeTeb7p.png", "report_screenshot": "uZEEeTeb7p.png", "resolved_screenshot": "screenshots/uZEEeTeb7p.png", "clean_action_id": "uZEEeTeb7p", "prefixed_action_id": "al_uZEEeTeb7p", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/uZEEeTeb7p.png", "action_id_screenshot": "screenshots/uZEEeTeb7p.png"}, {"name": "Tap on element with xpath: //android.widget.TextView[@text=\"Learn moreabout Zip\"]", "status": "passed", "duration": "490ms", "action_id": "V9ldRojdyD", "screenshot_filename": "V9ldRojdyD.png", "report_screenshot": "V9ldRojdyD.png", "resolved_screenshot": "screenshots/V9ldRojdyD.png", "clean_action_id": "V9ldRojdyD", "prefixed_action_id": "al_V9ldRojdyD", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/V9ldRojdyD.png", "action_id_screenshot": "screenshots/V9ldRojdyD.png"}, {"name": "Check if element with text=\"What\" exists", "status": "passed", "duration": "9627ms", "action_id": "screenshot_20250827_171844", "screenshot_filename": "screenshot_20250827_171844.png", "report_screenshot": "screenshot_20250827_171844.png", "resolved_screenshot": "screenshots/screenshot_20250827_171844.png", "clean_action_id": "screenshot_20250827_171844", "prefixed_action_id": "al_screenshot_20250827_171844", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171844.png", "action_id_screenshot": "screenshots/screenshot_20250827_171844.png"}, {"name": "Android Function: send_key_event - Key Event: BACK", "status": "passed", "duration": "538ms", "action_id": "screenshot_20250827_171138", "screenshot_filename": "screenshot_20250827_171138.png", "report_screenshot": "screenshot_20250827_171138.png", "resolved_screenshot": "screenshots/screenshot_20250827_171138.png", "clean_action_id": "screenshot_20250827_171138", "prefixed_action_id": "al_screenshot_20250827_171138", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171138.png", "action_id_screenshot": "screenshots/screenshot_20250827_171138.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@text=\"Learn more about PayPal Pay in 4\"]", "status": "passed", "duration": "653ms", "action_id": "screenshot_20250827_174732", "screenshot_filename": "screenshot_20250827_174732.png", "report_screenshot": "screenshot_20250827_174732.png", "resolved_screenshot": "screenshots/screenshot_20250827_174732.png", "clean_action_id": "screenshot_20250827_174732", "prefixed_action_id": "al_screenshot_20250827_174732", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174732.png", "action_id_screenshot": "screenshots/screenshot_20250827_174732.png"}, {"name": "Check if element with text=\"interest-\" exists", "status": "passed", "duration": "27412ms", "action_id": "screenshot_20250827_173505", "screenshot_filename": "screenshot_20250827_173505.png", "report_screenshot": "screenshot_20250827_173505.png", "resolved_screenshot": "screenshots/screenshot_20250827_173505.png", "clean_action_id": "screenshot_20250827_173505", "prefixed_action_id": "al_screenshot_20250827_173505", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_173505.png", "action_id_screenshot": "screenshots/screenshot_20250827_173505.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@resource-id=\"close-btn\"]", "status": "passed", "duration": "970ms", "action_id": "y5FboDiRLS", "screenshot_filename": "y5FboDiRLS.png", "report_screenshot": "y5FboDiRLS.png", "resolved_screenshot": "screenshots/y5FboDiRLS.png", "clean_action_id": "y5FboDiRLS", "prefixed_action_id": "al_y5FboDiRLS", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/y5FboDiRLS.png", "action_id_screenshot": "screenshots/y5FboDiRLS.png"}, {"name": "Swipe from (50%, 70%) to (50%, 40%)", "status": "passed", "duration": "1604ms", "action_id": "SeiSGMuR9u", "screenshot_filename": "SeiSGMuR9u.png", "report_screenshot": "SeiSGMuR9u.png", "resolved_screenshot": "screenshots/SeiSGMuR9u.png", "clean_action_id": "SeiSGMuR9u", "prefixed_action_id": "al_SeiSGMuR9u", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/SeiSGMuR9u.png", "action_id_screenshot": "screenshots/SeiSGMuR9u.png"}, {"name": "Tap on element with xpath: //android.widget.TextView[@text=\"Shop at\"]/following-sibling::android.widget.Button", "status": "passed", "duration": "573ms", "action_id": "screenshot_20250827_172021", "screenshot_filename": "screenshot_20250827_172021.png", "report_screenshot": "screenshot_20250827_172021.png", "resolved_screenshot": "screenshots/screenshot_20250827_172021.png", "clean_action_id": "screenshot_20250827_172021", "prefixed_action_id": "al_screenshot_20250827_172021", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_172021.png", "action_id_screenshot": "screenshots/screenshot_20250827_172021.png"}, {"name": "Check if element with xpath=\"//android.view.View[@content-desc=\"txtLocationTitle\"]\" exists", "status": "passed", "duration": "99ms", "action_id": "txtLocatio", "screenshot_filename": "txtLocatio.png", "report_screenshot": "txtLocatio.png", "resolved_screenshot": "screenshots/txtLocatio.png", "clean_action_id": "txtLocatio", "prefixed_action_id": "al_txtLocatio", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/txtLocatio.png", "action_id_screenshot": "screenshots/txtLocatio.png"}, {"name": "Tap on element with xpath: //android.view.View[@content-desc=\"stnPostCodeSelectionScreenBodyWidget\"]/android.view.View[1]/android.widget.ImageView", "status": "passed", "duration": "773ms", "action_id": "stnPostCod", "screenshot_filename": "stnPostCod.png", "report_screenshot": "stnPostCod.png", "resolved_screenshot": "screenshots/stnPostCod.png", "clean_action_id": "stnPostCod", "prefixed_action_id": "al_stnPostCod", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/stnPostCod.png", "action_id_screenshot": "screenshots/stnPostCod.png"}, {"name": "Terminate app: au.com.kmart", "status": "passed", "duration": "457ms", "action_id": "screenshot_20250827_181340", "screenshot_filename": "screenshot_20250827_181340.png", "report_screenshot": "screenshot_20250827_181340.png", "resolved_screenshot": "screenshots/screenshot_20250827_181340.png", "clean_action_id": "screenshot_20250827_181340", "prefixed_action_id": "al_screenshot_20250827_181340", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181340.png", "action_id_screenshot": "screenshots/screenshot_20250827_181340.png"}, {"name": "Launch app: au.com.kmart", "status": "passed", "duration": "254ms", "action_id": "screenshot_20250827_170420", "screenshot_filename": "screenshot_20250827_170420.png", "report_screenshot": "screenshot_20250827_170420.png", "resolved_screenshot": "screenshots/screenshot_20250827_170420.png", "clean_action_id": "screenshot_20250827_170420", "prefixed_action_id": "al_screenshot_20250827_170420", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_170420.png", "action_id_screenshot": "screenshots/screenshot_20250827_170420.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 2 of 5\")]", "status": "passed", "duration": "3887ms", "action_id": "g8u66qfKkX", "screenshot_filename": "g8u66qfKkX.png", "report_screenshot": "g8u66qfKkX.png", "resolved_screenshot": "screenshots/g8u66qfKkX.png", "clean_action_id": "g8u66qfKkX", "prefixed_action_id": "al_g8u66qfKkX", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/g8u66qfKkX.png", "action_id_screenshot": "screenshots/g8u66qfKkX.png"}, {"name": "Tap on image: find-products-browse.png", "status": "passed", "duration": "36815ms", "action_id": "screenshot_20250827_165001", "screenshot_filename": "screenshot_20250827_165001.png", "report_screenshot": "screenshot_20250827_165001.png", "resolved_screenshot": "screenshots/screenshot_20250827_165001.png", "clean_action_id": "screenshot_20250827_165001", "prefixed_action_id": "al_screenshot_20250827_165001", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_165001.png", "action_id_screenshot": "screenshots/screenshot_20250827_165001.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "2242ms", "action_id": "screenshot_20250827_170430", "screenshot_filename": "screenshot_20250827_170430.png", "report_screenshot": "screenshot_20250827_170430.png", "resolved_screenshot": "screenshots/screenshot_20250827_170430.png", "clean_action_id": "screenshot_20250827_170430", "prefixed_action_id": "al_screenshot_20250827_170430", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_170430.png", "action_id_screenshot": "screenshots/screenshot_20250827_170430.png"}, {"name": "Input text: \"Kids Toys\"", "status": "passed", "duration": "585ms", "action_id": "screenshot_20250827_171504", "screenshot_filename": "screenshot_20250827_171504.png", "report_screenshot": "screenshot_20250827_171504.png", "resolved_screenshot": "screenshots/screenshot_20250827_171504.png", "clean_action_id": "screenshot_20250827_171504", "prefixed_action_id": "al_screenshot_20250827_171504", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171504.png", "action_id_screenshot": "screenshots/screenshot_20250827_171504.png"}, {"name": "Android Function: send_key_event - Key Event: ENTER", "status": "passed", "duration": "19301ms", "action_id": "screenshot_20250827_181152", "screenshot_filename": "screenshot_20250827_181152.png", "report_screenshot": "screenshot_20250827_181152.png", "resolved_screenshot": "screenshots/screenshot_20250827_181152.png", "clean_action_id": "screenshot_20250827_181152", "prefixed_action_id": "al_screenshot_20250827_181152", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181152.png", "action_id_screenshot": "screenshots/screenshot_20250827_181152.png"}, {"name": "Wait till xpath=//android.widget.Button[@text=\"Filter\"]", "status": "passed", "duration": "7418ms", "action_id": "Wb6cwBudqO", "screenshot_filename": "Wb6cwBudqO.png", "report_screenshot": "Wb6cwBudqO.png", "resolved_screenshot": "screenshots/Wb6cwBudqO.png", "clean_action_id": "Wb6cwBudqO", "prefixed_action_id": "al_Wb6cwBudqO", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/Wb6cwBudqO.png", "action_id_screenshot": "screenshots/Wb6cwBudqO.png"}, {"name": "Tap on element with xpath: ((//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView[1])[1] with fallback: Coordinates (98, 308)", "status": "passed", "duration": "2403ms", "action_id": "Coordinate", "screenshot_filename": "Coordinate.png", "report_screenshot": "Coordinate.png", "resolved_screenshot": "screenshots/Coordinate.png", "clean_action_id": "Coordinate", "prefixed_action_id": "al_Coordinate", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/Coordinate.png", "action_id_screenshot": "screenshots/Coordinate.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "1647ms", "action_id": "mcscWdhpn2", "screenshot_filename": "mcscWdhpn2.png", "report_screenshot": "mcscWdhpn2.png", "resolved_screenshot": "screenshots/mcscWdhpn2.png", "clean_action_id": "mcscWdhpn2", "prefixed_action_id": "al_mcscWdhpn2", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/mcscWdhpn2.png", "action_id_screenshot": "screenshots/mcscWdhpn2.png"}, {"name": "Tap on Text: \"Add\"", "status": "passed", "duration": "2433ms", "action_id": "screenshot_20250827_164914", "screenshot_filename": "screenshot_20250827_164914.png", "report_screenshot": "screenshot_20250827_164914.png", "resolved_screenshot": "screenshots/screenshot_20250827_164914.png", "clean_action_id": "screenshot_20250827_164914", "prefixed_action_id": "al_screenshot_20250827_164914", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_164914.png", "action_id_screenshot": "screenshots/screenshot_20250827_164914.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 2 of 5\")]", "status": "passed", "duration": "1275ms", "action_id": "GEMv6goQtW", "screenshot_filename": "GEMv6goQtW.png", "report_screenshot": "GEMv6goQtW.png", "resolved_screenshot": "screenshots/GEMv6goQtW.png", "clean_action_id": "GEMv6goQtW", "prefixed_action_id": "al_GEMv6goQtW", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/GEMv6goQtW.png", "action_id_screenshot": "screenshots/GEMv6goQtW.png"}, {"name": "Tap on image: find-products-browse.png", "status": "passed", "duration": "7652ms", "action_id": "MuP17p1Xxx", "screenshot_filename": "MuP17p1Xxx.png", "report_screenshot": "MuP17p1Xxx.png", "resolved_screenshot": "screenshots/MuP17p1Xxx.png", "clean_action_id": "MuP17p1Xxx", "prefixed_action_id": "al_MuP17p1Xxx", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/MuP17p1Xxx.png", "action_id_screenshot": "screenshots/MuP17p1Xxx.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "2879ms", "action_id": "screenshot_20250827_174509", "screenshot_filename": "screenshot_20250827_174509.png", "report_screenshot": "screenshot_20250827_174509.png", "resolved_screenshot": "screenshots/screenshot_20250827_174509.png", "clean_action_id": "screenshot_20250827_174509", "prefixed_action_id": "al_screenshot_20250827_174509", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174509.png", "action_id_screenshot": "screenshots/screenshot_20250827_174509.png"}, {"name": "Input text: \"mat\"", "status": "passed", "duration": "360ms", "action_id": "screenshot_20250827_174333", "screenshot_filename": "screenshot_20250827_174333.png", "report_screenshot": "screenshot_20250827_174333.png", "resolved_screenshot": "screenshots/screenshot_20250827_174333.png", "clean_action_id": "screenshot_20250827_174333", "prefixed_action_id": "al_screenshot_20250827_174333", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174333.png", "action_id_screenshot": "screenshots/screenshot_20250827_174333.png"}, {"name": "Android Function: send_key_event - Key Event: ENTER", "status": "passed", "duration": "18993ms", "action_id": "screenshot_20250827_174441", "screenshot_filename": "screenshot_20250827_174441.png", "report_screenshot": "screenshot_20250827_174441.png", "resolved_screenshot": "screenshots/screenshot_20250827_174441.png", "clean_action_id": "screenshot_20250827_174441", "prefixed_action_id": "al_screenshot_20250827_174441", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174441.png", "action_id_screenshot": "screenshots/screenshot_20250827_174441.png"}, {"name": "Wait till xpath=//android.widget.Button[@text=\"Filter\"]", "status": "passed", "duration": "8304ms", "action_id": "screenshot_20250827_171505", "screenshot_filename": "screenshot_20250827_171505.png", "report_screenshot": "screenshot_20250827_171505.png", "resolved_screenshot": "screenshots/screenshot_20250827_171505.png", "clean_action_id": "screenshot_20250827_171505", "prefixed_action_id": "al_screenshot_20250827_171505", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171505.png", "action_id_screenshot": "screenshots/screenshot_20250827_171505.png"}, {"name": "Tap on element with xpath: ((//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView[1])[1] with fallback: Coordinates (98, 308)", "status": "passed", "duration": "693ms", "action_id": "Coordinate", "screenshot_filename": "Coordinate.png", "report_screenshot": "Coordinate.png", "resolved_screenshot": "screenshots/Coordinate.png", "clean_action_id": "Coordinate", "prefixed_action_id": "al_Coordinate", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/Coordinate.png", "action_id_screenshot": "screenshots/Coordinate.png"}, {"name": "Wait till text appears: \"SKU\"", "status": "passed", "duration": "17304ms", "action_id": "screenshot_20250827_173110", "screenshot_filename": "screenshot_20250827_173110.png", "report_screenshot": "screenshot_20250827_173110.png", "resolved_screenshot": "screenshots/screenshot_20250827_173110.png", "clean_action_id": "screenshot_20250827_173110", "prefixed_action_id": "al_screenshot_20250827_173110", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_173110.png", "action_id_screenshot": "screenshots/screenshot_20250827_173110.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "2699ms", "action_id": "MRkOQpNybH", "screenshot_filename": "MRkOQpNybH.png", "report_screenshot": "MRkOQpNybH.png", "resolved_screenshot": "screenshots/MRkOQpNybH.png", "clean_action_id": "MRkOQpNybH", "prefixed_action_id": "al_MRkOQpNybH", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/MRkOQpNybH.png", "action_id_screenshot": "screenshots/MRkOQpNybH.png"}, {"name": "Tap on Text: \"Add\"", "status": "passed", "duration": "2932ms", "action_id": "screenshot_20250827_172542", "screenshot_filename": "screenshot_20250827_172542.png", "report_screenshot": "screenshot_20250827_172542.png", "resolved_screenshot": "screenshots/screenshot_20250827_172542.png", "clean_action_id": "screenshot_20250827_172542", "prefixed_action_id": "al_screenshot_20250827_172542", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_172542.png", "action_id_screenshot": "screenshots/screenshot_20250827_172542.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "status": "passed", "duration": "1705ms", "action_id": "YC6bBrKQgq", "screenshot_filename": "YC6bBrKQgq.png", "report_screenshot": "YC6bBrKQgq.png", "resolved_screenshot": "screenshots/YC6bBrKQgq.png", "clean_action_id": "YC6bBrKQgq", "prefixed_action_id": "al_YC6bBrKQgq", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/YC6bBrKQgq.png", "action_id_screenshot": "screenshots/YC6bBrKQgq.png"}, {"name": "Tap if locator exists: xpath=\"//android.widget.Button[@content-desc=\"Checkout\"]\"", "status": "passed", "duration": "1022ms", "action_id": "screenshot_20250827_174537", "screenshot_filename": "screenshot_20250827_174537.png", "report_screenshot": "screenshot_20250827_174537.png", "resolved_screenshot": "screenshots/screenshot_20250827_174537.png", "clean_action_id": "screenshot_20250827_174537", "prefixed_action_id": "al_screenshot_20250827_174537", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174537.png", "action_id_screenshot": "screenshots/screenshot_20250827_174537.png"}, {"name": "Wait till xpath=//android.view.View[@text=\"Delivery\"]", "status": "passed", "duration": "6945ms", "action_id": "screenshot_20250827_173258", "screenshot_filename": "screenshot_20250827_173258.png", "report_screenshot": "screenshot_20250827_173258.png", "resolved_screenshot": "screenshots/screenshot_20250827_173258.png", "clean_action_id": "screenshot_20250827_173258", "prefixed_action_id": "al_screenshot_20250827_173258", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_173258.png", "action_id_screenshot": "screenshots/screenshot_20250827_173258.png"}, {"name": "Tap on element with xpath: //android.view.View[@text=\"Delivery\"]", "status": "passed", "duration": "293ms", "action_id": "screenshot_20250827_171103", "screenshot_filename": "screenshot_20250827_171103.png", "report_screenshot": "screenshot_20250827_171103.png", "resolved_screenshot": "screenshots/screenshot_20250827_171103.png", "clean_action_id": "screenshot_20250827_171103", "prefixed_action_id": "al_screenshot_20250827_171103", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171103.png", "action_id_screenshot": "screenshots/screenshot_20250827_171103.png"}, {"name": "Swipe from (50%, 70%) to (50%, 50%)", "status": "passed", "duration": "32551ms", "action_id": "screenshot_20250827_171659", "screenshot_filename": "screenshot_20250827_171659.png", "report_screenshot": "screenshot_20250827_171659.png", "resolved_screenshot": "screenshots/screenshot_20250827_171659.png", "clean_action_id": "screenshot_20250827_171659", "prefixed_action_id": "al_screenshot_20250827_171659", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171659.png", "action_id_screenshot": "screenshots/screenshot_20250827_171659.png"}, {"name": "Tap on element with xpath: //android.widget.Button[contains(@text,\"Remove\")]", "status": "passed", "duration": "548ms", "action_id": "wSHsGWAwPm", "screenshot_filename": "wSHsGWAwPm.png", "report_screenshot": "wSHsGWAwPm.png", "resolved_screenshot": "screenshots/wSHsGWAwPm.png", "clean_action_id": "wSHsGWAwPm", "prefixed_action_id": "al_wSHsGWAwPm", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/wSHsGWAwPm.png", "action_id_screenshot": "screenshots/wSHsGWAwPm.png"}, {"name": "Wait for 4 ms", "status": "passed", "duration": "4012ms", "action_id": "screenshot_20250827_181541", "screenshot_filename": "screenshot_20250827_181541.png", "report_screenshot": "screenshot_20250827_181541.png", "resolved_screenshot": "screenshots/screenshot_20250827_181541.png", "clean_action_id": "screenshot_20250827_181541", "prefixed_action_id": "al_screenshot_20250827_181541", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181541.png", "action_id_screenshot": "screenshots/screenshot_20250827_181541.png"}, {"name": "Tap on element with xpath: //android.widget.Button[contains(@text,\"Remove\")]", "status": "failed", "duration": "1332ms", "action_id": "screenshot_20250827_172351", "screenshot_filename": "screenshot_20250827_172351.png", "report_screenshot": "screenshot_20250827_172351.png", "resolved_screenshot": "screenshots/screenshot_20250827_172351.png", "clean_action_id": "screenshot_20250827_172351", "prefixed_action_id": "al_screenshot_20250827_172351", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_172351.png", "action_id_screenshot": "screenshots/screenshot_20250827_172351.png"}, {"name": "Wait for 4 ms", "status": "unknown", "duration": "4061ms", "action_id": "screenshot_20250827_164902", "screenshot_filename": "screenshot_20250827_164902.png", "report_screenshot": "screenshot_20250827_164902.png", "resolved_screenshot": "screenshots/screenshot_20250827_164902.png", "clean_action_id": "screenshot_20250827_164902", "prefixed_action_id": "al_screenshot_20250827_164902", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_164902.png", "action_id_screenshot": "screenshots/screenshot_20250827_164902.png"}, {"name": "Terminate app: au.com.kmart", "status": "unknown", "duration": "17238ms", "action_id": "screenshot_20250827_164916", "screenshot_filename": "screenshot_20250827_164916.png", "report_screenshot": "screenshot_20250827_164916.png", "resolved_screenshot": "screenshots/screenshot_20250827_164916.png", "clean_action_id": "screenshot_20250827_164916", "prefixed_action_id": "al_screenshot_20250827_164916", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_164916.png", "action_id_screenshot": "screenshots/screenshot_20250827_164916.png"}]}, {"name": "Delivery & CNC_AU-ANDROID\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Edit\n                            \n                            \n                                 Remove\n                            \n                            28 actions", "status": "passed", "steps": [{"name": "Execute Test Case: Onboarding-Start-AU (6 steps)", "status": "passed", "duration": "0ms", "action_id": "Onboarding", "screenshot_filename": "Onboarding.png", "report_screenshot": "Onboarding.png", "resolved_screenshot": "screenshots/Onboarding.png", "clean_action_id": "Onboarding", "prefixed_action_id": "al_Onboarding", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/Onboarding.png", "action_id_screenshot": "screenshots/Onboarding.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "3798ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png", "clean_action_id": "accessibil", "prefixed_action_id": "al_accessibil", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/accessibil.png", "action_id_screenshot": "screenshots/accessibil.png"}, {"name": "Wait till xpath=//android.widget.EditText[@resource-id=\"username\"]", "status": "passed", "duration": "121ms", "action_id": "screenshot_20250827_180909", "screenshot_filename": "screenshot_20250827_180909.png", "report_screenshot": "screenshot_20250827_180909.png", "resolved_screenshot": "screenshots/screenshot_20250827_180909.png", "clean_action_id": "screenshot_20250827_180909", "prefixed_action_id": "al_screenshot_20250827_180909", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_180909.png", "action_id_screenshot": "screenshots/screenshot_20250827_180909.png"}, {"name": "Execute Test Case: Kmart-Signin-AU-ANDROID (7 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250827_175214", "screenshot_filename": "screenshot_20250827_175214.png", "report_screenshot": "screenshot_20250827_175214.png", "resolved_screenshot": "screenshots/screenshot_20250827_175214.png", "clean_action_id": "screenshot_20250827_175214", "prefixed_action_id": "al_screenshot_20250827_175214", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175214.png", "action_id_screenshot": "screenshots/screenshot_20250827_175214.png"}, {"name": "Wait till xpath=//android.view.View[@content-desc=\"txtHomeGreetingText\"]", "status": "passed", "duration": "3104ms", "action_id": "txtHomeGre", "screenshot_filename": "txtHomeGre.png", "report_screenshot": "txtHomeGre.png", "resolved_screenshot": "screenshots/txtHomeGre.png", "clean_action_id": "txtHomeGre", "prefixed_action_id": "al_txtHomeGre", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/txtHomeGre.png", "action_id_screenshot": "screenshots/txtHomeGre.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "2833ms", "action_id": "screenshot_20250827_171933", "screenshot_filename": "screenshot_20250827_171933.png", "report_screenshot": "screenshot_20250827_171933.png", "resolved_screenshot": "screenshots/screenshot_20250827_171933.png", "clean_action_id": "screenshot_20250827_171933", "prefixed_action_id": "al_screenshot_20250827_171933", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171933.png", "action_id_screenshot": "screenshots/screenshot_20250827_171933.png"}, {"name": "Input text: \"Uno Card\"", "status": "passed", "duration": "1152ms", "action_id": "XoMyLp2unA", "screenshot_filename": "XoMyLp2unA.png", "report_screenshot": "XoMyLp2unA.png", "resolved_screenshot": "screenshots/XoMyLp2unA.png", "clean_action_id": "XoMyLp2unA", "prefixed_action_id": "al_XoMyLp2unA", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/XoMyLp2unA.png", "action_id_screenshot": "screenshots/XoMyLp2unA.png"}, {"name": "Android Function: send_key_event - Key Event: ENTER", "status": "passed", "duration": "837ms", "action_id": "screenshot_20250827_180538", "screenshot_filename": "screenshot_20250827_180538.png", "report_screenshot": "screenshot_20250827_180538.png", "resolved_screenshot": "screenshots/screenshot_20250827_180538.png", "clean_action_id": "screenshot_20250827_180538", "prefixed_action_id": "al_screenshot_20250827_180538", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_180538.png", "action_id_screenshot": "screenshots/screenshot_20250827_180538.png"}, {"name": "Wait till text appears: \"Filter\"", "status": "passed", "duration": "5003ms", "action_id": "screenshot_20250827_172631", "screenshot_filename": "screenshot_20250827_172631.png", "report_screenshot": "screenshot_20250827_172631.png", "resolved_screenshot": "screenshots/screenshot_20250827_172631.png", "clean_action_id": "screenshot_20250827_172631", "prefixed_action_id": "al_screenshot_20250827_172631", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_172631.png", "action_id_screenshot": "screenshots/screenshot_20250827_172631.png"}, {"name": "Tap on element with xpath: (//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView/following-sibling::android.view.View/android.widget.Button", "status": "passed", "duration": "788ms", "action_id": "screenshot_20250827_181008", "screenshot_filename": "screenshot_20250827_181008.png", "report_screenshot": "screenshot_20250827_181008.png", "resolved_screenshot": "screenshots/screenshot_20250827_181008.png", "clean_action_id": "screenshot_20250827_181008", "prefixed_action_id": "al_screenshot_20250827_181008", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181008.png", "action_id_screenshot": "screenshots/screenshot_20250827_181008.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "status": "passed", "duration": "1276ms", "action_id": "screenshot_20250827_181034", "screenshot_filename": "screenshot_20250827_181034.png", "report_screenshot": "screenshot_20250827_181034.png", "resolved_screenshot": "screenshots/screenshot_20250827_181034.png", "clean_action_id": "screenshot_20250827_181034", "prefixed_action_id": "al_screenshot_20250827_181034", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181034.png", "action_id_screenshot": "screenshots/screenshot_20250827_181034.png"}, {"name": "Tap if locator exists: xpath=\"//android.widget.Button[@content-desc=\"Checkout\"]\"", "status": "passed", "duration": "1078ms", "action_id": "screenshot_20250827_173936", "screenshot_filename": "screenshot_20250827_173936.png", "report_screenshot": "screenshot_20250827_173936.png", "resolved_screenshot": "screenshots/screenshot_20250827_173936.png", "clean_action_id": "screenshot_20250827_173936", "prefixed_action_id": "al_screenshot_20250827_173936", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_173936.png", "action_id_screenshot": "screenshots/screenshot_20250827_173936.png"}, {"name": "Wait for 10 ms", "status": "passed", "duration": "10023ms", "action_id": "VXo5C08UOn", "screenshot_filename": "VXo5C08UOn.png", "report_screenshot": "VXo5C08UOn.png", "resolved_screenshot": "screenshots/VXo5C08UOn.png", "clean_action_id": "VXo5C08UOn", "prefixed_action_id": "al_VXo5C08UOn", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/VXo5C08UOn.png", "action_id_screenshot": "screenshots/VXo5C08UOn.png"}, {"name": "Swipe up till image \"remove-btn-android.png\" is visible", "status": "passed", "duration": "2354ms", "action_id": "screenshot_20250827_165600", "screenshot_filename": "screenshot_20250827_165600.png", "report_screenshot": "screenshot_20250827_165600.png", "resolved_screenshot": "screenshots/screenshot_20250827_165600.png", "clean_action_id": "screenshot_20250827_165600", "prefixed_action_id": "al_screenshot_20250827_165600", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_165600.png", "action_id_screenshot": "screenshots/screenshot_20250827_165600.png"}, {"name": "Tap on element with xpath: //android.widget.Button[contains(@text,\"Remove\")]", "status": "passed", "duration": "204ms", "action_id": "screenshot_20250827_175349", "screenshot_filename": "screenshot_20250827_175349.png", "report_screenshot": "screenshot_20250827_175349.png", "resolved_screenshot": "screenshots/screenshot_20250827_175349.png", "clean_action_id": "screenshot_20250827_175349", "prefixed_action_id": "al_screenshot_20250827_175349", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175349.png", "action_id_screenshot": "screenshots/screenshot_20250827_175349.png"}, {"name": "Tap on image: bag-close-android.png", "status": "passed", "duration": "26498ms", "action_id": "screenshot_20250827_181021", "screenshot_filename": "screenshot_20250827_181021.png", "report_screenshot": "screenshot_20250827_181021.png", "resolved_screenshot": "screenshots/screenshot_20250827_181021.png", "clean_action_id": "screenshot_20250827_181021", "prefixed_action_id": "al_screenshot_20250827_181021", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181021.png", "action_id_screenshot": "screenshots/screenshot_20250827_181021.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "status": "passed", "duration": "1566ms", "action_id": "xblqGQSWzC", "screenshot_filename": "xblqGQSWzC.png", "report_screenshot": "xblqGQSWzC.png", "resolved_screenshot": "screenshots/xblqGQSWzC.png", "clean_action_id": "xblqGQSWzC", "prefixed_action_id": "al_xblqGQSWzC", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/xblqGQSWzC.png", "action_id_screenshot": "screenshots/xblqGQSWzC.png"}, {"name": "Swipe up till element xpath: \"//android.widget.Button[@content-desc=\"txtSign out\"]\" is visible", "status": "passed", "duration": "24008ms", "action_id": "screenshot_20250827_174241", "screenshot_filename": "screenshot_20250827_174241.png", "report_screenshot": "screenshot_20250827_174241.png", "resolved_screenshot": "screenshots/screenshot_20250827_174241.png", "clean_action_id": "screenshot_20250827_174241", "prefixed_action_id": "al_screenshot_20250827_174241", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174241.png", "action_id_screenshot": "screenshots/screenshot_20250827_174241.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@content-desc=\"txtSign out\"]", "status": "passed", "duration": "1003ms", "action_id": "FIBbvTJFpg", "screenshot_filename": "FIBbvTJFpg.png", "report_screenshot": "FIBbvTJFpg.png", "resolved_screenshot": "screenshots/FIBbvTJFpg.png", "clean_action_id": "FIBbvTJFpg", "prefixed_action_id": "al_FIBbvTJFpg", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/FIBbvTJFpg.png", "action_id_screenshot": "screenshots/FIBbvTJFpg.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 1 of 5\")]", "status": "passed", "duration": "155ms", "action_id": "screenshot_20250827_174309", "screenshot_filename": "screenshot_20250827_174309.png", "report_screenshot": "screenshot_20250827_174309.png", "resolved_screenshot": "screenshots/screenshot_20250827_174309.png", "clean_action_id": "screenshot_20250827_174309", "prefixed_action_id": "al_screenshot_20250827_174309", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174309.png", "action_id_screenshot": "screenshots/screenshot_20250827_174309.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "24563ms", "action_id": "screenshot_20250827_164132", "screenshot_filename": "screenshot_20250827_164132.png", "report_screenshot": "screenshot_20250827_164132.png", "resolved_screenshot": "screenshots/screenshot_20250827_164132.png", "clean_action_id": "screenshot_20250827_164132", "prefixed_action_id": "al_screenshot_20250827_164132", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_164132.png", "action_id_screenshot": "screenshots/screenshot_20250827_164132.png"}, {"name": "Input text: \"Uno Card\"", "status": "passed", "duration": "522ms", "action_id": "screenshot_20250827_175229", "screenshot_filename": "screenshot_20250827_175229.png", "report_screenshot": "screenshot_20250827_175229.png", "resolved_screenshot": "screenshots/screenshot_20250827_175229.png", "clean_action_id": "screenshot_20250827_175229", "prefixed_action_id": "al_screenshot_20250827_175229", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175229.png", "action_id_screenshot": "screenshots/screenshot_20250827_175229.png"}, {"name": "Android Function: send_key_event - Key Event: ENTER", "status": "passed", "duration": "61ms", "action_id": "screenshot_20250827_172752", "screenshot_filename": "screenshot_20250827_172752.png", "report_screenshot": "screenshot_20250827_172752.png", "resolved_screenshot": "screenshots/screenshot_20250827_172752.png", "clean_action_id": "screenshot_20250827_172752", "prefixed_action_id": "al_screenshot_20250827_172752", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_172752.png", "action_id_screenshot": "screenshots/screenshot_20250827_172752.png"}, {"name": "Wait till text appears: \"Filter\"", "status": "passed", "duration": "8499ms", "action_id": "screenshot_20250827_175015", "screenshot_filename": "screenshot_20250827_175015.png", "report_screenshot": "screenshot_20250827_175015.png", "resolved_screenshot": "screenshots/screenshot_20250827_175015.png", "clean_action_id": "screenshot_20250827_175015", "prefixed_action_id": "al_screenshot_20250827_175015", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175015.png", "action_id_screenshot": "screenshots/screenshot_20250827_175015.png"}, {"name": "Tap on element with xpath: (//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView/following-sibling::android.view.View/android.widget.Button", "status": "passed", "duration": "496ms", "action_id": "R0xsCnCRFk", "screenshot_filename": "R0xsCnCRFk.png", "report_screenshot": "R0xsCnCRFk.png", "resolved_screenshot": "screenshots/R0xsCnCRFk.png", "clean_action_id": "R0xsCnCRFk", "prefixed_action_id": "al_R0xsCnCRFk", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/R0xsCnCRFk.png", "action_id_screenshot": "screenshots/R0xsCnCRFk.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "status": "passed", "duration": "1269ms", "action_id": "uZEEeTeb7p", "screenshot_filename": "uZEEeTeb7p.png", "report_screenshot": "uZEEeTeb7p.png", "resolved_screenshot": "screenshots/uZEEeTeb7p.png", "clean_action_id": "uZEEeTeb7p", "prefixed_action_id": "al_uZEEeTeb7p", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/uZEEeTeb7p.png", "action_id_screenshot": "screenshots/uZEEeTeb7p.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@content-desc=\"Checkout\"]", "status": "passed", "duration": "1266ms", "action_id": "V9ldRojdyD", "screenshot_filename": "V9ldRojdyD.png", "report_screenshot": "V9ldRojdyD.png", "resolved_screenshot": "screenshots/V9ldRojdyD.png", "clean_action_id": "V9ldRojdyD", "prefixed_action_id": "al_V9ldRojdyD", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/V9ldRojdyD.png", "action_id_screenshot": "screenshots/V9ldRojdyD.png"}, {"name": "Execute Test Case: Delivery Buy Steps_AU-ANDROID (54 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250827_171844", "screenshot_filename": "screenshot_20250827_171844.png", "report_screenshot": "screenshot_20250827_171844.png", "resolved_screenshot": "screenshots/screenshot_20250827_171844.png", "clean_action_id": "screenshot_20250827_171844", "prefixed_action_id": "al_screenshot_20250827_171844", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171844.png", "action_id_screenshot": "screenshots/screenshot_20250827_171844.png"}]}, {"name": "AU - Performance_Android\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Edit\n                            \n                            \n                                 Remove\n                            \n                            51 actions", "status": "passed", "steps": [{"name": "Execute Test Case: Onboarding-Start-AU (6 steps)", "status": "passed", "duration": "0ms", "action_id": "Onboarding", "screenshot_filename": "Onboarding.png", "report_screenshot": "Onboarding.png", "resolved_screenshot": "screenshots/Onboarding.png", "clean_action_id": "Onboarding", "prefixed_action_id": "al_Onboarding", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/Onboarding.png", "action_id_screenshot": "screenshots/Onboarding.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "status": "passed", "duration": "1048ms", "action_id": "screenshot_20250827_180909", "screenshot_filename": "screenshot_20250827_180909.png", "report_screenshot": "screenshot_20250827_180909.png", "resolved_screenshot": "screenshots/screenshot_20250827_180909.png", "clean_action_id": "screenshot_20250827_180909", "prefixed_action_id": "al_screenshot_20250827_180909", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_180909.png", "action_id_screenshot": "screenshots/screenshot_20250827_180909.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "6148ms", "action_id": "screenshot_20250827_175214", "screenshot_filename": "screenshot_20250827_175214.png", "report_screenshot": "screenshot_20250827_175214.png", "resolved_screenshot": "screenshots/screenshot_20250827_175214.png", "clean_action_id": "screenshot_20250827_175214", "prefixed_action_id": "al_screenshot_20250827_175214", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175214.png", "action_id_screenshot": "screenshots/screenshot_20250827_175214.png"}, {"name": "Tap on Text: \"Help\"", "status": "passed", "duration": "2045ms", "action_id": "screenshot_20250827_171933", "screenshot_filename": "screenshot_20250827_171933.png", "report_screenshot": "screenshot_20250827_171933.png", "resolved_screenshot": "screenshots/screenshot_20250827_171933.png", "clean_action_id": "screenshot_20250827_171933", "prefixed_action_id": "al_screenshot_20250827_171933", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171933.png", "action_id_screenshot": "screenshots/screenshot_20250827_171933.png"}, {"name": "Tap on Text: \"FAQ\"", "status": "passed", "duration": "2138ms", "action_id": "XoMyLp2unA", "screenshot_filename": "XoMyLp2unA.png", "report_screenshot": "XoMyLp2unA.png", "resolved_screenshot": "screenshots/XoMyLp2unA.png", "clean_action_id": "XoMyLp2unA", "prefixed_action_id": "al_XoMyLp2unA", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/XoMyLp2unA.png", "action_id_screenshot": "screenshots/XoMyLp2unA.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "9591ms", "action_id": "screenshot_20250827_180538", "screenshot_filename": "screenshot_20250827_180538.png", "report_screenshot": "screenshot_20250827_180538.png", "resolved_screenshot": "screenshots/screenshot_20250827_180538.png", "clean_action_id": "screenshot_20250827_180538", "prefixed_action_id": "al_screenshot_20250827_180538", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_180538.png", "action_id_screenshot": "screenshots/screenshot_20250827_180538.png"}, {"name": "Tap on Text: \"click\"", "status": "passed", "duration": "2529ms", "action_id": "screenshot_20250827_172631", "screenshot_filename": "screenshot_20250827_172631.png", "report_screenshot": "screenshot_20250827_172631.png", "resolved_screenshot": "screenshots/screenshot_20250827_172631.png", "clean_action_id": "screenshot_20250827_172631", "prefixed_action_id": "al_screenshot_20250827_172631", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_172631.png", "action_id_screenshot": "screenshots/screenshot_20250827_172631.png"}, {"name": "Tap on image: custcare-no-android.png", "status": "passed", "duration": "7326ms", "action_id": "screenshot_20250827_181008", "screenshot_filename": "screenshot_20250827_181008.png", "report_screenshot": "screenshot_20250827_181008.png", "resolved_screenshot": "screenshots/screenshot_20250827_181008.png", "clean_action_id": "screenshot_20250827_181008", "prefixed_action_id": "al_screenshot_20250827_181008", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181008.png", "action_id_screenshot": "screenshots/screenshot_20250827_181008.png"}, {"name": "Launch app: au.com.kmart", "status": "passed", "duration": "202ms", "action_id": "screenshot_20250827_181034", "screenshot_filename": "screenshot_20250827_181034.png", "report_screenshot": "screenshot_20250827_181034.png", "resolved_screenshot": "screenshots/screenshot_20250827_181034.png", "clean_action_id": "screenshot_20250827_181034", "prefixed_action_id": "al_screenshot_20250827_181034", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181034.png", "action_id_screenshot": "screenshots/screenshot_20250827_181034.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 1 of 5\")]", "status": "passed", "duration": "330ms", "action_id": "screenshot_20250827_173936", "screenshot_filename": "screenshot_20250827_173936.png", "report_screenshot": "screenshot_20250827_173936.png", "resolved_screenshot": "screenshots/screenshot_20250827_173936.png", "clean_action_id": "screenshot_20250827_173936", "prefixed_action_id": "al_screenshot_20250827_173936", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_173936.png", "action_id_screenshot": "screenshots/screenshot_20250827_173936.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3008ms", "action_id": "VXo5C08UOn", "screenshot_filename": "VXo5C08UOn.png", "report_screenshot": "VXo5C08UOn.png", "resolved_screenshot": "screenshots/VXo5C08UOn.png", "clean_action_id": "VXo5C08UOn", "prefixed_action_id": "al_VXo5C08UOn", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/VXo5C08UOn.png", "action_id_screenshot": "screenshots/VXo5C08UOn.png"}, {"name": "Input text: \"kids toys\"", "status": "passed", "duration": "548ms", "action_id": "screenshot_20250827_165600", "screenshot_filename": "screenshot_20250827_165600.png", "report_screenshot": "screenshot_20250827_165600.png", "resolved_screenshot": "screenshots/screenshot_20250827_165600.png", "clean_action_id": "screenshot_20250827_165600", "prefixed_action_id": "al_screenshot_20250827_165600", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_165600.png", "action_id_screenshot": "screenshots/screenshot_20250827_165600.png"}, {"name": "Android Function: send_key_event - Key Event: ENTER", "status": "passed", "duration": "81ms", "action_id": "screenshot_20250827_175349", "screenshot_filename": "screenshot_20250827_175349.png", "report_screenshot": "screenshot_20250827_175349.png", "resolved_screenshot": "screenshots/screenshot_20250827_175349.png", "clean_action_id": "screenshot_20250827_175349", "prefixed_action_id": "al_screenshot_20250827_175349", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175349.png", "action_id_screenshot": "screenshots/screenshot_20250827_175349.png"}, {"name": "Wait till xpath=//android.widget.Button[@text=\"Filter\"]", "status": "passed", "duration": "5406ms", "action_id": "screenshot_20250827_181021", "screenshot_filename": "screenshot_20250827_181021.png", "report_screenshot": "screenshot_20250827_181021.png", "resolved_screenshot": "screenshots/screenshot_20250827_181021.png", "clean_action_id": "screenshot_20250827_181021", "prefixed_action_id": "al_screenshot_20250827_181021", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181021.png", "action_id_screenshot": "screenshots/screenshot_20250827_181021.png"}, {"name": "Execute Test Case: Click_Paginations_Android (15 steps)", "status": "passed", "duration": "0ms", "action_id": "Pagination", "screenshot_filename": "Pagination.png", "report_screenshot": "Pagination.png", "resolved_screenshot": "screenshots/Pagination.png", "clean_action_id": "Pagination", "prefixed_action_id": "al_Pagination", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/Pagination.png", "action_id_screenshot": "screenshots/Pagination.png"}, {"name": "Launch app: au.com.kmart", "status": "passed", "duration": "568ms", "action_id": "xblqGQSWzC", "screenshot_filename": "xblqGQSWzC.png", "report_screenshot": "xblqGQSWzC.png", "resolved_screenshot": "screenshots/xblqGQSWzC.png", "clean_action_id": "xblqGQSWzC", "prefixed_action_id": "al_xblqGQSWzC", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/xblqGQSWzC.png", "action_id_screenshot": "screenshots/xblqGQSWzC.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 1 of 5\")]", "status": "passed", "duration": "301ms", "action_id": "screenshot_20250827_174241", "screenshot_filename": "screenshot_20250827_174241.png", "report_screenshot": "screenshot_20250827_174241.png", "resolved_screenshot": "screenshots/screenshot_20250827_174241.png", "clean_action_id": "screenshot_20250827_174241", "prefixed_action_id": "al_screenshot_20250827_174241", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174241.png", "action_id_screenshot": "screenshots/screenshot_20250827_174241.png"}, {"name": "Tap on element with accessibility_id: txtHomeAccountCtaSignIn", "status": "passed", "duration": "99ms", "action_id": "accessibil", "screenshot_filename": "accessibil.png", "report_screenshot": "accessibil.png", "resolved_screenshot": "screenshots/accessibil.png", "clean_action_id": "accessibil", "prefixed_action_id": "al_accessibil", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/accessibil.png", "action_id_screenshot": "screenshots/accessibil.png"}, {"name": "Execute Test Case: Kmart-Signin-AU-ANDROID (7 steps)", "status": "passed", "duration": "0ms", "action_id": "FIBbvTJFpg", "screenshot_filename": "FIBbvTJFpg.png", "report_screenshot": "FIBbvTJFpg.png", "resolved_screenshot": "screenshots/FIBbvTJFpg.png", "clean_action_id": "FIBbvTJFpg", "prefixed_action_id": "al_FIBbvTJFpg", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/FIBbvTJFpg.png", "action_id_screenshot": "screenshots/FIBbvTJFpg.png"}, {"name": "Check if element with xpath=\"//android.view.View[@content-desc=\"txtHomeGreetingText\"]\" exists", "status": "passed", "duration": "2259ms", "action_id": "txtHomeGre", "screenshot_filename": "txtHomeGre.png", "report_screenshot": "txtHomeGre.png", "resolved_screenshot": "screenshots/txtHomeGre.png", "clean_action_id": "txtHomeGre", "prefixed_action_id": "al_txtHomeGre", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/txtHomeGre.png", "action_id_screenshot": "screenshots/txtHomeGre.png"}, {"name": "Tap on Text: \"Find\"", "status": "passed", "duration": "3054ms", "action_id": "screenshot_20250827_174309", "screenshot_filename": "screenshot_20250827_174309.png", "report_screenshot": "screenshot_20250827_174309.png", "resolved_screenshot": "screenshots/screenshot_20250827_174309.png", "clean_action_id": "screenshot_20250827_174309", "prefixed_action_id": "al_screenshot_20250827_174309", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174309.png", "action_id_screenshot": "screenshots/screenshot_20250827_174309.png"}, {"name": "Input text: \"cooker\"", "status": "passed", "duration": "364ms", "action_id": "screenshot_20250827_164132", "screenshot_filename": "screenshot_20250827_164132.png", "report_screenshot": "screenshot_20250827_164132.png", "resolved_screenshot": "screenshots/screenshot_20250827_164132.png", "clean_action_id": "screenshot_20250827_164132", "prefixed_action_id": "al_screenshot_20250827_164132", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_164132.png", "action_id_screenshot": "screenshots/screenshot_20250827_164132.png"}, {"name": "Android Function: send_key_event - Key Event: ENTER", "status": "passed", "duration": "786ms", "action_id": "screenshot_20250827_175229", "screenshot_filename": "screenshot_20250827_175229.png", "report_screenshot": "screenshot_20250827_175229.png", "resolved_screenshot": "screenshots/screenshot_20250827_175229.png", "clean_action_id": "screenshot_20250827_175229", "prefixed_action_id": "al_screenshot_20250827_175229", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175229.png", "action_id_screenshot": "screenshots/screenshot_20250827_175229.png"}, {"name": "Wait till xpath=//android.widget.Button[@text=\"Filter\"]", "status": "passed", "duration": "5405ms", "action_id": "screenshot_20250827_172752", "screenshot_filename": "screenshot_20250827_172752.png", "report_screenshot": "screenshot_20250827_172752.png", "resolved_screenshot": "screenshots/screenshot_20250827_172752.png", "clean_action_id": "screenshot_20250827_172752", "prefixed_action_id": "al_screenshot_20250827_172752", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_172752.png", "action_id_screenshot": "screenshots/screenshot_20250827_172752.png"}, {"name": "Tap on element with xpath: ((//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView[1])[1]", "status": "passed", "duration": "791ms", "action_id": "screenshot_20250827_175015", "screenshot_filename": "screenshot_20250827_175015.png", "report_screenshot": "screenshot_20250827_175015.png", "resolved_screenshot": "screenshots/screenshot_20250827_175015.png", "clean_action_id": "screenshot_20250827_175015", "prefixed_action_id": "al_screenshot_20250827_175015", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175015.png", "action_id_screenshot": "screenshots/screenshot_20250827_175015.png"}, {"name": "Wait till xpath=//android.widget.TextView[contains(@text,\"SKU\")]", "status": "passed", "duration": "2569ms", "action_id": "R0xsCnCRFk", "screenshot_filename": "R0xsCnCRFk.png", "report_screenshot": "R0xsCnCRFk.png", "resolved_screenshot": "screenshots/R0xsCnCRFk.png", "clean_action_id": "R0xsCnCRFk", "prefixed_action_id": "al_R0xsCnCRFk", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/R0xsCnCRFk.png", "action_id_screenshot": "screenshots/R0xsCnCRFk.png"}, {"name": "Swipe up till element xpath: \"//android.widget.Button[@resource-id=\"wishlist-button\"]\" is visible", "status": "passed", "duration": "8786ms", "action_id": "uZEEeTeb7p", "screenshot_filename": "uZEEeTeb7p.png", "report_screenshot": "uZEEeTeb7p.png", "resolved_screenshot": "screenshots/uZEEeTeb7p.png", "clean_action_id": "uZEEeTeb7p", "prefixed_action_id": "al_uZEEeTeb7p", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/uZEEeTeb7p.png", "action_id_screenshot": "screenshots/uZEEeTeb7p.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@resource-id=\"wishlist-button\"]", "status": "passed", "duration": "220ms", "action_id": "V9ldRojdyD", "screenshot_filename": "V9ldRojdyD.png", "report_screenshot": "V9ldRojdyD.png", "resolved_screenshot": "screenshots/V9ldRojdyD.png", "clean_action_id": "V9ldRojdyD", "prefixed_action_id": "al_V9ldRojdyD", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/V9ldRojdyD.png", "action_id_screenshot": "screenshots/V9ldRojdyD.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 3 of 5\")]", "status": "passed", "duration": "198ms", "action_id": "screenshot_20250827_171844", "screenshot_filename": "screenshot_20250827_171844.png", "report_screenshot": "screenshot_20250827_171844.png", "resolved_screenshot": "screenshots/screenshot_20250827_171844.png", "clean_action_id": "screenshot_20250827_171844", "prefixed_action_id": "al_screenshot_20250827_171844", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171844.png", "action_id_screenshot": "screenshots/screenshot_20250827_171844.png"}, {"name": "Swipe from (90%, 20%) to (30%, 20%)", "status": "passed", "duration": "616ms", "action_id": "screenshot_20250827_171138", "screenshot_filename": "screenshot_20250827_171138.png", "report_screenshot": "screenshot_20250827_171138.png", "resolved_screenshot": "screenshots/screenshot_20250827_171138.png", "clean_action_id": "screenshot_20250827_171138", "prefixed_action_id": "al_screenshot_20250827_171138", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171138.png", "action_id_screenshot": "screenshots/screenshot_20250827_171138.png"}, {"name": "Swipe from (90%, 20%) to (30%, 20%)", "status": "passed", "duration": "638ms", "action_id": "screenshot_20250827_174732", "screenshot_filename": "screenshot_20250827_174732.png", "report_screenshot": "screenshot_20250827_174732.png", "resolved_screenshot": "screenshots/screenshot_20250827_174732.png", "clean_action_id": "screenshot_20250827_174732", "prefixed_action_id": "al_screenshot_20250827_174732", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174732.png", "action_id_screenshot": "screenshots/screenshot_20250827_174732.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "status": "passed", "duration": "673ms", "action_id": "screenshot_20250827_173505", "screenshot_filename": "screenshot_20250827_173505.png", "report_screenshot": "screenshot_20250827_173505.png", "resolved_screenshot": "screenshots/screenshot_20250827_173505.png", "clean_action_id": "screenshot_20250827_173505", "prefixed_action_id": "al_screenshot_20250827_173505", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_173505.png", "action_id_screenshot": "screenshots/screenshot_20250827_173505.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "1099ms", "action_id": "y5FboDiRLS", "screenshot_filename": "y5FboDiRLS.png", "report_screenshot": "y5FboDiRLS.png", "resolved_screenshot": "screenshots/y5FboDiRLS.png", "clean_action_id": "y5FboDiRLS", "prefixed_action_id": "al_y5FboDiRLS", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/y5FboDiRLS.png", "action_id_screenshot": "screenshots/y5FboDiRLS.png"}, {"name": "Tap on Text: \"out\"", "status": "passed", "duration": "2255ms", "action_id": "SeiSGMuR9u", "screenshot_filename": "SeiSGMuR9u.png", "report_screenshot": "SeiSGMuR9u.png", "resolved_screenshot": "screenshots/SeiSGMuR9u.png", "clean_action_id": "SeiSGMuR9u", "prefixed_action_id": "al_SeiSGMuR9u", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/SeiSGMuR9u.png", "action_id_screenshot": "screenshots/SeiSGMuR9u.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 1 of 5\")]", "status": "passed", "duration": "150ms", "action_id": "screenshot_20250827_172021", "screenshot_filename": "screenshot_20250827_172021.png", "report_screenshot": "screenshot_20250827_172021.png", "resolved_screenshot": "screenshots/screenshot_20250827_172021.png", "clean_action_id": "screenshot_20250827_172021", "prefixed_action_id": "al_screenshot_20250827_172021", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_172021.png", "action_id_screenshot": "screenshots/screenshot_20250827_172021.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@content-desc=\"txtJoinTodayButton\"]", "status": "passed", "duration": "146ms", "action_id": "txtJoinTod", "screenshot_filename": "txtJoinTod.png", "report_screenshot": "txtJoinTod.png", "resolved_screenshot": "screenshots/txtJoinTod.png", "clean_action_id": "txtJoinTod", "prefixed_action_id": "al_txtJoinTod", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/txtJoinTod.png", "action_id_screenshot": "screenshots/txtJoinTod.png"}, {"name": "Check if element with xpath=\"//android.widget.TextView[@text=\"Create account\"]\" exists", "status": "passed", "duration": "1429ms", "action_id": "screenshot_20250827_181340", "screenshot_filename": "screenshot_20250827_181340.png", "report_screenshot": "screenshot_20250827_181340.png", "resolved_screenshot": "screenshots/screenshot_20250827_181340.png", "clean_action_id": "screenshot_20250827_181340", "prefixed_action_id": "al_screenshot_20250827_181340", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181340.png", "action_id_screenshot": "screenshots/screenshot_20250827_181340.png"}, {"name": "Android Function: send_key_event - Key Event: BACK", "status": "passed", "duration": "240ms", "action_id": "screenshot_20250827_170420", "screenshot_filename": "screenshot_20250827_170420.png", "report_screenshot": "screenshot_20250827_170420.png", "resolved_screenshot": "screenshots/screenshot_20250827_170420.png", "clean_action_id": "screenshot_20250827_170420", "prefixed_action_id": "al_screenshot_20250827_170420", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_170420.png", "action_id_screenshot": "screenshots/screenshot_20250827_170420.png"}, {"name": "Execute Test Case: Search and Add (Notebooks)_ANDROID (8 steps)", "status": "passed", "duration": "0ms", "action_id": "g8u66qfKkX", "screenshot_filename": "g8u66qfKkX.png", "report_screenshot": "g8u66qfKkX.png", "resolved_screenshot": "screenshots/g8u66qfKkX.png", "clean_action_id": "g8u66qfKkX", "prefixed_action_id": "al_g8u66qfKkX", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/g8u66qfKkX.png", "action_id_screenshot": "screenshots/g8u66qfKkX.png"}, {"name": "Tap on element with xpath: //android.view.View[contains(@text,\"Click\") and contains(@text,\"Collect\")]", "status": "passed", "duration": "798ms", "action_id": "screenshot_20250827_165001", "screenshot_filename": "screenshot_20250827_165001.png", "report_screenshot": "screenshot_20250827_165001.png", "resolved_screenshot": "screenshots/screenshot_20250827_165001.png", "clean_action_id": "screenshot_20250827_165001", "prefixed_action_id": "al_screenshot_20250827_165001", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_165001.png", "action_id_screenshot": "screenshots/screenshot_20250827_165001.png"}, {"name": "Tap on element with xpath: (//android.widget.Button[contains(@text,\"store details\")])[1]", "status": "passed", "duration": "1834ms", "action_id": "screenshot_20250827_170430", "screenshot_filename": "screenshot_20250827_170430.png", "report_screenshot": "screenshot_20250827_170430.png", "resolved_screenshot": "screenshots/screenshot_20250827_170430.png", "clean_action_id": "screenshot_20250827_170430", "prefixed_action_id": "al_screenshot_20250827_170430", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_170430.png", "action_id_screenshot": "screenshots/screenshot_20250827_170430.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@text=\"close\"]", "status": "passed", "duration": "613ms", "action_id": "screenshot_20250827_171504", "screenshot_filename": "screenshot_20250827_171504.png", "report_screenshot": "screenshot_20250827_171504.png", "resolved_screenshot": "screenshots/screenshot_20250827_171504.png", "clean_action_id": "screenshot_20250827_171504", "prefixed_action_id": "al_screenshot_20250827_171504", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171504.png", "action_id_screenshot": "screenshots/screenshot_20250827_171504.png"}, {"name": "Swipe from (50%, 80%) to (50%, 40%)", "status": "passed", "duration": "1677ms", "action_id": "screenshot_20250827_181152", "screenshot_filename": "screenshot_20250827_181152.png", "report_screenshot": "screenshot_20250827_181152.png", "resolved_screenshot": "screenshots/screenshot_20250827_181152.png", "clean_action_id": "screenshot_20250827_181152", "prefixed_action_id": "al_screenshot_20250827_181152", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181152.png", "action_id_screenshot": "screenshots/screenshot_20250827_181152.png"}, {"name": "Tap on element with xpath: //android.widget.Button[contains(@text,\"Remove\")]", "status": "passed", "duration": "261ms", "action_id": "Wb6cwBudqO", "screenshot_filename": "Wb6cwBudqO.png", "report_screenshot": "Wb6cwBudqO.png", "resolved_screenshot": "screenshots/Wb6cwBudqO.png", "clean_action_id": "Wb6cwBudqO", "prefixed_action_id": "al_Wb6cwBudqO", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/Wb6cwBudqO.png", "action_id_screenshot": "screenshots/Wb6cwBudqO.png"}, {"name": "Tap on image: bag-close-android.png", "status": "passed", "duration": "12628ms", "action_id": "mcscWdhpn2", "screenshot_filename": "mcscWdhpn2.png", "report_screenshot": "mcscWdhpn2.png", "resolved_screenshot": "screenshots/mcscWdhpn2.png", "clean_action_id": "mcscWdhpn2", "prefixed_action_id": "al_mcscWdhpn2", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/mcscWdhpn2.png", "action_id_screenshot": "screenshots/mcscWdhpn2.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "status": "passed", "duration": "688ms", "action_id": "screenshot_20250827_164914", "screenshot_filename": "screenshot_20250827_164914.png", "report_screenshot": "screenshot_20250827_164914.png", "resolved_screenshot": "screenshots/screenshot_20250827_164914.png", "clean_action_id": "screenshot_20250827_164914", "prefixed_action_id": "al_screenshot_20250827_164914", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_164914.png", "action_id_screenshot": "screenshots/screenshot_20250827_164914.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@content-desc=\"txtMoreAccountJoinTodayButton\"]", "status": "passed", "duration": "134ms", "action_id": "txtMoreAcc", "screenshot_filename": "txtMoreAcc.png", "report_screenshot": "txtMoreAcc.png", "resolved_screenshot": "screenshots/txtMoreAcc.png", "clean_action_id": "txtMoreAcc", "prefixed_action_id": "al_txtMoreAcc", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/txtMoreAcc.png", "action_id_screenshot": "screenshots/txtMoreAcc.png"}, {"name": "Check if element with xpath=\"//android.widget.TextView[@text=\"Create account\"]\" exists", "status": "passed", "duration": "1472ms", "action_id": "GEMv6goQtW", "screenshot_filename": "GEMv6goQtW.png", "report_screenshot": "GEMv6goQtW.png", "resolved_screenshot": "screenshots/GEMv6goQtW.png", "clean_action_id": "GEMv6goQtW", "prefixed_action_id": "al_GEMv6goQtW", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/GEMv6goQtW.png", "action_id_screenshot": "screenshots/GEMv6goQtW.png"}, {"name": "Android Function: send_key_event - Key Event: BACK", "status": "passed", "duration": "272ms", "action_id": "MuP17p1Xxx", "screenshot_filename": "MuP17p1Xxx.png", "report_screenshot": "MuP17p1Xxx.png", "resolved_screenshot": "screenshots/MuP17p1Xxx.png", "clean_action_id": "MuP17p1Xxx", "prefixed_action_id": "al_MuP17p1Xxx", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/MuP17p1Xxx.png", "action_id_screenshot": "screenshots/MuP17p1Xxx.png"}, {"name": "Terminate app: au.com.kmart", "status": "passed", "duration": "556ms", "action_id": "screenshot_20250827_174509", "screenshot_filename": "screenshot_20250827_174509.png", "report_screenshot": "screenshot_20250827_174509.png", "resolved_screenshot": "screenshots/screenshot_20250827_174509.png", "clean_action_id": "screenshot_20250827_174509", "prefixed_action_id": "al_screenshot_20250827_174509", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174509.png", "action_id_screenshot": "screenshots/screenshot_20250827_174509.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5016ms", "action_id": "screenshot_20250827_174333", "screenshot_filename": "screenshot_20250827_174333.png", "report_screenshot": "screenshot_20250827_174333.png", "resolved_screenshot": "screenshots/screenshot_20250827_174333.png", "clean_action_id": "screenshot_20250827_174333", "prefixed_action_id": "al_screenshot_20250827_174333", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174333.png", "action_id_screenshot": "screenshots/screenshot_20250827_174333.png"}]}, {"name": "Others AU ANDROID\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Edit\n                            \n                            \n                                 Remove\n                            \n                            37 actions", "status": "passed", "steps": [{"name": "Execute Test Case: Onboarding-Start-AU (6 steps)", "status": "passed", "duration": "0ms", "action_id": "Onboarding", "screenshot_filename": "Onboarding.png", "report_screenshot": "Onboarding.png", "resolved_screenshot": "screenshots/Onboarding.png", "clean_action_id": "Onboarding", "prefixed_action_id": "al_Onboarding", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/Onboarding.png", "action_id_screenshot": "screenshots/Onboarding.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@content-desc=\"btnBarcodeScanner\"]", "status": "passed", "duration": "964ms", "action_id": "btnBarcode", "screenshot_filename": "btnBarcode.png", "report_screenshot": "btnBarcode.png", "resolved_screenshot": "screenshots/btnBarcode.png", "clean_action_id": "btnBarcode", "prefixed_action_id": "al_btnBarcode", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/btnBarcode.png", "action_id_screenshot": "screenshots/btnBarcode.png"}, {"name": "Tap on element with xpath: //android.widget.Button[contains(@text,\"While using the app\")]", "status": "passed", "duration": "117ms", "action_id": "screenshot_20250827_180909", "screenshot_filename": "screenshot_20250827_180909.png", "report_screenshot": "screenshot_20250827_180909.png", "resolved_screenshot": "screenshots/screenshot_20250827_180909.png", "clean_action_id": "screenshot_20250827_180909", "prefixed_action_id": "al_screenshot_20250827_180909", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_180909.png", "action_id_screenshot": "screenshots/screenshot_20250827_180909.png"}, {"name": "Check if element with xpath=\"//android.view.View[@content-desc=\"Barcode Scanner\"]\" exists", "status": "passed", "duration": "163ms", "action_id": "screenshot_20250827_175214", "screenshot_filename": "screenshot_20250827_175214.png", "report_screenshot": "screenshot_20250827_175214.png", "resolved_screenshot": "screenshots/screenshot_20250827_175214.png", "clean_action_id": "screenshot_20250827_175214", "prefixed_action_id": "al_screenshot_20250827_175214", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175214.png", "action_id_screenshot": "screenshots/screenshot_20250827_175214.png"}, {"name": "Check if element with xpath=\"//android.widget.ImageView[@content-desc=\"imgHelp\"]\" exists", "status": "passed", "duration": "112ms", "action_id": "screenshot_20250827_171933", "screenshot_filename": "screenshot_20250827_171933.png", "report_screenshot": "screenshot_20250827_171933.png", "resolved_screenshot": "screenshots/screenshot_20250827_171933.png", "clean_action_id": "screenshot_20250827_171933", "prefixed_action_id": "al_screenshot_20250827_171933", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171933.png", "action_id_screenshot": "screenshots/screenshot_20250827_171933.png"}, {"name": "Check if element with xpath=\"//android.view.View[contains(@content-desc,\"rectangle frame to view helpful product information\")]\" exists", "status": "passed", "duration": "64ms", "action_id": "informatio", "screenshot_filename": "informatio.png", "report_screenshot": "informatio.png", "resolved_screenshot": "screenshots/informatio.png", "clean_action_id": "informatio", "prefixed_action_id": "al_informatio", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/informatio.png", "action_id_screenshot": "screenshots/informatio.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[@content-desc=\"imgBackArrow\"]", "status": "passed", "duration": "158ms", "action_id": "imgBackArr", "screenshot_filename": "imgBackArr.png", "report_screenshot": "imgBackArr.png", "resolved_screenshot": "screenshots/imgBackArr.png", "clean_action_id": "imgBackArr", "prefixed_action_id": "al_imgBackArr", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/imgBackArr.png", "action_id_screenshot": "screenshots/imgBackArr.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "status": "passed", "duration": "889ms", "action_id": "XoMyLp2unA", "screenshot_filename": "XoMyLp2unA.png", "report_screenshot": "XoMyLp2unA.png", "resolved_screenshot": "screenshots/XoMyLp2unA.png", "clean_action_id": "XoMyLp2unA", "prefixed_action_id": "al_XoMyLp2unA", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/XoMyLp2unA.png", "action_id_screenshot": "screenshots/XoMyLp2unA.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@content-desc=\"txtTrack My Order\"]", "status": "passed", "duration": "932ms", "action_id": "screenshot_20250827_180538", "screenshot_filename": "screenshot_20250827_180538.png", "report_screenshot": "screenshot_20250827_180538.png", "resolved_screenshot": "screenshots/screenshot_20250827_180538.png", "clean_action_id": "screenshot_20250827_180538", "prefixed_action_id": "al_screenshot_20250827_180538", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_180538.png", "action_id_screenshot": "screenshots/screenshot_20250827_180538.png"}, {"name": "Tap on element with xpath: //android.widget.EditText[@resource-id=\"orderId\"]", "status": "passed", "duration": "4268ms", "action_id": "screenshot_20250827_172631", "screenshot_filename": "screenshot_20250827_172631.png", "report_screenshot": "screenshot_20250827_172631.png", "resolved_screenshot": "screenshots/screenshot_20250827_172631.png", "clean_action_id": "screenshot_20250827_172631", "prefixed_action_id": "al_screenshot_20250827_172631", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_172631.png", "action_id_screenshot": "screenshots/screenshot_20250827_172631.png"}, {"name": "Input text: \"447743749\"", "status": "passed", "duration": "1159ms", "action_id": "screenshot_20250827_181008", "screenshot_filename": "screenshot_20250827_181008.png", "report_screenshot": "screenshot_20250827_181008.png", "resolved_screenshot": "screenshots/screenshot_20250827_181008.png", "clean_action_id": "screenshot_20250827_181008", "prefixed_action_id": "al_screenshot_20250827_181008", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181008.png", "action_id_screenshot": "screenshots/screenshot_20250827_181008.png"}, {"name": "Tap on element with xpath: //android.widget.EditText[@resource-id=\"email\"]", "status": "passed", "duration": "5834ms", "action_id": "screenshot_20250827_181034", "screenshot_filename": "screenshot_20250827_181034.png", "report_screenshot": "screenshot_20250827_181034.png", "resolved_screenshot": "screenshots/screenshot_20250827_181034.png", "clean_action_id": "screenshot_20250827_181034", "prefixed_action_id": "al_screenshot_20250827_181034", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181034.png", "action_id_screenshot": "screenshots/screenshot_20250827_181034.png"}, {"name": "Input text: \"<EMAIL>\"", "status": "passed", "duration": "2281ms", "action_id": "kmartprod0", "screenshot_filename": "kmartprod0.png", "report_screenshot": "kmartprod0.png", "resolved_screenshot": "screenshots/kmartprod0.png", "clean_action_id": "kmartprod0", "prefixed_action_id": "al_kmartprod0", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/kmartprod0.png", "action_id_screenshot": "screenshots/kmartprod0.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@text=\"Search for order\"]", "status": "passed", "duration": "389ms", "action_id": "screenshot_20250827_173936", "screenshot_filename": "screenshot_20250827_173936.png", "report_screenshot": "screenshot_20250827_173936.png", "resolved_screenshot": "screenshots/screenshot_20250827_173936.png", "clean_action_id": "screenshot_20250827_173936", "prefixed_action_id": "al_screenshot_20250827_173936", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_173936.png", "action_id_screenshot": "screenshots/screenshot_20250827_173936.png"}, {"name": "Check if element with xpath=\"//android.view.View[@text=\"Order 447743749 is refunded\"]\" exists", "status": "passed", "duration": "702ms", "action_id": "VXo5C08UOn", "screenshot_filename": "VXo5C08UOn.png", "report_screenshot": "VXo5C08UOn.png", "resolved_screenshot": "screenshots/VXo5C08UOn.png", "clean_action_id": "VXo5C08UOn", "prefixed_action_id": "al_VXo5C08UOn", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/VXo5C08UOn.png", "action_id_screenshot": "screenshots/VXo5C08UOn.png"}, {"name": "Tap on image: android-app-back.png", "status": "passed", "duration": "7579ms", "action_id": "screenshot_20250827_165600", "screenshot_filename": "screenshot_20250827_165600.png", "report_screenshot": "screenshot_20250827_165600.png", "resolved_screenshot": "screenshots/screenshot_20250827_165600.png", "clean_action_id": "screenshot_20250827_165600", "prefixed_action_id": "al_screenshot_20250827_165600", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_165600.png", "action_id_screenshot": "screenshots/screenshot_20250827_165600.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@content-desc=\"txtMoreAccountCtaSignIn\"]", "status": "passed", "duration": "2327ms", "action_id": "txtMoreAcc", "screenshot_filename": "txtMoreAcc.png", "report_screenshot": "txtMoreAcc.png", "resolved_screenshot": "screenshots/txtMoreAcc.png", "clean_action_id": "txtMoreAcc", "prefixed_action_id": "al_txtMoreAcc", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/txtMoreAcc.png", "action_id_screenshot": "screenshots/txtMoreAcc.png"}, {"name": "Execute Test Case: Kmart-Signin-AU-ANDROID (7 steps)", "status": "passed", "duration": "1190ms", "action_id": "screenshot_20250827_175349", "screenshot_filename": "screenshot_20250827_175349.png", "report_screenshot": "screenshot_20250827_175349.png", "resolved_screenshot": "screenshots/screenshot_20250827_175349.png", "clean_action_id": "screenshot_20250827_175349", "prefixed_action_id": "al_screenshot_20250827_175349", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175349.png", "action_id_screenshot": "screenshots/screenshot_20250827_175349.png"}, {"name": "Check if element with xpath=\"//android.widget.Button[@content-desc=\"txtMy OnePass Account\"]\" exists", "status": "passed", "duration": "4238ms", "action_id": "screenshot_20250827_181021", "screenshot_filename": "screenshot_20250827_181021.png", "report_screenshot": "screenshot_20250827_181021.png", "resolved_screenshot": "screenshots/screenshot_20250827_181021.png", "clean_action_id": "screenshot_20250827_181021", "prefixed_action_id": "al_screenshot_20250827_181021", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181021.png", "action_id_screenshot": "screenshots/screenshot_20250827_181021.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@content-desc=\"txtMy OnePass Account\"]", "status": "passed", "duration": "978ms", "action_id": "xblqGQSWzC", "screenshot_filename": "xblqGQSWzC.png", "report_screenshot": "xblqGQSWzC.png", "resolved_screenshot": "screenshots/xblqGQSWzC.png", "clean_action_id": "xblqGQSWzC", "prefixed_action_id": "al_xblqGQSWzC", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/xblqGQSWzC.png", "action_id_screenshot": "screenshots/xblqGQSWzC.png"}, {"name": "Check if element with xpath=\"//android.view.View[@content-desc=\"txtOnePassSubscritionBox\"]\" exists", "status": "passed", "duration": "3964ms", "action_id": "txtOnePass", "screenshot_filename": "txtOnePass.png", "report_screenshot": "txtOnePass.png", "resolved_screenshot": "screenshots/txtOnePass.png", "clean_action_id": "txtOnePass", "prefixed_action_id": "al_txtOnePass", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/txtOnePass.png", "action_id_screenshot": "screenshots/txtOnePass.png"}, {"name": "Tap on image: android-app-back.png", "status": "passed", "duration": "34746ms", "action_id": "screenshot_20250827_174241", "screenshot_filename": "screenshot_20250827_174241.png", "report_screenshot": "screenshot_20250827_174241.png", "resolved_screenshot": "screenshots/screenshot_20250827_174241.png", "clean_action_id": "screenshot_20250827_174241", "prefixed_action_id": "al_screenshot_20250827_174241", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174241.png", "action_id_screenshot": "screenshots/screenshot_20250827_174241.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@content-desc=\"txtMy orders & receipts\"]", "status": "passed", "duration": "1114ms", "action_id": "FIBbvTJFpg", "screenshot_filename": "FIBbvTJFpg.png", "report_screenshot": "FIBbvTJFpg.png", "resolved_screenshot": "screenshots/FIBbvTJFpg.png", "clean_action_id": "FIBbvTJFpg", "prefixed_action_id": "al_FIBbvTJFpg", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/FIBbvTJFpg.png", "action_id_screenshot": "screenshots/FIBbvTJFpg.png"}, {"name": "Tap on Text: \"Store\"", "status": "passed", "duration": "25245ms", "action_id": "screenshot_20250827_174309", "screenshot_filename": "screenshot_20250827_174309.png", "report_screenshot": "screenshot_20250827_174309.png", "resolved_screenshot": "screenshots/screenshot_20250827_174309.png", "clean_action_id": "screenshot_20250827_174309", "prefixed_action_id": "al_screenshot_20250827_174309", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174309.png", "action_id_screenshot": "screenshots/screenshot_20250827_174309.png"}, {"name": "Check if element with xpath=\"(//android.widget.GridView/android.view.View/android.view.View[2])[1]\" exists", "status": "passed", "duration": "1420ms", "action_id": "screenshot_20250827_164132", "screenshot_filename": "screenshot_20250827_164132.png", "report_screenshot": "screenshot_20250827_164132.png", "resolved_screenshot": "screenshots/screenshot_20250827_164132.png", "clean_action_id": "screenshot_20250827_164132", "prefixed_action_id": "al_screenshot_20250827_164132", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_164132.png", "action_id_screenshot": "screenshots/screenshot_20250827_164132.png"}, {"name": "Tap on element with xpath: (//android.widget.GridView/android.view.View/android.view.View[2])[1]", "status": "passed", "duration": "592ms", "action_id": "screenshot_20250827_175229", "screenshot_filename": "screenshot_20250827_175229.png", "report_screenshot": "screenshot_20250827_175229.png", "resolved_screenshot": "screenshots/screenshot_20250827_175229.png", "clean_action_id": "screenshot_20250827_175229", "prefixed_action_id": "al_screenshot_20250827_175229", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175229.png", "action_id_screenshot": "screenshots/screenshot_20250827_175229.png"}, {"name": "Check if element with xpath=\"//android.webkit.WebView[@text=\"Slyp Receipt\"]\" exists", "status": "passed", "duration": "1012ms", "action_id": "screenshot_20250827_172752", "screenshot_filename": "screenshot_20250827_172752.png", "report_screenshot": "screenshot_20250827_172752.png", "resolved_screenshot": "screenshots/screenshot_20250827_172752.png", "clean_action_id": "screenshot_20250827_172752", "prefixed_action_id": "al_screenshot_20250827_172752", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_172752.png", "action_id_screenshot": "screenshots/screenshot_20250827_172752.png"}, {"name": "Terminate app: au.com.kmart", "status": "passed", "duration": "531ms", "action_id": "screenshot_20250827_175015", "screenshot_filename": "screenshot_20250827_175015.png", "report_screenshot": "screenshot_20250827_175015.png", "resolved_screenshot": "screenshots/screenshot_20250827_175015.png", "clean_action_id": "screenshot_20250827_175015", "prefixed_action_id": "al_screenshot_20250827_175015", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175015.png", "action_id_screenshot": "screenshots/screenshot_20250827_175015.png"}, {"name": "Launch app: au.com.kmart", "status": "passed", "duration": "319ms", "action_id": "R0xsCnCRFk", "screenshot_filename": "R0xsCnCRFk.png", "report_screenshot": "R0xsCnCRFk.png", "resolved_screenshot": "screenshots/R0xsCnCRFk.png", "clean_action_id": "R0xsCnCRFk", "prefixed_action_id": "al_R0xsCnCRFk", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/R0xsCnCRFk.png", "action_id_screenshot": "screenshots/R0xsCnCRFk.png"}, {"name": "Tap on Text: \"Edit\"", "status": "passed", "duration": "5315ms", "action_id": "uZEEeTeb7p", "screenshot_filename": "uZEEeTeb7p.png", "report_screenshot": "uZEEeTeb7p.png", "resolved_screenshot": "screenshots/uZEEeTeb7p.png", "clean_action_id": "uZEEeTeb7p", "prefixed_action_id": "al_uZEEeTeb7p", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/uZEEeTeb7p.png", "action_id_screenshot": "screenshots/uZEEeTeb7p.png"}, {"name": "Tap on Text: \"current\"", "status": "passed", "duration": "2275ms", "action_id": "V9ldRojdyD", "screenshot_filename": "V9ldRojdyD.png", "report_screenshot": "V9ldRojdyD.png", "resolved_screenshot": "screenshots/V9ldRojdyD.png", "clean_action_id": "V9ldRojdyD", "prefixed_action_id": "al_V9ldRojdyD", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/V9ldRojdyD.png", "action_id_screenshot": "screenshots/V9ldRojdyD.png"}, {"name": "Tap on element with xpath: //android.widget.Button[contains(@text,\"While using the app\")]", "status": "passed", "duration": "114ms", "action_id": "screenshot_20250827_171844", "screenshot_filename": "screenshot_20250827_171844.png", "report_screenshot": "screenshot_20250827_171844.png", "resolved_screenshot": "screenshots/screenshot_20250827_171844.png", "clean_action_id": "screenshot_20250827_171844", "prefixed_action_id": "al_screenshot_20250827_171844", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171844.png", "action_id_screenshot": "screenshots/screenshot_20250827_171844.png"}, {"name": "Tap if locator exists: xpath=\"//android.widget.Button[@content-desc=\"btnSaveOrContinue\"]\"", "status": "passed", "duration": "1514ms", "action_id": "btnSaveOrC", "screenshot_filename": "btnSaveOrC.png", "report_screenshot": "btnSaveOrC.png", "resolved_screenshot": "screenshots/btnSaveOrC.png", "clean_action_id": "btnSaveOrC", "prefixed_action_id": "al_btnSaveOrC", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/btnSaveOrC.png", "action_id_screenshot": "screenshots/btnSaveOrC.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "status": "passed", "duration": "806ms", "action_id": "screenshot_20250827_171138", "screenshot_filename": "screenshot_20250827_171138.png", "report_screenshot": "screenshot_20250827_171138.png", "resolved_screenshot": "screenshots/screenshot_20250827_171138.png", "clean_action_id": "screenshot_20250827_171138", "prefixed_action_id": "al_screenshot_20250827_171138", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171138.png", "action_id_screenshot": "screenshots/screenshot_20250827_171138.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "3149ms", "action_id": "screenshot_20250827_174732", "screenshot_filename": "screenshot_20250827_174732.png", "report_screenshot": "screenshot_20250827_174732.png", "resolved_screenshot": "screenshots/screenshot_20250827_174732.png", "clean_action_id": "screenshot_20250827_174732", "prefixed_action_id": "al_screenshot_20250827_174732", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174732.png", "action_id_screenshot": "screenshots/screenshot_20250827_174732.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@content-desc=\"txtSign out\"]", "status": "passed", "duration": "198ms", "action_id": "screenshot_20250827_173505", "screenshot_filename": "screenshot_20250827_173505.png", "report_screenshot": "screenshot_20250827_173505.png", "resolved_screenshot": "screenshots/screenshot_20250827_173505.png", "clean_action_id": "screenshot_20250827_173505", "prefixed_action_id": "al_screenshot_20250827_173505", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_173505.png", "action_id_screenshot": "screenshots/screenshot_20250827_173505.png"}, {"name": "Terminate app: au.com.kmart", "status": "passed", "duration": "561ms", "action_id": "y5FboDiRLS", "screenshot_filename": "y5FboDiRLS.png", "report_screenshot": "y5FboDiRLS.png", "resolved_screenshot": "screenshots/y5FboDiRLS.png", "clean_action_id": "y5FboDiRLS", "prefixed_action_id": "al_y5FboDiRLS", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/y5FboDiRLS.png", "action_id_screenshot": "screenshots/y5FboDiRLS.png"}]}, {"name": "App Settings AU_ANDROID\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Edit\n                            \n                            \n                                 Remove\n                            \n                            72 actions", "status": "passed", "steps": [{"name": "Execute Test Case: Onboarding-Start-AU (6 steps)", "status": "passed", "duration": "0ms", "action_id": "Onboarding", "screenshot_filename": "Onboarding.png", "report_screenshot": "Onboarding.png", "resolved_screenshot": "screenshots/Onboarding.png", "clean_action_id": "Onboarding", "prefixed_action_id": "al_Onboarding", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/Onboarding.png", "action_id_screenshot": "screenshots/Onboarding.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@content-desc=\"txtHomeAccountCtaSignIn\"]", "status": "passed", "duration": "2837ms", "action_id": "txtHomeAcc", "screenshot_filename": "txtHomeAcc.png", "report_screenshot": "txtHomeAcc.png", "resolved_screenshot": "screenshots/txtHomeAcc.png", "clean_action_id": "txtHomeAcc", "prefixed_action_id": "al_txtHomeAcc", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/txtHomeAcc.png", "action_id_screenshot": "screenshots/txtHomeAcc.png"}, {"name": "Execute Test Case: Kmart-Signin-AU-ANDROID (7 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250827_180909", "screenshot_filename": "screenshot_20250827_180909.png", "report_screenshot": "screenshot_20250827_180909.png", "resolved_screenshot": "screenshots/screenshot_20250827_180909.png", "clean_action_id": "screenshot_20250827_180909", "prefixed_action_id": "al_screenshot_20250827_180909", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_180909.png", "action_id_screenshot": "screenshots/screenshot_20250827_180909.png"}, {"name": "Terminate app: com.android.settings", "status": "passed", "duration": "1125ms", "action_id": "screenshot_20250827_175214", "screenshot_filename": "screenshot_20250827_175214.png", "report_screenshot": "screenshot_20250827_175214.png", "resolved_screenshot": "screenshots/screenshot_20250827_175214.png", "clean_action_id": "screenshot_20250827_175214", "prefixed_action_id": "al_screenshot_20250827_175214", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175214.png", "action_id_screenshot": "screenshots/screenshot_20250827_175214.png"}, {"name": "Launch app: com.android.settings", "status": "passed", "duration": "399ms", "action_id": "screenshot_20250827_171933", "screenshot_filename": "screenshot_20250827_171933.png", "report_screenshot": "screenshot_20250827_171933.png", "resolved_screenshot": "screenshots/screenshot_20250827_171933.png", "clean_action_id": "screenshot_20250827_171933", "prefixed_action_id": "al_screenshot_20250827_171933", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171933.png", "action_id_screenshot": "screenshots/screenshot_20250827_171933.png"}, {"name": "Tap on element with xpath: //androidx.recyclerview.widget.RecyclerView[@resource-id=\"com.android.settings:id/recycler_view\"]/android.widget.LinearLayout[3]", "status": "passed", "duration": "251ms", "action_id": "recyclervi", "screenshot_filename": "recyclervi.png", "report_screenshot": "recyclervi.png", "resolved_screenshot": "screenshots/recyclervi.png", "clean_action_id": "recyclervi", "prefixed_action_id": "al_recyclervi", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/recyclervi.png", "action_id_screenshot": "screenshots/recyclervi.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5013ms", "action_id": "XoMyLp2unA", "screenshot_filename": "XoMyLp2unA.png", "report_screenshot": "XoMyLp2unA.png", "resolved_screenshot": "screenshots/XoMyLp2unA.png", "clean_action_id": "XoMyLp2unA", "prefixed_action_id": "al_XoMyLp2unA", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/XoMyLp2unA.png", "action_id_screenshot": "screenshots/XoMyLp2unA.png"}, {"name": "Tap on element with xpath: //android.widget.Switch[@resource-id=\"android:id/switch_widget\"]", "status": "passed", "duration": "1347ms", "action_id": "screenshot_20250827_180538", "screenshot_filename": "screenshot_20250827_180538.png", "report_screenshot": "screenshot_20250827_180538.png", "resolved_screenshot": "screenshots/screenshot_20250827_180538.png", "clean_action_id": "screenshot_20250827_180538", "prefixed_action_id": "al_screenshot_20250827_180538", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_180538.png", "action_id_screenshot": "screenshots/screenshot_20250827_180538.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5014ms", "action_id": "screenshot_20250827_172631", "screenshot_filename": "screenshot_20250827_172631.png", "report_screenshot": "screenshot_20250827_172631.png", "resolved_screenshot": "screenshots/screenshot_20250827_172631.png", "clean_action_id": "screenshot_20250827_172631", "prefixed_action_id": "al_screenshot_20250827_172631", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_172631.png", "action_id_screenshot": "screenshots/screenshot_20250827_172631.png"}, {"name": "Terminate app: au.com.kmart", "status": "passed", "duration": "491ms", "action_id": "screenshot_20250827_181008", "screenshot_filename": "screenshot_20250827_181008.png", "report_screenshot": "screenshot_20250827_181008.png", "resolved_screenshot": "screenshots/screenshot_20250827_181008.png", "clean_action_id": "screenshot_20250827_181008", "prefixed_action_id": "al_screenshot_20250827_181008", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181008.png", "action_id_screenshot": "screenshots/screenshot_20250827_181008.png"}, {"name": "Launch app: au.com.kmart", "status": "passed", "duration": "321ms", "action_id": "screenshot_20250827_181034", "screenshot_filename": "screenshot_20250827_181034.png", "report_screenshot": "screenshot_20250827_181034.png", "resolved_screenshot": "screenshots/screenshot_20250827_181034.png", "clean_action_id": "screenshot_20250827_181034", "prefixed_action_id": "al_screenshot_20250827_181034", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181034.png", "action_id_screenshot": "screenshots/screenshot_20250827_181034.png"}, {"name": "Check if element with xpath=\"//android.view.View[@content-desc=\"txtNo internet connection\"]\" exists", "status": "passed", "duration": "856ms", "action_id": "connection", "screenshot_filename": "connection.png", "report_screenshot": "connection.png", "resolved_screenshot": "screenshots/connection.png", "clean_action_id": "connection", "prefixed_action_id": "al_connection", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/connection.png", "action_id_screenshot": "screenshots/connection.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 2 of 5\")]", "status": "passed", "duration": "648ms", "action_id": "screenshot_20250827_173936", "screenshot_filename": "screenshot_20250827_173936.png", "report_screenshot": "screenshot_20250827_173936.png", "resolved_screenshot": "screenshots/screenshot_20250827_173936.png", "clean_action_id": "screenshot_20250827_173936", "prefixed_action_id": "al_screenshot_20250827_173936", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_173936.png", "action_id_screenshot": "screenshots/screenshot_20250827_173936.png"}, {"name": "Check if element with xpath=\"//android.view.View[@content-desc=\"txtNo internet connection\"]\" exists", "status": "passed", "duration": "82ms", "action_id": "connection", "screenshot_filename": "connection.png", "report_screenshot": "connection.png", "resolved_screenshot": "screenshots/connection.png", "clean_action_id": "connection", "prefixed_action_id": "al_connection", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/connection.png", "action_id_screenshot": "screenshots/connection.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 3 of 5\")]", "status": "passed", "duration": "688ms", "action_id": "VXo5C08UOn", "screenshot_filename": "VXo5C08UOn.png", "report_screenshot": "VXo5C08UOn.png", "resolved_screenshot": "screenshots/VXo5C08UOn.png", "clean_action_id": "VXo5C08UOn", "prefixed_action_id": "al_VXo5C08UOn", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/VXo5C08UOn.png", "action_id_screenshot": "screenshots/VXo5C08UOn.png"}, {"name": "Check if element with xpath=\"//android.view.View[@content-desc=\"txtNo internet connection\"]\" exists", "status": "passed", "duration": "78ms", "action_id": "connection", "screenshot_filename": "connection.png", "report_screenshot": "connection.png", "resolved_screenshot": "screenshots/connection.png", "clean_action_id": "connection", "prefixed_action_id": "al_connection", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/connection.png", "action_id_screenshot": "screenshots/connection.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "status": "passed", "duration": "749ms", "action_id": "screenshot_20250827_165600", "screenshot_filename": "screenshot_20250827_165600.png", "report_screenshot": "screenshot_20250827_165600.png", "resolved_screenshot": "screenshots/screenshot_20250827_165600.png", "clean_action_id": "screenshot_20250827_165600", "prefixed_action_id": "al_screenshot_20250827_165600", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_165600.png", "action_id_screenshot": "screenshots/screenshot_20250827_165600.png"}, {"name": "Check if element with xpath=\"//android.view.View[@content-desc=\"txtNo internet connection\"]\" exists", "status": "passed", "duration": "88ms", "action_id": "connection", "screenshot_filename": "connection.png", "report_screenshot": "connection.png", "resolved_screenshot": "screenshots/connection.png", "clean_action_id": "connection", "prefixed_action_id": "al_connection", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/connection.png", "action_id_screenshot": "screenshots/connection.png"}, {"name": "Terminate app: com.android.settings", "status": "passed", "duration": "488ms", "action_id": "screenshot_20250827_175349", "screenshot_filename": "screenshot_20250827_175349.png", "report_screenshot": "screenshot_20250827_175349.png", "resolved_screenshot": "screenshots/screenshot_20250827_175349.png", "clean_action_id": "screenshot_20250827_175349", "prefixed_action_id": "al_screenshot_20250827_175349", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175349.png", "action_id_screenshot": "screenshots/screenshot_20250827_175349.png"}, {"name": "Launch app: com.android.settings", "status": "passed", "duration": "234ms", "action_id": "screenshot_20250827_181021", "screenshot_filename": "screenshot_20250827_181021.png", "report_screenshot": "screenshot_20250827_181021.png", "resolved_screenshot": "screenshots/screenshot_20250827_181021.png", "clean_action_id": "screenshot_20250827_181021", "prefixed_action_id": "al_screenshot_20250827_181021", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181021.png", "action_id_screenshot": "screenshots/screenshot_20250827_181021.png"}, {"name": "Tap on element with xpath: //androidx.recyclerview.widget.RecyclerView[@resource-id=\"com.android.settings:id/recycler_view\"]/android.widget.LinearLayout[3]", "status": "passed", "duration": "113ms", "action_id": "recyclervi", "screenshot_filename": "recyclervi.png", "report_screenshot": "recyclervi.png", "resolved_screenshot": "screenshots/recyclervi.png", "clean_action_id": "recyclervi", "prefixed_action_id": "al_recyclervi", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/recyclervi.png", "action_id_screenshot": "screenshots/recyclervi.png"}, {"name": "Tap on element with xpath: //android.widget.Switch[@resource-id=\"android:id/switch_widget\"]", "status": "passed", "duration": "665ms", "action_id": "xblqGQSWzC", "screenshot_filename": "xblqGQSWzC.png", "report_screenshot": "xblqGQSWzC.png", "resolved_screenshot": "screenshots/xblqGQSWzC.png", "clean_action_id": "xblqGQSWzC", "prefixed_action_id": "al_xblqGQSWzC", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/xblqGQSWzC.png", "action_id_screenshot": "screenshots/xblqGQSWzC.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5010ms", "action_id": "screenshot_20250827_174241", "screenshot_filename": "screenshot_20250827_174241.png", "report_screenshot": "screenshot_20250827_174241.png", "resolved_screenshot": "screenshots/screenshot_20250827_174241.png", "clean_action_id": "screenshot_20250827_174241", "prefixed_action_id": "al_screenshot_20250827_174241", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174241.png", "action_id_screenshot": "screenshots/screenshot_20250827_174241.png"}, {"name": "Terminate app: au.com.kmart", "status": "passed", "duration": "504ms", "action_id": "FIBbvTJFpg", "screenshot_filename": "FIBbvTJFpg.png", "report_screenshot": "FIBbvTJFpg.png", "resolved_screenshot": "screenshots/FIBbvTJFpg.png", "clean_action_id": "FIBbvTJFpg", "prefixed_action_id": "al_FIBbvTJFpg", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/FIBbvTJFpg.png", "action_id_screenshot": "screenshots/FIBbvTJFpg.png"}, {"name": "Launch app: au.com.kmart", "status": "passed", "duration": "395ms", "action_id": "screenshot_20250827_174309", "screenshot_filename": "screenshot_20250827_174309.png", "report_screenshot": "screenshot_20250827_174309.png", "resolved_screenshot": "screenshots/screenshot_20250827_174309.png", "clean_action_id": "screenshot_20250827_174309", "prefixed_action_id": "al_screenshot_20250827_174309", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174309.png", "action_id_screenshot": "screenshots/screenshot_20250827_174309.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "status": "passed", "duration": "3629ms", "action_id": "screenshot_20250827_164132", "screenshot_filename": "screenshot_20250827_164132.png", "report_screenshot": "screenshot_20250827_164132.png", "resolved_screenshot": "screenshots/screenshot_20250827_164132.png", "clean_action_id": "screenshot_20250827_164132", "prefixed_action_id": "al_screenshot_20250827_164132", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_164132.png", "action_id_screenshot": "screenshots/screenshot_20250827_164132.png"}, {"name": "Swipe from (50%, 70%) to (50%, 10%)", "status": "passed", "duration": "4278ms", "action_id": "screenshot_20250827_175229", "screenshot_filename": "screenshot_20250827_175229.png", "report_screenshot": "screenshot_20250827_175229.png", "resolved_screenshot": "screenshots/screenshot_20250827_175229.png", "clean_action_id": "screenshot_20250827_175229", "prefixed_action_id": "al_screenshot_20250827_175229", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175229.png", "action_id_screenshot": "screenshots/screenshot_20250827_175229.png"}, {"name": "Tap on Text: \"out\"", "status": "passed", "duration": "2032ms", "action_id": "screenshot_20250827_172752", "screenshot_filename": "screenshot_20250827_172752.png", "report_screenshot": "screenshot_20250827_172752.png", "resolved_screenshot": "screenshots/screenshot_20250827_172752.png", "clean_action_id": "screenshot_20250827_172752", "prefixed_action_id": "al_screenshot_20250827_172752", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_172752.png", "action_id_screenshot": "screenshots/screenshot_20250827_172752.png"}, {"name": "Terminate app: com.android.chrome", "status": "passed", "duration": "378ms", "action_id": "screenshot_20250827_175015", "screenshot_filename": "screenshot_20250827_175015.png", "report_screenshot": "screenshot_20250827_175015.png", "resolved_screenshot": "screenshots/screenshot_20250827_175015.png", "clean_action_id": "screenshot_20250827_175015", "prefixed_action_id": "al_screenshot_20250827_175015", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_175015.png", "action_id_screenshot": "screenshots/screenshot_20250827_175015.png"}, {"name": "Restart app: com.android.chrome", "status": "passed", "duration": "333ms", "action_id": "R0xsCnCRFk", "screenshot_filename": "R0xsCnCRFk.png", "report_screenshot": "R0xsCnCRFk.png", "resolved_screenshot": "screenshots/R0xsCnCRFk.png", "clean_action_id": "R0xsCnCRFk", "prefixed_action_id": "al_R0xsCnCRFk", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/R0xsCnCRFk.png", "action_id_screenshot": "screenshots/R0xsCnCRFk.png"}, {"name": "Tap if locator exists: xpath=\"//android.widget.Button[@resource-id=\"com.android.chrome:id/signin_fre_dismiss_button\"]\"", "status": "passed", "duration": "30021ms", "action_id": "uZEEeTeb7p", "screenshot_filename": "uZEEeTeb7p.png", "report_screenshot": "uZEEeTeb7p.png", "resolved_screenshot": "screenshots/uZEEeTeb7p.png", "clean_action_id": "uZEEeTeb7p", "prefixed_action_id": "al_uZEEeTeb7p", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/uZEEeTeb7p.png", "action_id_screenshot": "screenshots/uZEEeTeb7p.png"}, {"name": "Tap if locator exists: xpath=\"//android.widget.Button[@resource-id=\"com.android.chrome:id/more_button\"]\"", "status": "passed", "duration": "10017ms", "action_id": "V9ldRojdyD", "screenshot_filename": "V9ldRojdyD.png", "report_screenshot": "V9ldRojdyD.png", "resolved_screenshot": "screenshots/V9ldRojdyD.png", "clean_action_id": "V9ldRojdyD", "prefixed_action_id": "al_V9ldRojdyD", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/V9ldRojdyD.png", "action_id_screenshot": "screenshots/V9ldRojdyD.png"}, {"name": "Tap if locator exists: xpath=\"//android.widget.Button[@resource-id=\"com.android.chrome:id/ack_button\"]\"", "status": "passed", "duration": "10027ms", "action_id": "screenshot_20250827_171844", "screenshot_filename": "screenshot_20250827_171844.png", "report_screenshot": "screenshot_20250827_171844.png", "resolved_screenshot": "screenshots/screenshot_20250827_171844.png", "clean_action_id": "screenshot_20250827_171844", "prefixed_action_id": "al_screenshot_20250827_171844", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171844.png", "action_id_screenshot": "screenshots/screenshot_20250827_171844.png"}, {"name": "Tap if locator exists: xpath=\"//android.widget.EditText[@resource-id=\"com.android.chrome:id/url_bar\"]\"", "status": "passed", "duration": "10016ms", "action_id": "screenshot_20250827_171138", "screenshot_filename": "screenshot_20250827_171138.png", "report_screenshot": "screenshot_20250827_171138.png", "resolved_screenshot": "screenshots/screenshot_20250827_171138.png", "clean_action_id": "screenshot_20250827_171138", "prefixed_action_id": "al_screenshot_20250827_171138", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171138.png", "action_id_screenshot": "screenshots/screenshot_20250827_171138.png"}, {"name": "Input text: \"kmart au\"", "status": "passed", "duration": "785ms", "action_id": "screenshot_20250827_174732", "screenshot_filename": "screenshot_20250827_174732.png", "report_screenshot": "screenshot_20250827_174732.png", "resolved_screenshot": "screenshots/screenshot_20250827_174732.png", "clean_action_id": "screenshot_20250827_174732", "prefixed_action_id": "al_screenshot_20250827_174732", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174732.png", "action_id_screenshot": "screenshots/screenshot_20250827_174732.png"}, {"name": "Android Function: send_key_event - Key Event: ENTER", "status": "passed", "duration": "400ms", "action_id": "screenshot_20250827_173505", "screenshot_filename": "screenshot_20250827_173505.png", "report_screenshot": "screenshot_20250827_173505.png", "resolved_screenshot": "screenshots/screenshot_20250827_173505.png", "clean_action_id": "screenshot_20250827_173505", "prefixed_action_id": "al_screenshot_20250827_173505", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_173505.png", "action_id_screenshot": "screenshots/screenshot_20250827_173505.png"}, {"name": "Tap on element with xpath: //android.widget.TextView[@text=\"https://www.kmart.com.au\"]", "status": "passed", "duration": "956ms", "action_id": "y5FboDiRLS", "screenshot_filename": "y5FboDiRLS.png", "report_screenshot": "y5FboDiRLS.png", "resolved_screenshot": "screenshots/y5FboDiRLS.png", "clean_action_id": "y5FboDiRLS", "prefixed_action_id": "al_y5FboDiRLS", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/y5FboDiRLS.png", "action_id_screenshot": "screenshots/y5FboDiRLS.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "612ms", "action_id": "SeiSGMuR9u", "screenshot_filename": "SeiSGMuR9u.png", "report_screenshot": "SeiSGMuR9u.png", "resolved_screenshot": "screenshots/SeiSGMuR9u.png", "clean_action_id": "SeiSGMuR9u", "prefixed_action_id": "al_SeiSGMuR9u", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/SeiSGMuR9u.png", "action_id_screenshot": "screenshots/SeiSGMuR9u.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@content-desc=\"Home & Living\"]", "status": "passed", "duration": "1200ms", "action_id": "screenshot_20250827_172021", "screenshot_filename": "screenshot_20250827_172021.png", "report_screenshot": "screenshot_20250827_172021.png", "resolved_screenshot": "screenshots/screenshot_20250827_172021.png", "clean_action_id": "screenshot_20250827_172021", "prefixed_action_id": "al_screenshot_20250827_172021", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_172021.png", "action_id_screenshot": "screenshots/screenshot_20250827_172021.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[@content-desc=\"Styled by You\"]/android.view.View[2]/android.view.View/android.widget.ImageView[1]", "status": "passed", "duration": "165ms", "action_id": "screenshot_20250827_181340", "screenshot_filename": "screenshot_20250827_181340.png", "report_screenshot": "screenshot_20250827_181340.png", "resolved_screenshot": "screenshots/screenshot_20250827_181340.png", "clean_action_id": "screenshot_20250827_181340", "prefixed_action_id": "al_screenshot_20250827_181340", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181340.png", "action_id_screenshot": "screenshots/screenshot_20250827_181340.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"$\")] with fallback: Text \"$\"", "status": "passed", "duration": "31240ms", "action_id": "screenshot_20250827_170420", "screenshot_filename": "screenshot_20250827_170420.png", "report_screenshot": "screenshot_20250827_170420.png", "resolved_screenshot": "screenshots/screenshot_20250827_170420.png", "clean_action_id": "screenshot_20250827_170420", "prefixed_action_id": "al_screenshot_20250827_170420", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_170420.png", "action_id_screenshot": "screenshots/screenshot_20250827_170420.png"}, {"name": "Android Function: send_key_event - Key Event: BACK", "status": "passed", "duration": "61ms", "action_id": "g8u66qfKkX", "screenshot_filename": "g8u66qfKkX.png", "report_screenshot": "g8u66qfKkX.png", "resolved_screenshot": "screenshots/g8u66qfKkX.png", "clean_action_id": "g8u66qfKkX", "prefixed_action_id": "al_g8u66qfKkX", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/g8u66qfKkX.png", "action_id_screenshot": "screenshots/g8u66qfKkX.png"}, {"name": "Android Function: send_key_event - Key Event: BACK", "status": "passed", "duration": "10128ms", "action_id": "screenshot_20250827_165001", "screenshot_filename": "screenshot_20250827_165001.png", "report_screenshot": "screenshot_20250827_165001.png", "resolved_screenshot": "screenshots/screenshot_20250827_165001.png", "clean_action_id": "screenshot_20250827_165001", "prefixed_action_id": "al_screenshot_20250827_165001", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_165001.png", "action_id_screenshot": "screenshots/screenshot_20250827_165001.png"}, {"name": "Execute Test Case: <PERSON><PERSON><PERSON> Add to Bag (10 steps)", "status": "passed", "duration": "0ms", "action_id": "screenshot_20250827_170430", "screenshot_filename": "screenshot_20250827_170430.png", "report_screenshot": "screenshot_20250827_170430.png", "resolved_screenshot": "screenshots/screenshot_20250827_170430.png", "clean_action_id": "screenshot_20250827_170430", "prefixed_action_id": "al_screenshot_20250827_170430", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_170430.png", "action_id_screenshot": "screenshots/screenshot_20250827_170430.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "status": "passed", "duration": "1405ms", "action_id": "screenshot_20250827_171504", "screenshot_filename": "screenshot_20250827_171504.png", "report_screenshot": "screenshot_20250827_171504.png", "resolved_screenshot": "screenshots/screenshot_20250827_171504.png", "clean_action_id": "screenshot_20250827_171504", "prefixed_action_id": "al_screenshot_20250827_171504", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171504.png", "action_id_screenshot": "screenshots/screenshot_20250827_171504.png"}, {"name": "Tap if locator exists: xpath=\"//android.widget.Button[@content-desc=\"Checkout\"]\"", "status": "passed", "duration": "168ms", "action_id": "screenshot_20250827_181152", "screenshot_filename": "screenshot_20250827_181152.png", "report_screenshot": "screenshot_20250827_181152.png", "resolved_screenshot": "screenshots/screenshot_20250827_181152.png", "clean_action_id": "screenshot_20250827_181152", "prefixed_action_id": "al_screenshot_20250827_181152", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181152.png", "action_id_screenshot": "screenshots/screenshot_20250827_181152.png"}, {"name": "Tap on element with xpath: //android.view.View[@text=\"Delivery\"]", "status": "passed", "duration": "6598ms", "action_id": "Wb6cwBudqO", "screenshot_filename": "Wb6cwBudqO.png", "report_screenshot": "Wb6cwBudqO.png", "resolved_screenshot": "screenshots/Wb6cwBudqO.png", "clean_action_id": "Wb6cwBudqO", "prefixed_action_id": "al_Wb6cwBudqO", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/Wb6cwBudqO.png", "action_id_screenshot": "screenshots/Wb6cwBudqO.png"}, {"name": "Swipe from (50%, 70%) to (50%, 50%)", "status": "passed", "duration": "1804ms", "action_id": "mcscWdhpn2", "screenshot_filename": "mcscWdhpn2.png", "report_screenshot": "mcscWdhpn2.png", "resolved_screenshot": "screenshots/mcscWdhpn2.png", "clean_action_id": "mcscWdhpn2", "prefixed_action_id": "al_mcscWdhpn2", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/mcscWdhpn2.png", "action_id_screenshot": "screenshots/mcscWdhpn2.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@text=\"Increase quantity\"]", "status": "passed", "duration": "548ms", "action_id": "screenshot_20250827_164914", "screenshot_filename": "screenshot_20250827_164914.png", "report_screenshot": "screenshot_20250827_164914.png", "resolved_screenshot": "screenshots/screenshot_20250827_164914.png", "clean_action_id": "screenshot_20250827_164914", "prefixed_action_id": "al_screenshot_20250827_164914", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_164914.png", "action_id_screenshot": "screenshots/screenshot_20250827_164914.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@text=\"Decrease quantity\"]", "status": "passed", "duration": "2602ms", "action_id": "GEMv6goQtW", "screenshot_filename": "GEMv6goQtW.png", "report_screenshot": "GEMv6goQtW.png", "resolved_screenshot": "screenshots/GEMv6goQtW.png", "clean_action_id": "GEMv6goQtW", "prefixed_action_id": "al_GEMv6goQtW", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/GEMv6goQtW.png", "action_id_screenshot": "screenshots/GEMv6goQtW.png"}, {"name": "Tap on element with xpath: //android.widget.Button[contains(@text,\"Remove\")]", "status": "passed", "duration": "1368ms", "action_id": "MuP17p1Xxx", "screenshot_filename": "MuP17p1Xxx.png", "report_screenshot": "MuP17p1Xxx.png", "resolved_screenshot": "screenshots/MuP17p1Xxx.png", "clean_action_id": "MuP17p1Xxx", "prefixed_action_id": "al_MuP17p1Xxx", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/MuP17p1Xxx.png", "action_id_screenshot": "screenshots/MuP17p1Xxx.png"}, {"name": "Tap on image: bag-close-android.png", "status": "passed", "duration": "7348ms", "action_id": "screenshot_20250827_174509", "screenshot_filename": "screenshot_20250827_174509.png", "report_screenshot": "screenshot_20250827_174509.png", "resolved_screenshot": "screenshots/screenshot_20250827_174509.png", "clean_action_id": "screenshot_20250827_174509", "prefixed_action_id": "al_screenshot_20250827_174509", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174509.png", "action_id_screenshot": "screenshots/screenshot_20250827_174509.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 2 of 5\")]", "status": "passed", "duration": "9293ms", "action_id": "screenshot_20250827_174333", "screenshot_filename": "screenshot_20250827_174333.png", "report_screenshot": "screenshot_20250827_174333.png", "resolved_screenshot": "screenshots/screenshot_20250827_174333.png", "clean_action_id": "screenshot_20250827_174333", "prefixed_action_id": "al_screenshot_20250827_174333", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174333.png", "action_id_screenshot": "screenshots/screenshot_20250827_174333.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 2 of 5\")]", "status": "passed", "duration": "520ms", "action_id": "screenshot_20250827_174441", "screenshot_filename": "screenshot_20250827_174441.png", "report_screenshot": "screenshot_20250827_174441.png", "resolved_screenshot": "screenshots/screenshot_20250827_174441.png", "clean_action_id": "screenshot_20250827_174441", "prefixed_action_id": "al_screenshot_20250827_174441", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174441.png", "action_id_screenshot": "screenshots/screenshot_20250827_174441.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "1333ms", "action_id": "screenshot_20250827_171505", "screenshot_filename": "screenshot_20250827_171505.png", "report_screenshot": "screenshot_20250827_171505.png", "resolved_screenshot": "screenshots/screenshot_20250827_171505.png", "clean_action_id": "screenshot_20250827_171505", "prefixed_action_id": "al_screenshot_20250827_171505", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171505.png", "action_id_screenshot": "screenshots/screenshot_20250827_171505.png"}, {"name": "Tap on Text: \"Catalogue\"", "status": "passed", "duration": "21270ms", "action_id": "screenshot_20250827_173110", "screenshot_filename": "screenshot_20250827_173110.png", "report_screenshot": "screenshot_20250827_173110.png", "resolved_screenshot": "screenshots/screenshot_20250827_173110.png", "clean_action_id": "screenshot_20250827_173110", "prefixed_action_id": "al_screenshot_20250827_173110", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_173110.png", "action_id_screenshot": "screenshots/screenshot_20250827_173110.png"}, {"name": "Tap on image: catalogue-menu-android.png", "status": "passed", "duration": "9212ms", "action_id": "MRkOQpNybH", "screenshot_filename": "MRkOQpNybH.png", "report_screenshot": "MRkOQpNybH.png", "resolved_screenshot": "screenshots/MRkOQpNybH.png", "clean_action_id": "MRkOQpNybH", "prefixed_action_id": "al_MRkOQpNybH", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/MRkOQpNybH.png", "action_id_screenshot": "screenshots/MRkOQpNybH.png"}, {"name": "Tap on Text: \"Search\"", "status": "passed", "duration": "3541ms", "action_id": "screenshot_20250827_172542", "screenshot_filename": "screenshot_20250827_172542.png", "report_screenshot": "screenshot_20250827_172542.png", "resolved_screenshot": "screenshots/screenshot_20250827_172542.png", "clean_action_id": "screenshot_20250827_172542", "prefixed_action_id": "al_screenshot_20250827_172542", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_172542.png", "action_id_screenshot": "screenshots/screenshot_20250827_172542.png"}, {"name": "Input text: \"Toys\"", "status": "passed", "duration": "1262ms", "action_id": "YC6bBrKQgq", "screenshot_filename": "YC6bBrKQgq.png", "report_screenshot": "YC6bBrKQgq.png", "resolved_screenshot": "screenshots/YC6bBrKQgq.png", "clean_action_id": "YC6bBrKQgq", "prefixed_action_id": "al_YC6bBrKQgq", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/YC6bBrKQgq.png", "action_id_screenshot": "screenshots/YC6bBrKQgq.png"}, {"name": "Android Function: send_key_event - Key Event: ENTER", "status": "passed", "duration": "88ms", "action_id": "screenshot_20250827_174537", "screenshot_filename": "screenshot_20250827_174537.png", "report_screenshot": "screenshot_20250827_174537.png", "resolved_screenshot": "screenshots/screenshot_20250827_174537.png", "clean_action_id": "screenshot_20250827_174537", "prefixed_action_id": "al_screenshot_20250827_174537", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_174537.png", "action_id_screenshot": "screenshots/screenshot_20250827_174537.png"}, {"name": "Wait till xpath=(//android.widget.TextView[contains(@text,\"$\")])[1]", "status": "passed", "duration": "2505ms", "action_id": "screenshot_20250827_173258", "screenshot_filename": "screenshot_20250827_173258.png", "report_screenshot": "screenshot_20250827_173258.png", "resolved_screenshot": "screenshots/screenshot_20250827_173258.png", "clean_action_id": "screenshot_20250827_173258", "prefixed_action_id": "al_screenshot_20250827_173258", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_173258.png", "action_id_screenshot": "screenshots/screenshot_20250827_173258.png"}, {"name": "Tap on element with xpath: (//android.widget.TextView[contains(@text,\"$\")])[1]", "status": "passed", "duration": "645ms", "action_id": "screenshot_20250827_171103", "screenshot_filename": "screenshot_20250827_171103.png", "report_screenshot": "screenshot_20250827_171103.png", "resolved_screenshot": "screenshots/screenshot_20250827_171103.png", "clean_action_id": "screenshot_20250827_171103", "prefixed_action_id": "al_screenshot_20250827_171103", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171103.png", "action_id_screenshot": "screenshots/screenshot_20250827_171103.png"}, {"name": "Wait till xpath=//android.widget.TextView[contains(@text,\"SKU\")]", "status": "passed", "duration": "5025ms", "action_id": "screenshot_20250827_171659", "screenshot_filename": "screenshot_20250827_171659.png", "report_screenshot": "screenshot_20250827_171659.png", "resolved_screenshot": "screenshots/screenshot_20250827_171659.png", "clean_action_id": "screenshot_20250827_171659", "prefixed_action_id": "al_screenshot_20250827_171659", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171659.png", "action_id_screenshot": "screenshots/screenshot_20250827_171659.png"}, {"name": "Swipe from (50%, 70%) to (50%, 30%)", "status": "passed", "duration": "3616ms", "action_id": "wSHsGWAwPm", "screenshot_filename": "wSHsGWAwPm.png", "report_screenshot": "wSHsGWAwPm.png", "resolved_screenshot": "screenshots/wSHsGWAwPm.png", "clean_action_id": "wSHsGWAwPm", "prefixed_action_id": "al_wSHsGWAwPm", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/wSHsGWAwPm.png", "action_id_screenshot": "screenshots/wSHsGWAwPm.png"}, {"name": "Tap on element with xpath: //android.widget.Button[@text=\"Add to bag\"] with fallback: Text \"Add\"", "status": "passed", "duration": "727ms", "action_id": "screenshot_20250827_181541", "screenshot_filename": "screenshot_20250827_181541.png", "report_screenshot": "screenshot_20250827_181541.png", "resolved_screenshot": "screenshots/screenshot_20250827_181541.png", "clean_action_id": "screenshot_20250827_181541", "prefixed_action_id": "al_screenshot_20250827_181541", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_181541.png", "action_id_screenshot": "screenshots/screenshot_20250827_181541.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "status": "passed", "duration": "706ms", "action_id": "screenshot_20250827_172351", "screenshot_filename": "screenshot_20250827_172351.png", "report_screenshot": "screenshot_20250827_172351.png", "resolved_screenshot": "screenshots/screenshot_20250827_172351.png", "clean_action_id": "screenshot_20250827_172351", "prefixed_action_id": "al_screenshot_20250827_172351", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_172351.png", "action_id_screenshot": "screenshots/screenshot_20250827_172351.png"}, {"name": "Tap if locator exists: xpath=\"//android.widget.Button[@content-desc=\"Checkout\"]\"", "status": "passed", "duration": "239ms", "action_id": "screenshot_20250827_164902", "screenshot_filename": "screenshot_20250827_164902.png", "report_screenshot": "screenshot_20250827_164902.png", "resolved_screenshot": "screenshots/screenshot_20250827_164902.png", "clean_action_id": "screenshot_20250827_164902", "prefixed_action_id": "al_screenshot_20250827_164902", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_164902.png", "action_id_screenshot": "screenshots/screenshot_20250827_164902.png"}, {"name": "Tap on element with xpath: //android.view.View[@text=\"Delivery\"]", "status": "passed", "duration": "6749ms", "action_id": "screenshot_20250827_164916", "screenshot_filename": "screenshot_20250827_164916.png", "report_screenshot": "screenshot_20250827_164916.png", "resolved_screenshot": "screenshots/screenshot_20250827_164916.png", "clean_action_id": "screenshot_20250827_164916", "prefixed_action_id": "al_screenshot_20250827_164916", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_164916.png", "action_id_screenshot": "screenshots/screenshot_20250827_164916.png"}, {"name": "Swipe up till element xpath: \"//android.widget.Button[contains(@text,\"Remove\")]\" is visible", "status": "passed", "duration": "2156ms", "action_id": "screenshot_20250827_172635", "screenshot_filename": "screenshot_20250827_172635.png", "report_screenshot": "screenshot_20250827_172635.png", "resolved_screenshot": "screenshots/screenshot_20250827_172635.png", "clean_action_id": "screenshot_20250827_172635", "prefixed_action_id": "al_screenshot_20250827_172635", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_172635.png", "action_id_screenshot": "screenshots/screenshot_20250827_172635.png"}, {"name": "Tap on element with xpath: //android.widget.Button[contains(@text,\"Remove\")]", "status": "passed", "duration": "501ms", "action_id": "mZBT5KezoO", "screenshot_filename": "mZBT5KezoO.png", "report_screenshot": "mZBT5KezoO.png", "resolved_screenshot": "screenshots/mZBT5KezoO.png", "clean_action_id": "mZBT5KezoO", "prefixed_action_id": "al_mZBT5KezoO", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/mZBT5KezoO.png", "action_id_screenshot": "screenshots/mZBT5KezoO.png"}, {"name": "Tap on image: banner-close-updated.png", "status": "passed", "duration": "23576ms", "action_id": "screenshot_20250827_171248", "screenshot_filename": "screenshot_20250827_171248.png", "report_screenshot": "screenshot_20250827_171248.png", "resolved_screenshot": "screenshots/screenshot_20250827_171248.png", "clean_action_id": "screenshot_20250827_171248", "prefixed_action_id": "al_screenshot_20250827_171248", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171248.png", "action_id_screenshot": "screenshots/screenshot_20250827_171248.png"}, {"name": "Wait for 5 ms", "status": "passed", "duration": "5012ms", "action_id": "screenshot_20250827_171937", "screenshot_filename": "screenshot_20250827_171937.png", "report_screenshot": "screenshot_20250827_171937.png", "resolved_screenshot": "screenshots/screenshot_20250827_171937.png", "clean_action_id": "screenshot_20250827_171937", "prefixed_action_id": "al_screenshot_20250827_171937", "timestamp": "2025-08-27 18:16:01", "screenshot": "screenshots/screenshot_20250827_171937.png", "action_id_screenshot": "screenshots/screenshot_20250827_171937.png"}]}], "passed": 8, "failed": 1, "skipped": 0, "status": "failed", "id": "06209342-96be-49bf-9d61-f1bc2bef7aa9"}