<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Suite Report - 8/26/2025, 6:47:02 PM</title>
    <link rel="stylesheet" href="./assets/report.css">
    <link rel="stylesheet" href="./assets/custom.css">
</head>
<body>
    <header class="header">
        <h1>Suites</h1>
        <div class="status-summary">
            Status: <span class="status-badge status-badge-failed">failed</span>
            <span class="stats-summary">
                <span class="passed-count">1</span> passed,
                <span class="failed-count">1</span> failed,
                <span class="skipped-count">0</span> skipped
            </span>
        </div>
    </header>

    <div class="content">
        <div class="suites-panel">
            <div class="suite-heading">
                <span class="expand-icon"></span>
                UI Execution 26/08/2025, 18:47:02
            </div>

            <ul class="test-list">
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="7 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-failed"></span>
                            #1 Calc-AndroidTest-Extra-Steps
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Edit
                            
                            
                                 Remove
                            
                            7 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="calculator.png" data-action-id="calculator" onclick="showStepDetails('step-0-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: com.coloros.calculator <span class="action-id-badge" title="Action ID: calculator">calculator</span>
                            </div>
                            <span class="test-step-duration">10905ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="nNQFuxq8rk.png" data-action-id="nNQFuxq8rk" onclick="showStepDetails('step-0-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"] <span class="action-id-badge" title="Action ID: nNQFuxq8rk">nNQFuxq8rk</span>
                            </div>
                            <span class="test-step-duration">552ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="screenshot_20250826_184622.png" data-action-id="screenshot_20250826_184622" onclick="showStepDetails('step-0-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Settings" <span class="action-id-badge" title="Action ID: screenshot_20250826_184622">screenshot_20250826_184622</span>
                            </div>
                            <span class="test-step-duration">2389ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="failed"
                            data-screenshot="ImageButto.png" data-action-id="ImageButto" onclick="showStepDetails('step-0-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-failed"></span>
                                Tap on element with xpath: //android.widget.ImageButton[@content-desc="xyz"] <span class="action-id-badge" title="Action ID: ImageButto">ImageButto</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="unknown"
                            data-screenshot="Scientific.png" data-action-id="Scientific" onclick="showStepDetails('step-0-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //android.widget.TextView[@content-desc="Scientific"] <span class="action-id-badge" title="Action ID: Scientific">Scientific</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="unknown"
                            data-screenshot="calculator.png" data-action-id="calculator" onclick="showStepDetails('step-0-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Terminate app: com.coloros.calculator <span class="action-id-badge" title="Action ID: calculator">calculator</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="unknown"
                            data-screenshot="cleanupSte.png" data-action-id="cleanupSte" onclick="showStepDetails('step-0-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                cleanupSteps action <span class="action-id-badge" title="Action ID: cleanupSte">cleanupSte</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="6 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #2 Calc-AndroidTest
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Edit
                            
                            
                                 Remove
                            
                            6 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="calculator.png" data-action-id="calculator" onclick="showStepDetails('step-1-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: com.coloros.calculator <span class="action-id-badge" title="Action ID: calculator">calculator</span>
                            </div>
                            <span class="test-step-duration">633ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="nNQFuxq8rk.png" data-action-id="nNQFuxq8rk" onclick="showStepDetails('step-1-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"] <span class="action-id-badge" title="Action ID: nNQFuxq8rk">nNQFuxq8rk</span>
                            </div>
                            <span class="test-step-duration">157ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="screenshot_20250826_184428.png" data-action-id="screenshot_20250826_184428" onclick="showStepDetails('step-1-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Settings" <span class="action-id-badge" title="Action ID: screenshot_20250826_184428">screenshot_20250826_184428</span>
                            </div>
                            <span class="test-step-duration">2249ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="ImageButto.png" data-action-id="ImageButto" onclick="showStepDetails('step-1-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //android.widget.ImageButton[@content-desc="Navigate up"] <span class="action-id-badge" title="Action ID: ImageButto">ImageButto</span>
                            </div>
                            <span class="test-step-duration">717ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="Scientific.png" data-action-id="Scientific" onclick="showStepDetails('step-1-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //android.widget.TextView[@content-desc="Scientific"] <span class="action-id-badge" title="Action ID: Scientific">Scientific</span>
                            </div>
                            <span class="test-step-duration">606ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="calculator.png" data-action-id="calculator" onclick="showStepDetails('step-1-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Terminate app: com.coloros.calculator <span class="action-id-badge" title="Action ID: calculator">calculator</span>
                            </div>
                            <span class="test-step-duration">31859ms</span>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>

        <div class="details-panel" id="details-panel">
            <!-- This will be populated by JavaScript when a test step is clicked -->
            <h3>Click on a test step to view details</h3>
        </div>
    </div>

    <script>
        // Store the test data for our JavaScript to use
        const testData = {"name":"UI Execution 26/08/2025, 18:47:02","testCases":[{"name":"Calc-AndroidTest-Extra-Steps\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Edit\n                            \n                            \n                                 Remove\n                            \n                            7 actions","status":"failed","steps":[{"name":"Restart app: com.coloros.calculator","status":"passed","duration":"10905ms","action_id":"calculator","screenshot_filename":"calculator.png","report_screenshot":"calculator.png","resolved_screenshot":"screenshots/calculator.png","clean_action_id":"calculator","prefixed_action_id":"al_calculator","timestamp":"2025-08-26 18:47:02","screenshot":"screenshots/calculator.png","action_id_screenshot":"screenshots/calculator.png"},{"name":"Tap on element with xpath: //android.widget.ImageView[@content-desc=\"More options,\"]","status":"passed","duration":"552ms","action_id":"nNQFuxq8rk","screenshot_filename":"nNQFuxq8rk.png","report_screenshot":"nNQFuxq8rk.png","resolved_screenshot":"screenshots/nNQFuxq8rk.png","clean_action_id":"nNQFuxq8rk","prefixed_action_id":"al_nNQFuxq8rk","timestamp":"2025-08-26 18:47:02","screenshot":"screenshots/nNQFuxq8rk.png","action_id_screenshot":"screenshots/nNQFuxq8rk.png"},{"name":"Tap on Text: \"Settings\"","status":"passed","duration":"2389ms","action_id":"screenshot_20250826_184622","screenshot_filename":"screenshot_20250826_184622.png","report_screenshot":"screenshot_20250826_184622.png","resolved_screenshot":"screenshots/screenshot_20250826_184622.png","clean_action_id":"screenshot_20250826_184622","prefixed_action_id":"al_screenshot_20250826_184622","timestamp":"2025-08-26 18:47:02","screenshot":"screenshots/screenshot_20250826_184622.png","action_id_screenshot":"screenshots/screenshot_20250826_184622.png"},{"name":"Tap on element with xpath: //android.widget.ImageButton[@content-desc=\"xyz\"]","status":"failed","duration":"0ms","action_id":"ImageButto","screenshot_filename":"ImageButto.png","report_screenshot":"ImageButto.png","resolved_screenshot":"screenshots/ImageButto.png","clean_action_id":"ImageButto","prefixed_action_id":"al_ImageButto","timestamp":"2025-08-26 18:47:02","screenshot":"screenshots/ImageButto.png","action_id_screenshot":"screenshots/ImageButto.png"},{"name":"Tap on element with xpath: //android.widget.TextView[@content-desc=\"Scientific\"]","status":"unknown","duration":"0ms","action_id":"Scientific","screenshot_filename":"Scientific.png","report_screenshot":"Scientific.png","resolved_screenshot":"screenshots/Scientific.png","clean_action_id":"Scientific","prefixed_action_id":"al_Scientific","timestamp":"2025-08-26 18:47:02","screenshot":"screenshots/Scientific.png","action_id_screenshot":"screenshots/Scientific.png"},{"name":"Terminate app: com.coloros.calculator","status":"unknown","duration":"0ms","action_id":"calculator","screenshot_filename":"calculator.png","report_screenshot":"calculator.png","resolved_screenshot":"screenshots/calculator.png","clean_action_id":"calculator","prefixed_action_id":"al_calculator","timestamp":"2025-08-26 18:47:02","screenshot":"screenshots/calculator.png","action_id_screenshot":"screenshots/calculator.png"},{"name":"cleanupSteps action","status":"unknown","duration":"0ms","action_id":"cleanupSte","screenshot_filename":"cleanupSte.png","report_screenshot":"cleanupSte.png","resolved_screenshot":"screenshots/cleanupSte.png","clean_action_id":"cleanupSte","prefixed_action_id":"al_cleanupSte","timestamp":"2025-08-26 18:47:02","screenshot":"screenshots/cleanupSte.png","action_id_screenshot":"screenshots/cleanupSte.png"}]},{"name":"Calc-AndroidTest\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Edit\n                            \n                            \n                                 Remove\n                            \n                            6 actions","status":"passed","steps":[{"name":"Restart app: com.coloros.calculator","status":"passed","duration":"633ms","action_id":"calculator","screenshot_filename":"calculator.png","report_screenshot":"calculator.png","resolved_screenshot":"screenshots/calculator.png","clean_action_id":"V78pQgEUmN","prefixed_action_id":"al_V78pQgEUmN","timestamp":"2025-08-26 18:47:02","screenshot":"screenshots/V78pQgEUmN.png","action_id_screenshot":"screenshots/calculator.png"},{"name":"Tap on element with xpath: //android.widget.ImageView[@content-desc=\"More options,\"]","status":"passed","duration":"157ms","action_id":"nNQFuxq8rk","screenshot_filename":"nNQFuxq8rk.png","report_screenshot":"nNQFuxq8rk.png","resolved_screenshot":"screenshots/nNQFuxq8rk.png","clean_action_id":"nNQFuxq8rk","prefixed_action_id":"al_nNQFuxq8rk","timestamp":"2025-08-26 18:47:02","screenshot":"screenshots/nNQFuxq8rk.png","action_id_screenshot":"screenshots/nNQFuxq8rk.png"},{"name":"Tap on Text: \"Settings\"","status":"passed","duration":"2249ms","action_id":"screenshot_20250826_184428","screenshot_filename":"screenshot_20250826_184428.png","report_screenshot":"screenshot_20250826_184428.png","resolved_screenshot":"screenshots/screenshot_20250826_184428.png","clean_action_id":"screenshot_20250826_184428","prefixed_action_id":"al_screenshot_20250826_184428","timestamp":"2025-08-26 18:47:02","screenshot":"screenshots/screenshot_20250826_184428.png","action_id_screenshot":"screenshots/screenshot_20250826_184428.png"},{"name":"Tap on element with xpath: //android.widget.ImageButton[@content-desc=\"Navigate up\"]","status":"passed","duration":"717ms","action_id":"ImageButto","screenshot_filename":"ImageButto.png","report_screenshot":"ImageButto.png","resolved_screenshot":"screenshots/ImageButto.png","clean_action_id":"screenshot_20250826_184607","prefixed_action_id":"al_screenshot_20250826_184607","timestamp":"2025-08-26 18:47:02","screenshot":"screenshots/screenshot_20250826_184607.png","action_id_screenshot":"screenshots/ImageButto.png"},{"name":"Tap on element with xpath: //android.widget.TextView[@content-desc=\"Scientific\"]","status":"passed","duration":"606ms","action_id":"Scientific","screenshot_filename":"Scientific.png","report_screenshot":"Scientific.png","resolved_screenshot":"screenshots/Scientific.png","clean_action_id":"screenshot_20250826_184620","prefixed_action_id":"al_screenshot_20250826_184620","timestamp":"2025-08-26 18:47:02","screenshot":"screenshots/screenshot_20250826_184620.png","action_id_screenshot":"screenshots/Scientific.png"},{"name":"Terminate app: com.coloros.calculator","status":"passed","duration":"31859ms","action_id":"calculator","screenshot_filename":"calculator.png","report_screenshot":"calculator.png","resolved_screenshot":"screenshots/calculator.png","clean_action_id":"screenshot_20250826_184622","prefixed_action_id":"al_screenshot_20250826_184622","timestamp":"2025-08-26 18:47:02","screenshot":"screenshots/screenshot_20250826_184622.png","action_id_screenshot":"screenshots/calculator.png"}]}],"passed":1,"failed":1,"skipped":0,"status":"failed","id":"a328a238-61c6-40d9-9079-697ec4f0d334","availableScreenshots":["ImageButto.png","Scientific.png","V78pQgEUmN.png","calculator.png","cleanupSte.png","nNQFuxq8rk.png","screenshot_20250826_184428.png","screenshot_20250826_184607.png","screenshot_20250826_184620.png","screenshot_20250826_184622.png"],"screenshots_map":{}};
    </script>

    <script src="./assets/report.js"></script>
</body>
</html>