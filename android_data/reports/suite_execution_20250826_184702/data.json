{"name": "UI Execution 26/08/2025, 18:47:02", "testCases": [{"name": "Calc-AndroidTest-Extra-Steps\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Edit\n                            \n                            \n                                 Remove\n                            \n                            7 actions", "status": "failed", "steps": [{"name": "Restart app: com.coloros.calculator", "status": "passed", "duration": "10905ms", "action_id": "calculator", "screenshot_filename": "calculator.png", "report_screenshot": "calculator.png", "resolved_screenshot": "screenshots/calculator.png", "clean_action_id": "calculator", "prefixed_action_id": "al_calculator", "timestamp": "2025-08-26 18:47:02", "screenshot": "screenshots/calculator.png", "action_id_screenshot": "screenshots/calculator.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[@content-desc=\"More options,\"]", "status": "passed", "duration": "552ms", "action_id": "nNQFuxq8rk", "screenshot_filename": "nNQFuxq8rk.png", "report_screenshot": "nNQFuxq8rk.png", "resolved_screenshot": "screenshots/nNQFuxq8rk.png", "clean_action_id": "nNQFuxq8rk", "prefixed_action_id": "al_nNQFuxq8rk", "timestamp": "2025-08-26 18:47:02", "screenshot": "screenshots/nNQFuxq8rk.png", "action_id_screenshot": "screenshots/nNQFuxq8rk.png"}, {"name": "Tap on Text: \"<PERSON>ting<PERSON>\"", "status": "passed", "duration": "2389ms", "action_id": "screenshot_20250826_184622", "screenshot_filename": "screenshot_20250826_184622.png", "report_screenshot": "screenshot_20250826_184622.png", "resolved_screenshot": "screenshots/screenshot_20250826_184622.png", "clean_action_id": "screenshot_20250826_184622", "prefixed_action_id": "al_screenshot_20250826_184622", "timestamp": "2025-08-26 18:47:02", "screenshot": "screenshots/screenshot_20250826_184622.png", "action_id_screenshot": "screenshots/screenshot_20250826_184622.png"}, {"name": "Tap on element with xpath: //android.widget.ImageButton[@content-desc=\"xyz\"]", "status": "failed", "duration": "0ms", "action_id": "ImageButto", "screenshot_filename": "ImageButto.png", "report_screenshot": "ImageButto.png", "resolved_screenshot": "screenshots/ImageButto.png", "clean_action_id": "ImageButto", "prefixed_action_id": "al_ImageButto", "timestamp": "2025-08-26 18:47:02", "screenshot": "screenshots/ImageButto.png", "action_id_screenshot": "screenshots/ImageButto.png"}, {"name": "Tap on element with xpath: //android.widget.TextView[@content-desc=\"Scientific\"]", "status": "unknown", "duration": "0ms", "action_id": "Scientific", "screenshot_filename": "Scientific.png", "report_screenshot": "Scientific.png", "resolved_screenshot": "screenshots/Scientific.png", "clean_action_id": "Scientific", "prefixed_action_id": "al_Scientific", "timestamp": "2025-08-26 18:47:02", "screenshot": "screenshots/Scientific.png", "action_id_screenshot": "screenshots/Scientific.png"}, {"name": "Terminate app: com.coloros.calculator", "status": "unknown", "duration": "0ms", "action_id": "calculator", "screenshot_filename": "calculator.png", "report_screenshot": "calculator.png", "resolved_screenshot": "screenshots/calculator.png", "clean_action_id": "calculator", "prefixed_action_id": "al_calculator", "timestamp": "2025-08-26 18:47:02", "screenshot": "screenshots/calculator.png", "action_id_screenshot": "screenshots/calculator.png"}, {"name": "cleanupSteps action", "status": "unknown", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png", "clean_action_id": "cleanupSte", "prefixed_action_id": "al_cleanupSte", "timestamp": "2025-08-26 18:47:02", "screenshot": "screenshots/cleanupSte.png", "action_id_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "Calc-AndroidTest\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Edit\n                            \n                            \n                                 Remove\n                            \n                            6 actions", "status": "passed", "steps": [{"name": "Restart app: com.coloros.calculator", "status": "passed", "duration": "633ms", "action_id": "calculator", "screenshot_filename": "calculator.png", "report_screenshot": "calculator.png", "resolved_screenshot": "screenshots/calculator.png", "clean_action_id": "V78pQgEUmN", "prefixed_action_id": "al_V78pQgEUmN", "timestamp": "2025-08-26 18:47:02", "screenshot": "screenshots/V78pQgEUmN.png", "action_id_screenshot": "screenshots/calculator.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[@content-desc=\"More options,\"]", "status": "passed", "duration": "157ms", "action_id": "nNQFuxq8rk", "screenshot_filename": "nNQFuxq8rk.png", "report_screenshot": "nNQFuxq8rk.png", "resolved_screenshot": "screenshots/nNQFuxq8rk.png", "clean_action_id": "nNQFuxq8rk", "prefixed_action_id": "al_nNQFuxq8rk", "timestamp": "2025-08-26 18:47:02", "screenshot": "screenshots/nNQFuxq8rk.png", "action_id_screenshot": "screenshots/nNQFuxq8rk.png"}, {"name": "Tap on Text: \"<PERSON>ting<PERSON>\"", "status": "passed", "duration": "2249ms", "action_id": "screenshot_20250826_184428", "screenshot_filename": "screenshot_20250826_184428.png", "report_screenshot": "screenshot_20250826_184428.png", "resolved_screenshot": "screenshots/screenshot_20250826_184428.png", "clean_action_id": "screenshot_20250826_184428", "prefixed_action_id": "al_screenshot_20250826_184428", "timestamp": "2025-08-26 18:47:02", "screenshot": "screenshots/screenshot_20250826_184428.png", "action_id_screenshot": "screenshots/screenshot_20250826_184428.png"}, {"name": "Tap on element with xpath: //android.widget.ImageButton[@content-desc=\"Navigate up\"]", "status": "passed", "duration": "717ms", "action_id": "ImageButto", "screenshot_filename": "ImageButto.png", "report_screenshot": "ImageButto.png", "resolved_screenshot": "screenshots/ImageButto.png", "clean_action_id": "screenshot_20250826_184607", "prefixed_action_id": "al_screenshot_20250826_184607", "timestamp": "2025-08-26 18:47:02", "screenshot": "screenshots/screenshot_20250826_184607.png", "action_id_screenshot": "screenshots/ImageButto.png"}, {"name": "Tap on element with xpath: //android.widget.TextView[@content-desc=\"Scientific\"]", "status": "passed", "duration": "606ms", "action_id": "Scientific", "screenshot_filename": "Scientific.png", "report_screenshot": "Scientific.png", "resolved_screenshot": "screenshots/Scientific.png", "clean_action_id": "screenshot_20250826_184620", "prefixed_action_id": "al_screenshot_20250826_184620", "timestamp": "2025-08-26 18:47:02", "screenshot": "screenshots/screenshot_20250826_184620.png", "action_id_screenshot": "screenshots/Scientific.png"}, {"name": "Terminate app: com.coloros.calculator", "status": "passed", "duration": "31859ms", "action_id": "calculator", "screenshot_filename": "calculator.png", "report_screenshot": "calculator.png", "resolved_screenshot": "screenshots/calculator.png", "clean_action_id": "screenshot_20250826_184622", "prefixed_action_id": "al_screenshot_20250826_184622", "timestamp": "2025-08-26 18:47:02", "screenshot": "screenshots/screenshot_20250826_184622.png", "action_id_screenshot": "screenshots/calculator.png"}]}], "passed": 1, "failed": 1, "skipped": 0, "status": "failed", "id": "a328a238-61c6-40d9-9079-697ec4f0d334"}