Action Log - 2025-08-27 11:23:33
================================================================================

[[11:23:33]] [INFO] Generating execution report...
[[11:23:33]] [SUCCESS] All tests passed successfully!
[[11:23:33]] [ERROR] Action 6 failed: cleanupSteps action type is obsolete in Android. Use multiStep with cleanup checkbox instead.
[[11:23:33]] [SUCCESS] Screenshot refreshed successfully
[[11:23:33]] [SUCCESS] Screenshot refreshed successfully
[[11:23:32]] [INFO] Executing action 6/6: cleanupSteps action
[[11:23:32]] [SUCCESS] Screenshot refreshed
[[11:23:32]] [INFO] Refreshing screenshot...
[[11:23:30]] [INFO] Executing action 5/6: Terminate app: com.coloros.calculator
[[11:23:30]] [ERROR] Action 4 failed: Element not found or not tappable after all retry strategies: xpath='//android.widget.TextView[@content-desc="Scientific"]'
[[11:22:07]] [SUCCESS] Screenshot refreshed successfully
[[11:22:07]] [SUCCESS] Screenshot refreshed successfully
[[11:22:06]] [INFO] Executing action 4/6: Tap on element with xpath: //android.widget.TextView[@content-desc="Scientific"]
[[11:22:06]] [SUCCESS] Screenshot refreshed
[[11:22:06]] [INFO] Refreshing screenshot...
[[11:22:03]] [SUCCESS] Screenshot refreshed successfully
[[11:22:03]] [SUCCESS] Screenshot refreshed successfully
[[11:22:02]] [INFO] Executing action 3/6: Tap on Text: "Settings"
[[11:22:02]] [SUCCESS] Screenshot refreshed
[[11:22:02]] [INFO] Refreshing screenshot...
[[11:22:00]] [SUCCESS] Screenshot refreshed successfully
[[11:22:00]] [SUCCESS] Screenshot refreshed successfully
[[11:21:33]] [INFO] Executing action 2/6: Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"]
[[11:21:33]] [SUCCESS] Screenshot refreshed
[[11:21:33]] [INFO] Refreshing screenshot...
[[11:21:31]] [INFO] Executing action 1/6: Restart app: com.coloros.calculator
[[11:21:31]] [INFO] ExecutionManager: Starting execution of 6 actions...
[[11:21:31]] [SUCCESS] Cleared 0 screenshots from database
[[11:21:31]] [INFO] Clearing screenshots from database before execution...
[[11:21:31]] [SUCCESS] All screenshots deleted successfully
[[11:21:31]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[11:21:31]] [INFO] Skipping report initialization - single test case execution
[[11:21:23]] [SUCCESS] Screenshot refreshed successfully
[[11:21:11]] [INFO] Skipping report generation - individual test case execution (not from test suite)
[[11:21:11]] [SUCCESS] Action logs saved successfully
[[11:21:11]] [ERROR] Execution failed but report was generated.
[[11:21:11]] [INFO] Saving 46 action log entries to file...
[[11:21:11]] [INFO] Generating execution report...
[[11:21:11]] [SUCCESS] All tests passed successfully!
[[11:21:11]] [ERROR] Action 6 failed: cleanupSteps action type is obsolete in Android. Use multiStep with cleanup checkbox instead.
[[11:21:09]] [INFO] Executing action 6/6: cleanupSteps action
[[11:21:09]] [SUCCESS] Screenshot refreshed
[[11:21:09]] [INFO] Refreshing screenshot...
[[11:21:08]] [INFO] Executing action 5/6: Terminate app: com.coloros.calculator
[[11:21:08]] [ERROR] Action 4 failed: Element not found or not tappable after all retry strategies: xpath='//android.widget.TextView[@content-desc="Scientific"]'
[[11:19:59]] [SUCCESS] Screenshot refreshed successfully
[[11:19:58]] [INFO] Executing action 4/6: Tap on element with xpath: //android.widget.TextView[@content-desc="Scientific"]
[[11:19:58]] [SUCCESS] Screenshot refreshed
[[11:19:58]] [INFO] Refreshing screenshot...
[[11:19:55]] [SUCCESS] Screenshot refreshed successfully
[[11:19:54]] [INFO] Executing action 3/6: Tap on Text: "Settings"
[[11:19:54]] [SUCCESS] Screenshot refreshed
[[11:19:54]] [INFO] Refreshing screenshot...
[[11:19:53]] [SUCCESS] Screenshot refreshed successfully
[[11:19:53]] [SUCCESS] Screenshot refreshed successfully
[[11:19:52]] [INFO] Executing action 2/6: Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"]
[[11:19:52]] [SUCCESS] Screenshot refreshed
[[11:19:52]] [INFO] Refreshing screenshot...
[[11:19:50]] [INFO] Executing action 1/6: Restart app: com.coloros.calculator
[[11:19:50]] [INFO] ExecutionManager: Starting execution of 6 actions...
[[11:19:50]] [SUCCESS] Cleared 0 screenshots from database
[[11:19:50]] [INFO] Clearing screenshots from database before execution...
[[11:19:50]] [SUCCESS] All screenshots deleted successfully
[[11:19:50]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[11:19:50]] [INFO] Skipping report initialization - single test case execution
[[11:19:46]] [SUCCESS] All screenshots deleted successfully
[[11:19:46]] [SUCCESS] Loaded test case "Calc-AndroidTest-Extra-Steps" with 6 actions
[[11:19:46]] [SUCCESS] Added action: cleanupSteps
[[11:19:46]] [SUCCESS] Added action: terminateApp
[[11:19:46]] [SUCCESS] Added action: tap
[[11:19:46]] [SUCCESS] Added action: tapOnText
[[11:19:46]] [SUCCESS] Added action: tap
[[11:19:46]] [SUCCESS] Added action: restartApp
[[11:19:46]] [INFO] All actions cleared
[[11:19:46]] [INFO] Cleaning up screenshots...
[[11:19:39]] [SUCCESS] Screenshot refreshed successfully
[[11:19:39]] [SUCCESS] Screenshot refreshed
[[11:19:39]] [INFO] Refreshing screenshot...
[[11:19:38]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8
[[11:19:38]] [INFO] Device info updated: RMX2151
[[11:19:26]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[11:18:38]] [SUCCESS] Found 1 device(s)
[[11:18:38]] [INFO] Refreshing device list...
