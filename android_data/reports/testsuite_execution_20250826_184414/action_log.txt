Action Log - 2025-08-26 18:47:02
================================================================================

[[18:47:02]] [INFO] Generating execution report...
[[18:47:02]] [WARNING] 1 test failed.
[[18:47:02]] [SUCCESS] Screenshot refreshed
[[18:47:02]] [INFO] Refreshing screenshot...
[[18:47:00]] [SUCCESS] Screenshot refreshed successfully
[[18:47:00]] [SUCCESS] Screenshot refreshed successfully
[[18:46:29]] [INFO] Executing action 13/13: Terminate app: com.coloros.calculator
[[18:46:29]] [SUCCESS] Screenshot refreshed
[[18:46:29]] [INFO] Refreshing screenshot...
[[18:46:27]] [SUCCESS] Screenshot refreshed successfully
[[18:46:27]] [SUCCESS] Screenshot refreshed successfully
[[18:46:27]] [INFO] Executing action 12/13: Tap on element with xpath: //android.widget.TextView[@content-desc="Scientific"]
[[18:46:27]] [SUCCESS] Screenshot refreshed
[[18:46:27]] [INFO] Refreshing screenshot...
[[18:46:26]] [SUCCESS] Screenshot refreshed successfully
[[18:46:26]] [SUCCESS] Screenshot refreshed successfully
[[18:46:25]] [INFO] Executing action 11/13: Tap on element with xpath: //android.widget.ImageButton[@content-desc="Navigate up"]
[[18:46:25]] [SUCCESS] Screenshot refreshed
[[18:46:25]] [INFO] Refreshing screenshot...
[[18:46:22]] [SUCCESS] Screenshot refreshed successfully
[[18:46:22]] [SUCCESS] Screenshot refreshed successfully
[[18:46:22]] [INFO] Executing action 10/13: Tap on Text: "Settings"
[[18:46:22]] [SUCCESS] Screenshot refreshed
[[18:46:22]] [INFO] Refreshing screenshot...
[[18:46:20]] [SUCCESS] Screenshot refreshed successfully
[[18:46:20]] [SUCCESS] Screenshot refreshed successfully
[[18:46:20]] [INFO] Executing action 9/13: Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"]
[[18:46:20]] [SUCCESS] Screenshot refreshed
[[18:46:20]] [INFO] Refreshing screenshot...
[[18:46:08]] [INFO] Executing action 8/13: Restart app: com.coloros.calculator
[[18:46:08]] [INFO] Skipping remaining steps in failed test case (moving from action 4 to next test case at 7)
[[18:46:08]] [ERROR] Action 4 failed: Element not found or not tappable after all retry strategies: xpath='//android.widget.ImageButton[@content-desc="xyz"]'
[[18:44:32]] [SUCCESS] Screenshot refreshed successfully
[[18:44:32]] [SUCCESS] Screenshot refreshed successfully
[[18:44:31]] [INFO] Executing action 4/13: Tap on element with xpath: //android.widget.ImageButton[@content-desc="xyz"]
[[18:44:31]] [SUCCESS] Screenshot refreshed
[[18:44:31]] [INFO] Refreshing screenshot...
[[18:44:28]] [SUCCESS] Screenshot refreshed successfully
[[18:44:28]] [SUCCESS] Screenshot refreshed successfully
[[18:44:28]] [INFO] Executing action 3/13: Tap on Text: "Settings"
[[18:44:28]] [SUCCESS] Screenshot refreshed
[[18:44:28]] [INFO] Refreshing screenshot...
[[18:44:26]] [SUCCESS] Screenshot refreshed successfully
[[18:44:26]] [SUCCESS] Screenshot refreshed successfully
[[18:44:26]] [INFO] Executing action 2/13: Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"]
[[18:44:26]] [SUCCESS] Screenshot refreshed
[[18:44:26]] [INFO] Refreshing screenshot...
[[18:44:14]] [INFO] Executing action 1/13: Restart app: com.coloros.calculator
[[18:44:14]] [INFO] ExecutionManager: Starting execution of 13 actions...
[[18:44:14]] [SUCCESS] Cleared 0 screenshots from database
[[18:44:14]] [INFO] Clearing screenshots from database before execution...
[[18:44:14]] [SUCCESS] All screenshots deleted successfully
[[18:44:14]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[18:44:14]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250826_184414/screenshots
[[18:44:14]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250826_184414
[[18:44:14]] [SUCCESS] Report directory initialized successfully
[[18:44:14]] [INFO] Initializing report directory and screenshots folder for test suite...
[[18:44:09]] [SUCCESS] All screenshots deleted successfully
[[18:44:09]] [INFO] All actions cleared
[[18:44:09]] [INFO] Cleaning up screenshots...
[[18:43:45]] [SUCCESS] Screenshot refreshed successfully
[[18:43:44]] [SUCCESS] Screenshot refreshed
[[18:43:44]] [INFO] Refreshing screenshot...
[[18:43:43]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8
[[18:43:43]] [INFO] Device info updated: RMX2151
[[18:43:22]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[18:43:20]] [SUCCESS] Found 1 device(s)
[[18:43:19]] [INFO] Refreshing device list...
