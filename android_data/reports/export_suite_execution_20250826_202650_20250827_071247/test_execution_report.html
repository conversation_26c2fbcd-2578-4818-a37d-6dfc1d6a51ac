<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Execution - 2025-08-27 07:12:47</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
    <style>
        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            color: #333;
            line-height: 1.6;
            padding: 20px;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 30px;
        }
        
        h1 {
            margin-bottom: 20px;
            color: #333;
            border-bottom: 1px solid #eee;
            padding-bottom: 15px;
        }
        
        .test-case {
            margin-bottom: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        
        .test-case-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 15px;
            background-color: #f8f8f8;
            border-bottom: 1px solid #ddd;
            cursor: pointer;
        }
        
        .test-case-header.success {
            background-color: #d4edda;
            border-color: #c3e6cb;
        }
        
        .test-case-header.error {
            background-color: #f8d7da;
            border-color: #f5c6cb;
        }
        
        .test-case-header h3 {
            margin: 0;
            font-size: 1.2rem;
            font-weight: 600;
        }
        
        .test-case-content {
            display: none;
            padding: 0;
        }
        
        .test-case-content.show {
            display: block;
        }
        
        .action-item {
            padding: 10px 15px;
            border-bottom: 1px solid #eee;
            display: flex;
            align-items: center;
        }
        
        .action-item:last-child {
            border-bottom: none;
        }

        .action-item.info-action {
            background-color: #d4edda;
        }
        
        .action-item .action-number {
            min-width: 30px;
            height: 30px;
            background-color: #e9ecef;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            font-weight: 600;
            font-size: 0.9rem;
        }
        
        .action-type {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 4px;
            margin-right: 10px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .action-type.tap {
            background-color: #cce5ff;
            color: #004085;
        }
        
        .action-type.swipe {
            background-color: #d1ecf1;
            color: #0c5460;
        }
        
        .action-type.text {
            background-color: #d4edda;
            color: #155724;
        }
        
        .action-type.wait {
            background-color: #ffeeba;
            color: #856404;
        }
        
        .action-type.addLog {
            background-color: #e2e3e5;
            color: #383d41;
        }
        
        .action-type.launchApp {
            background-color: #d0f0fd;
            color: #055160;
        }
        
        .action-type.terminateApp {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .action-type.clickElement {
            background-color: #e0cffc;
            color: #3b0764;
        }

        .action-type.info {
            background-color: #d4edda;
            color: #155724;
        }

        .action-type.cleanupSteps {
            background-color: #f8d7da;
            color: #721c24;
        }

        .screenshot-link {
            display: inline-block;
            background-color: #4a6fdc;
            color: white;
            padding: 2px 8px;
            border-radius: 4px;
            margin-left: 10px;
            cursor: pointer;
            font-size: 0.85rem;
        }
        
        .screenshot-link:hover {
            background-color: #3a5bb9;
        }
        
        .screenshot-modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.8);
            align-items: center;
            justify-content: center;
        }
        
        .screenshot-modal.show {
            display: flex;
        }
        
        .screenshot-modal-content {
            max-width: 90%;
            max-height: 90%;
            position: relative;
        }
        
        .screenshot-modal-content img {
            max-width: 100%;
            max-height: 90vh;
            border: 5px solid white;
            border-radius: 5px;
        }
        
        .close-modal {
            position: absolute;
            top: -20px;
            right: -20px;
            color: white;
            font-size: 24px;
            font-weight: bold;
            cursor: pointer;
            background-color: rgba(0, 0, 0, 0.5);
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .badge {
            margin-left: 10px;
        }
        
        .summary-section {
            margin-bottom: 30px;
        }
        
        .summary-stats {
            display: flex;
            gap: 20px;
            margin-bottom: 15px;
        }
        
        .stat-card {
            flex: 1;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        
        .stat-card.total {
            background-color: #e9ecef;
        }
        
        .stat-card.passed {
            background-color: #d4edda;
        }
        
        .stat-card.failed {
            background-color: #f8d7da;
        }
        
        .stat-card h4 {
            margin: 0;
            font-size: 1rem;
            font-weight: 600;
        }
        
        .stat-card .number {
            font-size: 2rem;
            font-weight: 700;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Test Execution - 2025-08-27 07:12:47</h1>
        
        <div class="summary-section">
            <h2>Summary</h2>
            <div class="summary-stats">
                <div class="stat-card total">
                    <h4>Total Test Cases</h4>
                    <div class="number">2</div>
                </div>
                <div class="stat-card passed">
                    <h4>Passed</h4>
                    <div class="number">1</div>
                </div>
                <div class="stat-card failed">
                    <h4>Failed</h4>
                    <div class="number">1</div>
                </div>
            </div>
        </div>
        
        <div class="test-cases-section">
            <h2>Test Cases</h2>
            
            
            <div class="test-case">
                <div class="test-case-header error">
                    <h3>Calc-AndroidTest-Extra-Steps
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Edit
                            
                            
                                 Remove
                            
                            7 actions</h3>
                    <div>
                        <span class="badge bg-danger">
                            Failed
                        </span>
                        <i class="bi bi-chevron-down toggle-icon"></i>
                    </div>
                </div>
                <div class="test-case-content">
                    <div class="action-list">
                        
                        <div class="action-item">
                            <div class="action-number">1</div>
                            <span class="action-type action">action</span>
                            <span class="action-description">Restart app: com.coloros.calculator</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">2</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">3</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Settings"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">4</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //android.widget.ImageButton[@content-desc="xyz"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">5</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //android.widget.TextView[@content-desc="Scientific"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">6</div>
                            <span class="action-type terminateApp">terminateApp</span>
                            <span class="action-description">Terminate app: com.coloros.calculator</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">7</div>
                            <span class="action-type cleanupSteps">cleanupSteps</span>
                            <span class="action-description">cleanupSteps action</span>
                            
                        </div>
                        
                    </div>
                </div>
            </div>
            
            <div class="test-case">
                <div class="test-case-header success">
                    <h3>Calc-AndroidTest
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Edit
                            
                            
                                 Remove
                            
                            6 actions</h3>
                    <div>
                        <span class="badge bg-success">
                            Passed
                        </span>
                        <i class="bi bi-chevron-down toggle-icon"></i>
                    </div>
                </div>
                <div class="test-case-content">
                    <div class="action-list">
                        
                        <div class="action-item">
                            <div class="action-number">1</div>
                            <span class="action-type action">action</span>
                            <span class="action-description">Restart app: com.coloros.calculator</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">2</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">3</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on Text: "Settings"</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">4</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //android.widget.ImageButton[@content-desc="Navigate up"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">5</div>
                            <span class="action-type tap">tap</span>
                            <span class="action-description">Tap on element with xpath: //android.widget.TextView[@content-desc="Scientific"]</span>
                            
                        </div>
                        
                        <div class="action-item">
                            <div class="action-number">6</div>
                            <span class="action-type terminateApp">terminateApp</span>
                            <span class="action-description">Terminate app: com.coloros.calculator</span>
                            
                        </div>
                        
                    </div>
                </div>
            </div>
            
        </div>
    </div>
    
    <div class="screenshot-modal">
        <div class="screenshot-modal-content">
            <span class="close-modal">&times;</span>
            <img src="" alt="Screenshot" id="screenshotModalImage">
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // Toggle test case expansion
            const testCaseHeaders = document.querySelectorAll('.test-case-header');
            testCaseHeaders.forEach(header => {
                header.addEventListener('click', function() {
                    const content = this.nextElementSibling;
                    content.classList.toggle('show');
                    const icon = this.querySelector('.toggle-icon');
                    icon.classList.toggle('bi-chevron-down');
                    icon.classList.toggle('bi-chevron-up');
                });
            });
            
            // Screenshot modal functionality
            const modal = document.querySelector('.screenshot-modal');
            const modalImg = document.getElementById('screenshotModalImage');
            const closeModal = document.querySelector('.close-modal');
            
            document.querySelectorAll('.screenshot-link').forEach(link => {
                link.addEventListener('click', function(e) {
                    e.stopPropagation();  // Prevent the click from propagating
                    const screenshotPath = this.getAttribute('data-screenshot');
                    modalImg.src = screenshotPath;
                    modal.classList.add('show');
                    
                    // Log for debugging
                    console.log('Opening screenshot:', screenshotPath);
                });
            });
            
            closeModal.addEventListener('click', function() {
                modal.classList.remove('show');
            });
            
            // Close modal when clicking outside of it
            modal.addEventListener('click', function(event) {
                if (event.target === modal) {
                    modal.classList.remove('show');
                }
            });
        });
    </script>
</body>
</html> 