Action Log - 2025-08-27 07:46:02
================================================================================

[[07:46:02]] [INFO] Generating execution report...
[[07:46:02]] [WARNING] 1 test failed.
[[07:46:02]] [SUCCESS] Screenshot refreshed
[[07:46:02]] [INFO] Refreshing screenshot...
[[07:46:00]] [SUCCESS] Screenshot refreshed successfully
[[07:45:57]] [INFO] Executing action 13/13: Terminate app: com.coloros.calculator
[[07:45:57]] [SUCCESS] Screenshot refreshed
[[07:45:57]] [INFO] Refreshing screenshot...
[[07:45:56]] [SUCCESS] Screenshot refreshed successfully
[[07:45:55]] [INFO] Executing action 12/13: Tap on element with xpath: //android.widget.TextView[@content-desc="Scientific"]
[[07:45:55]] [SUCCESS] Screenshot refreshed
[[07:45:55]] [INFO] Refreshing screenshot...
[[07:45:54]] [SUCCESS] Screenshot refreshed successfully
[[07:45:54]] [INFO] Executing action 11/13: Tap on element with xpath: //android.widget.ImageButton[@content-desc="Navigate up"]
[[07:45:54]] [SUCCESS] Screenshot refreshed
[[07:45:54]] [INFO] Refreshing screenshot...
[[07:45:50]] [SUCCESS] Screenshot refreshed successfully
[[07:45:50]] [INFO] Executing action 10/13: Tap on Text: "Settings"
[[07:45:50]] [SUCCESS] Screenshot refreshed
[[07:45:50]] [INFO] Refreshing screenshot...
[[07:45:49]] [SUCCESS] Screenshot refreshed successfully
[[07:45:48]] [INFO] Executing action 9/13: Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"]
[[07:45:48]] [SUCCESS] Screenshot refreshed
[[07:45:48]] [INFO] Refreshing screenshot...
[[07:45:36]] [INFO] Executing action 8/13: Restart app: com.coloros.calculator
[[07:45:36]] [INFO] Skipping remaining steps in failed test case (moving from action 4 to next test case at 7)
[[07:45:36]] [ERROR] Action 4 failed: Element not found or not tappable after all retry strategies: xpath='//android.widget.ImageButton[@content-desc="xyz"]'
[[07:44:21]] [SUCCESS] Screenshot refreshed successfully
[[07:44:21]] [SUCCESS] Screenshot refreshed successfully
[[07:44:20]] [INFO] Executing action 4/13: Tap on element with xpath: //android.widget.ImageButton[@content-desc="xyz"]
[[07:44:20]] [SUCCESS] Screenshot refreshed
[[07:44:20]] [INFO] Refreshing screenshot...
[[07:44:17]] [SUCCESS] Screenshot refreshed successfully
[[07:44:17]] [SUCCESS] Screenshot refreshed successfully
[[07:44:16]] [INFO] Executing action 3/13: Tap on Text: "Settings"
[[07:44:16]] [SUCCESS] Screenshot refreshed
[[07:44:16]] [INFO] Refreshing screenshot...
[[07:44:15]] [SUCCESS] Screenshot refreshed successfully
[[07:44:15]] [SUCCESS] Screenshot refreshed successfully
[[07:44:15]] [INFO] Executing action 2/13: Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"]
[[07:44:15]] [SUCCESS] Screenshot refreshed
[[07:44:15]] [INFO] Refreshing screenshot...
[[07:44:11]] [INFO] Executing action 1/13: Restart app: com.coloros.calculator
[[07:44:11]] [INFO] ExecutionManager: Starting execution of 13 actions...
[[07:44:11]] [SUCCESS] Cleared 0 screenshots from database
[[07:44:11]] [INFO] Clearing screenshots from database before execution...
[[07:44:11]] [SUCCESS] All screenshots deleted successfully
[[07:44:11]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[07:44:11]] [INFO] Screenshots directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250827_074411/screenshots
[[07:44:11]] [INFO] Report directory: /Users/<USER>/Documents/automation-tool/android_data/reports/testsuite_execution_20250827_074411
[[07:44:11]] [SUCCESS] Report directory initialized successfully
[[07:44:11]] [INFO] Initializing report directory and screenshots folder for test suite...
[[07:44:06]] [SUCCESS] All screenshots deleted successfully
[[07:44:06]] [INFO] All actions cleared
[[07:44:06]] [INFO] Cleaning up screenshots...
[[07:44:00]] [SUCCESS] All screenshots deleted successfully
[[07:44:00]] [INFO] All actions cleared
[[07:44:00]] [INFO] Cleaning up screenshots...
[[07:43:34]] [SUCCESS] Screenshot refreshed successfully
[[07:43:34]] [SUCCESS] Screenshot refreshed
[[07:43:34]] [INFO] Refreshing screenshot...
[[07:43:33]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8
[[07:43:33]] [INFO] Device info updated: RMX2151
[[07:43:24]] [INFO] Action log cleared
