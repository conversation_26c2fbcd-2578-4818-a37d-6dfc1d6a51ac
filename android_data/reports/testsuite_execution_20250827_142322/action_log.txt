Action Log - 2025-08-27 15:18:04
================================================================================

[[15:18:04]] [INFO] Generating execution report...
[[15:18:04]] [SUCCESS] All tests passed successfully!
[[15:18:04]] [SUCCESS] Screenshot refreshed
[[15:18:04]] [INFO] Refreshing screenshot...
[[15:18:04]] [INFO] x4yLCZHaCR=pass
[[15:18:03]] [SUCCESS] Screenshot refreshed successfully
[[15:18:02]] [INFO] x4yLCZHaCR=running
[[15:18:02]] [INFO] Executing action 68/68: Terminate app: au.com.kmart
[[15:18:02]] [SUCCESS] Screenshot refreshed
[[15:18:02]] [INFO] Refreshing screenshot...
[[15:18:02]] [INFO] QspAF2MJsL=pass
[[15:17:58]] [SUCCESS] Screenshot refreshed successfully
[[15:17:57]] [INFO] QspAF2MJsL=running
[[15:17:57]] [INFO] Executing action 67/68: Wait for 4 ms
[[15:17:57]] [SUCCESS] Screenshot refreshed
[[15:17:57]] [INFO] Refreshing screenshot...
[[15:17:57]] [INFO] 2p13JoJbbA=pass
[[15:17:55]] [SUCCESS] Screenshot refreshed successfully
[[15:17:55]] [INFO] 2p13JoJbbA=running
[[15:17:55]] [INFO] Executing action 66/68: Tap on element with xpath: //android.widget.Button[contains(@text,"Remove")]
[[15:17:55]] [SUCCESS] Screenshot refreshed
[[15:17:55]] [INFO] Refreshing screenshot...
[[15:17:55]] [INFO] rbzkUOQMtf=pass
[[15:17:50]] [SUCCESS] Screenshot refreshed successfully
[[15:17:49]] [INFO] rbzkUOQMtf=running
[[15:17:49]] [INFO] Executing action 65/68: Wait for 4 ms
[[15:17:49]] [SUCCESS] Screenshot refreshed
[[15:17:49]] [INFO] Refreshing screenshot...
[[15:17:49]] [INFO] 2p13JoJbbA=pass
[[15:17:48]] [SUCCESS] Screenshot refreshed successfully
[[15:17:17]] [INFO] 2p13JoJbbA=running
[[15:17:17]] [INFO] Executing action 64/68: Tap on element with xpath: //android.widget.Button[contains(@text,"Remove")]
[[15:17:17]] [SUCCESS] Screenshot refreshed
[[15:17:17]] [INFO] Refreshing screenshot...
[[15:17:17]] [INFO] eHLWiRoqqS=pass
[[15:17:14]] [SUCCESS] Screenshot refreshed successfully
[[15:17:13]] [INFO] eHLWiRoqqS=running
[[15:17:13]] [INFO] Executing action 63/68: Swipe from (50%, 70%) to (50%, 50%)
[[15:17:13]] [SUCCESS] Screenshot refreshed
[[15:17:13]] [INFO] Refreshing screenshot...
[[15:17:13]] [INFO] VYgfSNx3GG=pass
[[15:17:12]] [SUCCESS] Screenshot refreshed successfully
[[15:17:11]] [INFO] VYgfSNx3GG=running
[[15:17:11]] [INFO] Executing action 62/68: Tap on element with xpath: //android.view.View[@text="Delivery"]
[[15:17:11]] [SUCCESS] Screenshot refreshed
[[15:17:11]] [INFO] Refreshing screenshot...
[[15:17:11]] [INFO] XoMyLp2unA=pass
[[15:17:03]] [SUCCESS] Screenshot refreshed successfully
[[15:17:03]] [INFO] XoMyLp2unA=running
[[15:17:03]] [INFO] Executing action 61/68: Wait till xpath=//android.view.View[@text="Delivery"]
[[15:17:03]] [SUCCESS] Screenshot refreshed
[[15:17:03]] [INFO] Refreshing screenshot...
[[15:17:03]] [INFO] cTLBS0O1ot=pass
[[15:17:00]] [SUCCESS] Screenshot refreshed successfully
[[15:16:59]] [INFO] cTLBS0O1ot=running
[[15:16:59]] [INFO] Executing action 60/68: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[15:16:59]] [SUCCESS] Screenshot refreshed
[[15:16:59]] [INFO] Refreshing screenshot...
[[15:16:59]] [INFO] F4NGh9HrLw=pass
[[15:16:58]] [SUCCESS] Screenshot refreshed successfully
[[15:16:56]] [INFO] F4NGh9HrLw=running
[[15:16:56]] [INFO] Executing action 59/68: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[15:16:56]] [SUCCESS] Screenshot refreshed
[[15:16:56]] [INFO] Refreshing screenshot...
[[15:16:56]] [INFO] JRheDTvpJf=pass
[[15:16:52]] [SUCCESS] Screenshot refreshed successfully
[[15:16:51]] [INFO] JRheDTvpJf=running
[[15:16:51]] [INFO] Executing action 58/68: Tap on Text: "Add"
[[15:16:51]] [SUCCESS] Screenshot refreshed
[[15:16:51]] [INFO] Refreshing screenshot...
[[15:16:51]] [INFO] eHLWiRoqqS=pass
[[15:16:48]] [SUCCESS] Screenshot refreshed successfully
[[15:16:11]] [INFO] eHLWiRoqqS=running
[[15:16:11]] [INFO] Executing action 57/68: Swipe from (50%, 70%) to (50%, 30%)
[[15:16:11]] [SUCCESS] Screenshot refreshed
[[15:16:11]] [INFO] Refreshing screenshot...
[[15:16:11]] [INFO] kwF3J9NbRc=pass
[[15:16:07]] [SUCCESS] Screenshot refreshed successfully
[[15:16:06]] [INFO] kwF3J9NbRc=running
[[15:16:06]] [INFO] Executing action 56/68: Wait till text appears: "SKU"
[[15:16:06]] [SUCCESS] Screenshot refreshed
[[15:16:06]] [INFO] Refreshing screenshot...
[[15:16:06]] [INFO] kAQ1yIIw3h=pass
[[15:16:04]] [SUCCESS] Screenshot refreshed successfully
[[15:16:03]] [INFO] kAQ1yIIw3h=running
[[15:16:03]] [INFO] Executing action 55/68: Tap on element with xpath: ((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1] with fallback: Coordinates (98, 308)
[[15:16:03]] [SUCCESS] Screenshot refreshed
[[15:16:03]] [INFO] Refreshing screenshot...
[[15:16:03]] [INFO] lYPskZt0Ya=pass
[[15:15:49]] [SUCCESS] Screenshot refreshed successfully
[[15:15:49]] [INFO] lYPskZt0Ya=running
[[15:15:49]] [INFO] Executing action 54/68: Wait till xpath=//android.widget.Button[@text="Filter"]
[[15:15:49]] [SUCCESS] Screenshot refreshed
[[15:15:49]] [INFO] Refreshing screenshot...
[[15:15:49]] [INFO] oNKCP9pqiF=pass
[[15:15:48]] [SUCCESS] Screenshot refreshed successfully
[[15:15:47]] [INFO] oNKCP9pqiF=running
[[15:15:47]] [INFO] Executing action 53/68: Android Function: send_key_event - Key Event: ENTER
[[15:15:47]] [SUCCESS] Screenshot refreshed
[[15:15:47]] [INFO] Refreshing screenshot...
[[15:15:47]] [INFO] JRheDTvpJf=pass
[[15:15:45]] [SUCCESS] Screenshot refreshed successfully
[[15:15:44]] [INFO] JRheDTvpJf=running
[[15:15:44]] [INFO] Executing action 52/68: Input text: "mat"
[[15:15:44]] [SUCCESS] Screenshot refreshed
[[15:15:44]] [INFO] Refreshing screenshot...
[[15:15:44]] [INFO] o1gHFWHXTL=pass
[[15:15:41]] [SUCCESS] Screenshot refreshed successfully
[[15:15:40]] [INFO] o1gHFWHXTL=running
[[15:15:40]] [INFO] Executing action 51/68: Tap on Text: "Find"
[[15:15:40]] [SUCCESS] Screenshot refreshed
[[15:15:40]] [INFO] Refreshing screenshot...
[[15:15:40]] [INFO] o74txS2f4j=pass
[[15:15:32]] [SUCCESS] Screenshot refreshed successfully
[[15:15:31]] [INFO] o74txS2f4j=running
[[15:15:31]] [INFO] Executing action 50/68: Tap on image: find-products-browse.png
[[15:15:31]] [SUCCESS] Screenshot refreshed
[[15:15:31]] [INFO] Refreshing screenshot...
[[15:15:31]] [INFO] F4NGh9HrLw=pass
[[15:15:29]] [SUCCESS] Screenshot refreshed successfully
[[15:15:29]] [INFO] F4NGh9HrLw=running
[[15:15:29]] [INFO] Executing action 49/68: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 2 of 5")]
[[15:15:29]] [SUCCESS] Screenshot refreshed
[[15:15:29]] [INFO] Refreshing screenshot...
[[15:15:29]] [INFO] JRheDTvpJf=pass
[[15:15:25]] [SUCCESS] Screenshot refreshed successfully
[[15:15:25]] [INFO] JRheDTvpJf=running
[[15:15:25]] [INFO] Executing action 48/68: Tap on Text: "Add"
[[15:15:25]] [SUCCESS] Screenshot refreshed
[[15:15:25]] [INFO] Refreshing screenshot...
[[15:15:25]] [INFO] eHLWiRoqqS=pass
[[15:15:22]] [SUCCESS] Screenshot refreshed successfully
[[15:15:21]] [INFO] eHLWiRoqqS=running
[[15:15:21]] [INFO] Executing action 47/68: Swipe from (50%, 70%) to (50%, 30%)
[[15:15:21]] [SUCCESS] Screenshot refreshed
[[15:15:21]] [INFO] Refreshing screenshot...
[[15:15:21]] [INFO] kAQ1yIIw3h=pass
[[15:15:19]] [SUCCESS] Screenshot refreshed successfully
[[15:15:18]] [INFO] kAQ1yIIw3h=running
[[15:15:18]] [INFO] Executing action 46/68: Tap on element with xpath: ((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1] with fallback: Coordinates (98, 308)
[[15:15:18]] [SUCCESS] Screenshot refreshed
[[15:15:18]] [INFO] Refreshing screenshot...
[[15:15:18]] [INFO] lYPskZt0Ya=pass
[[15:15:11]] [SUCCESS] Screenshot refreshed successfully
[[15:15:11]] [INFO] lYPskZt0Ya=running
[[15:15:11]] [INFO] Executing action 45/68: Wait till xpath=//android.widget.Button[@text="Filter"]
[[15:15:11]] [SUCCESS] Screenshot refreshed
[[15:15:11]] [INFO] Refreshing screenshot...
[[15:15:11]] [INFO] YaIypAHEOz=pass
[[15:15:10]] [SUCCESS] Screenshot refreshed successfully
[[15:15:09]] [INFO] YaIypAHEOz=running
[[15:15:09]] [INFO] Executing action 44/68: Android Function: send_key_event - Key Event: ENTER
[[15:15:09]] [SUCCESS] Screenshot refreshed
[[15:15:09]] [INFO] Refreshing screenshot...
[[15:15:09]] [INFO] JRheDTvpJf=pass
[[15:15:07]] [SUCCESS] Screenshot refreshed successfully
[[15:15:07]] [INFO] JRheDTvpJf=running
[[15:15:07]] [INFO] Executing action 43/68: Input text: "Kids Toys"
[[15:15:07]] [SUCCESS] Screenshot refreshed
[[15:15:07]] [INFO] Refreshing screenshot...
[[15:15:07]] [INFO] o1gHFWHXTL=pass
[[15:15:04]] [SUCCESS] Screenshot refreshed successfully
[[15:15:03]] [INFO] o1gHFWHXTL=running
[[15:15:03]] [INFO] Executing action 42/68: Tap on Text: "Find"
[[15:15:03]] [SUCCESS] Screenshot refreshed
[[15:15:03]] [INFO] Refreshing screenshot...
[[15:15:03]] [INFO] o74txS2f4j=pass
[[15:14:43]] [SUCCESS] Screenshot refreshed successfully
[[15:14:42]] [INFO] o74txS2f4j=running
[[15:14:42]] [INFO] Executing action 41/68: Tap on image: find-products-browse.png
[[15:14:42]] [SUCCESS] Screenshot refreshed
[[15:14:42]] [INFO] Refreshing screenshot...
[[15:14:42]] [INFO] F4NGh9HrLw=pass
[[15:14:37]] [SUCCESS] Screenshot refreshed successfully
[[15:14:37]] [INFO] F4NGh9HrLw=running
[[15:14:37]] [INFO] Executing action 40/68: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 2 of 5")]
[[15:14:37]] [SUCCESS] Screenshot refreshed
[[15:14:37]] [INFO] Refreshing screenshot...
[[15:14:37]] [INFO] XPEr3w6Zof=pass
[[15:14:36]] [SUCCESS] Screenshot refreshed successfully
[[15:14:34]] [INFO] XPEr3w6Zof=running
[[15:14:34]] [INFO] Executing action 39/68: Launch app: au.com.kmart
[[15:14:34]] [SUCCESS] Screenshot refreshed
[[15:14:34]] [INFO] Refreshing screenshot...
[[15:14:34]] [INFO] XPEr3w6Zof=pass
[[15:14:32]] [SUCCESS] Screenshot refreshed successfully
[[15:14:32]] [INFO] XPEr3w6Zof=running
[[15:14:32]] [INFO] Executing action 38/68: Terminate app: au.com.kmart
[[15:14:32]] [SUCCESS] Screenshot refreshed
[[15:14:32]] [INFO] Refreshing screenshot...
[[15:14:32]] [INFO] Et3kvnFdxh=pass
[[15:14:30]] [SUCCESS] Screenshot refreshed successfully
[[15:14:29]] [INFO] Et3kvnFdxh=running
[[15:14:29]] [INFO] Executing action 37/68: Tap on element with xpath: //android.view.View[@content-desc="stnPostCodeSelectionScreenBodyWidget"]/android.view.View[1]/android.widget.ImageView
[[15:14:29]] [SUCCESS] Screenshot refreshed
[[15:14:29]] [INFO] Refreshing screenshot...
[[15:14:29]] [INFO] GWoppouz1l=pass
[[15:14:28]] [SUCCESS] Screenshot refreshed successfully
[[15:14:27]] [INFO] GWoppouz1l=running
[[15:14:27]] [INFO] Executing action 36/68: Check if element with xpath="//android.view.View[@content-desc="txtLocationTitle"]" exists
[[15:14:27]] [SUCCESS] Screenshot refreshed
[[15:14:27]] [INFO] Refreshing screenshot...
[[15:14:27]] [INFO] B6GDXWAmWp=pass
[[15:14:26]] [SUCCESS] Screenshot refreshed successfully
[[15:14:25]] [INFO] B6GDXWAmWp=running
[[15:14:25]] [INFO] Executing action 35/68: Tap on element with xpath: //android.widget.TextView[@text="Shop at"]/following-sibling::android.widget.Button
[[15:14:25]] [SUCCESS] Screenshot refreshed
[[15:14:25]] [INFO] Refreshing screenshot...
[[15:14:25]] [INFO] eHLWiRoqqS=pass
[[15:14:22]] [SUCCESS] Screenshot refreshed successfully
[[15:14:22]] [INFO] eHLWiRoqqS=running
[[15:14:22]] [INFO] Executing action 34/68: Swipe from (50%, 70%) to (50%, 40%)
[[15:14:22]] [SUCCESS] Screenshot refreshed
[[15:14:22]] [INFO] Refreshing screenshot...
[[15:14:22]] [INFO] mtYqeDttRc=pass
[[15:14:20]] [SUCCESS] Screenshot refreshed successfully
[[15:14:20]] [INFO] mtYqeDttRc=running
[[15:14:20]] [INFO] Executing action 33/68: Tap on element with xpath: //android.widget.Button[@resource-id="close-btn"]
[[15:14:20]] [SUCCESS] Screenshot refreshed
[[15:14:20]] [INFO] Refreshing screenshot...
[[15:14:20]] [INFO] P4b2BITpCf=pass
[[15:14:17]] [SUCCESS] Screenshot refreshed successfully
[[15:14:16]] [INFO] P4b2BITpCf=running
[[15:14:16]] [INFO] Executing action 32/68: Check if element with text="interest-" exists
[[15:14:16]] [SUCCESS] Screenshot refreshed
[[15:14:16]] [INFO] Refreshing screenshot...
[[15:14:16]] [INFO] q6cKxgMAIn=pass
[[15:14:14]] [SUCCESS] Screenshot refreshed successfully
[[15:14:13]] [INFO] q6cKxgMAIn=running
[[15:14:13]] [INFO] Executing action 31/68: Tap on element with xpath: //android.widget.Button[@text="Learn more about PayPal Pay in 4"]
[[15:14:13]] [SUCCESS] Screenshot refreshed
[[15:14:13]] [INFO] Refreshing screenshot...
[[15:14:13]] [INFO] Et3kvnFdxh=pass
[[15:14:12]] [SUCCESS] Screenshot refreshed successfully
[[15:14:12]] [INFO] Et3kvnFdxh=running
[[15:14:12]] [INFO] Executing action 30/68: Android Function: send_key_event - Key Event: BACK
[[15:14:12]] [SUCCESS] Screenshot refreshed
[[15:14:12]] [INFO] Refreshing screenshot...
[[15:14:12]] [INFO] P4b2BITpCf=pass
[[15:14:07]] [SUCCESS] Screenshot refreshed successfully
[[15:14:07]] [INFO] P4b2BITpCf=running
[[15:14:07]] [INFO] Executing action 29/68: Check if element with text="What" exists
[[15:14:07]] [SUCCESS] Screenshot refreshed
[[15:14:07]] [INFO] Refreshing screenshot...
[[15:14:07]] [INFO] inrxgdWzXr=pass
[[15:14:05]] [SUCCESS] Screenshot refreshed successfully
[[15:14:04]] [INFO] inrxgdWzXr=running
[[15:14:04]] [INFO] Executing action 28/68: Tap on element with xpath: //android.widget.TextView[@text="Learn moreabout Zip"]
[[15:14:04]] [SUCCESS] Screenshot refreshed
[[15:14:04]] [INFO] Refreshing screenshot...
[[15:14:04]] [INFO] Et3kvnFdxh=pass
[[15:14:03]] [SUCCESS] Screenshot refreshed successfully
[[15:14:03]] [INFO] Et3kvnFdxh=running
[[15:14:03]] [INFO] Executing action 27/68: Android Function: send_key_event - Key Event: BACK
[[15:14:03]] [SUCCESS] Screenshot refreshed
[[15:14:03]] [INFO] Refreshing screenshot...
[[15:14:03]] [INFO] DhWa2PCBXE=pass
[[15:13:58]] [SUCCESS] Screenshot refreshed successfully
[[15:13:58]] [INFO] DhWa2PCBXE=running
[[15:13:58]] [INFO] Executing action 26/68: Check if element with text="Apply" exists
[[15:13:58]] [SUCCESS] Screenshot refreshed
[[15:13:58]] [INFO] Refreshing screenshot...
[[15:13:58]] [INFO] pk2DLZFBmx=pass
[[15:13:56]] [SUCCESS] Screenshot refreshed successfully
[[15:13:56]] [INFO] pk2DLZFBmx=running
[[15:13:56]] [INFO] Executing action 25/68: Tap on element with xpath: //android.widget.TextView[@text="Learn moreabout AfterPay"]
[[15:13:56]] [SUCCESS] Screenshot refreshed
[[15:13:56]] [INFO] Refreshing screenshot...
[[15:13:56]] [INFO] ShJSdXvmVL=pass
[[15:13:52]] [SUCCESS] Screenshot refreshed successfully
[[15:13:51]] [INFO] ShJSdXvmVL=running
[[15:13:51]] [INFO] Executing action 24/68: Swipe up till element xpath: "//android.widget.TextView[@text="Learn moreabout AfterPay"]" is visible
[[15:13:51]] [SUCCESS] Screenshot refreshed
[[15:13:51]] [INFO] Refreshing screenshot...
[[15:13:51]] [INFO] y5FboDiRLS=pass
[[15:13:23]] [SUCCESS] Screenshot refreshed successfully
[[15:13:23]] [INFO] y5FboDiRLS=running
[[15:13:23]] [INFO] Executing action 23/68: Tap on image: share-close.png
[[15:13:23]] [SUCCESS] Screenshot refreshed
[[15:13:23]] [INFO] Refreshing screenshot...
[[15:13:23]] [INFO] EEx673tuI0=pass
[[15:13:20]] [SUCCESS] Screenshot refreshed successfully
[[15:13:19]] [INFO] EEx673tuI0=running
[[15:13:19]] [INFO] Executing action 22/68: Check if element with text="Share" exists
[[15:13:19]] [SUCCESS] Screenshot refreshed
[[15:13:19]] [INFO] Refreshing screenshot...
[[15:13:19]] [INFO] dCqKBG3e7u=pass
[[15:13:17]] [SUCCESS] Screenshot refreshed successfully
[[15:13:17]] [INFO] dCqKBG3e7u=running
[[15:13:17]] [INFO] Executing action 21/68: Tap on element with xpath: //android.view.View[@content-desc="Product Details"]/following-sibling::android.widget.ImageView[1]
[[15:13:17]] [SUCCESS] Screenshot refreshed
[[15:13:17]] [INFO] Refreshing screenshot...
[[15:13:17]] [INFO] kAQ1yIIw3h=pass
[[15:13:15]] [SUCCESS] Screenshot refreshed successfully
[[15:13:14]] [INFO] kAQ1yIIw3h=running
[[15:13:14]] [INFO] Executing action 20/68: Tap on element with xpath: ((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1] with fallback: Coordinates (98, 308)
[[15:13:14]] [SUCCESS] Screenshot refreshed
[[15:13:14]] [INFO] Refreshing screenshot...
[[15:13:14]] [INFO] OmKfD9iBjD=pass
[[15:13:12]] [SUCCESS] Screenshot refreshed successfully
[[15:13:11]] [INFO] OmKfD9iBjD=running
[[15:13:11]] [INFO] Executing action 19/68: Wait till xpath=((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1]
[[15:13:11]] [SUCCESS] Screenshot refreshed
[[15:13:11]] [INFO] Refreshing screenshot...
[[15:13:11]] [INFO] dMl1PH9Dlc=pass
[[15:12:59]] [SUCCESS] Screenshot refreshed successfully
[[15:12:58]] [INFO] dMl1PH9Dlc=running
[[15:12:58]] [INFO] Executing action 18/68: Wait for 10 ms
[[15:12:58]] [SUCCESS] Screenshot refreshed
[[15:12:58]] [INFO] Refreshing screenshot...
[[15:12:58]] [INFO] eHLWiRoqqS=pass
[[15:12:55]] [SUCCESS] Screenshot refreshed successfully
[[15:12:54]] [INFO] eHLWiRoqqS=running
[[15:12:54]] [INFO] Executing action 17/68: Swipe from (50%, 70%) to (50%, 30%)
[[15:12:54]] [SUCCESS] Screenshot refreshed
[[15:12:54]] [INFO] Refreshing screenshot...
[[15:12:54]] [INFO] huUnpMMjVR=pass
[[15:12:53]] [SUCCESS] Screenshot refreshed successfully
[[15:12:52]] [INFO] huUnpMMjVR=running
[[15:12:52]] [INFO] Executing action 16/68: Tap on element with xpath: //android.widget.Button[@text="In stock only i... ChipsClose"]
[[15:12:52]] [SUCCESS] Screenshot refreshed
[[15:12:52]] [INFO] Refreshing screenshot...
[[15:12:52]] [INFO] XmAxcBtFI0=pass
[[15:12:49]] [SUCCESS] Screenshot refreshed successfully
[[15:12:49]] [INFO] XmAxcBtFI0=running
[[15:12:49]] [INFO] Executing action 15/68: Check if element with xpath="//android.widget.Button[@text="In stock only i... ChipsClose"]" exists
[[15:12:49]] [SUCCESS] Screenshot refreshed
[[15:12:49]] [INFO] Refreshing screenshot...
[[15:12:49]] [INFO] a50JhCx0ir=pass
[[15:12:32]] [SUCCESS] Screenshot refreshed successfully
[[15:12:31]] [INFO] a50JhCx0ir=running
[[15:12:31]] [INFO] Executing action 14/68: Tap on Text: "Show"
[[15:12:31]] [SUCCESS] Screenshot refreshed
[[15:12:31]] [INFO] Refreshing screenshot...
[[15:12:31]] [INFO] dMl1PH9Dlc=pass
[[15:12:26]] [SUCCESS] Screenshot refreshed successfully
[[15:12:25]] [INFO] dMl1PH9Dlc=running
[[15:12:25]] [INFO] Executing action 13/68: Wait for 5 ms
[[15:12:25]] [SUCCESS] Screenshot refreshed
[[15:12:25]] [INFO] Refreshing screenshot...
[[15:12:25]] [INFO] a50JhCx0ir=pass
[[15:12:22]] [SUCCESS] Screenshot refreshed successfully
[[15:12:21]] [INFO] a50JhCx0ir=running
[[15:12:21]] [INFO] Executing action 12/68: Tap on Text: "only"
[[15:12:21]] [SUCCESS] Screenshot refreshed
[[15:12:21]] [INFO] Refreshing screenshot...
[[15:12:21]] [INFO] Y1O1clhMSJ=pass
[[15:12:20]] [SUCCESS] Screenshot refreshed successfully
[[15:12:19]] [INFO] Y1O1clhMSJ=running
[[15:12:19]] [INFO] Executing action 11/68: Tap on element with xpath: //android.widget.Button[@text="Filter"]
[[15:12:19]] [SUCCESS] Screenshot refreshed
[[15:12:19]] [INFO] Refreshing screenshot...
[[15:12:19]] [INFO] lYPskZt0Ya=pass
[[15:12:11]] [SUCCESS] Screenshot refreshed successfully
[[15:12:10]] [INFO] lYPskZt0Ya=running
[[15:12:10]] [INFO] Executing action 10/68: Wait till xpath=//android.widget.Button[@text="Filter"]
[[15:12:10]] [SUCCESS] Screenshot refreshed
[[15:12:10]] [INFO] Refreshing screenshot...
[[15:12:10]] [INFO] xUbWFa8Ok2=pass
[[15:12:07]] [SUCCESS] Screenshot refreshed successfully
[[15:12:06]] [INFO] xUbWFa8Ok2=running
[[15:12:06]] [INFO] Executing action 9/68: Tap on Text: "Latest"
[[15:12:06]] [SUCCESS] Screenshot refreshed
[[15:12:06]] [INFO] Refreshing screenshot...
[[15:12:06]] [INFO] RbNtEW6N9T=pass
[[15:12:03]] [SUCCESS] Screenshot refreshed successfully
[[15:12:02]] [INFO] RbNtEW6N9T=running
[[15:12:02]] [INFO] Executing action 8/68: Tap on Text: "Toys"
[[15:12:02]] [SUCCESS] Screenshot refreshed
[[15:12:02]] [INFO] Refreshing screenshot...
[[15:12:02]] [INFO] ltDXyWvtEz=pass
[[15:12:00]] [SUCCESS] Screenshot refreshed successfully
[[15:12:00]] [INFO] ltDXyWvtEz=running
[[15:12:00]] [INFO] Executing action 7/68: Tap on element with xpath: //android.view.View[@content-desc="Search"]/preceding::android.widget.ImageView[1]
[[15:12:00]] [SUCCESS] Screenshot refreshed
[[15:12:00]] [INFO] Refreshing screenshot...
[[15:12:00]] [INFO] QPKR6jUF9O=pass
[[15:11:59]] [SUCCESS] Screenshot refreshed successfully
[[15:11:58]] [INFO] QPKR6jUF9O=running
[[15:11:58]] [INFO] Executing action 6/68: Check if element with xpath="//android.widget.ImageView[@content-desc="Scan barcode"]" exists
[[15:11:58]] [SUCCESS] Screenshot refreshed
[[15:11:58]] [INFO] Refreshing screenshot...
[[15:11:58]] [INFO] vfwUVEyq6X=pass
[[15:11:57]] [SUCCESS] Screenshot refreshed successfully
[[15:11:56]] [INFO] vfwUVEyq6X=running
[[15:11:56]] [INFO] Executing action 5/68: Check if element with xpath="//android.widget.ImageView[@content-desc="More"]" exists
[[15:11:56]] [SUCCESS] Screenshot refreshed
[[15:11:56]] [INFO] Refreshing screenshot...
[[15:11:56]] [INFO] o74txS2f4j=pass
[[15:11:48]] [SUCCESS] Screenshot refreshed successfully
[[15:11:47]] [INFO] o74txS2f4j=running
[[15:11:47]] [INFO] Executing action 4/68: Tap on image: find-products-browse.png
[[15:11:47]] [SUCCESS] Screenshot refreshed
[[15:11:47]] [INFO] Refreshing screenshot...
[[15:11:47]] [INFO] F4NGh9HrLw=pass
[[15:11:45]] [SUCCESS] Screenshot refreshed successfully
[[15:11:45]] [INFO] F4NGh9HrLw=running
[[15:11:45]] [INFO] Executing action 3/68: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 2 of 5")]
[[15:11:45]] [SUCCESS] Screenshot refreshed
[[15:11:45]] [INFO] Refreshing screenshot...
[[15:11:45]] [INFO] H9fy9qcFbZ=pass
[[15:11:43]] [SUCCESS] Screenshot refreshed successfully
[[15:11:42]] [INFO] H9fy9qcFbZ=running
[[15:11:42]] [INFO] Executing action 2/68: Launch app: au.com.kmart
[[15:11:42]] [SUCCESS] Screenshot refreshed
[[15:11:42]] [INFO] Refreshing screenshot...
[[15:11:42]] [SUCCESS] Screenshot refreshed
[[15:11:42]] [INFO] Refreshing screenshot...
[[15:11:39]] [SUCCESS] Screenshot refreshed successfully
[[15:11:39]] [INFO] Executing Multi Step action step 6/6: Tap on element with accessibility_id: txtOnePassOnboardingSkipForNow
[[15:11:39]] [SUCCESS] Screenshot refreshed
[[15:11:39]] [INFO] Refreshing screenshot...
[[15:11:38]] [SUCCESS] Screenshot refreshed successfully
[[15:11:37]] [INFO] Executing Multi Step action step 5/6: Tap on element with accessibility_id: btnMayBeLater
[[15:11:37]] [SUCCESS] Screenshot refreshed
[[15:11:37]] [INFO] Refreshing screenshot...
[[15:11:35]] [SUCCESS] Screenshot refreshed successfully
[[15:11:34]] [INFO] Executing Multi Step action step 4/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[15:11:34]] [SUCCESS] Screenshot refreshed
[[15:11:34]] [INFO] Refreshing screenshot...
[[15:11:30]] [SUCCESS] Screenshot refreshed successfully
[[15:11:29]] [INFO] Executing Multi Step action step 3/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[15:11:29]] [SUCCESS] Screenshot refreshed
[[15:11:29]] [INFO] Refreshing screenshot...
[[15:11:28]] [SUCCESS] Screenshot refreshed successfully
[[15:11:26]] [INFO] Executing Multi Step action step 2/6: Launch app: au.com.kmart
[[15:11:26]] [SUCCESS] Screenshot refreshed
[[15:11:26]] [INFO] Refreshing screenshot...
[[15:11:23]] [INFO] Executing Multi Step action step 1/6: Android Function: clear_app - Package: au.com.kmart
[[15:11:23]] [INFO] Loaded 6 steps from test case: Onboarding-Start-AU
[[15:11:23]] [INFO] Loading steps for Multi Step action: Onboarding-Start-AU
[[15:11:23]] [INFO] H9fy9qcFbZ=running
[[15:11:23]] [INFO] Executing action 1/68: Execute Test Case: Onboarding-Start-AU (6 steps)
[[15:11:23]] [INFO] ExecutionManager: Starting execution of 68 actions...
[[15:11:23]] [SUCCESS] Cleared 0 screenshots from database
[[15:11:23]] [INFO] Clearing screenshots from database before execution...
[[15:11:23]] [SUCCESS] All screenshots deleted successfully
[[15:11:23]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[15:11:23]] [INFO] Skipping report initialization - single test case execution
[[15:09:52]] [SUCCESS] Screenshot refreshed successfully
[[15:09:50]] [INFO] Skipping report generation - individual test case execution (not from test suite)
[[15:09:50]] [SUCCESS] Action logs saved successfully
[[15:09:50]] [ERROR] Execution failed but report was generated.
[[15:09:50]] [INFO] Saving 1094 action log entries to file...
[[15:09:50]] [INFO] Generating execution report...
[[15:09:50]] [SUCCESS] All tests passed successfully!
[[15:09:50]] [SUCCESS] Screenshot refreshed
[[15:09:50]] [INFO] Refreshing screenshot...
[[15:09:50]] [INFO] x4yLCZHaCR=pass
[[15:09:49]] [SUCCESS] Screenshot refreshed successfully
[[15:09:48]] [INFO] x4yLCZHaCR=running
[[15:09:48]] [INFO] Executing action 68/68: Terminate app: au.com.kmart
[[15:09:48]] [SUCCESS] Screenshot refreshed
[[15:09:48]] [INFO] Refreshing screenshot...
[[15:09:48]] [INFO] QspAF2MJsL=pass
[[15:09:06]] [INFO] QspAF2MJsL=running
[[15:09:06]] [INFO] Executing action 67/68: Wait for 4 ms
[[15:09:06]] [INFO] 2p13JoJbbA=fail
[[15:09:06]] [ERROR] Action 66 failed: Element not found or not tappable after all retry strategies: xpath='//android.widget.Button[contains(@text,"Remove")]'
[[15:08:02]] [SUCCESS] Screenshot refreshed successfully
[[15:08:01]] [INFO] 2p13JoJbbA=running
[[15:08:01]] [INFO] Executing action 66/68: Tap on element with xpath: //android.widget.Button[contains(@text,"Remove")]
[[15:08:01]] [SUCCESS] Screenshot refreshed
[[15:08:01]] [INFO] Refreshing screenshot...
[[15:08:01]] [INFO] rbzkUOQMtf=pass
[[15:07:57]] [SUCCESS] Screenshot refreshed successfully
[[15:07:56]] [INFO] rbzkUOQMtf=running
[[15:07:56]] [INFO] Executing action 65/68: Wait for 4 ms
[[15:07:56]] [SUCCESS] Screenshot refreshed
[[15:07:56]] [INFO] Refreshing screenshot...
[[15:07:56]] [INFO] 2p13JoJbbA=pass
[[15:07:54]] [SUCCESS] Screenshot refreshed successfully
[[15:07:53]] [INFO] 2p13JoJbbA=running
[[15:07:53]] [INFO] Executing action 64/68: Tap on element with xpath: //android.widget.Button[contains(@text,"Remove")]
[[15:07:53]] [SUCCESS] Screenshot refreshed
[[15:07:53]] [INFO] Refreshing screenshot...
[[15:07:53]] [INFO] eHLWiRoqqS=pass
[[15:07:49]] [SUCCESS] Screenshot refreshed successfully
[[15:07:48]] [INFO] eHLWiRoqqS=running
[[15:07:48]] [INFO] Executing action 63/68: Swipe from (50%, 70%) to (50%, 50%)
[[15:07:48]] [SUCCESS] Screenshot refreshed
[[15:07:48]] [INFO] Refreshing screenshot...
[[15:07:48]] [INFO] VYgfSNx3GG=pass
[[15:07:46]] [SUCCESS] Screenshot refreshed successfully
[[15:07:46]] [INFO] VYgfSNx3GG=running
[[15:07:46]] [INFO] Executing action 62/68: Tap on element with xpath: //android.view.View[@text="Delivery"]
[[15:07:46]] [SUCCESS] Screenshot refreshed
[[15:07:46]] [INFO] Refreshing screenshot...
[[15:07:46]] [INFO] XoMyLp2unA=pass
[[15:07:37]] [SUCCESS] Screenshot refreshed successfully
[[15:07:36]] [INFO] XoMyLp2unA=running
[[15:07:36]] [INFO] Executing action 61/68: Wait till xpath=//android.view.View[@text="Delivery"]
[[15:07:36]] [SUCCESS] Screenshot refreshed
[[15:07:36]] [INFO] Refreshing screenshot...
[[15:07:36]] [INFO] cTLBS0O1ot=pass
[[15:07:35]] [SUCCESS] Screenshot refreshed successfully
[[15:07:34]] [INFO] cTLBS0O1ot=running
[[15:07:34]] [INFO] Executing action 60/68: Tap if locator exists: xpath="//android.widget.Button[@content-desc="Checkout"]"
[[15:07:34]] [SUCCESS] Screenshot refreshed
[[15:07:34]] [INFO] Refreshing screenshot...
[[15:07:34]] [INFO] F4NGh9HrLw=pass
[[15:07:32]] [SUCCESS] Screenshot refreshed successfully
[[15:07:31]] [INFO] F4NGh9HrLw=running
[[15:07:31]] [INFO] Executing action 59/68: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 4 of 5")]
[[15:07:31]] [SUCCESS] Screenshot refreshed
[[15:07:31]] [INFO] Refreshing screenshot...
[[15:07:31]] [INFO] JRheDTvpJf=pass
[[15:07:27]] [SUCCESS] Screenshot refreshed successfully
[[15:07:26]] [INFO] JRheDTvpJf=running
[[15:07:26]] [INFO] Executing action 58/68: Tap on Text: "Add"
[[15:07:26]] [SUCCESS] Screenshot refreshed
[[15:07:26]] [INFO] Refreshing screenshot...
[[15:07:26]] [INFO] eHLWiRoqqS=pass
[[15:07:23]] [SUCCESS] Screenshot refreshed successfully
[[15:07:21]] [INFO] eHLWiRoqqS=running
[[15:07:21]] [INFO] Executing action 57/68: Swipe from (50%, 70%) to (50%, 30%)
[[15:07:21]] [SUCCESS] Screenshot refreshed
[[15:07:21]] [INFO] Refreshing screenshot...
[[15:07:21]] [INFO] kwF3J9NbRc=pass
[[15:07:16]] [SUCCESS] Screenshot refreshed successfully
[[15:07:15]] [INFO] kwF3J9NbRc=running
[[15:07:15]] [INFO] Executing action 56/68: Wait till text appears: "SKU"
[[15:07:15]] [SUCCESS] Screenshot refreshed
[[15:07:15]] [INFO] Refreshing screenshot...
[[15:07:15]] [INFO] kAQ1yIIw3h=pass
[[15:07:13]] [SUCCESS] Screenshot refreshed successfully
[[15:07:12]] [INFO] kAQ1yIIw3h=running
[[15:07:12]] [INFO] Executing action 55/68: Tap on element with xpath: ((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1] with fallback: Coordinates (98, 308)
[[15:07:12]] [SUCCESS] Screenshot refreshed
[[15:07:12]] [INFO] Refreshing screenshot...
[[15:07:12]] [INFO] lYPskZt0Ya=pass
[[15:07:04]] [SUCCESS] Screenshot refreshed successfully
[[15:07:03]] [INFO] lYPskZt0Ya=running
[[15:07:03]] [INFO] Executing action 54/68: Wait till xpath=//android.widget.Button[@text="Filter"]
[[15:07:03]] [SUCCESS] Screenshot refreshed
[[15:07:03]] [INFO] Refreshing screenshot...
[[15:07:03]] [INFO] oNKCP9pqiF=pass
[[15:07:02]] [SUCCESS] Screenshot refreshed successfully
[[15:06:57]] [INFO] oNKCP9pqiF=running
[[15:06:57]] [INFO] Executing action 53/68: Android Function: send_key_event - Key Event: ENTER
[[15:06:57]] [SUCCESS] Screenshot refreshed
[[15:06:57]] [INFO] Refreshing screenshot...
[[15:06:57]] [INFO] JRheDTvpJf=pass
[[15:06:56]] [SUCCESS] Screenshot refreshed successfully
[[15:06:55]] [INFO] JRheDTvpJf=running
[[15:06:55]] [INFO] Executing action 52/68: Input text: "mat"
[[15:06:55]] [SUCCESS] Screenshot refreshed
[[15:06:55]] [INFO] Refreshing screenshot...
[[15:06:55]] [INFO] o1gHFWHXTL=pass
[[15:06:52]] [SUCCESS] Screenshot refreshed successfully
[[15:06:51]] [INFO] o1gHFWHXTL=running
[[15:06:51]] [INFO] Executing action 51/68: Tap on Text: "Find"
[[15:06:51]] [SUCCESS] Screenshot refreshed
[[15:06:51]] [INFO] Refreshing screenshot...
[[15:06:51]] [INFO] o74txS2f4j=pass
[[15:06:43]] [SUCCESS] Screenshot refreshed successfully
[[15:06:42]] [INFO] o74txS2f4j=running
[[15:06:42]] [INFO] Executing action 50/68: Tap on image: find-products-browse.png
[[15:06:42]] [SUCCESS] Screenshot refreshed
[[15:06:42]] [INFO] Refreshing screenshot...
[[15:06:42]] [INFO] F4NGh9HrLw=pass
[[15:06:40]] [INFO] F4NGh9HrLw=running
[[15:06:40]] [INFO] Executing action 49/68: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 2 of 5")]
[[15:06:40]] [INFO] JRheDTvpJf=fail
[[15:06:40]] [ERROR] Action 48 failed: Text 'Add' not found within timeout (30s)
[[15:06:08]] [SUCCESS] Screenshot refreshed successfully
[[15:06:07]] [INFO] JRheDTvpJf=running
[[15:06:07]] [INFO] Executing action 48/68: Tap on Text: "Add"
[[15:06:07]] [SUCCESS] Screenshot refreshed
[[15:06:07]] [INFO] Refreshing screenshot...
[[15:06:07]] [INFO] eHLWiRoqqS=pass
[[15:06:04]] [SUCCESS] Screenshot refreshed successfully
[[15:06:04]] [INFO] eHLWiRoqqS=running
[[15:06:04]] [INFO] Executing action 47/68: Swipe from (50%, 70%) to (50%, 30%)
[[15:06:04]] [SUCCESS] Screenshot refreshed
[[15:06:04]] [INFO] Refreshing screenshot...
[[15:06:04]] [INFO] kAQ1yIIw3h=pass
[[15:06:01]] [SUCCESS] Screenshot refreshed successfully
[[15:06:00]] [INFO] kAQ1yIIw3h=running
[[15:06:00]] [INFO] Executing action 46/68: Tap on element with xpath: ((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1] with fallback: Coordinates (98, 308)
[[15:06:00]] [SUCCESS] Screenshot refreshed
[[15:06:00]] [INFO] Refreshing screenshot...
[[15:06:00]] [INFO] lYPskZt0Ya=pass
[[15:05:52]] [SUCCESS] Screenshot refreshed successfully
[[15:05:51]] [INFO] lYPskZt0Ya=running
[[15:05:51]] [INFO] Executing action 45/68: Wait till xpath=//android.widget.Button[@text="Filter"]
[[15:05:51]] [SUCCESS] Screenshot refreshed
[[15:05:51]] [INFO] Refreshing screenshot...
[[15:05:51]] [INFO] YaIypAHEOz=pass
[[15:05:50]] [SUCCESS] Screenshot refreshed successfully
[[15:05:49]] [INFO] YaIypAHEOz=running
[[15:05:49]] [INFO] Executing action 44/68: Android Function: send_key_event - Key Event: ENTER
[[15:05:49]] [SUCCESS] Screenshot refreshed
[[15:05:49]] [INFO] Refreshing screenshot...
[[15:05:49]] [INFO] JRheDTvpJf=pass
[[15:05:48]] [SUCCESS] Screenshot refreshed successfully
[[15:05:31]] [INFO] JRheDTvpJf=running
[[15:05:31]] [INFO] Executing action 43/68: Input text: "Kids Toys"
[[15:05:31]] [SUCCESS] Screenshot refreshed
[[15:05:31]] [INFO] Refreshing screenshot...
[[15:05:31]] [INFO] o1gHFWHXTL=pass
[[15:05:28]] [SUCCESS] Screenshot refreshed successfully
[[15:05:27]] [INFO] o1gHFWHXTL=running
[[15:05:27]] [INFO] Executing action 42/68: Tap on Text: "Find"
[[15:05:27]] [SUCCESS] Screenshot refreshed
[[15:05:27]] [INFO] Refreshing screenshot...
[[15:05:27]] [INFO] o74txS2f4j=pass
[[15:05:19]] [SUCCESS] Screenshot refreshed successfully
[[15:05:19]] [INFO] o74txS2f4j=running
[[15:05:19]] [INFO] Executing action 41/68: Tap on image: find-products-browse.png
[[15:05:19]] [SUCCESS] Screenshot refreshed
[[15:05:19]] [INFO] Refreshing screenshot...
[[15:05:19]] [INFO] F4NGh9HrLw=pass
[[15:05:14]] [SUCCESS] Screenshot refreshed successfully
[[15:05:13]] [INFO] F4NGh9HrLw=running
[[15:05:13]] [INFO] Executing action 40/68: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 2 of 5")]
[[15:05:13]] [SUCCESS] Screenshot refreshed
[[15:05:13]] [INFO] Refreshing screenshot...
[[15:05:13]] [INFO] XPEr3w6Zof=pass
[[15:05:12]] [SUCCESS] Screenshot refreshed successfully
[[15:05:10]] [INFO] XPEr3w6Zof=running
[[15:05:10]] [INFO] Executing action 39/68: Launch app: au.com.kmart
[[15:05:10]] [SUCCESS] Screenshot refreshed
[[15:05:10]] [INFO] Refreshing screenshot...
[[15:05:10]] [INFO] XPEr3w6Zof=pass
[[15:05:08]] [INFO] XPEr3w6Zof=running
[[15:05:08]] [INFO] Executing action 38/68: Terminate app: au.com.kmart
[[15:05:08]] [INFO] Et3kvnFdxh=fail
[[15:05:08]] [ERROR] Action 37 failed: Element not found or not tappable after all retry strategies: xpath='//android.view.View[@content-desc="stnPostCodeSelectionScreenBodyWidget"]/android.view.View[1]/android.widget.ImageView'
[[15:03:49]] [INFO] Et3kvnFdxh=running
[[15:03:49]] [INFO] Executing action 37/68: Tap on element with xpath: //android.view.View[@content-desc="stnPostCodeSelectionScreenBodyWidget"]/android.view.View[1]/android.widget.ImageView
[[15:03:49]] [INFO] GWoppouz1l=fail
[[15:03:49]] [ERROR] Action 36 failed: Element not found: xpath='//android.view.View[@content-desc="txtLocationTitle"]'
[[15:03:08]] [INFO] GWoppouz1l=running
[[15:03:08]] [INFO] Executing action 36/68: Check if element with xpath="//android.view.View[@content-desc="txtLocationTitle"]" exists
[[15:03:08]] [INFO] B6GDXWAmWp=fail
[[15:03:08]] [ERROR] Action 35 failed: Element not found or not tappable after all retry strategies: xpath='//android.widget.TextView[@text="Shop at"]/following-sibling::android.widget.Button'
[[15:01:09]] [SUCCESS] Screenshot refreshed successfully
[[15:01:08]] [INFO] B6GDXWAmWp=running
[[15:01:08]] [INFO] Executing action 35/68: Tap on element with xpath: //android.widget.TextView[@text="Shop at"]/following-sibling::android.widget.Button
[[15:01:08]] [SUCCESS] Screenshot refreshed
[[15:01:08]] [INFO] Refreshing screenshot...
[[15:01:08]] [INFO] eHLWiRoqqS=pass
[[15:01:06]] [INFO] eHLWiRoqqS=running
[[15:01:06]] [INFO] Executing action 34/68: Swipe from (50%, 70%) to (50%, 40%)
[[15:01:06]] [INFO] mtYqeDttRc=fail
[[15:01:06]] [ERROR] Action 33 failed: Element not found or not tappable after all retry strategies: xpath='//android.widget.Button[@resource-id="close-btn"]'
[[15:00:07]] [INFO] mtYqeDttRc=running
[[15:00:07]] [INFO] Executing action 33/68: Tap on element with xpath: //android.widget.Button[@resource-id="close-btn"]
[[15:00:07]] [INFO] P4b2BITpCf=fail
[[15:00:07]] [ERROR] Action 32 failed: Text not found: "interest-"
[[14:59:01]] [INFO] P4b2BITpCf=running
[[14:59:01]] [INFO] Executing action 32/68: Check if element with text="interest-" exists
[[14:59:01]] [INFO] q6cKxgMAIn=fail
[[14:59:01]] [ERROR] Action 31 failed: Element not found or not tappable after all retry strategies: xpath='//android.widget.Button[@text="Learn more about PayPal Pay in 4"]'
[[14:57:29]] [SUCCESS] Screenshot refreshed successfully
[[14:57:28]] [INFO] q6cKxgMAIn=running
[[14:57:28]] [INFO] Executing action 31/68: Tap on element with xpath: //android.widget.Button[@text="Learn more about PayPal Pay in 4"]
[[14:57:28]] [SUCCESS] Screenshot refreshed
[[14:57:28]] [INFO] Refreshing screenshot...
[[14:57:28]] [INFO] Et3kvnFdxh=pass
[[14:57:27]] [INFO] Et3kvnFdxh=running
[[14:57:27]] [INFO] Executing action 30/68: Android Function: send_key_event - Key Event: BACK
[[14:57:27]] [INFO] P4b2BITpCf=fail
[[14:57:27]] [ERROR] Action 29 failed: Text not found: "What"
[[14:57:00]] [INFO] P4b2BITpCf=running
[[14:57:00]] [INFO] Executing action 29/68: Check if element with text="What" exists
[[14:57:00]] [INFO] inrxgdWzXr=fail
[[14:57:00]] [ERROR] Action 28 failed: Element not found or not tappable after all retry strategies: xpath='//android.widget.TextView[@text="Learn moreabout Zip"]'
[[14:55:56]] [SUCCESS] Screenshot refreshed successfully
[[14:55:55]] [INFO] inrxgdWzXr=running
[[14:55:55]] [INFO] Executing action 28/68: Tap on element with xpath: //android.widget.TextView[@text="Learn moreabout Zip"]
[[14:55:55]] [SUCCESS] Screenshot refreshed
[[14:55:55]] [INFO] Refreshing screenshot...
[[14:55:55]] [INFO] Et3kvnFdxh=pass
[[14:55:54]] [INFO] Et3kvnFdxh=running
[[14:55:54]] [INFO] Executing action 27/68: Android Function: send_key_event - Key Event: BACK
[[14:55:54]] [INFO] DhWa2PCBXE=fail
[[14:55:54]] [ERROR] Action 26 failed: Text not found: "Apply"
[[14:55:00]] [INFO] DhWa2PCBXE=running
[[14:55:00]] [INFO] Executing action 26/68: Check if element with text="Apply" exists
[[14:55:00]] [INFO] pk2DLZFBmx=fail
[[14:55:00]] [ERROR] Action 25 failed: Element not found or not tappable after all retry strategies: xpath='//android.widget.TextView[@text="Learn moreabout AfterPay"]'
[[14:53:23]] [INFO] pk2DLZFBmx=running
[[14:53:23]] [INFO] Executing action 25/68: Tap on element with xpath: //android.widget.TextView[@text="Learn moreabout AfterPay"]
[[14:53:23]] [INFO] ShJSdXvmVL=fail
[[14:53:23]] [ERROR] Action 24 failed: Element not visible after 3 swipe(s)
[[14:53:10]] [SUCCESS] Screenshot refreshed successfully
[[14:53:10]] [INFO] ShJSdXvmVL=running
[[14:53:10]] [INFO] Executing action 24/68: Swipe up till element xpath: "//android.widget.TextView[@text="Learn moreabout AfterPay"]" is visible
[[14:53:10]] [SUCCESS] Screenshot refreshed
[[14:53:10]] [INFO] Refreshing screenshot...
[[14:53:10]] [INFO] y5FboDiRLS=pass
[[14:53:01]] [SUCCESS] Screenshot refreshed successfully
[[14:53:00]] [INFO] y5FboDiRLS=running
[[14:53:00]] [INFO] Executing action 23/68: Tap on image: share-close.png
[[14:53:00]] [SUCCESS] Screenshot refreshed
[[14:53:00]] [INFO] Refreshing screenshot...
[[14:53:00]] [INFO] EEx673tuI0=pass
[[14:52:57]] [SUCCESS] Screenshot refreshed successfully
[[14:52:57]] [INFO] EEx673tuI0=running
[[14:52:57]] [INFO] Executing action 22/68: Check if element with text="Share" exists
[[14:52:57]] [SUCCESS] Screenshot refreshed
[[14:52:57]] [INFO] Refreshing screenshot...
[[14:52:57]] [INFO] dCqKBG3e7u=pass
[[14:52:55]] [SUCCESS] Screenshot refreshed successfully
[[14:52:54]] [INFO] dCqKBG3e7u=running
[[14:52:54]] [INFO] Executing action 21/68: Tap on element with xpath: //android.view.View[@content-desc="Product Details"]/following-sibling::android.widget.ImageView[1]
[[14:52:54]] [SUCCESS] Screenshot refreshed
[[14:52:54]] [INFO] Refreshing screenshot...
[[14:52:54]] [INFO] kAQ1yIIw3h=pass
[[14:52:53]] [SUCCESS] Screenshot refreshed successfully
[[14:52:51]] [INFO] kAQ1yIIw3h=running
[[14:52:51]] [INFO] Executing action 20/68: Tap on element with xpath: ((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1] with fallback: Coordinates (98, 308)
[[14:52:51]] [SUCCESS] Screenshot refreshed
[[14:52:51]] [INFO] Refreshing screenshot...
[[14:52:51]] [INFO] OmKfD9iBjD=pass
[[14:52:49]] [SUCCESS] Screenshot refreshed successfully
[[14:52:48]] [INFO] OmKfD9iBjD=running
[[14:52:48]] [INFO] Executing action 19/68: Wait till xpath=((//android.view.View[contains(@content-desc,"to bag")])[1]/android.view.View/android.widget.TextView[1])[1]
[[14:52:48]] [SUCCESS] Screenshot refreshed
[[14:52:48]] [INFO] Refreshing screenshot...
[[14:52:48]] [INFO] dMl1PH9Dlc=pass
[[14:51:57]] [SUCCESS] Screenshot refreshed successfully
[[14:51:57]] [INFO] dMl1PH9Dlc=running
[[14:51:57]] [INFO] Executing action 18/68: Wait for 10 ms
[[14:51:57]] [SUCCESS] Screenshot refreshed
[[14:51:57]] [INFO] Refreshing screenshot...
[[14:51:57]] [INFO] eHLWiRoqqS=pass
[[14:51:54]] [SUCCESS] Screenshot refreshed successfully
[[14:51:53]] [INFO] eHLWiRoqqS=running
[[14:51:53]] [INFO] Executing action 17/68: Swipe from (50%, 70%) to (50%, 30%)
[[14:51:53]] [SUCCESS] Screenshot refreshed
[[14:51:53]] [INFO] Refreshing screenshot...
[[14:51:53]] [INFO] huUnpMMjVR=pass
[[14:51:51]] [SUCCESS] Screenshot refreshed successfully
[[14:51:50]] [INFO] huUnpMMjVR=running
[[14:51:50]] [INFO] Executing action 16/68: Tap on element with xpath: //android.widget.Button[@text="In stock only i... ChipsClose"]
[[14:51:50]] [SUCCESS] Screenshot refreshed
[[14:51:50]] [INFO] Refreshing screenshot...
[[14:51:50]] [INFO] XmAxcBtFI0=pass
[[14:51:47]] [SUCCESS] Screenshot refreshed successfully
[[14:51:19]] [INFO] XmAxcBtFI0=running
[[14:51:19]] [INFO] Executing action 15/68: Check if element with xpath="//android.widget.Button[@text="In stock only i... ChipsClose"]" exists
[[14:51:19]] [SUCCESS] Screenshot refreshed
[[14:51:19]] [INFO] Refreshing screenshot...
[[14:51:19]] [INFO] a50JhCx0ir=pass
[[14:51:16]] [SUCCESS] Screenshot refreshed successfully
[[14:51:15]] [INFO] a50JhCx0ir=running
[[14:51:15]] [INFO] Executing action 14/68: Tap on Text: "Show"
[[14:51:15]] [SUCCESS] Screenshot refreshed
[[14:51:15]] [INFO] Refreshing screenshot...
[[14:51:15]] [INFO] dMl1PH9Dlc=pass
[[14:51:10]] [SUCCESS] Screenshot refreshed successfully
[[14:51:09]] [INFO] dMl1PH9Dlc=running
[[14:51:09]] [INFO] Executing action 13/68: Wait for 5 ms
[[14:51:09]] [SUCCESS] Screenshot refreshed
[[14:51:09]] [INFO] Refreshing screenshot...
[[14:51:09]] [INFO] a50JhCx0ir=pass
[[14:51:06]] [SUCCESS] Screenshot refreshed successfully
[[14:51:05]] [INFO] a50JhCx0ir=running
[[14:51:05]] [INFO] Executing action 12/68: Tap on Text: "only"
[[14:51:05]] [SUCCESS] Screenshot refreshed
[[14:51:05]] [INFO] Refreshing screenshot...
[[14:51:05]] [INFO] Y1O1clhMSJ=pass
[[14:51:03]] [SUCCESS] Screenshot refreshed successfully
[[14:51:03]] [INFO] Y1O1clhMSJ=running
[[14:51:03]] [INFO] Executing action 11/68: Tap on element with xpath: //android.widget.Button[@text="Filter"]
[[14:51:03]] [SUCCESS] Screenshot refreshed
[[14:51:03]] [INFO] Refreshing screenshot...
[[14:51:03]] [INFO] lYPskZt0Ya=pass
[[14:50:54]] [SUCCESS] Screenshot refreshed successfully
[[14:50:53]] [INFO] lYPskZt0Ya=running
[[14:50:53]] [INFO] Executing action 10/68: Wait till xpath=//android.widget.Button[@text="Filter"]
[[14:50:53]] [SUCCESS] Screenshot refreshed
[[14:50:53]] [INFO] Refreshing screenshot...
[[14:50:53]] [INFO] xUbWFa8Ok2=pass
[[14:50:50]] [SUCCESS] Screenshot refreshed successfully
[[14:50:49]] [INFO] xUbWFa8Ok2=running
[[14:50:49]] [INFO] Executing action 9/68: Tap on Text: "Latest"
[[14:50:49]] [SUCCESS] Screenshot refreshed
[[14:50:49]] [INFO] Refreshing screenshot...
[[14:50:49]] [INFO] RbNtEW6N9T=pass
[[14:50:26]] [SUCCESS] Screenshot refreshed successfully
[[14:50:25]] [INFO] RbNtEW6N9T=running
[[14:50:25]] [INFO] Executing action 8/68: Tap on Text: "Toys"
[[14:50:25]] [SUCCESS] Screenshot refreshed
[[14:50:25]] [INFO] Refreshing screenshot...
[[14:50:25]] [INFO] ltDXyWvtEz=pass
[[14:50:23]] [SUCCESS] Screenshot refreshed successfully
[[14:50:23]] [INFO] ltDXyWvtEz=running
[[14:50:23]] [INFO] Executing action 7/68: Tap on element with xpath: //android.view.View[@content-desc="Search"]/preceding::android.widget.ImageView[1]
[[14:50:23]] [SUCCESS] Screenshot refreshed
[[14:50:23]] [INFO] Refreshing screenshot...
[[14:50:23]] [INFO] QPKR6jUF9O=pass
[[14:50:22]] [SUCCESS] Screenshot refreshed successfully
[[14:50:21]] [INFO] QPKR6jUF9O=running
[[14:50:21]] [INFO] Executing action 6/68: Check if element with xpath="//android.widget.ImageView[@content-desc="Scan barcode"]" exists
[[14:50:21]] [SUCCESS] Screenshot refreshed
[[14:50:21]] [INFO] Refreshing screenshot...
[[14:50:21]] [INFO] vfwUVEyq6X=pass
[[14:50:20]] [SUCCESS] Screenshot refreshed successfully
[[14:50:19]] [INFO] vfwUVEyq6X=running
[[14:50:19]] [INFO] Executing action 5/68: Check if element with xpath="//android.widget.ImageView[@content-desc="More"]" exists
[[14:50:19]] [SUCCESS] Screenshot refreshed
[[14:50:19]] [INFO] Refreshing screenshot...
[[14:50:19]] [INFO] o74txS2f4j=pass
[[14:50:11]] [SUCCESS] Screenshot refreshed successfully
[[14:50:10]] [INFO] o74txS2f4j=running
[[14:50:10]] [INFO] Executing action 4/68: Tap on image: find-products-browse.png
[[14:50:10]] [SUCCESS] Screenshot refreshed
[[14:50:10]] [INFO] Refreshing screenshot...
[[14:50:10]] [INFO] F4NGh9HrLw=pass
[[14:50:08]] [SUCCESS] Screenshot refreshed successfully
[[14:50:07]] [INFO] F4NGh9HrLw=running
[[14:50:07]] [INFO] Executing action 3/68: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 2 of 5")]
[[14:50:07]] [SUCCESS] Screenshot refreshed
[[14:50:07]] [INFO] Refreshing screenshot...
[[14:50:07]] [INFO] H9fy9qcFbZ=pass
[[14:50:06]] [SUCCESS] Screenshot refreshed successfully
[[14:50:05]] [INFO] H9fy9qcFbZ=running
[[14:50:05]] [INFO] Executing action 2/68: Launch app: au.com.kmart
[[14:50:05]] [SUCCESS] Screenshot refreshed
[[14:50:05]] [INFO] Refreshing screenshot...
[[14:50:05]] [SUCCESS] Screenshot refreshed
[[14:50:05]] [INFO] Refreshing screenshot...
[[14:50:03]] [SUCCESS] Screenshot refreshed successfully
[[14:50:02]] [INFO] Executing Multi Step action step 6/6: Tap on element with accessibility_id: txtOnePassOnboardingSkipForNow
[[14:50:02]] [SUCCESS] Screenshot refreshed
[[14:50:02]] [INFO] Refreshing screenshot...
[[14:50:01]] [SUCCESS] Screenshot refreshed successfully
[[14:50:00]] [INFO] Executing Multi Step action step 5/6: Tap on element with accessibility_id: btnMayBeLater
[[14:50:00]] [SUCCESS] Screenshot refreshed
[[14:50:00]] [INFO] Refreshing screenshot...
[[14:49:58]] [SUCCESS] Screenshot refreshed successfully
[[14:49:57]] [INFO] Executing Multi Step action step 4/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[14:49:57]] [SUCCESS] Screenshot refreshed
[[14:49:57]] [INFO] Refreshing screenshot...
[[14:49:53]] [SUCCESS] Screenshot refreshed successfully
[[14:49:53]] [INFO] Executing Multi Step action step 3/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[14:49:53]] [SUCCESS] Screenshot refreshed
[[14:49:53]] [INFO] Refreshing screenshot...
[[14:49:51]] [SUCCESS] Screenshot refreshed successfully
[[14:49:49]] [INFO] Executing Multi Step action step 2/6: Launch app: au.com.kmart
[[14:49:49]] [SUCCESS] Screenshot refreshed
[[14:49:49]] [INFO] Refreshing screenshot...
[[14:49:46]] [INFO] Executing Multi Step action step 1/6: Android Function: clear_app - Package: au.com.kmart
[[14:49:46]] [INFO] Loaded 6 steps from test case: Onboarding-Start-AU
[[14:49:46]] [INFO] Loading steps for Multi Step action: Onboarding-Start-AU
[[14:49:46]] [INFO] H9fy9qcFbZ=running
[[14:49:46]] [INFO] Executing action 1/68: Execute Test Case: Onboarding-Start-AU (6 steps)
[[14:49:46]] [INFO] ExecutionManager: Starting execution of 68 actions...
[[14:49:46]] [SUCCESS] Cleared 0 screenshots from database
[[14:49:46]] [INFO] Clearing screenshots from database before execution...
[[14:49:46]] [SUCCESS] All screenshots deleted successfully
[[14:49:46]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[14:49:46]] [INFO] Skipping report initialization - single test case execution
[[14:49:43]] [SUCCESS] All screenshots deleted successfully
[[14:49:43]] [SUCCESS] Loaded test case "Browse & PDP_AU_ANDROID" with 68 actions
[[14:49:43]] [SUCCESS] Added action: terminateApp
[[14:49:43]] [SUCCESS] Added action: wait
[[14:49:43]] [SUCCESS] Added action: tap
[[14:49:43]] [SUCCESS] Added action: wait
[[14:49:43]] [SUCCESS] Added action: tap
[[14:49:43]] [SUCCESS] Added action: swipe
[[14:49:43]] [SUCCESS] Added action: tap
[[14:49:43]] [SUCCESS] Added action: waitTill
[[14:49:43]] [SUCCESS] Added action: tapIfLocatorExists
[[14:49:43]] [SUCCESS] Added action: tap
[[14:49:43]] [SUCCESS] Added action: tapOnText
[[14:49:43]] [SUCCESS] Added action: swipe
[[14:49:43]] [SUCCESS] Added action: waitTill
[[14:49:43]] [SUCCESS] Added action: tap
[[14:49:43]] [SUCCESS] Added action: waitTill
[[14:49:43]] [SUCCESS] Added action: androidFunctions
[[14:49:43]] [SUCCESS] Added action: text
[[14:49:43]] [SUCCESS] Added action: tapOnText
[[14:49:43]] [SUCCESS] Added action: tap
[[14:49:43]] [SUCCESS] Added action: tap
[[14:49:43]] [SUCCESS] Added action: tapOnText
[[14:49:43]] [SUCCESS] Added action: swipe
[[14:49:43]] [SUCCESS] Added action: tap
[[14:49:43]] [SUCCESS] Added action: waitTill
[[14:49:43]] [SUCCESS] Added action: androidFunctions
[[14:49:43]] [SUCCESS] Added action: text
[[14:49:43]] [SUCCESS] Added action: tapOnText
[[14:49:43]] [SUCCESS] Added action: tap
[[14:49:43]] [SUCCESS] Added action: tap
[[14:49:43]] [SUCCESS] Added action: launchApp
[[14:49:43]] [SUCCESS] Added action: terminateApp
[[14:49:43]] [SUCCESS] Added action: tap
[[14:49:43]] [SUCCESS] Added action: exists
[[14:49:43]] [SUCCESS] Added action: tap
[[14:49:43]] [SUCCESS] Added action: swipe
[[14:49:43]] [SUCCESS] Added action: tap
[[14:49:43]] [SUCCESS] Added action: exists
[[14:49:43]] [SUCCESS] Added action: tap
[[14:49:43]] [SUCCESS] Added action: androidFunctions
[[14:49:43]] [SUCCESS] Added action: exists
[[14:49:43]] [SUCCESS] Added action: tap
[[14:49:43]] [SUCCESS] Added action: androidFunctions
[[14:49:43]] [SUCCESS] Added action: exists
[[14:49:43]] [SUCCESS] Added action: tap
[[14:49:43]] [SUCCESS] Added action: swipeTillVisible
[[14:49:43]] [SUCCESS] Added action: tap
[[14:49:43]] [SUCCESS] Added action: exists
[[14:49:43]] [SUCCESS] Added action: tap
[[14:49:43]] [SUCCESS] Added action: tap
[[14:49:43]] [SUCCESS] Added action: waitTill
[[14:49:43]] [SUCCESS] Added action: wait
[[14:49:43]] [SUCCESS] Added action: swipe
[[14:49:43]] [SUCCESS] Added action: tap
[[14:49:43]] [SUCCESS] Added action: exists
[[14:49:43]] [SUCCESS] Added action: tapOnText
[[14:49:43]] [SUCCESS] Added action: wait
[[14:49:43]] [SUCCESS] Added action: tapOnText
[[14:49:43]] [SUCCESS] Added action: tap
[[14:49:43]] [SUCCESS] Added action: waitTill
[[14:49:43]] [SUCCESS] Added action: tapOnText
[[14:49:43]] [SUCCESS] Added action: tapOnText
[[14:49:43]] [SUCCESS] Added action: tap
[[14:49:43]] [SUCCESS] Added action: exists
[[14:49:43]] [SUCCESS] Added action: exists
[[14:49:43]] [SUCCESS] Added action: tap
[[14:49:43]] [SUCCESS] Added action: tap
[[14:49:43]] [SUCCESS] Added action: launchApp
[[14:49:43]] [SUCCESS] Added action: multiStep
[[14:49:43]] [INFO] All actions cleared
[[14:49:43]] [INFO] Cleaning up screenshots...
[[14:42:07]] [SUCCESS] Screenshot refreshed successfully
[[14:42:06]] [INFO] RlDZFks4Lc=pass
[[14:42:06]] [SUCCESS] Action executed: Action executed successfully: tap
[[14:42:06]] [SUCCESS] Screenshot refreshed
[[14:42:06]] [INFO] Refreshing screenshot...
[[14:42:05]] [INFO] Executing action: Tap on element with xpath: //android.widget.Button[contains(@text,"While using the app")]
[[14:42:04]] [SUCCESS] Screenshot refreshed successfully
[[14:42:04]] [INFO] jmKjclMUWT=pass
[[14:42:04]] [SUCCESS] Action executed: Action executed successfully: tapOnText
[[14:42:04]] [SUCCESS] Screenshot refreshed
[[14:42:04]] [INFO] Refreshing screenshot...
[[14:42:00]] [SUCCESS] Screenshot refreshed successfully
[[14:41:52]] [INFO] Executing action: Tap on Text: "current"
[[14:41:51]] [INFO] UoH0wdtcLk=pass
[[14:41:51]] [SUCCESS] Action executed: Action executed successfully: tapOnText
[[14:41:51]] [SUCCESS] Screenshot refreshed
[[14:41:51]] [INFO] Refreshing screenshot...
[[14:41:47]] [INFO] Executing action: Tap on Text: "Edit"
[[14:40:04]] [SUCCESS] Screenshot refreshed successfully
[[14:40:03]] [INFO] Skipping report generation - individual test case execution (not from test suite)
[[14:40:03]] [SUCCESS] Action logs saved successfully
[[14:40:03]] [ERROR] Execution failed but report was generated.
[[14:40:03]] [INFO] Saving 586 action log entries to file...
[[14:40:03]] [INFO] Generating execution report...
[[14:40:03]] [SUCCESS] All tests passed successfully!
[[14:40:03]] [SUCCESS] Screenshot refreshed
[[14:40:03]] [INFO] Refreshing screenshot...
[[14:40:03]] [INFO] XjclKOaCTh=pass
[[14:40:02]] [SUCCESS] Screenshot refreshed successfully
[[14:40:01]] [INFO] XjclKOaCTh=running
[[14:40:01]] [INFO] Executing action 37/37: Terminate app: au.com.kmart
[[14:40:01]] [SUCCESS] Screenshot refreshed
[[14:40:01]] [INFO] Refreshing screenshot...
[[14:40:01]] [INFO] 2p13JoJbbA=pass
[[14:39:58]] [SUCCESS] Screenshot refreshed successfully
[[14:39:57]] [INFO] 2p13JoJbbA=running
[[14:39:57]] [INFO] Executing action 36/37: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[14:39:57]] [SUCCESS] Screenshot refreshed
[[14:39:57]] [INFO] Refreshing screenshot...
[[14:39:57]] [INFO] qHdMgerbTE=pass
[[14:39:53]] [SUCCESS] Screenshot refreshed successfully
[[14:39:51]] [INFO] qHdMgerbTE=running
[[14:39:51]] [INFO] Executing action 35/37: Swipe from (50%, 70%) to (50%, 30%)
[[14:39:51]] [SUCCESS] Screenshot refreshed
[[14:39:51]] [INFO] Refreshing screenshot...
[[14:39:51]] [INFO] F4NGh9HrLw=pass
[[14:39:50]] [SUCCESS] Screenshot refreshed successfully
[[14:39:49]] [INFO] F4NGh9HrLw=running
[[14:39:49]] [INFO] Executing action 34/37: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[14:39:49]] [SUCCESS] Screenshot refreshed
[[14:39:49]] [INFO] Refreshing screenshot...
[[14:38:53]] [INFO] Executing action 33/37: Tap if locator exists: xpath="//android.widget.Button[@content-desc="btnSaveOrContinue"]"
[[14:38:53]] [INFO] RlDZFks4Lc=fail
[[14:38:53]] [ERROR] Action 32 failed: Element not found or not tappable after all retry strategies: xpath='//android.widget.Button[contains(@text,"While using the app")]'
[[14:37:02]] [INFO] RlDZFks4Lc=running
[[14:37:02]] [INFO] Executing action 32/37: Tap on element with xpath: //android.widget.Button[contains(@text,"While using the app")]
[[14:37:02]] [INFO] jmKjclMUWT=fail
[[14:37:02]] [ERROR] Action 31 failed: Text 'current' not found within timeout (30s)
[[14:36:28]] [INFO] jmKjclMUWT=running
[[14:36:28]] [INFO] Executing action 31/37: Tap on Text: "current"
[[14:36:28]] [INFO] UoH0wdtcLk=fail
[[14:36:28]] [ERROR] Action 30 failed: Text 'Edit' not found within timeout (30s)
[[14:35:52]] [SUCCESS] Screenshot refreshed successfully
[[14:35:52]] [INFO] UoH0wdtcLk=running
[[14:35:52]] [INFO] Executing action 30/37: Tap on Text: "Edit"
[[14:35:52]] [SUCCESS] Screenshot refreshed
[[14:35:52]] [INFO] Refreshing screenshot...
[[14:35:52]] [INFO] U48qCNydwd=pass
[[14:35:50]] [SUCCESS] Screenshot refreshed successfully
[[14:35:50]] [INFO] U48qCNydwd=running
[[14:35:50]] [INFO] Executing action 29/37: Launch app: au.com.kmart
[[14:35:50]] [SUCCESS] Screenshot refreshed
[[14:35:50]] [INFO] Refreshing screenshot...
[[14:35:50]] [INFO] XjclKOaCTh=pass
[[14:35:48]] [SUCCESS] Screenshot refreshed successfully
[[14:35:48]] [INFO] XjclKOaCTh=running
[[14:35:48]] [INFO] Executing action 28/37: Terminate app: au.com.kmart
[[14:35:48]] [SUCCESS] Screenshot refreshed
[[14:35:48]] [INFO] Refreshing screenshot...
[[14:35:48]] [INFO] q6cKxgMAIn=pass
[[14:35:46]] [SUCCESS] Screenshot refreshed successfully
[[14:35:45]] [INFO] q6cKxgMAIn=running
[[14:35:45]] [INFO] Executing action 27/37: Check if element with xpath="//android.webkit.WebView[@text="Slyp Receipt"]" exists
[[14:35:45]] [SUCCESS] Screenshot refreshed
[[14:35:45]] [INFO] Refreshing screenshot...
[[14:35:45]] [INFO] zdh8hKYC1a=pass
[[14:35:43]] [SUCCESS] Screenshot refreshed successfully
[[14:35:43]] [INFO] zdh8hKYC1a=running
[[14:35:43]] [INFO] Executing action 26/37: Tap on element with xpath: (//android.widget.GridView/android.view.View/android.view.View[2])[1]
[[14:35:43]] [SUCCESS] Screenshot refreshed
[[14:35:43]] [INFO] Refreshing screenshot...
[[14:35:43]] [INFO] P4b2BITpCf=pass
[[14:35:40]] [SUCCESS] Screenshot refreshed successfully
[[14:35:39]] [INFO] P4b2BITpCf=running
[[14:35:39]] [INFO] Executing action 25/37: Check if element with xpath="(//android.widget.GridView/android.view.View/android.view.View[2])[1]" exists
[[14:35:39]] [SUCCESS] Screenshot refreshed
[[14:35:39]] [INFO] Refreshing screenshot...
[[14:35:39]] [INFO] inrxgdWzXr=pass
[[14:35:09]] [SUCCESS] Screenshot refreshed successfully
[[14:35:08]] [INFO] inrxgdWzXr=running
[[14:35:08]] [INFO] Executing action 24/37: Tap on Text: "Store"
[[14:35:08]] [SUCCESS] Screenshot refreshed
[[14:35:08]] [INFO] Refreshing screenshot...
[[14:35:06]] [SUCCESS] Screenshot refreshed successfully
[[14:35:04]] [INFO] Executing action 23/37: Tap on element with xpath: //android.widget.Button[@content-desc="txtMy orders & receipts"]
[[14:35:04]] [SUCCESS] Screenshot refreshed
[[14:35:04]] [INFO] Refreshing screenshot...
[[14:35:04]] [INFO] GEMv6goQtW=pass
[[14:34:54]] [SUCCESS] Screenshot refreshed successfully
[[14:34:53]] [INFO] GEMv6goQtW=running
[[14:34:53]] [INFO] Executing action 22/37: Tap on image: android-app-back.png
[[14:34:53]] [SUCCESS] Screenshot refreshed
[[14:34:53]] [INFO] Refreshing screenshot...
[[14:34:53]] [INFO] DhWa2PCBXE=pass
[[14:34:51]] [SUCCESS] Screenshot refreshed successfully
[[14:34:51]] [INFO] DhWa2PCBXE=running
[[14:34:51]] [INFO] Executing action 21/37: Check if element with xpath="//android.view.View[@content-desc="txtOnePassSubscritionBox"]" exists
[[14:34:51]] [SUCCESS] Screenshot refreshed
[[14:34:51]] [INFO] Refreshing screenshot...
[[14:34:51]] [INFO] pk2DLZFBmx=pass
[[14:34:49]] [INFO] pk2DLZFBmx=running
[[14:34:49]] [INFO] Executing action 20/37: Tap on element with xpath: //android.widget.Button[@content-desc="txtMy OnePass Account"]
[[14:34:49]] [INFO] ShJSdXvmVL=fail
[[14:34:49]] [ERROR] Action 19 failed: Element not found: xpath='//android.widget.Button[@content-desc="txtMy OnePass Account"]'
[[14:34:07]] [SUCCESS] Screenshot refreshed successfully
[[14:34:06]] [INFO] ShJSdXvmVL=running
[[14:34:06]] [INFO] Executing action 19/37: Check if element with xpath="//android.widget.Button[@content-desc="txtMy OnePass Account"]" exists
[[14:34:06]] [SUCCESS] Screenshot refreshed
[[14:34:06]] [INFO] Refreshing screenshot...
[[14:34:06]] [SUCCESS] Screenshot refreshed
[[14:34:06]] [INFO] Refreshing screenshot...
[[14:34:05]] [SUCCESS] Screenshot refreshed successfully
[[14:34:04]] [INFO] Executing Multi Step action step 7/7: Tap on element with xpath: //android.widget.Button[@text="Sign in"]
[[14:34:04]] [SUCCESS] Screenshot refreshed
[[14:34:04]] [INFO] Refreshing screenshot...
[[14:34:03]] [SUCCESS] Screenshot refreshed successfully
[[14:34:02]] [INFO] Executing Multi Step action step 6/7: Input text: "Wonderbaby@6"
[[14:34:02]] [SUCCESS] Screenshot refreshed
[[14:34:02]] [INFO] Refreshing screenshot...
[[14:34:00]] [SUCCESS] Screenshot refreshed successfully
[[14:33:57]] [INFO] Executing Multi Step action step 5/7: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[14:33:57]] [SUCCESS] Screenshot refreshed
[[14:33:57]] [INFO] Refreshing screenshot...
[[14:33:54]] [SUCCESS] Screenshot refreshed successfully
[[14:33:54]] [INFO] Executing Multi Step action step 4/7: Tap on element with xpath: //android.widget.Button[@text="Continue to password"]
[[14:33:54]] [SUCCESS] Screenshot refreshed
[[14:33:54]] [INFO] Refreshing screenshot...
[[14:33:52]] [SUCCESS] Screenshot refreshed successfully
[[14:33:51]] [INFO] Executing Multi Step action step 3/7: Input text: "<EMAIL>"
[[14:33:51]] [SUCCESS] Screenshot refreshed
[[14:33:51]] [INFO] Refreshing screenshot...
[[14:33:50]] [SUCCESS] Screenshot refreshed successfully
[[14:33:49]] [INFO] Executing Multi Step action step 2/7: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[14:33:49]] [SUCCESS] Screenshot refreshed
[[14:33:49]] [INFO] Refreshing screenshot...
[[14:33:48]] [SUCCESS] Screenshot refreshed successfully
[[14:33:11]] [INFO] Executing Multi Step action step 1/7: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[14:33:11]] [INFO] Loaded 7 steps from test case: Kmart-Signin-AU-ANDROID
[[14:33:11]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[14:33:11]] [INFO] s0WyiD1w0B=running
[[14:33:11]] [INFO] Executing action 18/37: Execute Test Case: Kmart-Signin-AU-ANDROID (7 steps)
[[14:33:11]] [SUCCESS] Screenshot refreshed
[[14:33:11]] [INFO] Refreshing screenshot...
[[14:33:11]] [INFO] gekNSY5O2E=pass
[[14:33:08]] [SUCCESS] Screenshot refreshed successfully
[[14:33:07]] [INFO] gekNSY5O2E=running
[[14:33:07]] [INFO] Executing action 17/37: Tap on element with xpath: //android.widget.Button[@content-desc="txtMoreAccountCtaSignIn"]
[[14:33:07]] [SUCCESS] Screenshot refreshed
[[14:33:07]] [INFO] Refreshing screenshot...
[[14:33:07]] [INFO] GEMv6goQtW=pass
[[14:33:01]] [SUCCESS] Screenshot refreshed successfully
[[14:32:57]] [INFO] GEMv6goQtW=running
[[14:32:57]] [INFO] Executing action 16/37: Tap on image: android-app-back.png
[[14:32:57]] [SUCCESS] Screenshot refreshed
[[14:32:57]] [INFO] Refreshing screenshot...
[[14:32:57]] [INFO] 83tV9A4NOn=pass
[[14:32:54]] [SUCCESS] Screenshot refreshed successfully
[[14:32:53]] [INFO] 83tV9A4NOn=running
[[14:32:53]] [INFO] Executing action 15/37: Check if element with xpath="//android.view.View[@text="Order ********* is refunded"]" exists
[[14:32:53]] [SUCCESS] Screenshot refreshed
[[14:32:53]] [INFO] Refreshing screenshot...
[[14:32:53]] [INFO] aNN0yYFLEd=pass
[[14:32:52]] [SUCCESS] Screenshot refreshed successfully
[[14:32:51]] [INFO] aNN0yYFLEd=running
[[14:32:51]] [INFO] Executing action 14/37: Tap on element with xpath: //android.widget.Button[@text="Search for order"]
[[14:32:51]] [SUCCESS] Screenshot refreshed
[[14:32:51]] [INFO] Refreshing screenshot...
[[14:32:51]] [INFO] XJv08Gkucs=pass
[[14:32:48]] [SUCCESS] Screenshot refreshed successfully
[[14:32:22]] [INFO] XJv08Gkucs=running
[[14:32:22]] [INFO] Executing action 13/37: Input text: "<EMAIL>"
[[14:32:22]] [SUCCESS] Screenshot refreshed
[[14:32:22]] [INFO] Refreshing screenshot...
[[14:32:22]] [INFO] aNN0yYFLEd=pass
[[14:32:20]] [SUCCESS] Screenshot refreshed successfully
[[14:32:19]] [INFO] aNN0yYFLEd=running
[[14:32:19]] [INFO] Executing action 12/37: Tap on element with xpath: //android.widget.EditText[@resource-id="email"]
[[14:32:19]] [SUCCESS] Screenshot refreshed
[[14:32:19]] [INFO] Refreshing screenshot...
[[14:32:19]] [INFO] 7YbjwQH1Jc=pass
[[14:32:17]] [SUCCESS] Screenshot refreshed successfully
[[14:32:16]] [INFO] 7YbjwQH1Jc=running
[[14:32:16]] [INFO] Executing action 11/37: Input text: "*********"
[[14:32:16]] [SUCCESS] Screenshot refreshed
[[14:32:16]] [INFO] Refreshing screenshot...
[[14:32:16]] [INFO] OmKfD9iBjD=pass
[[14:32:11]] [SUCCESS] Screenshot refreshed successfully
[[14:32:10]] [INFO] OmKfD9iBjD=running
[[14:32:10]] [INFO] Executing action 10/37: Tap on element with xpath: //android.widget.EditText[@resource-id="orderId"]
[[14:32:10]] [SUCCESS] Screenshot refreshed
[[14:32:10]] [INFO] Refreshing screenshot...
[[14:32:10]] [INFO] eHLWiRoqqS=pass
[[14:32:08]] [SUCCESS] Screenshot refreshed successfully
[[14:32:07]] [INFO] eHLWiRoqqS=running
[[14:32:07]] [INFO] Executing action 9/37: Tap on element with xpath: //android.widget.Button[@content-desc="txtTrack My Order"]
[[14:32:07]] [SUCCESS] Screenshot refreshed
[[14:32:07]] [INFO] Refreshing screenshot...
[[14:32:07]] [INFO] F4NGh9HrLw=pass
[[14:32:06]] [SUCCESS] Screenshot refreshed successfully
[[14:32:05]] [INFO] F4NGh9HrLw=running
[[14:32:05]] [INFO] Executing action 8/37: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[14:32:05]] [SUCCESS] Screenshot refreshed
[[14:32:05]] [INFO] Refreshing screenshot...
[[14:32:05]] [INFO] 74XW7x54ad=pass
[[14:32:03]] [SUCCESS] Screenshot refreshed successfully
[[14:32:02]] [INFO] 74XW7x54ad=running
[[14:32:02]] [INFO] Executing action 7/37: Tap on element with xpath: //android.widget.ImageView[@content-desc="imgBackArrow"]
[[14:32:02]] [SUCCESS] Screenshot refreshed
[[14:32:02]] [INFO] Refreshing screenshot...
[[14:32:02]] [INFO] xUbWFa8Ok2=pass
[[14:32:01]] [SUCCESS] Screenshot refreshed successfully
[[14:32:00]] [INFO] xUbWFa8Ok2=running
[[14:32:00]] [INFO] Executing action 6/37: Check if element with xpath="//android.view.View[contains(@content-desc,"rectangle frame to view helpful product information")]" exists
[[14:32:00]] [SUCCESS] Screenshot refreshed
[[14:32:00]] [INFO] Refreshing screenshot...
[[14:32:00]] [INFO] RbNtEW6N9T=pass
[[14:31:59]] [SUCCESS] Screenshot refreshed successfully
[[14:31:58]] [INFO] RbNtEW6N9T=running
[[14:31:58]] [INFO] Executing action 5/37: Check if element with xpath="//android.widget.ImageView[@content-desc="imgHelp"]" exists
[[14:31:58]] [SUCCESS] Screenshot refreshed
[[14:31:58]] [INFO] Refreshing screenshot...
[[14:31:58]] [INFO] F4NGh9HrLw=pass
[[14:31:57]] [SUCCESS] Screenshot refreshed successfully
[[14:31:55]] [INFO] F4NGh9HrLw=running
[[14:31:55]] [INFO] Executing action 4/37: Check if element with xpath="//android.view.View[@content-desc="Barcode Scanner"]" exists
[[14:31:55]] [SUCCESS] Screenshot refreshed
[[14:31:55]] [INFO] Refreshing screenshot...
[[14:31:55]] [INFO] RlDZFks4Lc=pass
[[14:31:54]] [SUCCESS] Screenshot refreshed successfully
[[14:31:54]] [INFO] RlDZFks4Lc=running
[[14:31:54]] [INFO] Executing action 3/37: Tap on element with xpath: //android.widget.Button[contains(@text,"While using the app")]
[[14:31:54]] [SUCCESS] Screenshot refreshed
[[14:31:54]] [INFO] Refreshing screenshot...
[[14:31:54]] [INFO] Dzn2Q7JTe0=pass
[[14:31:52]] [SUCCESS] Screenshot refreshed successfully
[[14:31:51]] [INFO] Dzn2Q7JTe0=running
[[14:31:51]] [INFO] Executing action 2/37: Tap on element with xpath: //android.widget.Button[@content-desc="btnBarcodeScanner"]
[[14:31:51]] [SUCCESS] Screenshot refreshed
[[14:31:51]] [INFO] Refreshing screenshot...
[[14:31:51]] [SUCCESS] Screenshot refreshed
[[14:31:51]] [INFO] Refreshing screenshot...
[[14:31:46]] [SUCCESS] Screenshot refreshed successfully
[[14:31:46]] [INFO] Executing Multi Step action step 6/6: Tap on element with accessibility_id: txtOnePassOnboardingSkipForNow
[[14:31:46]] [SUCCESS] Screenshot refreshed
[[14:31:46]] [INFO] Refreshing screenshot...
[[14:31:45]] [SUCCESS] Screenshot refreshed successfully
[[14:31:44]] [INFO] Executing Multi Step action step 5/6: Tap on element with accessibility_id: btnMayBeLater
[[14:31:44]] [SUCCESS] Screenshot refreshed
[[14:31:44]] [INFO] Refreshing screenshot...
[[14:31:43]] [SUCCESS] Screenshot refreshed successfully
[[14:31:42]] [INFO] Executing Multi Step action step 4/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[14:31:42]] [SUCCESS] Screenshot refreshed
[[14:31:42]] [INFO] Refreshing screenshot...
[[14:31:38]] [SUCCESS] Screenshot refreshed successfully
[[14:31:37]] [INFO] Executing Multi Step action step 3/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[14:31:37]] [SUCCESS] Screenshot refreshed
[[14:31:37]] [INFO] Refreshing screenshot...
[[14:31:36]] [SUCCESS] Screenshot refreshed successfully
[[14:31:34]] [INFO] Executing Multi Step action step 2/6: Launch app: au.com.kmart
[[14:31:34]] [SUCCESS] Screenshot refreshed
[[14:31:34]] [INFO] Refreshing screenshot...
[[14:31:31]] [INFO] Executing Multi Step action step 1/6: Android Function: clear_app - Package: au.com.kmart
[[14:31:31]] [INFO] Loaded 6 steps from test case: Onboarding-Start-AU
[[14:31:31]] [INFO] Loading steps for Multi Step action: Onboarding-Start-AU
[[14:31:31]] [INFO] Executing action 1/37: Execute Test Case: Onboarding-Start-AU (6 steps)
[[14:31:31]] [INFO] ExecutionManager: Starting execution of 37 actions...
[[14:31:31]] [SUCCESS] Cleared 0 screenshots from database
[[14:31:31]] [INFO] Clearing screenshots from database before execution...
[[14:31:31]] [SUCCESS] All screenshots deleted successfully
[[14:31:31]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[14:31:31]] [INFO] Skipping report initialization - single test case execution
[[14:31:04]] [SUCCESS] Screenshot refreshed successfully
[[14:31:02]] [INFO] Skipping report generation - individual test case execution (not from test suite)
[[14:31:02]] [SUCCESS] Action logs saved successfully
[[14:31:02]] [ERROR] Execution failed but report was generated.
[[14:31:02]] [INFO] Saving 313 action log entries to file...
[[14:31:02]] [INFO] Generating execution report...
[[14:31:02]] [SUCCESS] All tests passed successfully!
[[14:31:02]] [SUCCESS] Screenshot refreshed
[[14:31:02]] [INFO] Refreshing screenshot...
[[14:31:02]] [INFO] XjclKOaCTh=pass
[[14:31:00]] [SUCCESS] Screenshot refreshed successfully
[[14:30:59]] [INFO] XjclKOaCTh=running
[[14:30:59]] [INFO] Executing action 37/37: Terminate app: au.com.kmart
[[14:30:59]] [SUCCESS] Screenshot refreshed
[[14:30:59]] [INFO] Refreshing screenshot...
[[14:30:59]] [INFO] 2p13JoJbbA=pass
[[14:30:56]] [SUCCESS] Screenshot refreshed successfully
[[14:30:55]] [INFO] 2p13JoJbbA=running
[[14:30:55]] [INFO] Executing action 36/37: Tap on element with xpath: //android.widget.Button[@content-desc="txtSign out"]
[[14:30:55]] [SUCCESS] Screenshot refreshed
[[14:30:55]] [INFO] Refreshing screenshot...
[[14:30:55]] [INFO] qHdMgerbTE=pass
[[14:30:51]] [SUCCESS] Screenshot refreshed successfully
[[14:30:50]] [INFO] qHdMgerbTE=running
[[14:30:50]] [INFO] Executing action 35/37: Swipe from (50%, 70%) to (50%, 30%)
[[14:30:50]] [SUCCESS] Screenshot refreshed
[[14:30:50]] [INFO] Refreshing screenshot...
[[14:30:50]] [INFO] F4NGh9HrLw=pass
[[14:30:49]] [SUCCESS] Screenshot refreshed successfully
[[14:30:48]] [INFO] F4NGh9HrLw=running
[[14:30:48]] [INFO] Executing action 34/37: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[14:30:48]] [SUCCESS] Screenshot refreshed
[[14:30:48]] [INFO] Refreshing screenshot...
[[14:29:54]] [INFO] Executing action 33/37: Tap if locator exists: xpath="//android.widget.Button[@content-desc="btnSaveOrContinue"]"
[[14:29:54]] [INFO] RlDZFks4Lc=fail
[[14:29:54]] [ERROR] Action 32 failed: Element not found or not tappable after all retry strategies: xpath='//android.widget.Button[contains(@text,"While using the app")]'
[[14:29:02]] [INFO] RlDZFks4Lc=running
[[14:29:02]] [INFO] Executing action 32/37: Tap on element with xpath: //android.widget.Button[contains(@text,"While using the app")]
[[14:29:02]] [INFO] jmKjclMUWT=fail
[[14:29:02]] [ERROR] Action 31 failed: Text 'current' not found within timeout (30s)
[[14:28:27]] [INFO] jmKjclMUWT=running
[[14:28:27]] [INFO] Executing action 31/37: Tap on Text: "current"
[[14:28:27]] [INFO] UoH0wdtcLk=fail
[[14:28:27]] [ERROR] Action 30 failed: Text 'Edit' not found within timeout (30s)
[[14:27:54]] [SUCCESS] Screenshot refreshed successfully
[[14:27:54]] [INFO] UoH0wdtcLk=running
[[14:27:54]] [INFO] Executing action 30/37: Tap on Text: "Edit"
[[14:27:54]] [SUCCESS] Screenshot refreshed
[[14:27:54]] [INFO] Refreshing screenshot...
[[14:27:54]] [INFO] U48qCNydwd=pass
[[14:27:53]] [SUCCESS] Screenshot refreshed successfully
[[14:27:50]] [INFO] U48qCNydwd=running
[[14:27:50]] [INFO] Executing action 29/37: Launch app: au.com.kmart
[[14:27:50]] [SUCCESS] Screenshot refreshed
[[14:27:50]] [INFO] Refreshing screenshot...
[[14:27:50]] [INFO] XjclKOaCTh=pass
[[14:27:49]] [INFO] XjclKOaCTh=running
[[14:27:49]] [INFO] Executing action 28/37: Terminate app: au.com.kmart
[[14:27:49]] [INFO] q6cKxgMAIn=fail
[[14:27:49]] [ERROR] Action 27 failed: Element not found: xpath='//android.webkit.WebView[@text="Slyp Receipt"]'
[[14:27:15]] [INFO] q6cKxgMAIn=running
[[14:27:15]] [INFO] Executing action 27/37: Check if element with xpath="//android.webkit.WebView[@text="Slyp Receipt"]" exists
[[14:27:15]] [INFO] zdh8hKYC1a=fail
[[14:27:15]] [ERROR] Action 26 failed: Element not found or not tappable after all retry strategies: xpath='(//android.widget.GridView/android.view.View/android.view.View[2])[1]'
[[14:26:49]] [INFO] zdh8hKYC1a=running
[[14:26:49]] [INFO] Executing action 26/37: Tap on element with xpath: (//android.widget.GridView/android.view.View/android.view.View[2])[1]
[[14:26:49]] [INFO] P4b2BITpCf=fail
[[14:26:49]] [ERROR] Action 25 failed: Element not found: xpath='(//android.widget.GridView/android.view.View/android.view.View[2])[1]'
[[14:26:03]] [SUCCESS] Screenshot refreshed successfully
[[14:26:03]] [INFO] P4b2BITpCf=running
[[14:26:03]] [INFO] Executing action 25/37: Check if element with xpath="(//android.widget.GridView/android.view.View/android.view.View[2])[1]" exists
[[14:26:03]] [SUCCESS] Screenshot refreshed
[[14:26:03]] [INFO] Refreshing screenshot...
[[14:26:03]] [INFO] inrxgdWzXr=pass
[[14:25:54]] [SUCCESS] Screenshot refreshed successfully
[[14:25:54]] [INFO] inrxgdWzXr=running
[[14:25:54]] [INFO] Executing action 24/37: Tap on Text: "Store"
[[14:25:54]] [SUCCESS] Screenshot refreshed
[[14:25:54]] [INFO] Refreshing screenshot...
[[14:25:52]] [SUCCESS] Screenshot refreshed successfully
[[14:25:51]] [INFO] Executing action 23/37: Tap on element with xpath: //android.widget.Button[@content-desc="txtMy orders & receipts"]
[[14:25:51]] [SUCCESS] Screenshot refreshed
[[14:25:51]] [INFO] Refreshing screenshot...
[[14:25:51]] [INFO] GEMv6goQtW=pass
[[14:25:40]] [SUCCESS] Screenshot refreshed successfully
[[14:25:39]] [INFO] GEMv6goQtW=running
[[14:25:39]] [INFO] Executing action 22/37: Tap on image: android-app-back.png
[[14:25:39]] [SUCCESS] Screenshot refreshed
[[14:25:39]] [INFO] Refreshing screenshot...
[[14:25:39]] [INFO] DhWa2PCBXE=pass
[[14:25:38]] [SUCCESS] Screenshot refreshed successfully
[[14:25:37]] [INFO] DhWa2PCBXE=running
[[14:25:37]] [INFO] Executing action 21/37: Check if element with xpath="//android.view.View[@content-desc="txtOnePassSubscritionBox"]" exists
[[14:25:37]] [SUCCESS] Screenshot refreshed
[[14:25:37]] [INFO] Refreshing screenshot...
[[14:25:37]] [INFO] pk2DLZFBmx=pass
[[14:25:35]] [SUCCESS] Screenshot refreshed successfully
[[14:25:34]] [INFO] pk2DLZFBmx=running
[[14:25:34]] [INFO] Executing action 20/37: Tap on element with xpath: //android.widget.Button[@content-desc="txtMy OnePass Account"]
[[14:25:34]] [SUCCESS] Screenshot refreshed
[[14:25:34]] [INFO] Refreshing screenshot...
[[14:25:34]] [INFO] ShJSdXvmVL=pass
[[14:25:26]] [SUCCESS] Screenshot refreshed successfully
[[14:25:26]] [INFO] ShJSdXvmVL=running
[[14:25:26]] [INFO] Executing action 19/37: Check if element with xpath="//android.widget.Button[@content-desc="txtMy OnePass Account"]" exists
[[14:25:26]] [SUCCESS] Screenshot refreshed
[[14:25:26]] [INFO] Refreshing screenshot...
[[14:25:26]] [SUCCESS] Screenshot refreshed
[[14:25:26]] [INFO] Refreshing screenshot...
[[14:25:24]] [SUCCESS] Screenshot refreshed successfully
[[14:25:24]] [INFO] Executing Multi Step action step 7/7: Tap on element with xpath: //android.widget.Button[@text="Sign in"]
[[14:25:24]] [SUCCESS] Screenshot refreshed
[[14:25:24]] [INFO] Refreshing screenshot...
[[14:25:22]] [SUCCESS] Screenshot refreshed successfully
[[14:25:21]] [INFO] Executing Multi Step action step 6/7: Input text: "Wonderbaby@6"
[[14:25:21]] [SUCCESS] Screenshot refreshed
[[14:25:21]] [INFO] Refreshing screenshot...
[[14:25:20]] [SUCCESS] Screenshot refreshed successfully
[[14:25:19]] [INFO] Executing Multi Step action step 5/7: Tap on element with xpath: //android.widget.EditText[@resource-id="password"]
[[14:25:19]] [SUCCESS] Screenshot refreshed
[[14:25:19]] [INFO] Refreshing screenshot...
[[14:25:18]] [SUCCESS] Screenshot refreshed successfully
[[14:25:17]] [INFO] Executing Multi Step action step 4/7: Tap on element with xpath: //android.widget.Button[@text="Continue to password"]
[[14:25:17]] [SUCCESS] Screenshot refreshed
[[14:25:17]] [INFO] Refreshing screenshot...
[[14:25:15]] [SUCCESS] Screenshot refreshed successfully
[[14:25:15]] [INFO] Executing Multi Step action step 3/7: Input text: "<EMAIL>"
[[14:25:15]] [SUCCESS] Screenshot refreshed
[[14:25:15]] [INFO] Refreshing screenshot...
[[14:25:13]] [SUCCESS] Screenshot refreshed successfully
[[14:25:13]] [INFO] Executing Multi Step action step 2/7: Tap on element with xpath: //android.widget.EditText[@resource-id="username"]
[[14:25:13]] [SUCCESS] Screenshot refreshed
[[14:25:13]] [INFO] Refreshing screenshot...
[[14:25:11]] [SUCCESS] Screenshot refreshed successfully
[[14:25:11]] [INFO] Executing Multi Step action step 1/7: Wait till xpath=//android.widget.EditText[@resource-id="username"]
[[14:25:11]] [INFO] Loaded 7 steps from test case: Kmart-Signin-AU-ANDROID
[[14:25:11]] [INFO] Loading steps for Multi Step action: Kmart-Signin-AU-ANDROID
[[14:25:11]] [INFO] s0WyiD1w0B=running
[[14:25:11]] [INFO] Executing action 18/37: Execute Test Case: Kmart-Signin-AU-ANDROID (7 steps)
[[14:25:11]] [SUCCESS] Screenshot refreshed
[[14:25:11]] [INFO] Refreshing screenshot...
[[14:25:11]] [INFO] gekNSY5O2E=pass
[[14:25:07]] [SUCCESS] Screenshot refreshed successfully
[[14:25:06]] [INFO] gekNSY5O2E=running
[[14:25:06]] [INFO] Executing action 17/37: Tap on element with xpath: //android.widget.Button[@content-desc="txtMoreAccountCtaSignIn"]
[[14:25:06]] [SUCCESS] Screenshot refreshed
[[14:25:06]] [INFO] Refreshing screenshot...
[[14:25:06]] [INFO] GEMv6goQtW=pass
[[14:25:00]] [SUCCESS] Screenshot refreshed successfully
[[14:24:57]] [INFO] GEMv6goQtW=running
[[14:24:57]] [INFO] Executing action 16/37: Tap on image: android-app-back.png
[[14:24:57]] [SUCCESS] Screenshot refreshed
[[14:24:57]] [INFO] Refreshing screenshot...
[[14:24:57]] [INFO] 83tV9A4NOn=pass
[[14:24:55]] [SUCCESS] Screenshot refreshed successfully
[[14:24:54]] [INFO] 83tV9A4NOn=running
[[14:24:54]] [INFO] Executing action 15/37: Check if element with xpath="//android.view.View[@text="Order ********* is refunded"]" exists
[[14:24:54]] [SUCCESS] Screenshot refreshed
[[14:24:54]] [INFO] Refreshing screenshot...
[[14:24:54]] [INFO] aNN0yYFLEd=pass
[[14:24:53]] [SUCCESS] Screenshot refreshed successfully
[[14:24:52]] [INFO] aNN0yYFLEd=running
[[14:24:52]] [INFO] Executing action 14/37: Tap on element with xpath: //android.widget.Button[@text="Search for order"]
[[14:24:52]] [SUCCESS] Screenshot refreshed
[[14:24:52]] [INFO] Refreshing screenshot...
[[14:24:52]] [INFO] XJv08Gkucs=pass
[[14:24:47]] [SUCCESS] Screenshot refreshed successfully
[[14:24:09]] [INFO] XJv08Gkucs=running
[[14:24:09]] [INFO] Executing action 13/37: Input text: "<EMAIL>"
[[14:24:09]] [SUCCESS] Screenshot refreshed
[[14:24:09]] [INFO] Refreshing screenshot...
[[14:24:09]] [INFO] aNN0yYFLEd=pass
[[14:24:07]] [SUCCESS] Screenshot refreshed successfully
[[14:24:07]] [INFO] aNN0yYFLEd=running
[[14:24:07]] [INFO] Executing action 12/37: Tap on element with xpath: //android.widget.EditText[@resource-id="email"]
[[14:24:07]] [SUCCESS] Screenshot refreshed
[[14:24:07]] [INFO] Refreshing screenshot...
[[14:24:07]] [INFO] 7YbjwQH1Jc=pass
[[14:24:04]] [SUCCESS] Screenshot refreshed successfully
[[14:24:04]] [INFO] 7YbjwQH1Jc=running
[[14:24:04]] [INFO] Executing action 11/37: Input text: "*********"
[[14:24:04]] [SUCCESS] Screenshot refreshed
[[14:24:04]] [INFO] Refreshing screenshot...
[[14:24:04]] [INFO] OmKfD9iBjD=pass
[[14:23:58]] [SUCCESS] Screenshot refreshed successfully
[[14:23:57]] [INFO] OmKfD9iBjD=running
[[14:23:57]] [INFO] Executing action 10/37: Tap on element with xpath: //android.widget.EditText[@resource-id="orderId"]
[[14:23:57]] [SUCCESS] Screenshot refreshed
[[14:23:57]] [INFO] Refreshing screenshot...
[[14:23:57]] [INFO] eHLWiRoqqS=pass
[[14:23:55]] [SUCCESS] Screenshot refreshed successfully
[[14:23:55]] [INFO] eHLWiRoqqS=running
[[14:23:55]] [INFO] Executing action 9/37: Tap on element with xpath: //android.widget.Button[@content-desc="txtTrack My Order"]
[[14:23:55]] [SUCCESS] Screenshot refreshed
[[14:23:55]] [INFO] Refreshing screenshot...
[[14:23:55]] [INFO] F4NGh9HrLw=pass
[[14:23:53]] [SUCCESS] Screenshot refreshed successfully
[[14:23:52]] [INFO] F4NGh9HrLw=running
[[14:23:52]] [INFO] Executing action 8/37: Tap on element with xpath: //android.widget.ImageView[contains(@content-desc,"Tab 5 of 5")]
[[14:23:52]] [SUCCESS] Screenshot refreshed
[[14:23:52]] [INFO] Refreshing screenshot...
[[14:23:52]] [INFO] 74XW7x54ad=pass
[[14:23:50]] [SUCCESS] Screenshot refreshed successfully
[[14:23:49]] [INFO] 74XW7x54ad=running
[[14:23:49]] [INFO] Executing action 7/37: Tap on element with xpath: //android.widget.ImageView[@content-desc="imgBackArrow"]
[[14:23:49]] [SUCCESS] Screenshot refreshed
[[14:23:49]] [INFO] Refreshing screenshot...
[[14:23:49]] [INFO] xUbWFa8Ok2=pass
[[14:23:48]] [SUCCESS] Screenshot refreshed successfully
[[14:23:47]] [INFO] xUbWFa8Ok2=running
[[14:23:47]] [INFO] Executing action 6/37: Check if element with xpath="//android.view.View[contains(@content-desc,"rectangle frame to view helpful product information")]" exists
[[14:23:47]] [SUCCESS] Screenshot refreshed
[[14:23:47]] [INFO] Refreshing screenshot...
[[14:23:47]] [INFO] RbNtEW6N9T=pass
[[14:23:46]] [SUCCESS] Screenshot refreshed successfully
[[14:23:44]] [INFO] RbNtEW6N9T=running
[[14:23:44]] [INFO] Executing action 5/37: Check if element with xpath="//android.widget.ImageView[@content-desc="imgHelp"]" exists
[[14:23:44]] [SUCCESS] Screenshot refreshed
[[14:23:44]] [INFO] Refreshing screenshot...
[[14:23:44]] [INFO] F4NGh9HrLw=pass
[[14:23:43]] [SUCCESS] Screenshot refreshed successfully
[[14:23:42]] [INFO] F4NGh9HrLw=running
[[14:23:42]] [INFO] Executing action 4/37: Check if element with xpath="//android.view.View[@content-desc="Barcode Scanner"]" exists
[[14:23:42]] [SUCCESS] Screenshot refreshed
[[14:23:42]] [INFO] Refreshing screenshot...
[[14:23:42]] [INFO] RlDZFks4Lc=pass
[[14:23:40]] [SUCCESS] Screenshot refreshed successfully
[[14:23:40]] [INFO] RlDZFks4Lc=running
[[14:23:40]] [INFO] Executing action 3/37: Tap on element with xpath: //android.widget.Button[contains(@text,"While using the app")]
[[14:23:40]] [SUCCESS] Screenshot refreshed
[[14:23:40]] [INFO] Refreshing screenshot...
[[14:23:40]] [INFO] Dzn2Q7JTe0=pass
[[14:23:39]] [SUCCESS] Screenshot refreshed successfully
[[14:23:38]] [INFO] Dzn2Q7JTe0=running
[[14:23:38]] [INFO] Executing action 2/37: Tap on element with xpath: //android.widget.Button[@content-desc="btnBarcodeScanner"]
[[14:23:38]] [SUCCESS] Screenshot refreshed
[[14:23:38]] [INFO] Refreshing screenshot...
[[14:23:38]] [SUCCESS] Screenshot refreshed
[[14:23:38]] [INFO] Refreshing screenshot...
[[14:23:36]] [SUCCESS] Screenshot refreshed successfully
[[14:23:35]] [INFO] Executing Multi Step action step 6/6: Tap on element with accessibility_id: txtOnePassOnboardingSkipForNow
[[14:23:35]] [SUCCESS] Screenshot refreshed
[[14:23:35]] [INFO] Refreshing screenshot...
[[14:23:34]] [SUCCESS] Screenshot refreshed successfully
[[14:23:33]] [INFO] Executing Multi Step action step 5/6: Tap on element with accessibility_id: btnMayBeLater
[[14:23:33]] [SUCCESS] Screenshot refreshed
[[14:23:33]] [INFO] Refreshing screenshot...
[[14:23:32]] [SUCCESS] Screenshot refreshed successfully
[[14:23:30]] [INFO] Executing Multi Step action step 4/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[14:23:30]] [SUCCESS] Screenshot refreshed
[[14:23:30]] [INFO] Refreshing screenshot...
[[14:23:26]] [SUCCESS] Screenshot refreshed successfully
[[14:23:25]] [INFO] Executing Multi Step action step 3/6: Tap on element with accessibility_id: btnOnboardingLocationLaterButton
[[14:23:25]] [SUCCESS] Screenshot refreshed
[[14:23:25]] [INFO] Refreshing screenshot...
[[14:23:24]] [SUCCESS] Screenshot refreshed successfully
[[14:23:22]] [INFO] Executing Multi Step action step 2/6: Launch app: au.com.kmart
[[14:23:22]] [SUCCESS] Screenshot refreshed
[[14:23:22]] [INFO] Refreshing screenshot...
[[14:23:18]] [INFO] Executing Multi Step action step 1/6: Android Function: clear_app - Package: au.com.kmart
[[14:23:18]] [INFO] Loaded 6 steps from test case: Onboarding-Start-AU
[[14:23:18]] [INFO] Loading steps for Multi Step action: Onboarding-Start-AU
[[14:23:18]] [INFO] Executing action 1/37: Execute Test Case: Onboarding-Start-AU (6 steps)
[[14:23:18]] [INFO] ExecutionManager: Starting execution of 37 actions...
[[14:23:18]] [SUCCESS] Cleared 0 screenshots from database
[[14:23:18]] [INFO] Clearing screenshots from database before execution...
[[14:23:18]] [SUCCESS] All screenshots deleted successfully
[[14:23:18]] [INFO] Deleting all screenshots in app/static/screenshots folder...
[[14:23:18]] [INFO] Skipping report initialization - single test case execution
[[14:23:17]] [SUCCESS] All screenshots deleted successfully
[[14:23:17]] [SUCCESS] Loaded test case "Others AU ANDROID" with 37 actions
[[14:23:17]] [SUCCESS] Added action: terminateApp
[[14:23:17]] [SUCCESS] Added action: tap
[[14:23:17]] [SUCCESS] Added action: swipe
[[14:23:17]] [SUCCESS] Added action: tap
[[14:23:17]] [SUCCESS] Added action: tapIfLocatorExists
[[14:23:17]] [SUCCESS] Added action: tap
[[14:23:17]] [SUCCESS] Added action: tapOnText
[[14:23:17]] [SUCCESS] Added action: tapOnText
[[14:23:17]] [SUCCESS] Added action: launchApp
[[14:23:17]] [SUCCESS] Added action: terminateApp
[[14:23:17]] [SUCCESS] Added action: exists
[[14:23:17]] [SUCCESS] Added action: tap
[[14:23:17]] [SUCCESS] Added action: exists
[[14:23:17]] [SUCCESS] Added action: tapOnText
[[14:23:17]] [SUCCESS] Added action: tap
[[14:23:17]] [SUCCESS] Added action: tap
[[14:23:17]] [SUCCESS] Added action: exists
[[14:23:17]] [SUCCESS] Added action: tap
[[14:23:17]] [SUCCESS] Added action: exists
[[14:23:17]] [SUCCESS] Added action: multiStep
[[14:23:17]] [SUCCESS] Added action: tap
[[14:23:17]] [SUCCESS] Added action: tap
[[14:23:17]] [SUCCESS] Added action: exists
[[14:23:17]] [SUCCESS] Added action: tap
[[14:23:17]] [SUCCESS] Added action: text
[[14:23:17]] [SUCCESS] Added action: tap
[[14:23:17]] [SUCCESS] Added action: text
[[14:23:17]] [SUCCESS] Added action: tap
[[14:23:17]] [SUCCESS] Added action: tap
[[14:23:17]] [SUCCESS] Added action: tap
[[14:23:17]] [SUCCESS] Added action: tap
[[14:23:17]] [SUCCESS] Added action: exists
[[14:23:17]] [SUCCESS] Added action: exists
[[14:23:17]] [SUCCESS] Added action: exists
[[14:23:17]] [SUCCESS] Added action: tap
[[14:23:17]] [SUCCESS] Added action: tap
[[14:23:17]] [SUCCESS] Added action: multiStep
[[14:23:17]] [INFO] All actions cleared
[[14:23:17]] [INFO] Cleaning up screenshots...
[[14:22:26]] [SUCCESS] Screenshot refreshed successfully
[[14:22:24]] [SUCCESS] Screenshot refreshed
[[14:22:24]] [INFO] Refreshing screenshot...
[[14:22:23]] [SUCCESS] Connected to device: PJTCI7EMSSONYPU8
[[14:22:23]] [INFO] Device info updated: RMX2151
[[14:22:12]] [INFO] Connecting to device: PJTCI7EMSSONYPU8 (Platform: Android)...
[[14:21:41]] [SUCCESS] Found 1 device(s)
[[14:21:41]] [INFO] Refreshing device list...
