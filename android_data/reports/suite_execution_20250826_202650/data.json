{"name": "UI Execution 26/08/2025, 20:26:50", "testCases": [{"name": "Calc-AndroidTest-Extra-Steps\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Edit\n                            \n                            \n                                 Remove\n                            \n                            7 actions", "status": "failed", "steps": [{"name": "Restart app: com.coloros.calculator", "status": "passed", "duration": "1035ms", "action_id": "calculator", "screenshot_filename": "calculator.png", "report_screenshot": "calculator.png", "resolved_screenshot": "screenshots/calculator.png", "clean_action_id": "calculator", "prefixed_action_id": "al_calculator", "timestamp": "2025-08-26 20:26:50", "screenshot": "screenshots/calculator.png", "action_id_screenshot": "screenshots/calculator.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[@content-desc=\"More options,\"]", "status": "passed", "duration": "6559ms", "action_id": "screenshot_20250826_202604", "screenshot_filename": "screenshot_20250826_202604.png", "report_screenshot": "screenshot_20250826_202604.png", "resolved_screenshot": "screenshots/screenshot_20250826_202604.png", "clean_action_id": "screenshot_20250826_202604", "prefixed_action_id": "al_screenshot_20250826_202604", "timestamp": "2025-08-26 20:26:50", "screenshot": "screenshots/screenshot_20250826_202604.png", "action_id_screenshot": "screenshots/screenshot_20250826_202604.png"}, {"name": "Tap on Text: \"<PERSON>ting<PERSON>\"", "status": "passed", "duration": "2719ms", "action_id": "screenshot_20250826_202607", "screenshot_filename": "screenshot_20250826_202607.png", "report_screenshot": "screenshot_20250826_202607.png", "resolved_screenshot": "screenshots/screenshot_20250826_202607.png", "clean_action_id": "screenshot_20250826_202607", "prefixed_action_id": "al_screenshot_20250826_202607", "timestamp": "2025-08-26 20:26:50", "screenshot": "screenshots/screenshot_20250826_202607.png", "action_id_screenshot": "screenshots/screenshot_20250826_202607.png"}, {"name": "Tap on element with xpath: //android.widget.ImageButton[@content-desc=\"xyz\"]", "status": "failed", "duration": "0ms", "action_id": "ImageButto", "screenshot_filename": "ImageButto.png", "report_screenshot": "ImageButto.png", "resolved_screenshot": "screenshots/ImageButto.png", "clean_action_id": "ImageButto", "prefixed_action_id": "al_ImageButto", "timestamp": "2025-08-26 20:26:50", "screenshot": "screenshots/ImageButto.png", "action_id_screenshot": "screenshots/ImageButto.png"}, {"name": "Tap on element with xpath: //android.widget.TextView[@content-desc=\"Scientific\"]", "status": "unknown", "duration": "0ms", "action_id": "Scientific", "screenshot_filename": "Scientific.png", "report_screenshot": "Scientific.png", "resolved_screenshot": "screenshots/Scientific.png", "clean_action_id": "Scientific", "prefixed_action_id": "al_Scientific", "timestamp": "2025-08-26 20:26:51", "screenshot": "screenshots/Scientific.png", "action_id_screenshot": "screenshots/Scientific.png"}, {"name": "Terminate app: com.coloros.calculator", "status": "unknown", "duration": "0ms", "action_id": "calculator", "screenshot_filename": "calculator.png", "report_screenshot": "calculator.png", "resolved_screenshot": "screenshots/calculator.png", "clean_action_id": "calculator", "prefixed_action_id": "al_calculator", "timestamp": "2025-08-26 20:26:51", "screenshot": "screenshots/calculator.png", "action_id_screenshot": "screenshots/calculator.png"}, {"name": "cleanupSteps action", "status": "unknown", "duration": "0ms", "action_id": "cleanupSte", "screenshot_filename": "cleanupSte.png", "report_screenshot": "cleanupSte.png", "resolved_screenshot": "screenshots/cleanupSte.png", "clean_action_id": "cleanupSte", "prefixed_action_id": "al_cleanupSte", "timestamp": "2025-08-26 20:26:51", "screenshot": "screenshots/cleanupSte.png", "action_id_screenshot": "screenshots/cleanupSte.png"}]}, {"name": "Calc-AndroidTest\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Edit\n                            \n                            \n                                 Remove\n                            \n                            6 actions", "status": "passed", "steps": [{"name": "Restart app: com.coloros.calculator", "status": "passed", "duration": "594ms", "action_id": "calculator", "screenshot_filename": "calculator.png", "report_screenshot": "calculator.png", "resolved_screenshot": "screenshots/calculator.png", "clean_action_id": "G0yOqxZ4nF", "prefixed_action_id": "al_G0yOqxZ4nF", "timestamp": "2025-08-26 20:26:51", "screenshot": "screenshots/G0yOqxZ4nF.png", "action_id_screenshot": "screenshots/calculator.png"}, {"name": "Tap on element with xpath: //android.widget.ImageView[@content-desc=\"More options,\"]", "status": "passed", "duration": "488ms", "action_id": "SJlCXeU69X", "screenshot_filename": "SJlCXeU69X.png", "report_screenshot": "SJlCXeU69X.png", "resolved_screenshot": "screenshots/SJlCXeU69X.png", "clean_action_id": "SJlCXeU69X", "prefixed_action_id": "al_SJlCXeU69X", "timestamp": "2025-08-26 20:26:51", "screenshot": "screenshots/SJlCXeU69X.png", "action_id_screenshot": "screenshots/SJlCXeU69X.png"}, {"name": "Tap on Text: \"<PERSON>ting<PERSON>\"", "status": "passed", "duration": "2662ms", "action_id": "screenshot_20250826_202403", "screenshot_filename": "screenshot_20250826_202403.png", "report_screenshot": "screenshot_20250826_202403.png", "resolved_screenshot": "screenshots/screenshot_20250826_202403.png", "clean_action_id": "screenshot_20250826_202403", "prefixed_action_id": "al_screenshot_20250826_202403", "timestamp": "2025-08-26 20:26:51", "screenshot": "screenshots/screenshot_20250826_202403.png", "action_id_screenshot": "screenshots/screenshot_20250826_202403.png"}, {"name": "Tap on element with xpath: //android.widget.ImageButton[@content-desc=\"Navigate up\"]", "status": "passed", "duration": "34590ms", "action_id": "ImageButto", "screenshot_filename": "ImageButto.png", "report_screenshot": "ImageButto.png", "resolved_screenshot": "screenshots/ImageButto.png", "clean_action_id": "screenshot_20250826_202551", "prefixed_action_id": "al_screenshot_20250826_202551", "timestamp": "2025-08-26 20:26:51", "screenshot": "screenshots/screenshot_20250826_202551.png", "action_id_screenshot": "screenshots/ImageButto.png"}, {"name": "Tap on element with xpath: //android.widget.TextView[@content-desc=\"Scientific\"]", "status": "passed", "duration": "1426ms", "action_id": "Scientific", "screenshot_filename": "Scientific.png", "report_screenshot": "Scientific.png", "resolved_screenshot": "screenshots/Scientific.png", "clean_action_id": "screenshot_20250826_202604", "prefixed_action_id": "al_screenshot_20250826_202604", "timestamp": "2025-08-26 20:26:51", "screenshot": "screenshots/screenshot_20250826_202604.png", "action_id_screenshot": "screenshots/Scientific.png"}, {"name": "Terminate app: com.coloros.calculator", "status": "passed", "duration": "997ms", "action_id": "calculator", "screenshot_filename": "calculator.png", "report_screenshot": "calculator.png", "resolved_screenshot": "screenshots/calculator.png", "clean_action_id": "screenshot_20250826_202607", "prefixed_action_id": "al_screenshot_20250826_202607", "timestamp": "2025-08-26 20:26:51", "screenshot": "screenshots/screenshot_20250826_202607.png", "action_id_screenshot": "screenshots/calculator.png"}]}], "passed": 1, "failed": 1, "skipped": 0, "status": "failed", "id": "9dc39d7e-0f47-4065-9363-a615acc089df"}