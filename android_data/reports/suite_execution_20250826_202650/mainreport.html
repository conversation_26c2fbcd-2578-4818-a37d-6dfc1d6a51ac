<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Suite Report - 8/26/2025, 8:26:51 PM</title>
    <link rel="stylesheet" href="./assets/report.css">
    <link rel="stylesheet" href="./assets/custom.css">
</head>
<body>
    <header class="header">
        <h1>Suites</h1>
        <div class="status-summary">
            Status: <span class="status-badge status-badge-failed">failed</span>
            <span class="stats-summary">
                <span class="passed-count">1</span> passed,
                <span class="failed-count">1</span> failed,
                <span class="skipped-count">0</span> skipped
            </span>
        </div>
    </header>

    <div class="content">
        <div class="suites-panel">
            <div class="suite-heading">
                <span class="expand-icon"></span>
                UI Execution 26/08/2025, 20:26:50
            </div>

            <ul class="test-list">
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="7 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-failed"></span>
                            #1 Calc-AndroidTest-Extra-Steps
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Edit
                            
                            
                                 Remove
                            
                            7 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="calculator.png" data-action-id="calculator" onclick="showStepDetails('step-0-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: com.coloros.calculator <span class="action-id-badge" title="Action ID: calculator">calculator</span>
                            </div>
                            <span class="test-step-duration">1035ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="screenshot_20250826_202604.png" data-action-id="screenshot_20250826_202604" onclick="showStepDetails('step-0-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"] <span class="action-id-badge" title="Action ID: screenshot_20250826_202604">screenshot_20250826_202604</span>
                            </div>
                            <span class="test-step-duration">6559ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="screenshot_20250826_202607.png" data-action-id="screenshot_20250826_202607" onclick="showStepDetails('step-0-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Settings" <span class="action-id-badge" title="Action ID: screenshot_20250826_202607">screenshot_20250826_202607</span>
                            </div>
                            <span class="test-step-duration">2719ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="failed"
                            data-screenshot="ImageButto.png" data-action-id="ImageButto" onclick="showStepDetails('step-0-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-failed"></span>
                                Tap on element with xpath: //android.widget.ImageButton[@content-desc="xyz"] <span class="action-id-badge" title="Action ID: ImageButto">ImageButto</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="unknown"
                            data-screenshot="Scientific.png" data-action-id="Scientific" onclick="showStepDetails('step-0-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Tap on element with xpath: //android.widget.TextView[@content-desc="Scientific"] <span class="action-id-badge" title="Action ID: Scientific">Scientific</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="unknown"
                            data-screenshot="calculator.png" data-action-id="calculator" onclick="showStepDetails('step-0-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                Terminate app: com.coloros.calculator <span class="action-id-badge" title="Action ID: calculator">calculator</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                        <li class="test-step" data-step-id="7" data-status="unknown"
                            data-screenshot="cleanupSte.png" data-action-id="cleanupSte" onclick="showStepDetails('step-0-6')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-unknown"></span>
                                cleanupSteps action <span class="action-id-badge" title="Action ID: cleanupSte">cleanupSte</span>
                            </div>
                            <span class="test-step-duration">0ms</span>
                        </li>
                    </ul>
                </li>
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="6 actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-passed"></span>
                            #2 Calc-AndroidTest
                            
                            
                        
                        
                            
                                 Retry
                            
                            
                                 Stop
                            
                            
                                 Edit
                            
                            
                                 Remove
                            
                            6 actions
                        </div>
                        <span class="test-duration">0ms</span>
                    </div>
                    <ul class="test-steps">
                        <li class="test-step" data-step-id="1" data-status="passed"
                            data-screenshot="calculator.png" data-action-id="calculator" onclick="showStepDetails('step-1-0')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Restart app: com.coloros.calculator <span class="action-id-badge" title="Action ID: calculator">calculator</span>
                            </div>
                            <span class="test-step-duration">594ms</span>
                        </li>
                        <li class="test-step" data-step-id="2" data-status="passed"
                            data-screenshot="SJlCXeU69X.png" data-action-id="SJlCXeU69X" onclick="showStepDetails('step-1-1')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //android.widget.ImageView[@content-desc="More options,"] <span class="action-id-badge" title="Action ID: SJlCXeU69X">SJlCXeU69X</span>
                            </div>
                            <span class="test-step-duration">488ms</span>
                        </li>
                        <li class="test-step" data-step-id="3" data-status="passed"
                            data-screenshot="screenshot_20250826_202403.png" data-action-id="screenshot_20250826_202403" onclick="showStepDetails('step-1-2')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on Text: "Settings" <span class="action-id-badge" title="Action ID: screenshot_20250826_202403">screenshot_20250826_202403</span>
                            </div>
                            <span class="test-step-duration">2662ms</span>
                        </li>
                        <li class="test-step" data-step-id="4" data-status="passed"
                            data-screenshot="ImageButto.png" data-action-id="ImageButto" onclick="showStepDetails('step-1-3')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //android.widget.ImageButton[@content-desc="Navigate up"] <span class="action-id-badge" title="Action ID: ImageButto">ImageButto</span>
                            </div>
                            <span class="test-step-duration">34590ms</span>
                        </li>
                        <li class="test-step" data-step-id="5" data-status="passed"
                            data-screenshot="Scientific.png" data-action-id="Scientific" onclick="showStepDetails('step-1-4')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Tap on element with xpath: //android.widget.TextView[@content-desc="Scientific"] <span class="action-id-badge" title="Action ID: Scientific">Scientific</span>
                            </div>
                            <span class="test-step-duration">1426ms</span>
                        </li>
                        <li class="test-step" data-step-id="6" data-status="passed"
                            data-screenshot="calculator.png" data-action-id="calculator" onclick="showStepDetails('step-1-5')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-passed"></span>
                                Terminate app: com.coloros.calculator <span class="action-id-badge" title="Action ID: calculator">calculator</span>
                            </div>
                            <span class="test-step-duration">997ms</span>
                        </li>
                    </ul>
                </li>
            </ul>
        </div>

        <div class="details-panel" id="details-panel">
            <!-- This will be populated by JavaScript when a test step is clicked -->
            <h3>Click on a test step to view details</h3>
        </div>
    </div>

    <script>
        // Store the test data for our JavaScript to use
        const testData = {"name":"UI Execution 26/08/2025, 20:26:50","testCases":[{"name":"Calc-AndroidTest-Extra-Steps\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Edit\n                            \n                            \n                                 Remove\n                            \n                            7 actions","status":"failed","steps":[{"name":"Restart app: com.coloros.calculator","status":"passed","duration":"1035ms","action_id":"calculator","screenshot_filename":"calculator.png","report_screenshot":"calculator.png","resolved_screenshot":"screenshots/calculator.png","clean_action_id":"calculator","prefixed_action_id":"al_calculator","timestamp":"2025-08-26 20:26:50","screenshot":"screenshots/calculator.png","action_id_screenshot":"screenshots/calculator.png"},{"name":"Tap on element with xpath: //android.widget.ImageView[@content-desc=\"More options,\"]","status":"passed","duration":"6559ms","action_id":"screenshot_20250826_202604","screenshot_filename":"screenshot_20250826_202604.png","report_screenshot":"screenshot_20250826_202604.png","resolved_screenshot":"screenshots/screenshot_20250826_202604.png","clean_action_id":"screenshot_20250826_202604","prefixed_action_id":"al_screenshot_20250826_202604","timestamp":"2025-08-26 20:26:50","screenshot":"screenshots/screenshot_20250826_202604.png","action_id_screenshot":"screenshots/screenshot_20250826_202604.png"},{"name":"Tap on Text: \"Settings\"","status":"passed","duration":"2719ms","action_id":"screenshot_20250826_202607","screenshot_filename":"screenshot_20250826_202607.png","report_screenshot":"screenshot_20250826_202607.png","resolved_screenshot":"screenshots/screenshot_20250826_202607.png","clean_action_id":"screenshot_20250826_202607","prefixed_action_id":"al_screenshot_20250826_202607","timestamp":"2025-08-26 20:26:50","screenshot":"screenshots/screenshot_20250826_202607.png","action_id_screenshot":"screenshots/screenshot_20250826_202607.png"},{"name":"Tap on element with xpath: //android.widget.ImageButton[@content-desc=\"xyz\"]","status":"failed","duration":"0ms","action_id":"ImageButto","screenshot_filename":"ImageButto.png","report_screenshot":"ImageButto.png","resolved_screenshot":"screenshots/ImageButto.png","clean_action_id":"ImageButto","prefixed_action_id":"al_ImageButto","timestamp":"2025-08-26 20:26:50","screenshot":"screenshots/ImageButto.png","action_id_screenshot":"screenshots/ImageButto.png"},{"name":"Tap on element with xpath: //android.widget.TextView[@content-desc=\"Scientific\"]","status":"unknown","duration":"0ms","action_id":"Scientific","screenshot_filename":"Scientific.png","report_screenshot":"Scientific.png","resolved_screenshot":"screenshots/Scientific.png","clean_action_id":"Scientific","prefixed_action_id":"al_Scientific","timestamp":"2025-08-26 20:26:51","screenshot":"screenshots/Scientific.png","action_id_screenshot":"screenshots/Scientific.png"},{"name":"Terminate app: com.coloros.calculator","status":"unknown","duration":"0ms","action_id":"calculator","screenshot_filename":"calculator.png","report_screenshot":"calculator.png","resolved_screenshot":"screenshots/calculator.png","clean_action_id":"calculator","prefixed_action_id":"al_calculator","timestamp":"2025-08-26 20:26:51","screenshot":"screenshots/calculator.png","action_id_screenshot":"screenshots/calculator.png"},{"name":"cleanupSteps action","status":"unknown","duration":"0ms","action_id":"cleanupSte","screenshot_filename":"cleanupSte.png","report_screenshot":"cleanupSte.png","resolved_screenshot":"screenshots/cleanupSte.png","clean_action_id":"cleanupSte","prefixed_action_id":"al_cleanupSte","timestamp":"2025-08-26 20:26:51","screenshot":"screenshots/cleanupSte.png","action_id_screenshot":"screenshots/cleanupSte.png"}]},{"name":"Calc-AndroidTest\n                            \n                            \n                        \n                        \n                            \n                                 Retry\n                            \n                            \n                                 Stop\n                            \n                            \n                                 Edit\n                            \n                            \n                                 Remove\n                            \n                            6 actions","status":"passed","steps":[{"name":"Restart app: com.coloros.calculator","status":"passed","duration":"594ms","action_id":"calculator","screenshot_filename":"calculator.png","report_screenshot":"calculator.png","resolved_screenshot":"screenshots/calculator.png","clean_action_id":"G0yOqxZ4nF","prefixed_action_id":"al_G0yOqxZ4nF","timestamp":"2025-08-26 20:26:51","screenshot":"screenshots/G0yOqxZ4nF.png","action_id_screenshot":"screenshots/calculator.png"},{"name":"Tap on element with xpath: //android.widget.ImageView[@content-desc=\"More options,\"]","status":"passed","duration":"488ms","action_id":"SJlCXeU69X","screenshot_filename":"SJlCXeU69X.png","report_screenshot":"SJlCXeU69X.png","resolved_screenshot":"screenshots/SJlCXeU69X.png","clean_action_id":"SJlCXeU69X","prefixed_action_id":"al_SJlCXeU69X","timestamp":"2025-08-26 20:26:51","screenshot":"screenshots/SJlCXeU69X.png","action_id_screenshot":"screenshots/SJlCXeU69X.png"},{"name":"Tap on Text: \"Settings\"","status":"passed","duration":"2662ms","action_id":"screenshot_20250826_202403","screenshot_filename":"screenshot_20250826_202403.png","report_screenshot":"screenshot_20250826_202403.png","resolved_screenshot":"screenshots/screenshot_20250826_202403.png","clean_action_id":"screenshot_20250826_202403","prefixed_action_id":"al_screenshot_20250826_202403","timestamp":"2025-08-26 20:26:51","screenshot":"screenshots/screenshot_20250826_202403.png","action_id_screenshot":"screenshots/screenshot_20250826_202403.png"},{"name":"Tap on element with xpath: //android.widget.ImageButton[@content-desc=\"Navigate up\"]","status":"passed","duration":"34590ms","action_id":"ImageButto","screenshot_filename":"ImageButto.png","report_screenshot":"ImageButto.png","resolved_screenshot":"screenshots/ImageButto.png","clean_action_id":"screenshot_20250826_202551","prefixed_action_id":"al_screenshot_20250826_202551","timestamp":"2025-08-26 20:26:51","screenshot":"screenshots/screenshot_20250826_202551.png","action_id_screenshot":"screenshots/ImageButto.png"},{"name":"Tap on element with xpath: //android.widget.TextView[@content-desc=\"Scientific\"]","status":"passed","duration":"1426ms","action_id":"Scientific","screenshot_filename":"Scientific.png","report_screenshot":"Scientific.png","resolved_screenshot":"screenshots/Scientific.png","clean_action_id":"screenshot_20250826_202604","prefixed_action_id":"al_screenshot_20250826_202604","timestamp":"2025-08-26 20:26:51","screenshot":"screenshots/screenshot_20250826_202604.png","action_id_screenshot":"screenshots/Scientific.png"},{"name":"Terminate app: com.coloros.calculator","status":"passed","duration":"997ms","action_id":"calculator","screenshot_filename":"calculator.png","report_screenshot":"calculator.png","resolved_screenshot":"screenshots/calculator.png","clean_action_id":"screenshot_20250826_202607","prefixed_action_id":"al_screenshot_20250826_202607","timestamp":"2025-08-26 20:26:51","screenshot":"screenshots/screenshot_20250826_202607.png","action_id_screenshot":"screenshots/calculator.png"}]}],"passed":1,"failed":1,"skipped":0,"status":"failed","id":"9dc39d7e-0f47-4065-9363-a615acc089df","availableScreenshots":["G0yOqxZ4nF.png","ImageButto.png","SJlCXeU69X.png","Scientific.png","calculator.png","cleanupSte.png","screenshot_20250826_202403.png","screenshot_20250826_202551.png","screenshot_20250826_202604.png","screenshot_20250826_202607.png"],"screenshots_map":{}};
    </script>

    <script src="./assets/report.js"></script>
</body>
</html>