#!/usr/bin/env python3
"""
Configuration file for using the consolidated database.
This replaces the original config.py when using the consolidated database.
"""

import os
import logging
from pathlib import Path
from app.utils.consolidated_db_adapter import get_consolidated_db

# Set up logging
logger = logging.getLogger(__name__)

# Base directory
BASE_DIR = Path(__file__).resolve().parent

# Get consolidated database adapter
try:
    consolidated_db = get_consolidated_db()
    logger.info("Successfully connected to consolidated database")
except Exception as e:
    logger.error(f"Failed to connect to consolidated database: {e}")
    raise

# Platform detection
PLATFORM = 'ios'  # Default platform
if 'PLATFORM' in os.environ:
    PLATFORM = os.environ['PLATFORM'].lower()
elif 'android' in str(BASE_DIR).lower():
    PLATFORM = 'android'

logger.info(f"Using platform: {PLATFORM}")

# Port configuration
PORT = int(os.environ.get('PORT', 8080 if PLATFORM == 'ios' else 8081))
APPIUM_PORT = int(os.environ.get('APPIUM_PORT', 4723))
WDA_PORT = int(os.environ.get('WDA_PORT', 8200 if PLATFORM == 'ios' else 8201))

# Get directory paths from consolidated database
try:
    # Default directories based on platform
    if PLATFORM == 'ios':
        DEFAULT_DIRECTORIES = {
            'TEST_CASES': Path('/Users/<USER>/Documents/automation-tool/ios_data/test_cases'),
            'REPORTS': BASE_DIR / 'reports',
            'SCREENSHOTS': BASE_DIR / 'screenshots',
            'REFERENCE_IMAGES': Path('/Users/<USER>/Documents/automation-tool/ios_data/reference_images'),
            'TEST_SUITES': Path('/Users/<USER>/Documents/automation-tool/ios_data/test_suites'),
            'RESULTS': BASE_DIR / 'reports' / 'suites',
            'RECORDINGS': BASE_DIR / 'recordings',
            'TEMP_FILES': BASE_DIR / 'temp',
        }
    else:  # android
        DEFAULT_DIRECTORIES = {
            'TEST_CASES': Path('/Users/<USER>/Documents/automation-tool/android_data/test_cases'),
            'REPORTS': BASE_DIR / 'reports_android',
            'SCREENSHOTS': BASE_DIR / 'screenshots_android',
            'REFERENCE_IMAGES': Path('/Users/<USER>/Documents/automation-tool/android_data/reference_images'),
            'TEST_SUITES': Path('/Users/<USER>/Documents/automation-tool/android_data/test_suites'),
            'RESULTS': BASE_DIR / 'reports_android' / 'suites',
            'RECORDINGS': BASE_DIR / 'recordings_android',
            'TEMP_FILES': BASE_DIR / 'temp_android',
        }
    
    # Get paths from consolidated database or use defaults
    DIRECTORIES = {}
    for name, default_path in DEFAULT_DIRECTORIES.items():
        # Get path from consolidated database
        db_path = consolidated_db.get_path(name)
        if db_path:
            # Check if it's an absolute or relative path
            path_obj = Path(db_path)
            if path_obj.is_absolute():
                DIRECTORIES[name] = path_obj
            else:
                DIRECTORIES[name] = BASE_DIR / db_path
            logger.info(f"Using database path for {name}: {DIRECTORIES[name]}")
        else:
            DIRECTORIES[name] = default_path
            logger.info(f"Using default path for {name}: {DIRECTORIES[name]}")
            
            # Save default path to consolidated database
            try:
                if default_path.is_absolute():
                    consolidated_db.save_path(name, str(default_path))
                else:
                    consolidated_db.save_path(name, str(default_path.relative_to(BASE_DIR)))
            except Exception as e:
                logger.warning(f"Could not save default path for {name}: {e}")
    
    # Files to push directory
    db_files_to_push = consolidated_db.get_path('FILES_TO_PUSH')
    if db_files_to_push:
        path_obj = Path(db_files_to_push)
        if path_obj.is_absolute():
            FILES_TO_PUSH_DIR = path_obj
        else:
            FILES_TO_PUSH_DIR = BASE_DIR / db_files_to_push
        logger.info(f"Using database path for FILES_TO_PUSH: {FILES_TO_PUSH_DIR}")
    else:
        FILES_TO_PUSH_DIR = Path('/Users/<USER>/Documents/automation-tool/files_to_push')
        logger.info(f"Using default path for FILES_TO_PUSH: {FILES_TO_PUSH_DIR}")
        consolidated_db.save_path('FILES_TO_PUSH', str(FILES_TO_PUSH_DIR))
        
except Exception as e:
    # Fall back to default directories if database access fails
    logger.warning(f"Error accessing consolidated database, using defaults: {str(e)}")
    
    if PLATFORM == 'ios':
        DIRECTORIES = {
            'TEST_CASES': Path('/Users/<USER>/Documents/automation-tool/ios_data/test_cases'),
            'REPORTS': BASE_DIR / 'reports',
            'SCREENSHOTS': BASE_DIR / 'screenshots',
            'REFERENCE_IMAGES': Path('/Users/<USER>/Documents/automation-tool/ios_data/reference_images'),
            'TEST_SUITES': Path('/Users/<USER>/Documents/automation-tool/ios_data/test_suites'),
            'RESULTS': BASE_DIR / 'reports' / 'suites',
            'RECORDINGS': BASE_DIR / 'recordings',
            'TEMP_FILES': BASE_DIR / 'temp',
        }
    else:  # android
        DIRECTORIES = {
            'TEST_CASES': Path('/Users/<USER>/Documents/automation-tool/android_data/test_cases'),
            'REPORTS': BASE_DIR / 'reports_android',
            'SCREENSHOTS': BASE_DIR / 'screenshots_android',
            'REFERENCE_IMAGES': Path('/Users/<USER>/Documents/automation-tool/android_data/reference_images'),
            'TEST_SUITES': Path('/Users/<USER>/Documents/automation-tool/android_data/test_suites'),
            'RESULTS': BASE_DIR / 'reports_android' / 'suites',
            'RECORDINGS': BASE_DIR / 'recordings_android',
            'TEMP_FILES': BASE_DIR / 'temp_android',
        }
    
    FILES_TO_PUSH_DIR = Path('/Users/<USER>/Documents/automation-tool/files_to_push')

# Global values from consolidated database
try:
    GLOBAL_VALUES = consolidated_db.get_all_global_values(PLATFORM)
    if not GLOBAL_VALUES:
        # Set default global values if none exist
        default_globals = {
            'default_element_timeout': '60',
            'Test Run Retry': '2',
            'Auto Rerun Failed': 'False',
            'Test Case Delay': '15',
            'Max Step Execution Time': '300',
            'Connection Retry Attempts': '3',
            'Connection Retry Delay': '2'
        }
        for key, value in default_globals.items():
            consolidated_db.set_global_value(key, value, PLATFORM)
        GLOBAL_VALUES = default_globals
        logger.info(f"Initialized default global values for {PLATFORM}")
    
    logger.info(f"Loaded global values from consolidated database: {GLOBAL_VALUES}")
except Exception as e:
    logger.warning(f"Error loading global values from consolidated database: {e}")
    GLOBAL_VALUES = {
        'default_element_timeout': '60',
        'Test Run Retry': '2',
        'Auto Rerun Failed': 'False',
        'Test Case Delay': '15',
        'Max Step Execution Time': '300',
        'Connection Retry Attempts': '3',
        'Connection Retry Delay': '2'
    }

# Ensure all directories exist
for name, path in DIRECTORIES.items():
    try:
        path.mkdir(parents=True, exist_ok=True)
        logger.info(f"Ensured directory exists: {name} -> {path}")
    except Exception as e:
        logger.warning(f"Could not create directory {name} at {path}: {e}")

# Ensure FILES_TO_PUSH_DIR exists
try:
    FILES_TO_PUSH_DIR.mkdir(parents=True, exist_ok=True)
except Exception as e:
    logger.warning(f"Could not create FILES_TO_PUSH_DIR at {FILES_TO_PUSH_DIR}: {e}")

# ADB Configuration
ADB_CONFIG = {
    'TIMEOUT': 10,  # seconds
    'MAX_RETRIES': 3
}

# Flask Configuration Class
class FlaskConfig:
    SECRET_KEY = os.getenv('FLASK_SECRET_KEY', 'mobile-automation-secret-key')
    TESTING = False
    DEBUG = False
    PORT = PORT
    HOST = '0.0.0.0'
    TEST_CASES_DIR = str(DIRECTORIES['TEST_CASES'])

logger.info("Configuration loaded successfully with consolidated database")
logger.info(f"Platform: {PLATFORM}")
logger.info(f"Port: {PORT}")
logger.info(f"Directories: {DIRECTORIES}")