const fs = require('fs');
const path = require('path');

// Function to generate a timestamp in the format YYYYMMDD_HHMMSS
function getTimestamp() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const hours = String(now.getHours()).padStart(2, '0');
    const minutes = String(now.getMinutes()).padStart(2, '0');
    const seconds = String(now.getSeconds()).padStart(2, '0');

    return year + month + day + '_' + hours + minutes + seconds;
}

// Function to scan the screenshots directory and get a list of available screenshots
function getAvailableScreenshots(reportDir) {
    const screenshotsDir = path.join(reportDir, 'screenshots');
    if (!fs.existsSync(screenshotsDir)) {
        console.log('Screenshots directory not found:', screenshotsDir);
        return [];
    }

    try {
        const files = fs.readdirSync(screenshotsDir);
        const pngFiles = files.filter(file => file.endsWith('.png'));

        // Log information about the screenshots
        console.log(`Found ${pngFiles.length} PNG files in ${screenshotsDir}`);

        // Log all screenshots
        console.log(`Found ${pngFiles.length} screenshots:`);
        pngFiles.forEach(file => {
            console.log(`  - ${file}`);
        });


        return pngFiles;
    } catch (error) {
        console.error('Error reading screenshots directory:', error);
        return [];
    }
}

// Function to parse action_log.txt and extract action_ids
function parseActionLog(reportDir) {
    const actionLogPath = path.join(reportDir, 'action_log.txt');
    if (!fs.existsSync(actionLogPath)) {
        console.log('Action log not found:', actionLogPath);
        return {};
    }

    try {
        const logContent = fs.readFileSync(actionLogPath, 'utf8');
        const lines = logContent.split('\n');

        // Map to store step index to action_id mapping
        const actionIdMap = {};

        // First, look for explicit action_id mentions in the log
        console.log('Looking for action_id mentions in the log...');
        const actionIdLines = lines.filter(line => line.includes('action_id:'));

        // Extract action IDs from log lines and match with action names
        const actionLines = lines.filter(line => line.includes('[INFO] Executing action'));

        // First, extract all action names and their indices
        const actionNameMap = {};
        actionLines.forEach((line, index) => {
            const actionMatch = line.match(/Executing action \d+\/\d+: (.*?)( \(with screenshot\))?$/);
            if (actionMatch) {
                const actionName = actionMatch[1].trim();
                actionNameMap[actionName] = index;
                console.log(`Action ${index}: ${actionName}`);
            }
        });

        // Now match action IDs with action names
        actionIdLines.forEach(line => {
            const actionIdMatch = line.match(/\(action_id: ([a-zA-Z0-9_]+)\)/);
            if (actionIdMatch) {
                const actionId = actionIdMatch[1];

                // Try to find the action name in this line
                const actionNameMatch = line.match(/\[INFO\] (.*?) \(action_id:/);
                if (actionNameMatch) {
                    const actionName = actionNameMatch[1].trim();

                    // If we have this action name in our map, use its index
                    if (actionName in actionNameMap) {
                        const stepIndex = actionNameMap[actionName];
                        actionIdMap[stepIndex] = actionId;
                        console.log(`Found action_id in log: ${actionId} for action "${actionName}" (step ${stepIndex})`);
                    } else {
                        console.log(`Could not find action "${actionName}" in action name map`);
                    }
                } else {
                    console.log(`Could not extract action name from line with action_id ${actionId}`);
                }
            }
        });

        // If we didn't find any action IDs in the log, try to match with screenshots
        if (Object.keys(actionIdMap).length === 0) {
            console.log('No action IDs found in log, trying to match with screenshots...');

            // Look for action execution lines and try to match with screenshots
            const screenshotsDir = path.join(reportDir, 'screenshots');
            if (fs.existsSync(screenshotsDir)) {
                const screenshots = fs.readdirSync(screenshotsDir)
                    .filter(file => file.endsWith('.png'))
                    .map(file => file.replace('.png', ''));

                console.log('Found action ID screenshots:', screenshots);

                // Extract action names and try to match with screenshots
                const actionLines = lines.filter(line => line.includes('[INFO] Executing action'));
                console.log('Found action execution lines:', actionLines.length);

                actionLines.forEach((line, index) => {
                    // Extract action name
                    const actionMatch = line.match(/Executing action \d+\/\d+: (.*?)( \(with screenshot\))?$/);
                    if (actionMatch) {
                        const actionName = actionMatch[1].trim();
                        console.log(`Action ${index}: ${actionName}`);

                        // For now, just assign action IDs in order they appear in the screenshots array
                        if (index < screenshots.length) {
                            actionIdMap[index] = screenshots[index];
                            console.log(`Assigned action ID ${screenshots[index]} to step ${index}`);
                        }
                    }
                });
            }
        }

        return actionIdMap;
    } catch (error) {
        console.error('Error parsing action log:', error);
        return {};
    }
}

// Get sample HTML content
function getSampleContent() {
    // Create a simple test suite data structure
    const sampleData = {
        name: "UI Execution Sample",
        testCases: [
            {
                name: "Sample Test Case",
                status: "passed",
                duration: "1000ms",
                steps: [
                    { name: "Step 1", status: "passed", duration: "500ms" },
                    { name: "Step 2", status: "passed", duration: "500ms" }
                ]
            }
        ],
        passed: 1,
        failed: 0,
        skipped: 0,
        status: "passed"
    };

    // Generate content from the sample data
    return generateContentFromData(sampleData);
}

// Generate HTML content using test suite data
function generateContentFromData(testSuiteData) {
    // Use current timestamp for the report
    const timestamp = new Date().toLocaleString();

    // Start building HTML
    let html = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Suite Report - ${timestamp}</title>
    <link rel="stylesheet" href="./assets/report.css">
    <link rel="stylesheet" href="./assets/custom.css">
</head>
<body>
    <header class="header">
        <h1>Suites</h1>
        <div class="status-summary">
            Status: <span class="status-badge status-badge-${testSuiteData.status || 'unknown'}">${testSuiteData.status || 'Unknown'}</span>
            <span class="stats-summary">
                <span class="passed-count">${testSuiteData.passed || 0}</span> passed,
                <span class="failed-count">${testSuiteData.failed || 0}</span> failed,
                <span class="skipped-count">${testSuiteData.skipped || 0}</span> skipped
            </span>
        </div>
    </header>

    <div class="content">
        <div class="suites-panel">
            <div class="suite-heading">
                <span class="expand-icon"></span>
                ${testSuiteData.name || 'Test Suite'}
            </div>

            <ul class="test-list">`;

    // Add test cases if they exist
    if (testSuiteData.testCases && Array.isArray(testSuiteData.testCases)) {
        testSuiteData.testCases.forEach((testCase, index) => {
            const status = testCase.status || 'unknown';
            // Clean test case name by removing UI control text and .json extension
            let testCaseName = testCase.name || 'Unnamed Test';
            testCaseName = cleanTestCaseName(testCaseName);
            const actionCount = testCase.steps ? testCase.steps.length : 0;

            html += `
                <li class="test-item">
                    <div class="test-header">
                        <div class="test-case" data-actions="${actionCount} actions">
                            <span class="expand-icon"></span>
                            <span class="status-icon status-icon-${status}"></span>
                            #${index + 1} ${testCaseName || 'Unnamed Test'}
                        </div>
                        <span class="test-duration">${testCase.duration || '0ms'}</span>
                    </div>
                    <ul class="test-steps">`;

            // Add steps if they exist
            if (testCase.steps && Array.isArray(testCase.steps)) {
                testCase.steps.forEach((step, stepIndex) => {
                    // Create a unique ID for the step that can be used to reference the screenshot
                    const uniqueStepId = `step-${index}-${stepIndex}`;

                    // Determine screenshot filename - ALWAYS use action_id for consistency
                    let screenshotFilename = '';

                    // Check if we have an action_id to use for the filename
                    if (step.action_id) {
                        // ALWAYS use the action_id for the filename
                        screenshotFilename = `${step.action_id}.png`;
                        console.log(`Step ${uniqueStepId}: Using action_id for screenshot filename: ${screenshotFilename}`);

                        // Get available screenshots from the test data or the current context
                        const availableScreenshotsList = testSuiteData.availableScreenshots || availableScreenshots || [];

                        // Check if this screenshot exists in the available screenshots
                        if (!availableScreenshotsList.includes(screenshotFilename)) {
                            console.log(`Warning: Screenshot ${screenshotFilename} not found in available screenshots`);

                            // Try to find a matching screenshot by action_id
                            const matchingScreenshot = availableScreenshotsList.find(s => s.startsWith(step.action_id));
                            if (matchingScreenshot) {
                                screenshotFilename = matchingScreenshot;
                                console.log(`Found matching screenshot by action_id: ${screenshotFilename}`);
                            }
                        }
                    } else if (step.screenshot_filename) {
                        // Use the screenshot_filename value which should be set by the action
                        screenshotFilename = step.screenshot_filename;
                        console.log(`Step ${uniqueStepId}: Using screenshot_filename ${screenshotFilename}`);
                    } else if (step.report_screenshot) {
                        // Use the report_screenshot value which should be standardized
                        screenshotFilename = step.report_screenshot;
                        console.log(`Step ${uniqueStepId}: Using report_screenshot ${screenshotFilename}`);
                    } else if (step.screenshot) {
                        // Just get the filename part if a full path is provided
                        screenshotFilename = step.screenshot.split('/').pop();
                        console.log(`Step ${uniqueStepId}: Extracted screenshot ${screenshotFilename} from path`);
                    } else {
                        // Generate a new action_id and use it for the filename
                        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
                        let actionId = '';
                        for (let i = 0; i < 10; i++) {
                            actionId += chars.charAt(Math.floor(Math.random() * chars.length));
                        }
                        step.action_id = actionId;
                        screenshotFilename = `${actionId}.png`;
                        console.log(`Step ${uniqueStepId}: Generated new action_id for screenshot: ${screenshotFilename}`);
                    }

                    // Store the screenshot path in the step object for reference in the script
                    step.resolved_screenshot = `screenshots/${screenshotFilename}`;

                    // Also store the action_id in the step object for reference
                    if (step.action_id) {
                        step.action_id_screenshot = `screenshots/${step.action_id}.png`;
                    }

                    // Use action_id for data-screenshot if available
                    let dataScreenshot = '';
                    if (step.action_id) {
                        dataScreenshot = `data-screenshot="${step.action_id}.png"`;
                        console.log(`Step ${uniqueStepId}: Using action_id for data-screenshot: ${step.action_id}.png`);
                    } else {
                        dataScreenshot = `data-screenshot="${screenshotFilename}"`;
                        console.log(`Step ${uniqueStepId}: Using screenshot filename for data-screenshot: ${screenshotFilename}`);
                    }

                    // Log the screenshot assignment for debugging
                    console.log(`Step ${uniqueStepId}: Using screenshot ${screenshotFilename}`);

                    // Add action_id to the display if available
                    const actionIdDisplay = step.action_id ?
                        `<span class="action-id-badge" title="Action ID: ${step.action_id}">${step.action_id}</span>` : '';

                    html += `
                        <li class="test-step" data-step-id="${stepIndex + 1}" data-status="${step.status || 'unknown'}"
                            ${dataScreenshot} data-action-id="${step.action_id || ''}" onclick="showStepDetails('${uniqueStepId}')">
                            <div class="test-step-name">
                                <span class="status-icon status-icon-${step.status || 'unknown'}"></span>
                                ${step.name || 'Unnamed Step'} ${actionIdDisplay}
                            </div>
                            <span class="test-step-duration">${step.duration || '0ms'}</span>
                        </li>`;
                });
            }

            html += `
                    </ul>
                </li>`;
        });
    }

    html += `
            </ul>
        </div>

        <div class="details-panel" id="details-panel">
            <!-- This will be populated by JavaScript when a test step is clicked -->
            <h3>Click on a test step to view details</h3>
        </div>
    </div>

    <script>
        // Store the test data for our JavaScript to use
        const testData = ${JSON.stringify({
            ...testSuiteData,
            screenshots_map: testSuiteData.screenshots_map || {}
        })};
    </script>

    <script src="./assets/report.js"></script>
</body>
</html>`;

    return html;
}

// Clean test case name by removing UI control text
function cleanTestCaseName(name) {
    if (!name) return 'Unnamed Test';

    // Remove common UI control text patterns
    let cleanName = name
        .replace(/\s*Retry\s*/gi, '')
        .replace(/\s*Stop\s*/gi, '')
        .replace(/\s*Edit\s*/gi, '')
        .replace(/\s*Remove\s*/gi, '')
        .replace(/\s*Delete\s*/gi, '')
        .replace(/\s*Cancel\s*/gi, '')
        .replace(/\s*Save\s*/gi, '')
        .replace(/\s*Load\s*/gi, '')
        .replace(/\s*Import\s*/gi, '')
        .replace(/\s*Export\s*/gi, '')
        .replace(/\.json$/i, '') // Remove .json extension
        .replace(/\s+/g, ' ') // Replace multiple spaces with single space
        .trim();

    // If the name becomes empty after cleaning, use a default
    return cleanName || 'Unnamed Test';
}

// Generate report with specified output path or default timestamp-based path
function generateReport(dataFilePath, outputPath = null) {
    const timestamp = getTimestamp();

    // If no output path is specified, use the default path
    let reportPath;
    let reportDir;
    if (outputPath) {
        reportPath = outputPath;
        reportDir = path.dirname(reportPath);
    } else {
        const reportName = 'TestSuite_Execution_' + timestamp + '.html';
        reportPath = path.join(__dirname, reportName);
        reportDir = path.dirname(reportPath);
    }

    // Ensure assets directory exists
    const assetsDir = path.join(reportDir, 'assets');
    if (!fs.existsSync(assetsDir)) {
        fs.mkdirSync(assetsDir, { recursive: true });
    }

    // Get available screenshots and action IDs
    const availableScreenshots = getAvailableScreenshots(reportDir);
    console.log('Available screenshots:', availableScreenshots);

    // Parse action log to get action IDs
    const actionIdMap = parseActionLog(reportDir);
    console.log('Action ID map:', actionIdMap);

    // Define CSS content directly if the file doesn't exist
    const cssPath = path.join(assetsDir, 'report.css');
    if (!fs.existsSync(cssPath)) {
        const cssContent = `/* Test Report Styling */

/* General Styles */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
    line-height: 1.5;
    color: #333;
    background-color: #f5f5f7;
    margin: 0;
    padding: 0;
}

/* Header Styles */
.header {
    background-color: #fff;
    padding: 20px;
    border-bottom: 1px solid #e1e4e8;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.header h1 {
    font-size: 24px;
    font-weight: 600;
    margin-bottom: 8px;
}

.status-summary {
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.status-badge {
    display: inline-block;
    padding: 3px 8px;
    border-radius: 4px;
    font-weight: 500;
    font-size: 12px;
    text-transform: uppercase;
}

.status-badge-passed {
    background-color: #28a745;
    color: white;
}

.status-badge-failed {
    background-color: #dc3545;
    color: white;
}

.status-badge-skipped {
    background-color: #ffc107;
    color: #212529;
}

.passed-count {
    color: #28a745;
    font-weight: 600;
}

.failed-count {
    color: #dc3545;
    font-weight: 600;
}

.skipped-count {
    color: #ffc107;
    font-weight: 600;
}

/* Content Layout */
.content {
    display: flex;
    min-height: calc(100vh - 80px);
}

.suites-panel {
    flex: 0 0 60%;
    background-color: #fff;
    border-right: 1px solid #e1e4e8;
    overflow-y: auto;
}

.details-panel {
    flex: 1;
    padding: 20px;
    background-color: #f8f9fa;
    overflow-y: auto;
}

/* Suite Styles */
.suite-heading {
    padding: 15px 20px;
    font-size: 16px;
    font-weight: 600;
    background-color: #f1f3f5;
    border-bottom: 1px solid #e1e4e8;
    display: flex;
    align-items: center;
    cursor: pointer;
}

.expand-icon {
    display: inline-block;
    width: 16px;
    height: 16px;
    margin-right: 8px;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 16 16'%3E%3Cpath fill='%23333' d='M6 12l4-4-4-4z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: center;
    transition: transform 0.2s;
}

.expanded .expand-icon {
    transform: rotate(90deg);
}

/* Test List Styles */
.test-list {
    list-style: none;
}

.test-item {
    border-bottom: 1px solid #e1e4e8;
}

.test-header {
    padding: 12px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    cursor: pointer;
}

.test-case {
    display: flex;
    align-items: center;
    font-size: 14px;
    line-height: 1.4;
    flex-wrap: wrap;
}

.test-case:after {
    content: attr(data-actions);
    display: block;
    width: 100%;
    margin-top: 4px;
    font-size: 12px;
    color: #555;
    padding-left: 20px;
}

.test-duration {
    font-size: 12px;
    color: #6c757d;
}

.status-icon {
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-right: 8px;
    border-radius: 50%;
}

.status-icon-passed {
    background-color: #28a745;
}

.status-icon-failed {
    background-color: #dc3545;
}

.status-icon-skipped {
    background-color: #ffc107;
}

.status-icon-unknown {
    background-color: #6c757d;
}

/* Test Steps Styles */
.test-steps {
    list-style: none;
    background-color: #f8f9fa;
    display: none;
}

.test-item.expanded .test-steps {
    display: block;
}

.test-step {
    padding: 8px 20px 8px 40px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #e9ecef;
    cursor: pointer;
    transition: background-color 0.2s;
}

.test-step:hover {
    background-color: #e9ecef;
}

.test-step.active {
    background-color: #e2e6ea;
    border-left: 3px solid #007bff;
}

/* Failed step highlighting */
.test-step[data-status="failed"] {
    background-color: #f8d7da !important;
    border-left: 3px solid #dc3545 !important;
}

.test-step[data-status="failed"]:hover {
    background-color: #f5c6cb !important;
}

.test-step[data-status="failed"] .test-step-name {
    color: #721c24;
    font-weight: 500;
}

.test-step-name {
    display: flex;
    align-items: flex-start;
    font-size: 13px;
    overflow-wrap: break-word;
    word-break: break-word;
    width: 80%;
}

.test-step-duration {
    font-size: 12px;
    color: #6c757d;
    flex-shrink: 0;
    text-align: right;
}

/* Action ID Badge */
.action-id-badge {
    display: inline-block;
    font-size: 10px;
    background-color: #e9ecef;
    color: #495057;
    padding: 1px 4px;
    border-radius: 3px;
    margin-left: 5px;
    font-family: monospace;
    vertical-align: middle;
}

/* Step Details */
.step-details {
    margin: 20px;
    padding: 15px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.step-details h3 {
    margin-bottom: 10px;
    font-size: 16px;
    font-weight: 600;
}

.screenshot-container {
    margin: 15px 0;
    text-align: center;
}

.action-id {
    font-size: 13px;
    color: #333;
    margin-bottom: 8px;
    font-family: monospace;
    background-color: #f8f9fa;
    padding: 3px 6px;
    border-radius: 3px;
    display: inline-block;
}

.action-id-value {
    font-weight: bold;
    color: #007bff;
}

.screenshot-container img {
    max-width: 50%;  /* Reduced from 100% to 50% */
    max-height: 400px; /* Added max height */
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    object-fit: contain; /* Maintain aspect ratio */
}

/* Error Details */
.error-details {
    margin-top: 15px;
    border-top: 1px solid #f5c6cb;
    padding-top: 15px;
}

.error-details h4 {
    color: #dc3545;
    margin-bottom: 10px;
    font-size: 16px;
}

.error-details pre {
    background-color: #f8f9fa;
    border: 1px solid #eaecef;
    border-radius: 4px;
    padding: 10px;
    overflow-x: auto;
    font-size: 13px;
    color: #e83e8c;
}

/* Responsive adjustments */
@media screen and (max-width: 992px) {
    .content {
        flex-direction: column;
    }

    .suites-panel {
        flex: 0 0 auto;
        border-right: none;
        border-bottom: 1px solid #e1e4e8;
    }
}`;
        fs.writeFileSync(cssPath, cssContent);
    }

    // Define JS content directly if the file doesn't exist
    const jsPath = path.join(assetsDir, 'report.js');
    if (!fs.existsSync(jsPath)) {
        const jsContent = `// Report interaction JavaScript

document.addEventListener('DOMContentLoaded', function() {
    // Initialize expand/collapse functionality for test suites
    const suiteHeadings = document.querySelectorAll('.suite-heading');
    suiteHeadings.forEach(heading => {
        heading.addEventListener('click', function() {
            const parentElement = this.parentElement;
            parentElement.classList.toggle('expanded');
            const testList = parentElement.querySelector('.test-list');
            if (testList) {
                testList.style.display = parentElement.classList.contains('expanded') ? 'block' : 'none';
            }
        });
    });

    // Initialize expand/collapse functionality for test cases
    const testHeaders = document.querySelectorAll('.test-header');
    testHeaders.forEach(header => {
        header.addEventListener('click', function() {
            const testItem = this.parentElement;
            testItem.classList.toggle('expanded');
            const steps = testItem.querySelector('.test-steps');
            if (steps) {
                steps.style.display = testItem.classList.contains('expanded') ? 'block' : 'none';
            }
        });
    });

    // Expand all suites by default
    suiteHeadings.forEach(heading => {
        const parentElement = heading.parentElement;
        parentElement.classList.add('expanded');
        const testList = parentElement.querySelector('.test-list');
        if (testList) {
            testList.style.display = 'block';
        }
    });

    // Simple function to handle missing screenshots
    window.handleMissingScreenshot = function(imgElement) {
        // Basic fallback if fetching directory is not supported
        imgElement.style.display = 'none';
        const noScreenshotMsg = imgElement.nextElementSibling;
        if (noScreenshotMsg) {
            noScreenshotMsg.style.display = 'block';
            noScreenshotMsg.style.color = 'rgb(108, 117, 125)';
        }
    };

    // Function to show step details
    window.showStepDetails = function(stepId) {
        const detailsPanel = document.getElementById('details-panel');
        if (!detailsPanel) return;

        const [_, testCaseIndex, stepIndex] = stepId.split('-');

        // Get the test data from the script that has it
        const testDataScript = document.querySelector('script:not([src])');
        if (!testDataScript) {
            detailsPanel.innerHTML = '<h3>Error: Test data not found</h3>';
            return;
        }

        try {
            // Extract the test data from the script content
            const scriptContent = testDataScript.textContent;
            const testDataMatch = scriptContent.match(/const testData = (.*?);/s);
            if (!testDataMatch) {
                detailsPanel.innerHTML = '<h3>Error: Test data format not recognized</h3>';
                return;
            }

            const testDataString = testDataMatch[1];
            const testData = JSON.parse(testDataString);

            const testCase = testData.testCases[testCaseIndex];
            const step = testCase.steps[stepIndex];

            // Create content
            // Escape HTML special characters in step name and test case name
            const escapedStepName = (step.name || 'Unnamed Step')
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#039;');

            const escapedTestCaseName = (testCase.name || 'Unnamed Test Case')
                .replace(/&/g, '&amp;')
                .replace(/</g, '&lt;')
                .replace(/>/g, '&gt;')
                .replace(/"/g, '&quot;')
                .replace(/'/g, '&#039;');

            let detailsContent = '<div class="step-details">' +
                '<h3>' + escapedStepName + '</h3>' +
                '<p><strong>Status:</strong> <span class="status-badge status-badge-' + step.status + '">' + step.status + '</span></p>' +
                '<p><strong>Duration:</strong> ' + step.duration + '</p>' +
                '<p><strong>Test Case:</strong> ' + escapedTestCaseName + '</p>';

            // Get screenshot path from the resolved_screenshot property
            let imgSrc = '';

            // ALWAYS use the action_id for the screenshot if available
            if (step.action_id) {
                // Use the action_id directly for the screenshot
                imgSrc = 'screenshots/' + step.action_id + '.png';
                console.log('Using action_id for screenshot: ' + imgSrc);

                // Set up fallback paths for onerror
                step.fallback_paths = [];

                // Add fallback for prefixed version (with al_)
                if (!step.action_id.startsWith('al_')) {
                    step.fallback_paths.push('screenshots/al_' + step.action_id + '.png');
                }

                // Add the action ID to the step details for display
                const stepActionIdElement = document.getElementById('stepActionId');
                if (stepActionIdElement) {
                    stepActionIdElement.textContent = 'Action ID: ' + step.action_id;
                    stepActionIdElement.style.display = 'block';
                }
            }
            // If no action_id, fall back to other options
            else if (step.resolved_screenshot) {
                imgSrc = step.resolved_screenshot;
                console.log('Using resolved_screenshot: ' + imgSrc);
                const stepActionIdElement = document.getElementById('stepActionId');
                if (stepActionIdElement) {
                    stepActionIdElement.style.display = 'none';
                }
            }
            else if (step.alt_screenshot) {
                imgSrc = step.alt_screenshot;
                console.log('Using alternative screenshot: ' + imgSrc);
                const stepActionIdElement = document.getElementById('stepActionId');
                if (stepActionIdElement) {
                    stepActionIdElement.style.display = 'none';
                }
            }
            else {
                imgSrc = '';
                console.log('No screenshot available for this step');
                const stepActionIdElement = document.getElementById('stepActionId');
                if (stepActionIdElement) {
                    stepActionIdElement.style.display = 'none';
                }
            }

            // Add data-step-id attribute to help with debugging
            // Get the action_id if available
            const actionId = step.action_id || '';

            // Add action_id display if available
            let actionIdHtml = '';
            if (actionId) {
                actionIdHtml = '<div class="action-id">Action ID: <span class="action-id-value">' + actionId + '</span></div>';
            }

            // Create fallback onerror handler with multiple paths to try
            let onerrorHandler = 'onerror="';

            // Add fallbacks for different screenshot naming formats
            if (step.fallback_paths && step.fallback_paths.length > 0) {
                // Create a chain of fallbacks
                for (let i = 0; i < step.fallback_paths.length; i++) {
                    const fallbackPath = step.fallback_paths[i];
                    onerrorHandler += 'this.onerror=null; this.src=\'' + fallbackPath + '\'; ';
                }
                // Final fallback to handle missing screenshot
                onerrorHandler += 'this.onerror=function(){handleMissingScreenshot(this);}';
            } else {
                // Default handler if no fallbacks
                onerrorHandler += 'handleMissingScreenshot(this)';
            }
            onerrorHandler += '"';

            // Only show screenshots for takeScreenshot actions
            const stepName = (step.name || '').toLowerCase();
            const isTakeScreenshotAction = stepName.includes('takescreenshot');

            if (isTakeScreenshotAction) {
                detailsContent += '<div class="screenshot-container">' +
                    '<h4>Screenshot:</h4>' +
                    '<p id="stepActionId" style="display: none;">Action ID: ' + actionId + '</p>' +
                    '<img src="' + imgSrc + '" alt="Step Screenshot" data-step-id="' + stepIndex + '" ' +
                    onerrorHandler + ' />' +
                    '<p id="no-screenshot-' + stepIndex + '" style="display: none;">No screenshot available for this step</p>' +
                    '</div>';
            }

            // Add error info if present
            if (step.error) {
                // Escape HTML special characters in error message
                const escapedError = (step.error || '')
                    .replace(/&/g, '&amp;')
                    .replace(/</g, '&lt;')
                    .replace(/>/g, '&gt;')
                    .replace(/"/g, '&quot;')
                    .replace(/'/g, '&#039;');

                detailsContent += '<div class="error-details">' +
                    '<h4>Error:</h4>' +
                    '<pre>' + escapedError + '</pre>' +
                    '</div>';
            }

            detailsContent += '</div>';

            detailsPanel.innerHTML = detailsContent;
        } catch (error) {
            detailsPanel.innerHTML = '<h3>Error processing test data: ' + error.message + '</h3>';
        }
    };

    // Highlight active step when clicked
    const testSteps = document.querySelectorAll('.test-step');
    testSteps.forEach(step => {
        step.addEventListener('click', function() {
            // Remove highlight from all steps
            testSteps.forEach(s => s.classList.remove('active'));
            // Add highlight to current step
            this.classList.add('active');
        });
    });
});`;
        fs.writeFileSync(jsPath, jsContent);
    }

    let content;

    // Check if we have a data file path
    if (dataFilePath && fs.existsSync(dataFilePath)) {
        try {
            // Read and parse the JSON file
            const rawData = fs.readFileSync(dataFilePath, 'utf8');
            const testSuiteData = JSON.parse(rawData);

            // Enhance the test data with action IDs and available screenshots
            if (testSuiteData.testCases && Array.isArray(testSuiteData.testCases)) {
                testSuiteData.testCases.forEach((testCase, testCaseIndex) => {
                    if (testCase.steps && Array.isArray(testCase.steps)) {
                        testCase.steps.forEach((step, stepIndex) => {
                            // First, check if there are any action_id-based screenshots in the available screenshots
                            // Look for 10-character alphanumeric filenames (without 'al_' prefix)
                            const actionIdScreenshots = availableScreenshots.filter(s =>
                                s.endsWith('.png') &&
                                (s.match(/^[a-zA-Z0-9]{10}\.png$/) || s.match(/^al_[a-zA-Z0-9]{10}\.png$/))
                            );
                            console.log(`Found ${actionIdScreenshots.length} action_id-based screenshots`);

                            // Try to extract action_id from step name if it exists
                            let actionIdFromName = null;
                            if (step.name) {
                                // Look for both formats: with or without 'al_' prefix
                                const actionIdMatch = step.name.match(/(al_[a-zA-Z0-9]{10})|([a-zA-Z0-9]{10})/);
                                if (actionIdMatch) {
                                    // Use the first match that's not undefined
                                    actionIdFromName = actionIdMatch[1] || actionIdMatch[2];
                                    // If it starts with 'al_', remove the prefix
                                    if (actionIdFromName && actionIdFromName.startsWith('al_')) {
                                        actionIdFromName = actionIdFromName.substring(3);
                                    }
                                    console.log(`Extracted action_id from step name: ${actionIdFromName}`);
                                }
                            }

                            // Check if we have a screenshot with this action_id from the name
                            if (actionIdFromName) {
                                const actionIdScreenshot = `${actionIdFromName}.png`;
                                if (availableScreenshots.includes(actionIdScreenshot)) {
                                    // Update the step's action_id to match the one from the name
                                    step.action_id = actionIdFromName;
                                    step.action_id_screenshot = `screenshots/${actionIdScreenshot}`;
                                    step.screenshot_filename = actionIdScreenshot;
                                    step.report_screenshot = actionIdScreenshot;
                                    step.resolved_screenshot = `screenshots/${actionIdScreenshot}`;
                                    console.log(`Found screenshot for action_id from name ${actionIdFromName}: ${step.action_id_screenshot}`);
                                }
                            }
                            // If we already have an action_id in the step, check if there's a matching screenshot
                            else if (step.action_id) {
                                // If action_id starts with 'al_', remove the prefix
                                let cleanActionId = step.action_id;
                                if (cleanActionId.startsWith('al_')) {
                                    cleanActionId = cleanActionId.substring(3);
                                    // Update the step's action_id to the clean version
                                    step.action_id = cleanActionId;
                                    console.log(`Removed 'al_' prefix from action_id: ${step.action_id}`);
                                }

                                // Store both versions of the action_id for fallback
                                step.clean_action_id = cleanActionId;
                                step.prefixed_action_id = `al_${cleanActionId}`;

                                // Try both with and without 'al_' prefix
                                const actionIdScreenshot = `${cleanActionId}.png`;
                                const prefixedActionIdScreenshot = `al_${cleanActionId}.png`;

                                if (availableScreenshots.includes(actionIdScreenshot)) {
                                    step.action_id_screenshot = `screenshots/${actionIdScreenshot}`;
                                    step.screenshot_filename = actionIdScreenshot;
                                    step.report_screenshot = actionIdScreenshot;
                                    step.resolved_screenshot = `screenshots/${actionIdScreenshot}`;
                                    console.log(`Found screenshot for existing action_id ${cleanActionId}: ${step.action_id_screenshot}`);
                                }
                                // Also check for the prefixed version
                                else if (availableScreenshots.includes(prefixedActionIdScreenshot)) {
                                    step.action_id_screenshot = `screenshots/${actionIdScreenshot}`; // Still use clean version in report
                                    step.screenshot_filename = actionIdScreenshot; // Still use clean version in report
                                    step.report_screenshot = actionIdScreenshot; // Still use clean version in report
                                    step.resolved_screenshot = `screenshots/${actionIdScreenshot}`; // Still use clean version in report
                                    step.original_screenshot = prefixedActionIdScreenshot; // Store original for reference
                                    console.log(`Found screenshot with 'al_' prefix for action_id ${cleanActionId}: ${prefixedActionIdScreenshot}`);
                                }
                                // If no matching screenshot, try to find one by step index
                                else if (actionIdScreenshots.length > stepIndex) {
                                    // Use the action_id from the screenshot filename
                                    let actionIdFromScreenshot = actionIdScreenshots[stepIndex].replace('.png', '');
                                    // If it starts with 'al_', remove the prefix
                                    if (actionIdFromScreenshot.startsWith('al_')) {
                                        actionIdFromScreenshot = actionIdFromScreenshot.substring(3);
                                    }

                                    step.action_id = actionIdFromScreenshot;
                                    step.action_id_screenshot = `screenshots/${actionIdFromScreenshot}.png`;
                                    step.screenshot_filename = `${actionIdFromScreenshot}.png`;
                                    step.report_screenshot = `${actionIdFromScreenshot}.png`;
                                    step.resolved_screenshot = `screenshots/${actionIdFromScreenshot}.png`;
                                    console.log(`Assigned action_id from screenshot ${actionIdFromScreenshot} to step ${stepIndex}`);
                                }
                            }
                            // If no action_id in the step, try to assign one from available screenshots
                            else if (actionIdScreenshots.length > stepIndex) {
                                // Use the action_id from the screenshot filename
                                let actionIdFromScreenshot = actionIdScreenshots[stepIndex].replace('.png', '');
                                // If it starts with 'al_', remove the prefix
                                if (actionIdFromScreenshot.startsWith('al_')) {
                                    actionIdFromScreenshot = actionIdFromScreenshot.substring(3);
                                }

                                step.action_id = actionIdFromScreenshot;
                                step.action_id_screenshot = `screenshots/${actionIdFromScreenshot}.png`;
                                step.screenshot_filename = `${actionIdFromScreenshot}.png`;
                                step.report_screenshot = `${actionIdFromScreenshot}.png`;
                                step.resolved_screenshot = `screenshots/${actionIdFromScreenshot}.png`;
                                console.log(`Assigned action_id from screenshot ${actionIdFromScreenshot} to step ${stepIndex}`);
                            }
                        });
                    }
                });
            }

            // Write the updated test data back to the data.json file
            if (dataFilePath) {
                try {
                    fs.writeFileSync(dataFilePath, JSON.stringify(testSuiteData, null, 2), 'utf8');
                    console.log(`Updated data.json file with correct action_id values: ${dataFilePath}`);
                } catch (error) {
                    console.error('Error writing updated data.json file:', error.message);
                }
            }

            // Add available screenshots to the test data
            testSuiteData.availableScreenshots = availableScreenshots;

            // Generate content from the enhanced data
            content = generateContentFromData(testSuiteData);
        } catch (error) {
            console.error('Error processing data file:', error.message);
            content = getSampleContent(); // Fallback to sample content
        }
    } else {
        // No data file, use sample content
        content = getSampleContent();
    }

    // Write to file
    fs.writeFileSync(reportPath, content, 'utf8');

    console.log(reportPath);
    return reportPath;
}

// When called directly, generate a report
if (require.main === module) {
    // Check if a data file path is provided as an argument
    const dataFilePath = process.argv.length > 2 ? process.argv[2] : null;
    // Check if an output path is provided as an argument
    const outputPath = process.argv.length > 3 ? process.argv[3] : null;
    generateReport(dataFilePath, outputPath);
}

module.exports = {
    generateReport
};