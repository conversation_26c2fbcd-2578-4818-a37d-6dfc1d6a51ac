import os
import json
import time
import sqlite3
import logging
from pathlib import Path
import uuid
import sys
from datetime import datetime
from typing import List, Dict, Any, Optional

# Add parent directory to path to import config
parent_dir = Path(__file__).resolve().parent.parent
if str(parent_dir) not in sys.path:
    sys.path.insert(0, str(parent_dir))
from config import DIRECTORIES

# Import platform-specific database adapter
try:
    # Add parent directory to path to access root utils
    parent_dir = Path(__file__).resolve().parent.parent
    if str(parent_dir) not in sys.path:
        sys.path.insert(0, str(parent_dir))
    from utils.platform_db_adapter import PlatformDBAdapter
    USE_PLATFORM_DB = True
except ImportError:
    USE_PLATFORM_DB = False

logger = logging.getLogger(__name__)

class TestSuitesManager:
    def __init__(self):
        self.suites_dir = Path(DIRECTORIES['TEST_SUITES'])
        self.suites_dir.mkdir(parents=True, exist_ok=True)
        self.logger = logging.getLogger(__name__)
        
        # Initialize database connection
        self.db_path = None
        self._init_database_connection()
    
    def _init_database_connection(self):
        """Initialize database connection for test suites"""
        try:
            from utils.database import get_db_path
            self.db_path = get_db_path('test_suites')
            self.logger.info(f"Database connection initialized: {self.db_path}")

            # Verify the database file exists
            if not os.path.exists(self.db_path):
                self.logger.warning(f"Database file not found: {self.db_path}")
                self.db_path = None
        except Exception as e:
            self.logger.error(f"Failed to initialize database connection: {e}")
            self.db_path = None

    def load_test_suites(self) -> List[Dict[str, Any]]:
        """Load all test suites using hybrid approach (database + files)"""
        return self.load_test_suites_hybrid()
    
    def load_test_suites_hybrid(self) -> List[Dict[str, Any]]:
        """Load test suites from database first, then from files for any missing ones"""
        suites = []
        db_suite_files = set()
        
        # First, try to load from database
        if self.db_path:
            try:
                with sqlite3.connect(self.db_path) as conn:
                    cursor = conn.cursor()
                    cursor.execute("""
                        SELECT suite_id, name, description, file_path, test_case_count, 
                               created_at, updated_at, status, platform, json_payload
                        FROM test_suites 
                        WHERE status = 'active' AND platform = 'ios'
                        ORDER BY updated_at DESC
                    """)
                    
                    for row in cursor.fetchall():
                        suite_id, name, description, file_path, test_case_count, created_at, updated_at, status, platform, json_payload = row
                        
                        suite_data = {
                            'id': suite_id,
                            'name': name or 'Unnamed Suite',
                            'description': description or '',
                            'test_cases': [],
                            'test_case_count': test_case_count or 0,
                            'created': created_at,
                            'updated': updated_at,
                            'status': status,
                            'platform': platform,
                            'file_path': file_path
                        }
                        
                        # Extract test cases from JSON payload if available
                        if json_payload:
                            try:
                                if isinstance(json_payload, str):
                                    json_data = json.loads(json_payload)
                                else:
                                    json_data = json_payload
                                
                                if isinstance(json_data, dict) and 'test_cases' in json_data:
                                    suite_data['test_cases'] = json_data['test_cases']
                            except (json.JSONDecodeError, TypeError) as e:
                                self.logger.warning(f"Failed to parse JSON payload for suite {suite_id}: {e}")
                        
                        suites.append(suite_data)
                        
                        # Track which files we've loaded from database
                        if file_path:
                            db_suite_files.add(Path(file_path).name)
                            
                    self.logger.info(f"Loaded {len(suites)} test suites from database")
            except Exception as e:
                self.logger.error(f"Error loading test suites from database: {e}")
        
        # Then load any remaining test suites from JSON files not in database
        try:
            for file in self.suites_dir.glob('*.json'):
                if file.name not in db_suite_files:
                    try:
                        with open(file, 'r') as f:
                            suite_data = json.load(f)
                            
                        # Ensure required fields
                        suite_data.setdefault('id', file.stem)
                        suite_data.setdefault('name', file.stem.replace('_', ' ').title())
                        suite_data.setdefault('description', '')
                        suite_data.setdefault('test_cases', [])
                        suite_data.setdefault('created', '')
                        suite_data.setdefault('updated', '')
                        suite_data.setdefault('status', 'active')
                        
                        # Add file metadata
                        stat = file.stat()
                        if not suite_data['created']:
                            suite_data['created'] = datetime.fromtimestamp(stat.st_ctime).isoformat()
                        if not suite_data['updated']:
                            suite_data['updated'] = datetime.fromtimestamp(stat.st_mtime).isoformat()
                        
                        suite_data['test_case_count'] = len(suite_data.get('test_cases', []))
                        suite_data['file_path'] = str(file)
                        
                        suites.append(suite_data)
                        self.logger.debug(f"Loaded test suite from file: {file.name}")
                        
                    except Exception as e:
                        self.logger.error(f"Error loading test suite {file}: {e}")
            
            self.logger.info(f"Total test suites loaded: {len(suites)}")
        except Exception as e:
            self.logger.error(f"Error loading test suites from files: {e}")
        
        return sorted(suites, key=lambda x: x.get('updated', ''), reverse=True)
    
    def sync_to_database(self, suite_data: Dict[str, Any], file_path: str = None) -> bool:
        """Sync test suite data to database"""
        if not self.db_path:
            self.logger.warning("No database connection available for sync")
            return False
        
        try:
            # Generate suite_id if not present
            if 'id' not in suite_data or not suite_data['id']:
                suite_data['id'] = str(uuid.uuid4())
            
            suite_id = suite_data['id']
            name = suite_data.get('name', 'Unnamed Suite')
            description = suite_data.get('description', '')
            test_case_count = len(suite_data.get('test_cases', []))
            
            # Prepare JSON payload
            json_payload = json.dumps(suite_data)
            
            # Get current timestamp
            current_time = datetime.now().isoformat()
            
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.cursor()
                
                # Insert or replace test suite
                cursor.execute("""
                    INSERT OR REPLACE INTO test_suites 
                    (suite_id, name, description, file_path, test_case_count, 
                     created_at, updated_at, status, platform, json_payload)
                    VALUES (?, ?, ?, ?, ?, 
                            COALESCE((SELECT created_at FROM test_suites WHERE suite_id = ?), ?),
                            ?, 'active', 'ios', ?)
                """, (
                    suite_id, name, description, file_path, test_case_count,
                    suite_id, current_time, current_time, json_payload
                ))
                
                conn.commit()
                self.logger.info(f"Synced test suite {suite_id} to database")
                return True
                
        except Exception as e:
            self.logger.error(f"Failed to sync test suite to database: {e}")
            return False

    def create_test_suite(self, name, description, test_cases):
        """Create a new test suite"""
        suite_id = str(uuid.uuid4())
        suite_data = {
            'id': suite_id,
            'name': name,
            'description': description,
            'test_cases': test_cases,
            'created': time.strftime("%Y-%m-%d %H:%M:%S"),
            'updated': time.strftime("%Y-%m-%d %H:%M:%S")
        }

        file_path = self.suites_dir / f"{suite_id}.json"
        with open(file_path, 'w') as f:
            json.dump(suite_data, f, indent=4)

        # Sync to database
        self.sync_to_database(suite_data, str(file_path))

        return suite_data

    def update_test_suite(self, suite_id, updated_data):
        """Update an existing test suite by ID"""
        file_path = self.suites_dir / f"{suite_id}.json"
        if not file_path.exists():
            return None # Indicate suite not found

        try:
            # Prepare data for saving (ensure ID is consistent, add updated timestamp)
            suite_data_to_save = updated_data.copy()
            suite_data_to_save['id'] = suite_id # Ensure ID matches the file
            suite_data_to_save['updated'] = time.strftime("%Y-%m-%d %H:%M:%S")
            # Preserve original creation time if it exists in the file
            try:
                 with open(file_path, 'r') as f_read:
                    original_data = json.load(f_read)
                    if 'created' in original_data:
                         suite_data_to_save['created'] = original_data['created']
                    else: # Add created time if missing from original
                         suite_data_to_save['created'] = suite_data_to_save['updated']
            except Exception:
                 # If reading fails, just use current time for created too
                 suite_data_to_save['created'] = suite_data_to_save['updated']

            # Overwrite the file with updated data
            with open(file_path, 'w') as f:
                json.dump(suite_data_to_save, f, indent=4)

            # Save to database
            try:
                from app.utils.database import save_test_suite
                # Prepare data for database
                db_suite_data = {
                    'id': suite_id,
                    'name': suite_data_to_save.get('name', 'Unknown Suite'),
                    'status': 'updated',
                    'passed': 0,
                    'failed': 0,
                    'skipped': 0,
                    'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
                    'testCases': []
                }
                save_test_suite(db_suite_data)
                print(f"Saved updated test suite {suite_id} to database")
            except Exception as db_error:
                print(f"Error saving updated test suite to database: {str(db_error)}")

            # Return the updated data (which includes the ID)
            return suite_data_to_save
        except Exception as e:
            print(f"Error updating test suite {suite_id}: {str(e)}") # Use logger if available
            # logger.error(f"Error updating test suite {suite_id}: {str(e)}")
            return None # Indicate failure

    def duplicate_test_suite(self, suite_id):
        """Duplicate a test suite by ID

        Args:
            suite_id: The ID of the test suite to duplicate

        Returns:
            dict: The duplicated test suite data, or None if failed
        """
        # Load the original test suite
        file_path = self.suites_dir / f"{suite_id}.json"
        if not file_path.exists():
            print(f"Cannot duplicate non-existent test suite: {suite_id}")
            return None

        try:
            # Load the original data
            with open(file_path, 'r') as f:
                original_data = json.load(f)

            # Create a deep copy of the data
            new_data = json.loads(json.dumps(original_data))

            # Generate a new name with timestamp
            original_name = new_data.get('name', 'Unnamed Suite')
            timestamp = time.strftime("%Y%m%d%H%M%S")
            new_data['name'] = f"{original_name}_Copy_{timestamp}"

            # Create a new test suite with the copied data
            return self.create_test_suite(
                name=new_data['name'],
                description=new_data.get('description', ''),
                test_cases=new_data.get('test_cases', [])
            )
        except Exception as e:
            print(f"Error duplicating test suite {suite_id}: {str(e)}")
            return None

    def rename_test_suite(self, suite_id, new_name):
        """Rename a test suite by ID and update the filename to match the new name.

        Args:
            suite_id: The ID of the test suite to rename
            new_name: The new name for the test suite

        Returns:
            dict: A dictionary with the updated test suite data and new filename, or None if failed
        """
        # Load the original test suite
        old_file_path = self.suites_dir / f"{suite_id}.json"
        if not old_file_path.exists():
            print(f"Cannot rename non-existent test suite: {suite_id}")
            return None

        try:
            # Load the original data
            with open(old_file_path, 'r') as f:
                suite_data = json.load(f)

            # Update the name in the suite data
            suite_data['name'] = new_name

            # Update the 'updated' timestamp
            suite_data['updated'] = time.strftime("%Y-%m-%d %H:%M:%S")

            # Generate a new filename based on the new name
            # Remove spaces and special characters
            base_name = ''.join(c for c in new_name if c.isalnum() or c in '_ ').strip()
            base_name = base_name.replace(' ', '_')

            # Use current timestamp for new filename
            timestamp = time.strftime("%Y%m%d%H%M%S")
            new_filename = f"{base_name}_{timestamp}.json"
            new_file_path = self.suites_dir / new_filename

            # Save the suite with the new filename
            with open(new_file_path, 'w') as f:
                json.dump(suite_data, f, indent=4)

            # Delete the old file
            try:
                if old_file_path.exists():
                    old_file_path.unlink()
                    print(f"Deleted old test suite file: {suite_id}.json")
            except Exception as e:
                print(f"Failed to delete old test suite file {suite_id}.json: {e}")

            # Update database
            try:
                from app.utils.database import save_test_suite
                # Prepare data for database
                db_suite_data = {
                    'id': new_filename.replace('.json', ''),  # Use new filename as ID
                    'name': new_name,
                    'status': 'renamed',
                    'passed': 0,
                    'failed': 0,
                    'skipped': 0,
                    'timestamp': time.strftime("%Y-%m-%d %H:%M:%S"),
                    'testCases': []
                }
                save_test_suite(db_suite_data)
                print(f"Saved renamed test suite to database with new ID: {new_filename.replace('.json', '')}")
            except Exception as e:
                print(f"Error saving renamed test suite to database: {str(e)}")

            print(f"Successfully renamed test suite {suite_id} to '{new_name}' with new filename {new_filename}")
            return {
                'id': new_filename.replace('.json', ''),
                'filename': new_filename,
                'suite_data': suite_data,
                'old_id': suite_id
            }

        except Exception as e:
            print(f"Error renaming test suite {suite_id}: {str(e)}")
            return None

    def delete_test_suite(self, suite_id):
        """Delete a test suite by ID"""
        file_path = self.suites_dir / f"{suite_id}.json"
        if file_path.exists():
            file_path.unlink()

            # Delete from database
            try:
                import sqlite3
                from app.utils.database import DB_PATH
                conn = sqlite3.connect(DB_PATH)
                cursor = conn.cursor()

                # Delete from test_suites table
                cursor.execute('DELETE FROM test_suites WHERE suite_id = ?', (suite_id,))

                # Delete from test_cases table
                cursor.execute('DELETE FROM test_cases WHERE suite_id = ?', (suite_id,))

                # Delete from test_steps table
                cursor.execute('DELETE FROM test_steps WHERE suite_id = ?', (suite_id,))

                # Delete from screenshots table
                cursor.execute('DELETE FROM screenshots WHERE suite_id = ?', (suite_id,))

                conn.commit()
                conn.close()

                print(f"Deleted test suite {suite_id} from database")
            except Exception as e:
                print(f"Error deleting test suite from database: {str(e)}")

            return True
