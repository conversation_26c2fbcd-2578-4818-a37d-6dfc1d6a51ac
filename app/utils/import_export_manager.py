"""Import/Export Manager for Test Cases and Test Suites
Handles ZIP file creation/extraction and database synchronization"""

import os
import json
import zipfile
import tempfile
import shutil
import uuid
import logging
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple

from .database import (
    save_test_suite, get_db_path, init_db
)
from .test_case_manager import TestCaseManager
from test_suites_manager import TestSuitesManager

logger = logging.getLogger(__name__)

class ImportExportManager:
    """Manages import and export operations for test cases and test suites"""
    
    def __init__(self, test_cases_dir: str, test_suites_dir: str):
        self.test_cases_dir = Path(test_cases_dir)
        self.test_suites_dir = Path(test_suites_dir)
        self.test_case_manager = TestCaseManager(test_cases_dir)
        self.test_suites_manager = TestSuitesManager()
        
        # Ensure directories exist
        self.test_cases_dir.mkdir(parents=True, exist_ok=True)
        self.test_suites_dir.mkdir(parents=True, exist_ok=True)
    
    def export_test_cases(self, output_path: str) -> bool:
        """
        Export all test cases to a ZIP file
        
        Args:
            output_path: Path where the ZIP file should be created
            
        Returns:
            bool: True if export was successful, False otherwise
        """
        try:
            logger.info(f"Starting test cases export to {output_path}")
            
            with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # Add all JSON test case files
                for json_file in self.test_cases_dir.glob('*.json'):
                    if not json_file.name.endswith('.bak'):
                        zipf.write(json_file, f"test_cases/{json_file.name}")
                        logger.debug(f"Added {json_file.name} to export")
                
                # Create metadata file with export info
                metadata = {
                    "export_type": "test_cases",
                    "export_timestamp": datetime.now().isoformat(),
                    "total_files": len(list(self.test_cases_dir.glob('*.json'))),
                    "version": "1.0"
                }
                
                # Add metadata to ZIP
                metadata_json = json.dumps(metadata, indent=2)
                zipf.writestr("export_metadata.json", metadata_json)
            
            logger.info(f"Successfully exported test cases to {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting test cases: {str(e)}")
            return False
    
    def import_test_cases(self, zip_path: str, conflict_resolution: str = "skip") -> Dict[str, Any]:
        """
        Import test cases from a ZIP file
        
        Args:
            zip_path: Path to the ZIP file containing test cases
            conflict_resolution: How to handle conflicts ("skip", "overwrite", "rename")
            
        Returns:
            Dict containing import results and statistics
        """
        try:
            logger.info(f"Starting test cases import from {zip_path}")
            
            results = {
                "success": True,
                "imported": 0,
                "skipped": 0,
                "errors": 0,
                "conflicts": [],
                "error_details": []
            }
            
            with tempfile.TemporaryDirectory() as temp_dir:
                # Extract ZIP file
                with zipfile.ZipFile(zip_path, 'r') as zipf:
                    zipf.extractall(temp_dir)
                
                # Look for test cases in the extracted files
                test_cases_path = Path(temp_dir) / "test_cases"
                if not test_cases_path.exists():
                    # Try root directory if no test_cases folder
                    test_cases_path = Path(temp_dir)
                
                # Process each JSON file
                for json_file in test_cases_path.glob('*.json'):
                    if json_file.name == "export_metadata.json":
                        continue
                    
                    try:
                        # Load test case data
                        with open(json_file, 'r') as f:
                            test_case_data = json.load(f)
                        
                        # Check for conflicts
                        target_path = self.test_cases_dir / json_file.name
                        if target_path.exists():
                            results["conflicts"].append(json_file.name)
                            
                            if conflict_resolution == "skip":
                                results["skipped"] += 1
                                logger.info(f"Skipped existing test case: {json_file.name}")
                                continue
                            elif conflict_resolution == "rename":
                                # Generate new filename with timestamp
                                timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
                                name_parts = json_file.stem.split('_')
                                if len(name_parts) > 1:
                                    new_name = f"{name_parts[0]}_imported_{timestamp}.json"
                                else:
                                    new_name = f"{json_file.stem}_imported_{timestamp}.json"
                                target_path = self.test_cases_dir / new_name
                        
                        # Copy file to test cases directory
                        shutil.copy2(json_file, target_path)
                        
                        # Update database
                        self._sync_test_case_to_db(test_case_data, target_path.name)
                        
                        results["imported"] += 1
                        logger.info(f"Imported test case: {target_path.name}")
                        
                    except Exception as e:
                        results["errors"] += 1
                        results["error_details"].append(f"{json_file.name}: {str(e)}")
                        logger.error(f"Error importing {json_file.name}: {str(e)}")
            
            logger.info(f"Import completed. Imported: {results['imported']}, Skipped: {results['skipped']}, Errors: {results['errors']}")
            return results
            
        except Exception as e:
            logger.error(f"Error during test cases import: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "imported": 0,
                "skipped": 0,
                "errors": 1,
                "conflicts": [],
                "error_details": [str(e)]
            }
    
    def export_test_suites(self, output_path: str) -> bool:
        """
        Export all test suites to a ZIP file
        
        Args:
            output_path: Path where the ZIP file should be created
            
        Returns:
            bool: True if export was successful, False otherwise
        """
        try:
            logger.info(f"Starting test suites export to {output_path}")
            
            with zipfile.ZipFile(output_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # Add all JSON test suite files
                for json_file in self.test_suites_dir.glob('*.json'):
                    zipf.write(json_file, f"test_suites/{json_file.name}")
                    logger.debug(f"Added {json_file.name} to export")
                
                # Create metadata file with export info
                metadata = {
                    "export_type": "test_suites",
                    "export_timestamp": datetime.now().isoformat(),
                    "total_files": len(list(self.test_suites_dir.glob('*.json'))),
                    "version": "1.0"
                }
                
                # Add metadata to ZIP
                metadata_json = json.dumps(metadata, indent=2)
                zipf.writestr("export_metadata.json", metadata_json)
            
            logger.info(f"Successfully exported test suites to {output_path}")
            return True
            
        except Exception as e:
            logger.error(f"Error exporting test suites: {str(e)}")
            return False
    
    def import_test_suites(self, zip_path: str, conflict_resolution: str = "skip") -> Dict[str, Any]:
        """
        Import test suites from a ZIP file
        
        Args:
            zip_path: Path to the ZIP file containing test suites
            conflict_resolution: How to handle conflicts ("skip", "overwrite", "rename")
            
        Returns:
            Dict containing import results and statistics
        """
        try:
            logger.info(f"Starting test suites import from {zip_path}")
            
            results = {
                "success": True,
                "imported": 0,
                "skipped": 0,
                "errors": 0,
                "conflicts": [],
                "error_details": []
            }
            
            with tempfile.TemporaryDirectory() as temp_dir:
                # Extract ZIP file
                with zipfile.ZipFile(zip_path, 'r') as zipf:
                    zipf.extractall(temp_dir)
                
                # Look for test suites in the extracted files
                test_suites_path = Path(temp_dir) / "test_suites"
                if not test_suites_path.exists():
                    # Try root directory if no test_suites folder
                    test_suites_path = Path(temp_dir)
                
                # Process each JSON file
                for json_file in test_suites_path.glob('*.json'):
                    if json_file.name == "export_metadata.json":
                        continue
                    
                    try:
                        # Load test suite data
                        with open(json_file, 'r') as f:
                            test_suite_data = json.load(f)
                        
                        # Check for conflicts
                        target_path = self.test_suites_dir / json_file.name
                        if target_path.exists():
                            results["conflicts"].append(json_file.name)
                            
                            if conflict_resolution == "skip":
                                results["skipped"] += 1
                                logger.info(f"Skipped existing test suite: {json_file.name}")
                                continue
                            elif conflict_resolution == "rename":
                                # Generate new UUID for renamed suite
                                new_uuid = str(uuid.uuid4())
                                target_path = self.test_suites_dir / f"{new_uuid}.json"
                                test_suite_data["id"] = new_uuid
                                test_suite_data["name"] = f"{test_suite_data.get('name', 'Imported Suite')} (Imported)"
                        
                        # Copy file to test suites directory
                        shutil.copy2(json_file, target_path)
                        
                        # Update database
                        self._sync_test_suite_to_db(test_suite_data)
                        
                        results["imported"] += 1
                        logger.info(f"Imported test suite: {target_path.name}")
                        
                    except Exception as e:
                        results["errors"] += 1
                        results["error_details"].append(f"{json_file.name}: {str(e)}")
                        logger.error(f"Error importing {json_file.name}: {str(e)}")
            
            logger.info(f"Import completed. Imported: {results['imported']}, Skipped: {results['skipped']}, Errors: {results['errors']}")
            return results
            
        except Exception as e:
            logger.error(f"Error during test suites import: {str(e)}")
            return {
                "success": False,
                "error": str(e),
                "imported": 0,
                "skipped": 0,
                "errors": 1,
                "conflicts": [],
                "error_details": [str(e)]
            }
    
    def _sync_test_case_to_db(self, test_case_data: Dict[str, Any], filename: str):
        """Synchronize imported test case to database"""
        try:
            import sqlite3
            from .database import get_db_path

            # Generate a unique suite_id for the imported test case
            import uuid
            suite_id = str(uuid.uuid4())

            # Connect to database
            db_path = get_db_path()
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # Insert test case into test_cases table
            test_case_name = test_case_data.get('name', 'Imported Test Case')
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            cursor.execute('''
                INSERT OR REPLACE INTO test_cases
                (suite_id, test_idx, name, status, duration, timestamp, retry_count, max_retries, error)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (suite_id, 0, test_case_name, 'imported', '0ms', timestamp, 0, 0, None))

            # Insert test steps into test_steps table
            actions = test_case_data.get('actions', [])
            for step_idx, action in enumerate(actions):
                action_id = action.get('action_id', f'imported_{step_idx}')
                action_type = action.get('type', 'unknown')
                action_name = f"{action_type} - {action_id}"

                cursor.execute('''
                    INSERT OR REPLACE INTO test_steps
                    (suite_id, test_idx, step_idx, name, action_type, action_id, status, duration, timestamp, screenshot_path, error, enabled)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (suite_id, 0, step_idx, action_name, action_type, action_id, 'imported', '0ms', timestamp, None, None, 1))

            conn.commit()
            conn.close()

            logger.info(f"Successfully synced test case {filename} to database with suite_id {suite_id}")

        except Exception as e:
            logger.error(f"Error syncing test case {filename} to database: {str(e)}")
    
    def _sync_test_suite_to_db(self, test_suite_data: Dict[str, Any]):
        """Synchronize imported test suite to database"""
        try:
            import sqlite3
            from .database import get_db_path

            # Connect to database
            db_path = get_db_path()
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # Insert test suite into test_suites table
            suite_id = test_suite_data.get('id')
            suite_name = test_suite_data.get('name', 'Imported Test Suite')
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            cursor.execute('''
                INSERT OR REPLACE INTO test_suites
                (suite_id, name, status, passed, failed, skipped, timestamp, report_dir, error)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (suite_id, suite_name, 'imported', 0, 0, 0, timestamp, None, None))

            # Insert test cases referenced by the suite
            test_cases = test_suite_data.get('test_cases', [])
            for test_idx, test_case_filename in enumerate(test_cases):
                # Try to load the test case to get its name
                test_case_name = test_case_filename
                try:
                    test_case_path = self.test_cases_dir / test_case_filename
                    if test_case_path.exists():
                        with open(test_case_path, 'r') as f:
                            test_case_data = json.load(f)
                            test_case_name = test_case_data.get('name', test_case_filename)
                except Exception:
                    pass  # Use filename if we can't load the test case

                cursor.execute('''
                    INSERT OR REPLACE INTO test_cases
                    (suite_id, test_idx, name, status, duration, timestamp, retry_count, max_retries, error)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (suite_id, test_idx, test_case_name, 'imported', '0ms', timestamp, 0, 0, None))

            conn.commit()
            conn.close()

            logger.info(f"Successfully synced test suite {suite_id} to database with {len(test_cases)} test cases")

        except Exception as e:
            logger.error(f"Error syncing test suite to database: {str(e)}")
