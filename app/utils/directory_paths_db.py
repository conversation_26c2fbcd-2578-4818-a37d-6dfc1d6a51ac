import os
import sqlite3
import logging
from pathlib import Path
from shared_directory_paths_db import SharedDirectoryPathsDB

logger = logging.getLogger(__name__)

class DirectoryPathsDB(SharedDirectoryPathsDB):
    """
    iOS-specific database handler for storing directory paths configuration
    Reads from ios_settings.db according to DATABASE_MIGRATION_GUIDE.md specifications
    """

    def __init__(self):
        # Don't call parent __init__ to avoid shared database creation
        self.platform = 'ios'

        # Use iOS-specific settings database
        self.db_path = os.path.join(
            os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
            'data',
            'ios_settings.db'
        )
        self.platform_db_path = self.db_path
        logger.info(f"iOS DirectoryPathsDB initialized with: {self.db_path}")

    def get_path(self, name, default=None):
        """
        Get a directory path from the iOS settings database

        Args:
            name: The name/key of the directory (e.g., 'TEST_CASES', 'REPORTS')
            default: Default value to return if the path is not found

        Returns:
            str: The path, or the default value if not found
        """
        try:
            if not os.path.exists(self.db_path):
                logger.warning(f"iOS settings database not found: {self.db_path}")
                return default

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT setting_value FROM settings
                WHERE platform = 'ios'
                AND category = 'directory_path'
                AND setting_key = ?
            """, (name,))

            result = cursor.fetchone()
            conn.close()

            if result:
                path = result[0]
                logger.debug(f"Found path in iOS settings database for {name}: {path}")
                return path
            else:
                logger.debug(f"No path found in iOS settings database for {name}, using default: {default}")
                return default

        except Exception as e:
            logger.error(f"Error getting directory path from iOS settings database: {str(e)}")
            return default

    def get_all_paths(self):
        """
        Get all directory paths from the iOS settings database

        Returns:
            dict: Dictionary of all directory paths
        """
        try:
            if not os.path.exists(self.db_path):
                logger.warning(f"iOS settings database not found: {self.db_path}")
                return {}

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT setting_key, setting_value FROM settings
                WHERE platform = 'ios'
                AND category = 'directory_path'
            """)

            results = cursor.fetchall()
            conn.close()

            paths = {key: value for key, value in results}
            logger.debug(f"Retrieved {len(paths)} paths from iOS settings database")
            return paths

        except Exception as e:
            logger.error(f"Error getting all directory paths from iOS settings database: {str(e)}")
            return {}

    def save_path(self, name, path):
        """
        Save a directory path to the iOS settings database

        Args:
            name: The name/key of the directory (e.g., 'TEST_CASES', 'TEMP_FILES')
            path: The path to the directory

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            if not os.path.exists(self.db_path):
                logger.error(f"iOS settings database not found: {self.db_path}")
                return False

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # Check if the setting already exists
            cursor.execute("""
                SELECT id FROM settings
                WHERE platform = 'ios'
                AND category = 'directory_path'
                AND setting_key = ?
            """, (name,))

            existing = cursor.fetchone()

            if existing:
                # Update existing setting
                cursor.execute("""
                    UPDATE settings
                    SET setting_value = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE platform = 'ios'
                    AND category = 'directory_path'
                    AND setting_key = ?
                """, (path, name))
                logger.info(f"Updated iOS directory path: {name} = {path}")
            else:
                # Insert new setting
                cursor.execute("""
                    INSERT INTO settings (platform, category, setting_key, setting_value, description)
                    VALUES ('ios', 'directory_path', ?, ?, ?)
                """, (name, path, f"Directory path for {name}"))
                logger.info(f"Inserted new iOS directory path: {name} = {path}")

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            logger.error(f"Error saving directory path to iOS settings database: {str(e)}")
            return False

    def save_all_paths(self, paths_dict):
        """
        Save multiple directory paths to the iOS settings database

        Args:
            paths_dict: Dictionary of directory paths {name: path}

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Attempting to save all paths to iOS settings database: {paths_dict}")
            if not paths_dict:
                logger.warning("Empty paths dictionary provided to save_all_paths")
                return False

            if not os.path.exists(self.db_path):
                logger.error(f"iOS settings database not found: {self.db_path}")
                return False

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            for name, path in paths_dict.items():
                logger.info(f"Processing iOS path: {name} = {path}")

                # Check if the setting already exists
                cursor.execute("""
                    SELECT id FROM settings
                    WHERE platform = 'ios'
                    AND category = 'directory_path'
                    AND setting_key = ?
                """, (name,))

                existing = cursor.fetchone()

                if existing:
                    # Update existing setting
                    cursor.execute("""
                        UPDATE settings
                        SET setting_value = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE platform = 'ios'
                        AND category = 'directory_path'
                        AND setting_key = ?
                    """, (path, name))
                    logger.info(f"Updated iOS directory path: {name} = {path}")
                else:
                    # Insert new setting
                    cursor.execute("""
                        INSERT INTO settings (platform, category, setting_key, setting_value, description)
                        VALUES ('ios', 'directory_path', ?, ?, ?)
                    """, (name, path, f"Directory path for {name}"))
                    logger.info(f"Inserted new iOS directory path: {name} = {path}")

            conn.commit()
            conn.close()
            logger.info(f"All iOS directory paths saved successfully: {paths_dict}")
            return True

        except Exception as e:
            logger.error(f"Error saving all directory paths to iOS settings database: {str(e)}")
            return False

    def get_all_paths(self):
        """
        Get all directory paths from the iOS settings database

        Returns:
            dict: Dictionary of all directory paths
        """
        try:
            if not os.path.exists(self.db_path):
                logger.warning(f"iOS settings database not found: {self.db_path}")
                return {}

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT setting_key, setting_value FROM settings
                WHERE platform = 'ios'
                AND category = 'directory_path'
            """)

            results = cursor.fetchall()
            conn.close()

            paths_dict = {name: path for name, path in results}
            logger.debug(f"Retrieved {len(paths_dict)} directory paths from iOS settings database")
            return paths_dict

        except Exception as e:
            logger.error(f"Error getting all directory paths from iOS settings database: {str(e)}")
            return {}

    def get_all_environments(self):
        """
        Get all environments from the iOS environments database

        Returns:
            list: List of environment dictionaries
        """
        try:
            # Use iOS-specific environments database
            env_db_path = os.path.join(
                os.path.dirname(os.path.dirname(os.path.abspath(__file__))),
                'data',
                'ios_environments.db'
            )
            
            if not os.path.exists(env_db_path):
                logger.warning(f"iOS environments database not found: {env_db_path}")
                return []

            conn = sqlite3.connect(env_db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT id, environment_id, platform, name, description, is_active, created_at, updated_at
                FROM environments
                WHERE platform = 'ios'
                ORDER BY name
            """)

            environments = []
            for row in cursor.fetchall():
                environments.append({
                    'id': row[0],
                    'environment_id': row[1],
                    'platform': row[2],
                    'name': row[3],
                    'description': row[4],
                    'is_active': bool(row[5]),
                    'created_at': row[6],
                    'updated_at': row[7]
                })

            conn.close()
            logger.debug(f"Retrieved {len(environments)} environments from iOS environments database")
            return environments

        except Exception as e:
            logger.error(f"Error getting all environments from iOS environments database: {str(e)}")
            return []

# Create singleton instance
directory_paths_db = DirectoryPathsDB()