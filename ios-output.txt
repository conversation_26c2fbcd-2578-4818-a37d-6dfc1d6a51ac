2025-08-31 06:40:01,949 - __main__ - WARNING - Dynamic port allocation failed: No module named 'utils.dynamic_port_init'. Using fallback ports.
2025-08-31 06:40:01,958 - utils.directory_paths_db - INFO - iOS DirectoryPathsDB initialized with: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app/data/ios_settings.db
2025-08-31 06:40:01,958 - utils.directory_paths_db - INFO - iOS DirectoryPathsDB initialized with: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app/data/ios_settings.db
2025-08-31 06:40:01,959 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/ios_data/test_cases
2025-08-31 06:40:01,959 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/ios_data/reports
2025-08-31 06:40:01,960 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/screenshots_ios
2025-08-31 06:40:01,960 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/ios_data/reference_images
2025-08-31 06:40:01,960 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/ios_data/test_suites
2025-08-31 06:40:01,961 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/reports_ios/suites
2025-08-31 06:40:01,961 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/recordings_ios
2025-08-31 06:40:01,961 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/my_custom_temp_ios
2025-08-31 06:40:01,962 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/ios_data/files_to_push
2025-08-31 06:40:01,962 - __main__ - INFO - iOS app configured to use only the 5 specified database files in app/data/
2025-08-31 06:40:01,962 - __main__ - INFO - Using intelligent port management for iOS platform...
2025-08-31 06:40:01,962 - __main__ - WARNING - Port manager not available, falling back to basic cleanup
2025-08-31 06:40:01,962 - __main__ - INFO - Using custom ports (Flask: 8080, Appium: 4723, WDA: 8200) - preserving existing processes for multi-instance support
2025-08-31 06:40:01,962 - __main__ - INFO - Skipping process termination when using custom ports for multi-instance support
2025-08-31 06:40:03,000 - appium_device_controller - WARNING - TouchAction not available in this Appium Python Client version - using W3C Actions fallback
2025-08-31 06:40:03,032 - AppiumDeviceController - INFO - Successfully imported Airtest library.
2025-08-31 06:40:03,399 - utils.database - INFO - iOS migration mode - skipping database initialization
2025-08-31 06:40:03,400 - utils.database - INFO - Checking initial database state...
2025-08-31 06:40:03,400 - utils.database - INFO - Database state: 16 suites, 59 cases, 0 steps, 0 screenshots, 0 tracking entries
2025-08-31 06:40:03,400 - utils.database - INFO - Sample suites: [('EZyIO4', 'WW', 'active', '2025-08-30 05:45:29'), ('vuYvvC', 'TC3', 'active', '2025-08-30 05:45:29'), ('ReLhSZ', 'Kmart AU Android', 'active', '2025-08-30 05:45:29')]
2025-08-31 06:40:03,411 - test_suites_manager - INFO - Database connection initialized: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app/data/ios_test_suites.db
2025-08-31 06:40:03,412 - app - INFO - Using directories from config.py:
2025-08-31 06:40:03,412 - app - INFO -   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/ios_data/test_cases
2025-08-31 06:40:03,412 - app - INFO -   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/ios_data/reference_images
2025-08-31 06:40:03,412 - app - INFO -   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/screenshots_ios
2025-08-31 06:40:03,412 - utils.test_case_manager - INFO - TestCaseManager initialized with directory: /Users/<USER>/Documents/automation-tool/ios_data/test_cases
2025-08-31 06:40:03,413 - utils.test_case_manager - INFO - Database connection initialized: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app/data/ios_test_suites.db
2025-08-31 06:40:03,413 - test_suites_manager - INFO - Database connection initialized: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app/data/ios_test_suites.db
[2025-08-31 06:40:03,460] INFO in global_values_db: iOS migration mode: forcing use of platform-specific database for global values
[2025-08-31 06:40:03,460] ERROR in global_values_db: Failed to use platform-specific database, falling back to ios_globals.db: name 'PlatformDBAdapter' is not defined
[2025-08-31 06:40:03,461] INFO in global_values_db: Using global values database at: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app/data/ios_globals.db
[2025-08-31 06:40:03,461] INFO in global_values_db: Global values database initialized successfully
[2025-08-31 06:40:03,461] INFO in global_values_db: Using global values from config.py
[2025-08-31 06:40:03,461] INFO in global_values_db: Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 2, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
[2025-08-31 06:40:03,491] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8200
[2025-08-31 06:40:03,506] INFO in appium_device_controller: Appium server is running and ready
[2025-08-31 06:40:03,506] INFO in appium_device_controller: Appium server is already running and responsive
Starting Mobile App Automation Tool...
Configuration:
  - Flask server port: 8080
  - Appium server port: 4723
  - WebDriverAgent port: 8200
Open your web browser and navigate to: http://localhost:8080
 * Serving Flask app 'app'
 * Debug mode: on
[2025-08-31 06:40:03,576] INFO in _internal: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8080
 * Running on http://**************:8080
[2025-08-31 06:40:03,576] INFO in _internal: [33mPress CTRL+C to quit[0m
