2025-08-31 07:00:37,176 - __main__ - WARNING - Dynamic port allocation failed: No module named 'utils.dynamic_port_init'. Using fallback ports.
2025-08-31 07:00:37,184 - utils.directory_paths_db - INFO - iOS DirectoryPathsDB initialized with: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app/data/ios_settings.db
2025-08-31 07:00:37,184 - utils.directory_paths_db - INFO - iOS DirectoryPathsDB initialized with: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app/data/ios_settings.db
2025-08-31 07:00:37,185 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/ios_data/test_cases
2025-08-31 07:00:37,185 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/ios_data/reports
2025-08-31 07:00:37,186 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/screenshots_ios
2025-08-31 07:00:37,186 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/ios_data/reference_images
2025-08-31 07:00:37,186 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/ios_data/test_suites
2025-08-31 07:00:37,187 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/reports_ios/suites
2025-08-31 07:00:37,187 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/recordings_ios
2025-08-31 07:00:37,188 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/my_custom_temp_ios
2025-08-31 07:00:37,188 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/ios_data/files_to_push
2025-08-31 07:00:37,188 - __main__ - INFO - iOS app configured to use only the 5 specified database files in app/data/
2025-08-31 07:00:37,188 - __main__ - INFO - Using intelligent port management for iOS platform...
2025-08-31 07:00:37,188 - __main__ - WARNING - Port manager not available, falling back to basic cleanup
2025-08-31 07:00:37,188 - __main__ - INFO - Using custom ports (Flask: 8080, Appium: 4723, WDA: 8200) - preserving existing processes for multi-instance support
2025-08-31 07:00:37,188 - __main__ - INFO - Skipping process termination when using custom ports for multi-instance support
