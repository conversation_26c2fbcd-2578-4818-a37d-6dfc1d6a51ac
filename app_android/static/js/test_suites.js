class TestSuitesManager {
    constructor() {
        this.selectedTestCases = [];  // Changed from Set to Array to maintain order
        this.allTestCases = [];  // Store all test cases for the selector
        this.sortable = null;  // Reference to Sortable instance
        this.initializeEventListeners();
        this.loadTestCases();
        this.loadTestSuites();
    }

    /**
     * Auto-save functionality:
     * - Automatically saves test suite changes when test cases are added/removed/reordered
     * - Saves changes when name or description fields are modified (with 1-second debounce)
     * - Only works when editing an existing test suite (not during creation)
     * - Shows visual feedback for save status
     */

    initializeEventListeners() {
        // Save Test Suite button click handler
        document.getElementById('saveTestSuiteBtn').addEventListener('click', () => this.saveTestSuite());

        // Auto-save when name or description changes (with debouncing)
        this.setupAutoSaveOnFieldChange();

        // Test case checkbox change handler (keep for backward compatibility)
        document.getElementById('availableTestCases').addEventListener('change', async (e) => {
            if (e.target.type === 'checkbox') {
                const testCaseId = e.target.value;
                if (e.target.checked) {
                    if (!this.selectedTestCases.includes(testCaseId)) {
                        this.selectedTestCases.push(testCaseId);
                    }
                } else {
                    this.selectedTestCases = this.selectedTestCases.filter(id => id !== testCaseId);
                }
                this.updateSelectedTestCasesList();

                // Auto-save the changes if we're in edit mode
                await this.autoSaveTestSuiteChanges();
            }
        });

        // Add test case button click handler
        const addTestCaseBtn = document.getElementById('addTestCaseBtn');
        if (addTestCaseBtn) {
            addTestCaseBtn.addEventListener('click', () => {
                const selector = document.getElementById('testCaseSelector');
                const selectedValue = selector.value;

                if (selectedValue && !this.selectedTestCases.includes(selectedValue)) {
                    this.selectedTestCases.push(selectedValue);
                    this.updateSelectedTestCasesList();

                    // Reset the selector
                    selector.value = '';
                }
            });
        }
    }

    async loadTestCases() {
        try {
            const response = await fetch('/api/test_cases/files');
            const data = await response.json();

            if (data.status === 'success' && data.files) {
                // Convert file list to test case objects
                const testCases = data.files.map(filename => ({
                    id: filename.replace('.json', ''),
                    name: filename.replace('.json', '').replace(/_/g, ' '),
                    filename: filename
                }));
                this.allTestCases = testCases; // Store all test cases
                this.displayTestCases(testCases);
                this.populateTestCaseSelector(testCases);
            } else {
                console.error('Failed to load test cases:', data.error);
                this.showError('Failed to load test cases');
            }
        } catch (error) {
            console.error('Error loading test cases:', error);
            this.showError('Error loading test cases');
        }
    }

    displayTestCases(testCases) {
        const container = document.getElementById('availableTestCases');
        container.innerHTML = '';

        testCases.forEach(testCase => {
            const listItem = document.createElement('div');
            listItem.className = 'list-group-item list-group-item-action d-flex justify-content-between align-items-center';

            // Create label badges HTML if labels exist
            const labelsHtml = testCase.labels && testCase.labels.length > 0 
                ? testCase.labels.map(label => 
                    `<span class="badge bg-secondary me-1">${label}</span>`
                ).join('') 
                : '';

            listItem.innerHTML = `
                <div class="d-flex align-items-center">
                    <div class="form-check">
                        <input class="form-check-input me-3" type="checkbox" value="${testCase.filename}" id="testCase_${testCase.filename}">
                        <label class="form-check-label" for="testCase_${testCase.filename}">
                            <span class="fw-bold">${testCase.name}</span>
                            <div class="mt-1">${labelsHtml}</div>
                        </label>
                    </div>
                </div>
                <span class="badge bg-light text-dark">Created: ${(() => {
                    if (testCase.created) {
                        try {
                            const date = new Date(testCase.created);
                            if (!isNaN(date.getTime())) {
                                return date.toLocaleDateString();
                            }
                        } catch (e) {
                            console.warn('Invalid date format for test case:', testCase.created);
                        }
                    }
                    return 'Unknown';
                })()}</span>
            `;

            container.appendChild(listItem);
        });
    }

    populateTestCaseSelector(testCases) {
        const selector = document.getElementById('testCaseSelector');
        if (!selector) return;

        // Clear existing options except the first placeholder
        while (selector.options.length > 1) {
            selector.remove(1);
        }

        // Sort test cases by name for easier selection
        const sortedTestCases = [...testCases].sort((a, b) =>
            (a.name || '').localeCompare(b.name || '')
        );

        // Add options for each test case
        sortedTestCases.forEach(testCase => {
            const option = document.createElement('option');
            option.value = testCase.filename;
            option.textContent = testCase.name;
            selector.appendChild(option);
        });
    }

    updateSelectedTestCasesList() {
        const container = document.getElementById('selectedTestCases');

        // Destroy existing Sortable instance if it exists
        if (this.sortable) {
            this.sortable.destroy();
            this.sortable = null;
        }

        container.innerHTML = '';

        this.selectedTestCases.forEach(testCaseId => {
            // Find test case info from our stored test cases
            const testCaseInfo = this.allTestCases.find(tc => tc.filename === testCaseId);

            const div = document.createElement('div');
            div.className = 'list-group-item d-flex justify-content-between align-items-center';
            div.dataset.testCaseId = testCaseId;

            // If we found the test case in our list, use its name
            if (testCaseInfo) {
                div.innerHTML = `
                    <div class="d-flex align-items-center">
                        <i class="bi bi-grip-vertical drag-handle me-2" style="cursor: grab;"></i>
                        <span>${testCaseInfo.name || 'Unknown Test Case'}</span>
                        ${testCaseInfo.labels && testCaseInfo.labels.length > 0 ? 
                            `<div class="ms-2">
                                ${testCaseInfo.labels.map(label => 
                                    `<span class="badge bg-secondary me-1">${label}</span>`
                                ).join('')}
                            </div>` : ''}
                    </div>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-primary" onclick="testSuitesManager.editTestCase('${testCaseId}')" title="Edit Test Case">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button type="button" class="btn btn-danger" onclick="testSuitesManager.removeTestCase('${testCaseId}')" title="Remove Test Case">
                            <i class="bi bi-x"></i>
                        </button>
                    </div>
                `;
            } else {
                // Fallback for test cases not found in the list
                const checkbox = document.getElementById(`testCase_${testCaseId}`);
                let displayName;

                if (checkbox && checkbox.nextElementSibling) {
                    displayName = checkbox.nextElementSibling.textContent.trim();
                } else {
                    // Extract a simple name from the filename if possible
                    displayName = testCaseId.split('_')[0] || testCaseId;
                }

                div.innerHTML = `
                    <div class="d-flex align-items-center">
                        <i class="bi bi-grip-vertical drag-handle me-2" style="cursor: grab;"></i>
                        <span>${displayName} <small class="text-muted">(ID: ${testCaseId})</small></span>
                    </div>
                    <div class="btn-group btn-group-sm" role="group">
                        <button type="button" class="btn btn-outline-primary" onclick="testSuitesManager.editTestCase('${testCaseId}')" title="Edit Test Case">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button type="button" class="btn btn-danger" onclick="testSuitesManager.removeTestCase('${testCaseId}')" title="Remove Test Case">
                            <i class="bi bi-x"></i>
                        </button>
                    </div>
                `;
            }

            container.appendChild(div);
        });

        // Initialize Sortable on the container
        if (container.children.length > 0) {
            this.initSortable(container);
        }

        // Update checkboxes to match selected state
        document.querySelectorAll('#availableTestCases input[type="checkbox"]').forEach(checkbox => {
            checkbox.checked = this.selectedTestCases.includes(checkbox.value);
        });
    }

    initSortable(container) {
        // Make sure Sortable.js is available
        if (typeof Sortable !== 'undefined') {
            this.sortable = new Sortable(container, {
                animation: 150,
                handle: '.drag-handle',
                ghostClass: 'sortable-ghost',
                chosenClass: 'sortable-chosen',
                dragClass: 'sortable-drag',

                // Update the array when items are reordered
                onEnd: async (evt) => {
                    // Get the new order of test cases
                    const items = Array.from(container.children);
                    this.selectedTestCases = items.map(item => item.dataset.testCaseId);

                    // Auto-save the changes if we're in edit mode
                    await this.autoSaveTestSuiteChanges();
                }
            });
        } else {
            console.error('Sortable.js is not loaded. Drag and drop functionality will not work.');
        }
    }

    async removeTestCase(testCaseId) {
        // Remove from array
        this.selectedTestCases = this.selectedTestCases.filter(id => id !== testCaseId);

        // Uncheck the checkbox if it exists
        const checkbox = document.getElementById(`testCase_${testCaseId}`);
        if (checkbox) {
            checkbox.checked = false;
        }

        // Update the UI
        this.updateSelectedTestCasesList();

        // Auto-save the changes if we're in edit mode
        await this.autoSaveTestSuiteChanges();
    }

    async editTestCase(testCaseId) {
        try {
            // Find test case info from our stored test cases
            const testCaseInfo = this.allTestCases.find(tc => tc.filename === testCaseId);
            const testCaseName = testCaseInfo ? testCaseInfo.name : testCaseId;
            
            // Show the edit modal
            this.showEditTestCaseModal(testCaseId, testCaseName);
        } catch (error) {
            console.error('Error opening test case editor:', error);
            this.showError('Failed to open test case editor: ' + error.message);
        }
    }

    showEditTestCaseModal(testCaseId, testCaseName) {
        // Create modal HTML if it doesn't exist
        let modal = document.getElementById('editTestCaseModal');
        if (!modal) {
            this.createEditTestCaseModal();
            modal = document.getElementById('editTestCaseModal');
        }

        // Set modal title
        const modalTitle = modal.querySelector('.modal-title');
        modalTitle.innerHTML = `<i class="bi bi-pencil"></i> Edit Test Case: ${testCaseName}`;

        // Store test case ID for later use
        modal.dataset.testCaseId = testCaseId;

        // Load test case data
        this.loadTestCaseForEditing(testCaseId);

        // Show the modal
        const bootstrapModal = new bootstrap.Modal(modal);
        bootstrapModal.show();
    }

    createEditTestCaseModal() {
        const modalHTML = `
            <div class="modal fade" id="editTestCaseModal" tabindex="-1" aria-labelledby="editTestCaseModalLabel" aria-hidden="true" data-bs-backdrop="static">
                <div class="modal-dialog modal-xl">
                    <div class="modal-content">
                        <div class="modal-header bg-primary text-white">
                            <h5 class="modal-title" id="editTestCaseModalLabel">
                                <i class="bi bi-pencil"></i> Edit Test Case
                            </h5>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body p-0">
                            <div class="row g-0" style="height: 70vh;">
                                <!-- Action Builder Section -->
                                <div class="col-md-6 border-end">
                                    <div class="p-3">
                                        <h6 class="mb-3"><i class="bi bi-tools"></i> Action Builder</h6>
                                        <div id="editActionBuilder">
                                            <!-- Action Builder will be loaded here -->
                                        </div>
                                    </div>
                                </div>
                                <!-- Action List Section -->
                                <div class="col-md-6">
                                    <div class="p-3">
                                        <div class="d-flex justify-content-between align-items-center mb-3">
                                            <h6 class="mb-0"><i class="bi bi-list-ol"></i> Actions List</h6>
                                            <div class="btn-group btn-group-sm" role="group">
                                                <button id="editClearAllBtn" class="btn btn-outline-danger" title="Clear All Actions">
                                                    <i class="bi bi-trash"></i> Clear All
                                                </button>
                                            </div>
                                        </div>
                                        <div id="editActionsList" class="list-group" style="max-height: 60vh; overflow-y: auto;">
                                            <!-- Actions will be populated here -->
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                                <i class="bi bi-x-circle"></i> Cancel
                            </button>
                            <button type="button" class="btn btn-primary" id="saveEditedTestCaseBtn">
                                <i class="bi bi-check-circle"></i> Save Changes
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;

        // Add modal to the document
        document.body.insertAdjacentHTML('beforeend', modalHTML);

        // Add event listeners
        this.setupEditModalEventListeners();
    }

    setupEditModalEventListeners() {
        const modal = document.getElementById('editTestCaseModal');
        const saveBtn = document.getElementById('saveEditedTestCaseBtn');
        const clearAllBtn = document.getElementById('editClearAllBtn');

        // Save button event listener
        saveBtn.addEventListener('click', () => {
            this.saveEditedTestCase();
        });

        // Clear all button event listener
        clearAllBtn.addEventListener('click', () => {
            if (confirm('Are you sure you want to clear all actions? This cannot be undone.')) {
                this.clearEditActions();
            }
        });
    }

    async loadTestCaseForEditing(testCaseId) {
        try {
            // Show loading state
            const actionsList = document.getElementById('editActionsList');
            actionsList.innerHTML = '<div class="text-center p-3"><div class="spinner-border" role="status"></div><p class="mt-2">Loading test case...</p></div>';

            // Load test case data from server
            const response = await fetch(`/api/test_cases/load/${testCaseId}`);
            const data = await response.json();

            if (data.status === 'success') {
                // Store the loaded actions for editing
                this.editingActions = data.actions || [];
                
                // Populate the actions list
                this.populateEditActionsList();
                
                // Initialize the action builder for editing
                this.initializeEditActionBuilder();
            } else {
                throw new Error(data.error || 'Failed to load test case');
            }
        } catch (error) {
            console.error('Error loading test case for editing:', error);
            const actionsList = document.getElementById('editActionsList');
            actionsList.innerHTML = `<div class="text-center p-3 text-danger"><i class="bi bi-exclamation-triangle"></i><p class="mt-2">Failed to load test case: ${error.message}</p></div>`;
        }
    }

    populateEditActionsList() {
        const actionsList = document.getElementById('editActionsList');
        
        if (!this.editingActions || this.editingActions.length === 0) {
            actionsList.innerHTML = '<div class="text-center p-3 text-muted"><i class="bi bi-info-circle"></i><p class="mt-2">No actions in this test case</p></div>';
            return;
        }

        actionsList.innerHTML = '';
        
        this.editingActions.forEach((action, index) => {
            const actionItem = this.createEditActionItem(action, index);
            actionsList.appendChild(actionItem);
        });
    }

    createEditActionItem(action, index) {
        const div = document.createElement('div');
        div.className = 'list-group-item d-flex justify-content-between align-items-center';
        div.dataset.actionIndex = index;

        const actionText = this.getActionDisplayText(action);
        
        div.innerHTML = `
            <div class="d-flex align-items-center flex-grow-1">
                <span class="badge bg-secondary me-2">${index + 1}</span>
                <span class="action-text">${actionText}</span>
            </div>
            <div class="btn-group btn-group-sm" role="group">
                <button type="button" class="btn btn-outline-primary" onclick="testSuitesManager.editAction(${index})" title="Edit Action">
                    <i class="bi bi-pencil"></i>
                </button>
                <button type="button" class="btn btn-outline-danger" onclick="testSuitesManager.removeEditAction(${index})" title="Remove Action">
                    <i class="bi bi-trash"></i>
                </button>
            </div>
        `;

        return div;
    }

    getActionDisplayText(action) {
        switch (action.action) {
            case 'tap':
                return `Tap at (${action.x}, ${action.y})`;
            case 'text':
                return `Type: "${action.text}"`;
            case 'wait':
                return `Wait ${action.duration}ms`;
            case 'click_element':
                return `Click element: ${action.locator || action.image || 'Unknown'}`;
            case 'swipe':
                return `Swipe from (${action.start_x}, ${action.start_y}) to (${action.end_x}, ${action.end_y})`;
            default:
                return `${action.action} action`;
        }
    }

    initializeEditActionBuilder() {
        const actionBuilder = document.getElementById('editActionBuilder');
        actionBuilder.innerHTML = `
            <div class="action-builder">
                <div class="form-group mb-3">
                    <label for="editActionType">Action Type</label>
                    <select id="editActionType" class="form-control">
                        <option value="tap">Tap</option>
                        <option value="doubleTap">Double Tap</option>
                        <option value="swipe">Swipe</option>
                        <option value="text">Input Text</option>
                        <option value="tapAndType">Tap and Type (iOS)</option>
                        <option value="tapOnText">Tap on Text</option>
                        <option value="wait">Wait</option>
                        <option value="deviceBack">Device Back (Android Only)</option>
                        <option value="getValue">Get Value</option>
                        <option value="compareValue">Compare Value</option>
                        <option value="getParam">Get Parameter</option>
                        <option value="setParam">Set Parameter</option>
                        <option value="launchApp">Launch App</option>
                        <option value="restartApp">Restart App</option>
                        <option value="terminateApp">Terminate App</option>
                        <option value="uninstallApp">Uninstall App</option>
                        <option value="takeScreenshot">Take Screenshot</option>
                        <option value="info">INFO</option>
                    </select>
                </div>

                <!-- Basic Tap Action Form -->
                <div id="editTapActionForm" class="action-form">
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label>X Coordinate</label>
                                <input type="number" id="editTapX" class="form-control" value="0">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label>Y Coordinate</label>
                                <input type="number" id="editTapY" class="form-control" value="0">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Text Input Action Form -->
                <div id="editTextActionForm" class="action-form d-none">
                    <div class="form-group mb-3">
                        <label>Text to Input</label>
                        <input type="text" id="editTextInput" class="form-control" placeholder="Enter text">
                    </div>
                    <div class="row">
                        <div class="col-6">
                            <div class="form-group">
                                <label>X Coordinate</label>
                                <input type="number" id="editTextX" class="form-control" value="0">
                            </div>
                        </div>
                        <div class="col-6">
                            <div class="form-group">
                                <label>Y Coordinate</label>
                                <input type="number" id="editTextY" class="form-control" value="0">
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Wait Action Form -->
                <div id="editWaitActionForm" class="action-form d-none">
                    <div class="form-group">
                        <label>Wait Duration (seconds)</label>
                        <input type="number" id="editWaitDuration" class="form-control" value="1" min="0.1" step="0.1">
                    </div>
                </div>

                <!-- Info Action Form -->
                <div id="editInfoActionForm" class="action-form d-none">
                    <div class="form-group">
                        <label>Info Message</label>
                        <input type="text" id="editInfoMessage" class="form-control" placeholder="Enter info message">
                    </div>
                </div>

                <!-- Add Action Button -->
                <div class="mt-3">
                    <button id="editAddAction" class="btn btn-success">
                        <i class="bi bi-plus"></i> Add Action
                    </button>
                </div>
            </div>
        `;

        // Initialize event listeners for the edit action builder
        this.setupEditActionBuilderListeners();
    }

    setupEditActionBuilderListeners() {
        const actionTypeSelect = document.getElementById('editActionType');
        const addActionBtn = document.getElementById('editAddAction');

        // Handle action type change
        actionTypeSelect.addEventListener('change', () => {
            this.updateEditActionForm();
        });

        // Handle add action
        addActionBtn.addEventListener('click', () => {
            this.addEditAction();
        });
    }

    updateEditActionForm() {
        const selectedType = document.getElementById('editActionType').value;
        
        // Hide all forms
        const forms = document.querySelectorAll('#editActionBuilder .action-form');
        forms.forEach(form => form.classList.add('d-none'));
        
        // Show the relevant form
        switch(selectedType) {
            case 'tap':
            case 'doubleTap':
                document.getElementById('editTapActionForm').classList.remove('d-none');
                break;
            case 'text':
            case 'tapAndType':
                document.getElementById('editTextActionForm').classList.remove('d-none');
                break;
            case 'wait':
                document.getElementById('editWaitActionForm').classList.remove('d-none');
                break;
            case 'info':
                document.getElementById('editInfoActionForm').classList.remove('d-none');
                break;
            default:
                // For other action types, show tap form as default
                document.getElementById('editTapActionForm').classList.remove('d-none');
                break;
        }
    }

    addEditAction() {
        const actionType = document.getElementById('editActionType').value;
        let action = {
            type: actionType,
            timestamp: Date.now()
        };

        // Collect form data based on action type
        switch(actionType) {
            case 'tap':
            case 'doubleTap':
                action.x = parseInt(document.getElementById('editTapX').value) || 0;
                action.y = parseInt(document.getElementById('editTapY').value) || 0;
                break;
            case 'text':
                action.text = document.getElementById('editTextInput').value || '';
                action.x = parseInt(document.getElementById('editTextX').value) || 0;
                action.y = parseInt(document.getElementById('editTextY').value) || 0;
                break;
            case 'tapAndType':
                action.text = document.getElementById('editTextInput').value || '';
                action.x = parseInt(document.getElementById('editTextX').value) || 0;
                action.y = parseInt(document.getElementById('editTextY').value) || 0;
                break;
            case 'wait':
                action.duration = parseFloat(document.getElementById('editWaitDuration').value) || 1;
                break;
            case 'info':
                action.message = document.getElementById('editInfoMessage').value || '';
                break;
            case 'launchApp':
            case 'restartApp':
            case 'terminateApp':
            case 'uninstallApp':
                action.package_id = 'com.example.app'; // Default package
                break;
            case 'takeScreenshot':
                action.filename = `screenshot_${Date.now()}.png`;
                break;
            default:
                // For other types, add basic structure
                break;
        }

        // Add to editing actions array
        if (!this.editingActions) {
            this.editingActions = [];
        }
        this.editingActions.push(action);

        // Refresh the actions list
        this.populateEditActionsList();

        // Clear form
        this.clearEditActionForm();
    }

    clearEditActionForm() {
        // Reset all form fields
        document.getElementById('editTapX').value = '0';
        document.getElementById('editTapY').value = '0';
        document.getElementById('editTextInput').value = '';
        document.getElementById('editTextX').value = '0';
        document.getElementById('editTextY').value = '0';
        document.getElementById('editWaitDuration').value = '1';
        document.getElementById('editInfoMessage').value = '';
    }

    async saveEditedTestCase() {
        try {
            const modal = document.getElementById('editTestCaseModal');
            const testCaseId = modal.dataset.testCaseId;
            
            if (!testCaseId) {
                throw new Error('No test case ID found');
            }

            // Show loading state
            const saveBtn = document.getElementById('saveEditedTestCaseBtn');
            const originalText = saveBtn.innerHTML;
            saveBtn.innerHTML = '<i class="bi bi-hourglass-split"></i> Saving...';
            saveBtn.disabled = true;

            // Prepare the data to save
            const saveData = {
                actions: this.editingActions || []
            };

            // Send save request to server
            const response = await fetch(`/api/test_cases/save_modified`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    filename: testCaseId,
                    ...saveData
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                // Close the modal
                const bootstrapModal = bootstrap.Modal.getInstance(modal);
                bootstrapModal.hide();

                // Show success message
                this.showSuccess('Test case updated successfully!');

                // Refresh the test suite display if needed
                // This will ensure any changes are reflected in the UI
                await this.loadTestCases();
            } else {
                throw new Error(data.error || 'Failed to save test case');
            }
        } catch (error) {
            console.error('Error saving edited test case:', error);
            this.showError('Failed to save test case: ' + error.message);
        } finally {
            // Restore save button
            const saveBtn = document.getElementById('saveEditedTestCaseBtn');
            if (saveBtn) {
                saveBtn.innerHTML = '<i class="bi bi-check-circle"></i> Save Changes';
                saveBtn.disabled = false;
            }
        }
    }

    clearEditActions() {
        this.editingActions = [];
        this.populateEditActionsList();
    }

    removeEditAction(index) {
        if (this.editingActions && index >= 0 && index < this.editingActions.length) {
            this.editingActions.splice(index, 1);
            this.populateEditActionsList();
        }
    }

    editAction(index) {
        const action = this.editingActions[index];
        if (!action) return;

        // Populate the form with action data
        document.getElementById('editActionType').value = action.type;
        this.updateEditActionForm();

        // Fill form fields based on action type
        switch(action.type) {
            case 'tap':
            case 'doubleTap':
                document.getElementById('editTapX').value = action.x || 0;
                document.getElementById('editTapY').value = action.y || 0;
                break;
            case 'text':
            case 'tapAndType':
                document.getElementById('editTextInput').value = action.text || '';
                document.getElementById('editTextX').value = action.x || 0;
                document.getElementById('editTextY').value = action.y || 0;
                break;
            case 'wait':
                document.getElementById('editWaitDuration').value = action.duration || 1;
                break;
            case 'info':
                document.getElementById('editInfoMessage').value = action.message || '';
                break;
        }

        // Remove the action from the list (it will be re-added when user clicks Add Action)
        this.editingActions.splice(index, 1);
        this.populateEditActionsList();
    }

    async autoSaveTestSuiteChanges() {
        // Check if we're currently editing a test suite
        const saveButton = document.getElementById('saveTestSuiteBtn');
        const isEditMode = saveButton && saveButton.dataset.mode === 'edit';

        if (!isEditMode) {
            // Not in edit mode, no need to auto-save
            return;
        }

        const suiteId = saveButton.dataset.suiteId;
        if (!suiteId) {
            console.warn('No suite ID found for auto-save');
            return;
        }

        try {
            // Show auto-save indicator
            this.showAutoSaveIndicator('Saving...');

            // Get current form values
            const name = document.getElementById('testSuiteName').value;
            const description = document.getElementById('testSuiteDescription').value;

            // Validate that we have required data
            if (!name) {
                // Don't auto-save if name is missing
                this.hideAutoSaveIndicator();
                return;
            }

            // Allow auto-save even with no test cases during editing
            // The validation for minimum test cases will happen during execution

            // Save the changes to the server
            const response = await fetch(`/api/test_suites/${suiteId}/update`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name,
                    description,
                    test_cases: this.selectedTestCases
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                console.log('Test suite auto-saved successfully');
                // Show a subtle success indicator
                this.showAutoSaveIndicator('✓ Saved', 'success');
            } else {
                console.error('Auto-save failed:', data.error);
                this.showAutoSaveIndicator('✗ Save failed', 'error');
                this.showError('Failed to auto-save changes: ' + (data.error || 'Unknown error'));
            }
        } catch (error) {
            console.error('Error during auto-save:', error);
            this.showAutoSaveIndicator('✗ Save failed', 'error');
            this.showError('Error auto-saving changes: ' + error.message);
        }
    }

    showAutoSaveIndicator(message, type = 'info') {
        // Show auto-save indicator next to the save button
        const saveButton = document.getElementById('saveTestSuiteBtn');
        if (!saveButton) return;

        // Remove any existing indicator
        this.hideAutoSaveIndicator();

        // Create indicator element
        const indicator = document.createElement('span');
        indicator.id = 'autoSaveIndicator';
        indicator.className = 'ms-2 small';
        indicator.textContent = message;

        // Style based on type
        switch (type) {
            case 'success':
                indicator.className += ' text-success';
                break;
            case 'error':
                indicator.className += ' text-danger';
                break;
            case 'info':
            default:
                indicator.className += ' text-muted';
                break;
        }

        // Insert after the save button
        saveButton.parentNode.insertBefore(indicator, saveButton.nextSibling);

        // Auto-hide success/error messages after 3 seconds
        if (type === 'success' || type === 'error') {
            setTimeout(() => {
                this.hideAutoSaveIndicator();
            }, 3000);
        }
    }

    hideAutoSaveIndicator() {
        const indicator = document.getElementById('autoSaveIndicator');
        if (indicator) {
            indicator.remove();
        }
    }

    setupAutoSaveOnFieldChange() {
        // Debounce timer for auto-save
        let autoSaveTimer = null;

        const debouncedAutoSave = () => {
            clearTimeout(autoSaveTimer);
            autoSaveTimer = setTimeout(async () => {
                await this.autoSaveTestSuiteChanges();
            }, 1000); // Wait 1 second after user stops typing
        };

        // Add event listeners to name and description fields
        const nameField = document.getElementById('testSuiteName');
        const descriptionField = document.getElementById('testSuiteDescription');

        if (nameField) {
            nameField.addEventListener('input', debouncedAutoSave);
        }

        if (descriptionField) {
            descriptionField.addEventListener('input', debouncedAutoSave);
        }
    }

    async loadTestSuites() {
        try {
            const response = await fetch('/api/test_suites/list');
            const data = await response.json();

            if (data.status === 'success') {
                this.displayTestSuites(data.test_suites);
            } else {
                console.error('Failed to load test suites:', data.error);
                this.showError('Failed to load test suites');
            }
        } catch (error) {
            console.error('Error loading test suites:', error);
            this.showError('Error loading test suites');
        }
    }

    displayTestSuites(testSuites) {
        const testSuitesList = document.getElementById('testSuitesList');
        testSuitesList.innerHTML = ''; // Clear existing list

        if (testSuites.length === 0) {
            testSuitesList.innerHTML = '<p class="text-muted">No test suites created yet.</p>';
            return;
        }

        testSuites.forEach(suite => {
            const listItem = document.createElement('div');
            listItem.className = 'list-group-item'; // Use the new list item class

            const testCaseCount = suite.test_cases ? suite.test_cases.length : 0;
            let created = 'Unknown';
            if (suite.created) {
                try {
                    const date = new Date(suite.created);
                    if (!isNaN(date.getTime())) {
                        created = date.toLocaleDateString();
                    }
                } catch (e) {
                    console.warn('Invalid date format for suite:', suite.created);
                }
            }

            listItem.innerHTML = `
                <div class="test-suite-info">
                    <span class="test-suite-name">${suite.name}</span>
                    <div class="test-suite-meta">
                        <span><i class="bi bi-file-earmark-text"></i> ${testCaseCount} Test Case${testCaseCount !== 1 ? 's' : ''}</span>
                        <span class="ms-3"><i class="bi bi-calendar-event"></i> Created: ${created}</span>
                    </div>
                </div>
                <div class="test-suite-actions">
                    <button class="btn btn-sm btn-outline-primary edit-suite-btn" data-suite-id="${suite.id}" title="Edit Test Suite">
                        <i class="bi bi-pencil"></i> Edit
                    </button>
                    <button class="btn btn-sm btn-outline-secondary rename-suite-btn" data-suite-id="${suite.id}" data-suite-name="${suite.name}" title="Rename Test Suite">
                        <i class="bi bi-pencil-square"></i> Rename
                    </button>
                    <button class="btn btn-sm btn-outline-secondary duplicate-suite-btn" data-suite-id="${suite.id}" title="Duplicate Test Suite">
                        <i class="bi bi-files"></i> Duplicate
                    </button>
                    <button class="btn btn-sm btn-outline-danger delete-suite-btn" data-suite-id="${suite.id}" title="Delete Test Suite">
                        <i class="bi bi-trash"></i> Delete
                    </button>
                </div>
            `;

            testSuitesList.appendChild(listItem);
        });

        // Re-add event listeners for the new buttons
        this.addTestSuiteActionListeners();
    }

    addTestSuiteActionListeners() {
        const editButtons = document.querySelectorAll('.edit-suite-btn');
        const renameButtons = document.querySelectorAll('.rename-suite-btn');
        const duplicateButtons = document.querySelectorAll('.duplicate-suite-btn');
        const deleteButtons = document.querySelectorAll('.delete-suite-btn');

        console.log('Adding test suite action listeners:');
        console.log('Edit buttons found:', editButtons.length);
        console.log('Rename buttons found:', renameButtons.length);
        console.log('Duplicate buttons found:', duplicateButtons.length);
        console.log('Delete buttons found:', deleteButtons.length);

        editButtons.forEach(button => {
            button.addEventListener('click', (event) => {
                const suiteId = event.currentTarget.dataset.suiteId;
                this.editTestSuite(suiteId);
            });
        });

        renameButtons.forEach(button => {
            button.addEventListener('click', (event) => {
                const suiteId = event.currentTarget.dataset.suiteId;
                const currentName = event.currentTarget.dataset.suiteName;
                this.renameTestSuite(suiteId, currentName);
            });
        });

        duplicateButtons.forEach(button => {
            button.addEventListener('click', (event) => {
                const suiteId = event.currentTarget.dataset.suiteId;
                this.duplicateTestSuite(suiteId);
            });
        });

        deleteButtons.forEach(button => {
            button.addEventListener('click', (event) => {
                const suiteId = event.currentTarget.dataset.suiteId;
                if (confirm(`Are you sure you want to delete test suite ${suiteId}?`)) {
                    this.deleteTestSuite(suiteId);
                }
            });
        });
    }

    async saveTestSuite() {
        const name = document.getElementById('testSuiteName').value;
        const description = document.getElementById('testSuiteDescription').value;
        const saveButton = document.getElementById('saveTestSuiteBtn');
        const isEdit = saveButton.dataset.mode === 'edit';
        const suiteId = isEdit ? saveButton.dataset.suiteId : null;

        if (!name) {
            this.showError('Please enter a name for the test suite');
            return;
        }

        if (this.selectedTestCases.length === 0) {
            this.showError('Please select at least one test case');
            return;
        }

        try {
            const endpoint = isEdit
                ? `/api/test_suites/${suiteId}/update`
                : '/api/test_suites/create';

            const response = await fetch(endpoint, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    name,
                    description,
                    test_cases: this.selectedTestCases // Already an array, no need for Array.from
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                // Close modal and reset form
                const modal = bootstrap.Modal.getInstance(document.getElementById('createTestSuiteModal'));
                modal.hide();
                document.getElementById('createTestSuiteForm').reset();
                this.selectedTestCases = []; // Reset to empty array
                this.updateSelectedTestCasesList();

                // Refresh test suites list
                this.loadTestSuites();

                // Uncheck all checkboxes
                document.querySelectorAll('#availableTestCases input[type="checkbox"]').forEach(checkbox => {
                    checkbox.checked = false;
                });

                // Reset button text and mode for next time
                saveButton.textContent = 'Save Test Suite';
                saveButton.dataset.mode = 'create';
                delete saveButton.dataset.suiteId;

                // Reset modal title
                document.getElementById('createTestSuiteModalLabel').textContent = 'Create New Test Suite';

                // Hide any auto-save indicators
                this.hideAutoSaveIndicator();

                this.showSuccess(isEdit ? 'Test suite updated successfully' : 'Test suite created successfully');
            } else {
                this.showError(data.error || 'Failed to save test suite');
            }
        } catch (error) {
            console.error('Error saving test suite:', error);
            this.showError('Error saving test suite');
        }
    }

    async renameTestSuite(suiteId, currentName) {
        // Prompt for new name
        const newName = prompt('Enter a new name for the test suite:', currentName);

        // Check if user cancelled or entered an empty name
        if (!newName || newName.trim() === '') {
            return;
        }

        try {
            // Use the new rename endpoint that handles filename changes
            const response = await fetch(`/api/test_suites/${suiteId}/rename`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    new_name: newName
                })
            });

            const data = await response.json();

            if (data.status === 'success') {
                this.loadTestSuites();
                this.showSuccess(`Test suite renamed successfully. New ID: ${data.id}`);
            } else {
                this.showError(data.error || 'Failed to rename test suite');
            }
        } catch (error) {
            console.error('Error renaming test suite:', error);
            this.showError('Error renaming test suite');
        }
    }

    async duplicateTestSuite(suiteId) {
        try {
            const response = await fetch(`/api/test_suites/${suiteId}/duplicate`, {
                method: 'POST'
            });

            const data = await response.json();

            if (data.status === 'success') {
                this.loadTestSuites();
                this.showSuccess('Test suite duplicated successfully');
            } else {
                this.showError(data.error || 'Failed to duplicate test suite');
            }
        } catch (error) {
            console.error('Error duplicating test suite:', error);
            this.showError('Error duplicating test suite');
        }
    }

    async getTestSuite(suiteId) {
        try {
            const response = await fetch(`/api/test_suites/${suiteId}`);
            const data = await response.json();

            if (data.status === 'success') {
                return data.test_suite;
            } else {
                this.showError(data.error || 'Failed to fetch test suite');
                return null;
            }
        } catch (error) {
            console.error('Error fetching test suite:', error);
            this.showError('Error fetching test suite');
            return null;
        }
    }

    async deleteTestSuite(suiteId) {
        if (!confirm('Are you sure you want to delete this test suite?')) {
            return;
        }

        try {
            const response = await fetch(`/api/test_suites/${suiteId}`, {
                method: 'DELETE'
            });

            const data = await response.json();

            if (data.status === 'success') {
                this.loadTestSuites();
                this.showSuccess('Test suite deleted successfully');
            } else {
                this.showError(data.error || 'Failed to delete test suite');
            }
        } catch (error) {
            console.error('Error deleting test suite:', error);
            this.showError('Error deleting test suite');
        }
    }

    async runTestSuite(suiteId) {
        try {
            // First, fetch the test suite details to get the test cases
            const response = await fetch(`/api/test_suites/${suiteId}`);
            const data = await response.json();

            if (data.status !== 'success') {
                this.showError(data.error || 'Failed to fetch test suite details');
                return;
            }

            // Show the execution configuration modal
            await this.showExecutionConfigModal(suiteId, data.test_suite);

        } catch (error) {
            console.error('Error running test suite:', error);
            this.showError('Error running test suite');
        }
    }

    async showExecutionConfigModal(suiteId, testSuite) {
        return new Promise((resolve, reject) => {
            // Populate modal with test suite information
            document.getElementById('testSuiteExecutionInfo').textContent = `Test Suite: ${testSuite.name}`;
            document.getElementById('testSuiteExecutionTestCount').textContent = `${testSuite.test_cases.length} test cases`;

            // Load default values from database
            this.loadExecutionDefaults();

            // Show the modal
            const modal = new bootstrap.Modal(document.getElementById('testSuiteExecutionModal'));
            modal.show();

            // Handle Save and Execute button
            const saveAndExecuteBtn = document.getElementById('saveAndExecuteBtn');
            const newHandler = async () => {
                saveAndExecuteBtn.removeEventListener('click', newHandler);

                const recordExecution = document.getElementById('recordExecutionSelect').value;
                const retryFailedTests = parseInt(document.getElementById('retryFailedTestsSelect').value);

                // Save settings to database
                await this.saveExecutionSettings(recordExecution, retryFailedTests);

                // Hide modal
                modal.hide();

                // Start execution with settings
                await this.executeTestSuiteWithSettings(suiteId, testSuite, recordExecution, retryFailedTests);
                resolve();
            };

            saveAndExecuteBtn.addEventListener('click', newHandler);

            // Handle modal close/cancel
            const modalElement = document.getElementById('testSuiteExecutionModal');
            const closeHandler = () => {
                modalElement.removeEventListener('hidden.bs.modal', closeHandler);
                saveAndExecuteBtn.removeEventListener('click', newHandler);
                reject(new Error('User cancelled execution'));
            };
            modalElement.addEventListener('hidden.bs.modal', closeHandler);
        });
    }

    async loadExecutionDefaults() {
        try {
            // Load default values from database
            const response = await fetch('/api/execution_settings');
            if (response.ok) {
                const settings = await response.json();
                document.getElementById('recordExecutionSelect').value = settings.record_execution || 'NO';
                document.getElementById('retryFailedTestsSelect').value = settings.retry_failed_tests || '0';
            }
        } catch (error) {
            console.error('Error loading execution defaults:', error);
            // Use hardcoded defaults if API fails
            document.getElementById('recordExecutionSelect').value = 'NO';
            document.getElementById('retryFailedTestsSelect').value = '0';
        }
    }

    async saveExecutionSettings(recordExecution, retryFailedTests) {
        try {
            await fetch('/api/execution_settings', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    record_execution: recordExecution,
                    retry_failed_tests: retryFailedTests.toString()
                })
            });
        } catch (error) {
            console.error('Error saving execution settings:', error);
        }
    }

    async executeTestSuiteWithSettings(suiteId, testSuite, recordExecution, retryFailedTests) {
        try {
            // Show minimal loading indicator for test suite execution
            this.showTestSuiteExecutionLoading('Running test suite...');

            // Disable device screenshot refreshing during execution
            this.disableDeviceScreenshots();

            // Call the API to run the test suite with execution settings
            const runResponse = await fetch(`/api/test_suites/${suiteId}/run`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    device_id: window.app ? window.app.deviceId : null,  // Include device ID for session isolation
                    record_execution: recordExecution,
                    retry_failed_tests: retryFailedTests
                })
            });

            // Hide loading indicator and re-enable device screenshots
            this.hideTestSuiteExecutionLoading();
            this.enableDeviceScreenshots();

            const runData = await runResponse.json();

            if (runData.status === 'success') {
                this.showSuccess('Test suite execution started');

                // If a report URL is returned, offer to open it
                if (runData.report_url) {
                    // Add a button to open the report
                    this.showReportButton(runData.report_url, testSuite.name);

                    // Refresh the Reports tab if it exists
                    if (window.reportsManager) {
                        window.reportsManager.loadReports();
                    }
                }

                // Optionally, switch to a results tab or show execution status
                const testCasesTab = document.getElementById('test-cases-tab-btn');
                if (testCasesTab) {
                    const tabInstance = new bootstrap.Tab(testCasesTab);
                    tabInstance.show();
                }
            } else {
                this.showError(runData.error || 'Failed to run test suite');
            }
        } catch (error) {
            console.error('Error running test suite:', error);
            this.hideTestSuiteExecutionLoading();
            this.enableDeviceScreenshots();
            this.showError('Error running test suite');
        }
    }

    async editTestSuite(suiteId) {
        try {
            // Fetch the test suite details
            const response = await fetch(`/api/test_suites/${suiteId}`);
            const data = await response.json();

            if (data.status !== 'success') {
                this.showError(data.error || 'Failed to fetch test suite details');
                return;
            }

            const suite = data.test_suite;

            // Populate the edit form
            document.getElementById('testSuiteName').value = suite.name;
            document.getElementById('testSuiteDescription').value = suite.description || '';

            // Clear existing selections
            this.selectedTestCases = [];

            // Ensure all available checkboxes are unchecked first
            document.querySelectorAll('#availableTestCases input[type="checkbox"]').forEach(checkbox => {
                checkbox.checked = false;
            });

            // Process the test cases in this suite (maintain order)
            if (Array.isArray(suite.test_cases)) {
                suite.test_cases.forEach(testCaseData => {
                    let filename = null;

                    // Determine the filename based on data structure
                    if (typeof testCaseData === 'string') {
                        filename = testCaseData;
                    } else if (testCaseData && typeof testCaseData.filename === 'string') {
                        filename = testCaseData.filename;
                    }

                    // Process if we have a valid filename
                    if (filename) {
                        // Add to our array if not already present
                        if (!this.selectedTestCases.includes(filename)) {
                            this.selectedTestCases.push(filename);
                        }

                        // Check the checkbox if it exists
                        const checkbox = document.getElementById(`testCase_${filename}`);
                        if (checkbox) {
                            checkbox.checked = true;
                        } else {
                            console.warn(`Checkbox for test case filename ${filename} not found in the available list.`);
                        }
                    } else {
                        console.warn('Skipping invalid test case data during edit:', testCaseData);
                    }
                });
            }

            // Update the selected test cases list
            this.updateSelectedTestCasesList();

            // Show the modal
            const modal = new bootstrap.Modal(document.getElementById('createTestSuiteModal'));
            modal.show();

            // Change the title to indicate editing
            document.getElementById('createTestSuiteModalLabel').textContent = 'Edit Test Suite';

            // Change the save button text
            const saveButton = document.getElementById('saveTestSuiteBtn');
            saveButton.textContent = 'Update Test Suite';
            saveButton.dataset.mode = 'edit';
            saveButton.dataset.suiteId = suiteId;
        } catch (error) {
            console.error('Error editing test suite:', error);
            this.showError('Error editing test suite');
        }
    }

    showError(message) {
        // Display error message using Bootstrap toast or alert
        alert(message);
    }

    showSuccess(message) {
        // Display success message using Bootstrap toast or alert
        alert(message);
    }

    showLoading(message) {
        // Implement loading indicator if needed
        console.log(`Loading: ${message}`);
    }

    hideLoading() {
        // Hide loading indicator if needed
        console.log('Loading complete');
    }

    /**
     * Show a button to open the test report
     *
     * @param {string} reportUrl - URL to the test report
     * @param {string} suiteName - Name of the test suite
     */
    showReportButton(reportUrl, suiteName) {
        // Remove any existing report notification
        const existingContainer = document.getElementById('report-notification-container');
        if (existingContainer) {
            existingContainer.remove();
        }

        // Create notification container
        const container = document.createElement('div');
        container.id = 'report-notification-container';
        container.className = 'position-fixed bottom-0 end-0 p-3';
        container.style.zIndex = '1050';

        // Create a Bootstrap toast
        container.innerHTML = `
            <div class="toast show" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="toast-header bg-success text-white">
                    <i class="bi bi-file-earmark-check me-2"></i>
                    <strong class="me-auto">Test Report Ready</strong>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                </div>
                <div class="toast-body">
                    <div class="mb-2">Test suite "${suiteName}" execution report is ready.</div>
                    <div class="d-flex justify-content-between">
                        <a href="${reportUrl}" target="_blank" class="btn btn-primary btn-sm">
                            <i class="bi bi-eye me-1"></i> View Report
                        </a>
                    </div>
                </div>
            </div>
        `;

        // Add to document body
        document.body.appendChild(container);

        // Set up auto-dismiss after 30 seconds
        setTimeout(() => {
            const toast = container.querySelector('.toast');
            if (toast) {
                const bsToast = bootstrap.Toast.getInstance(toast);
                if (bsToast) {
                    bsToast.hide();
                } else {
                    container.remove();
                }
            }
        }, 30000);

        // Set up event listeners
        const closeBtn = container.querySelector('.btn-close');
        if (closeBtn) {
            closeBtn.addEventListener('click', () => {
                container.remove();
            });
        }
    }

    showLoading(message = 'Loading...') {
        let loadingOverlay = document.getElementById('loading-overlay');
        if (!loadingOverlay) {
            loadingOverlay = document.createElement('div');
            loadingOverlay.id = 'loading-overlay';
            loadingOverlay.style.position = 'fixed';
            loadingOverlay.style.top = '0';
            loadingOverlay.style.left = '0';
            loadingOverlay.style.width = '100%';
            loadingOverlay.style.height = '100%';
            loadingOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
            loadingOverlay.style.zIndex = '9999';
            loadingOverlay.style.display = 'flex';
            loadingOverlay.style.justifyContent = 'center';
            loadingOverlay.style.alignItems = 'center';
            loadingOverlay.style.color = '#fff';
            loadingOverlay.style.fontSize = '1.5rem';

            const spinner = document.createElement('div');
            spinner.className = 'spinner-border mr-3';
            spinner.setAttribute('role', 'status');

            const loadingText = document.createElement('span');
            loadingText.id = 'loading-message';
            loadingText.style.marginLeft = '10px';

            loadingOverlay.appendChild(spinner);
            loadingOverlay.appendChild(loadingText);

            document.body.appendChild(loadingOverlay);
        }

        document.getElementById('loading-message').textContent = message;
        loadingOverlay.style.display = 'flex';
    }

    hideLoading() {
        const loadingOverlay = document.getElementById('loading-overlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
    }

    showTestSuiteExecutionLoading(message) {
        // Create a minimal loading overlay for test suite execution
        let loadingOverlay = document.getElementById('test-suite-execution-loading');
        if (!loadingOverlay) {
            loadingOverlay = document.createElement('div');
            loadingOverlay.id = 'test-suite-execution-loading';
            loadingOverlay.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                width: 100%;
                height: 100%;
                background-color: rgba(0, 0, 0, 0.3);
                display: flex;
                justify-content: center;
                align-items: center;
                z-index: 9999;
                backdrop-filter: blur(2px);
            `;

            const loadingContent = document.createElement('div');
            loadingContent.style.cssText = `
                background: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
                text-align: center;
                min-width: 300px;
            `;

            const spinner = document.createElement('div');
            spinner.className = 'spinner-border text-primary';
            spinner.style.cssText = 'width: 3rem; height: 3rem; margin-bottom: 15px;';
            spinner.setAttribute('role', 'status');

            const loadingText = document.createElement('div');
            loadingText.id = 'test-suite-execution-message';
            loadingText.style.cssText = 'font-size: 16px; color: #333; font-weight: 500;';
            loadingText.textContent = message;

            const subText = document.createElement('div');
            subText.style.cssText = 'font-size: 12px; color: #666; margin-top: 10px;';
            subText.textContent = 'Device screenshots and overlays are disabled for optimal performance';

            loadingContent.appendChild(spinner);
            loadingContent.appendChild(loadingText);
            loadingContent.appendChild(subText);
            loadingOverlay.appendChild(loadingContent);

            document.body.appendChild(loadingOverlay);
        } else {
            document.getElementById('test-suite-execution-message').textContent = message;
        }

        loadingOverlay.style.display = 'flex';
    }

    hideTestSuiteExecutionLoading() {
        const loadingOverlay = document.getElementById('test-suite-execution-loading');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
    }

    disableDeviceScreenshots() {
        // Disable automatic screenshot refreshing
        const refreshBtn = document.getElementById('refreshScreenBtn');
        if (refreshBtn) {
            refreshBtn.disabled = true;
            refreshBtn.title = 'Screenshot refresh disabled during test suite execution';
        }

        // Hide execution overlays
        if (window.executionOverlay) {
            window.executionOverlay.hide();
        }

        // Hide all device screen overlays
        const deviceScreenOverlays = document.querySelectorAll('.device-screen-overlay, .overlay-canvas, .loading-overlay');
        deviceScreenOverlays.forEach(overlay => {
            if (overlay) {
                overlay.style.display = 'none';
            }
        });

        // Hide fixed device screen if it exists
        if (window.fixedDeviceScreen) {
            window.fixedDeviceScreen.hide();
        }

        // Disable any automatic screenshot intervals
        if (window.screenshotInterval) {
            clearInterval(window.screenshotInterval);
            window.screenshotInterval = null;
        }

        // Hide device screen container overlays
        const deviceContainer = document.querySelector('.device-screen-container');
        if (deviceContainer) {
            const overlays = deviceContainer.querySelectorAll('.overlay, .device-overlay, .execution-overlay');
            overlays.forEach(overlay => {
                overlay.style.display = 'none';
            });
        }

        console.log('Device screenshots and overlays disabled for test suite execution');
    }

    enableDeviceScreenshots() {
        // Re-enable screenshot refreshing
        const refreshBtn = document.getElementById('refreshScreenBtn');
        if (refreshBtn) {
            refreshBtn.disabled = false;
            refreshBtn.title = 'Refresh device screenshot';
        }

        // Re-show device screen overlays
        const deviceScreenOverlays = document.querySelectorAll('.device-screen-overlay, .overlay-canvas');
        deviceScreenOverlays.forEach(overlay => {
            if (overlay) {
                overlay.style.display = '';
            }
        });

        // Re-show fixed device screen if it exists
        if (window.fixedDeviceScreen) {
            window.fixedDeviceScreen.show();
        }

        // Re-show device screen container overlays
        const deviceContainer = document.querySelector('.device-screen-container');
        if (deviceContainer) {
            const overlays = deviceContainer.querySelectorAll('.overlay, .device-overlay');
            overlays.forEach(overlay => {
                overlay.style.display = '';
            });
        }

        console.log('Device screenshots and overlays re-enabled after test suite execution');
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.testSuitesManager = new TestSuitesManager();
});