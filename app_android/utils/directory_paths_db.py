import os
import sqlite3
import logging
from pathlib import Path
import sys

# Add the parent directory to the path to import shared_directory_paths_db
sys.path.append(os.path.join(os.path.dirname(__file__), '..', '..'))
from shared_directory_paths_db import SharedDirectoryPathsDB

logger = logging.getLogger(__name__)

class DirectoryPathsDB(SharedDirectoryPathsDB):
    """
    Android-specific database handler for storing directory paths configuration
    Inherits from SharedDirectoryPathsDB to use shared database for directory paths
    and platform-specific database for environment variables
    """

    def __init__(self):
        # Don't call parent __init__ to avoid shared database creation
        self.platform = 'android'

        # Set the Android-specific settings database path directly
        from app_android.utils.database import get_db_path
        self.db_path = get_db_path('settings')
        self.platform_db_path = self.db_path

        logger.info(f"Android DirectoryPathsDB initialized with: {self.db_path}")

    def get_path(self, name, default=None):
        """
        Get a directory path from the Android settings database

        Args:
            name: The name/key of the directory (e.g., 'TEST_CASES', 'TEMP_FILES')
            default: Default value to return if the path is not found

        Returns:
            str: The path, or the default value if not found
        """
        try:
            from app_android.utils.database import get_db_path

            android_settings_db_path = get_db_path('settings')

            if not os.path.exists(android_settings_db_path):
                logger.warning(f"Android settings database not found: {android_settings_db_path}")
                return default

            conn = sqlite3.connect(android_settings_db_path)
            cursor = conn.cursor()

            # Try the standard key first
            cursor.execute("""
                SELECT setting_value FROM settings
                WHERE platform = 'android'
                AND category = 'directory_path'
                AND setting_key = ?
            """, (name,))

            result = cursor.fetchone()

            # If not found, try the Android-specific key (e.g., TEST_CASES_ANDROID)
            if not result:
                android_key = f"{name}_ANDROID"
                cursor.execute("""
                    SELECT setting_value FROM settings
                    WHERE platform = 'android'
                    AND category = 'directory_path'
                    AND setting_key = ?
                """, (android_key,))
                result = cursor.fetchone()

            conn.close()

            if result:
                path = result[0]
                logger.debug(f"Found path in Android settings database for {name}: {path}")
                return path
            else:
                logger.debug(f"No path found in Android settings database for {name}, using default: {default}")
                return default

        except Exception as e:
            logger.error(f"Error getting directory path from Android settings database: {str(e)}")
            return default

    def save_all_paths(self, paths_dict):
        """
        Save multiple directory paths to the Android settings database

        Args:
            paths_dict: Dictionary of directory paths {name: path}

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info(f"Attempting to save all paths to Android settings database: {paths_dict}")
            if not paths_dict:
                logger.warning("Empty paths dictionary provided to save_all_paths")
                return False

            from app_android.utils.database import get_db_path

            android_settings_db_path = get_db_path('settings')

            if not os.path.exists(android_settings_db_path):
                logger.error(f"Android settings database not found: {android_settings_db_path}")
                return False

            conn = sqlite3.connect(android_settings_db_path)
            cursor = conn.cursor()

            for name, path in paths_dict.items():
                logger.info(f"Processing Android path: {name} = {path}")

                # Check if the setting already exists
                cursor.execute("""
                    SELECT id FROM settings
                    WHERE platform = 'android'
                    AND category = 'directory_path'
                    AND setting_key = ?
                """, (name,))

                existing = cursor.fetchone()

                if existing:
                    # Update existing setting
                    cursor.execute("""
                        UPDATE settings
                        SET setting_value = ?, updated_at = CURRENT_TIMESTAMP
                        WHERE platform = 'android'
                        AND category = 'directory_path'
                        AND setting_key = ?
                    """, (path, name))
                    logger.info(f"Updated Android directory path: {name} = {path}")
                else:
                    # Insert new setting
                    cursor.execute("""
                        INSERT INTO settings (platform, category, setting_key, setting_value, description)
                        VALUES ('android', 'directory_path', ?, ?, ?)
                    """, (name, path, f"Directory path for {name}"))
                    logger.info(f"Inserted new Android directory path: {name} = {path}")

            conn.commit()
            conn.close()
            logger.info(f"All Android directory paths saved successfully: {paths_dict}")
            return True

        except Exception as e:
            logger.error(f"Error saving all directory paths to Android settings database: {str(e)}")
            return False

    def get_all_paths(self):
        """
        Get all directory paths from the Android settings database

        Returns:
            dict: Dictionary of all directory paths with standard keys
        """
        try:
            from app_android.utils.database import get_db_path

            android_settings_db_path = get_db_path('settings')

            if not os.path.exists(android_settings_db_path):
                logger.warning(f"Android settings database not found: {android_settings_db_path}")
                return {}

            conn = sqlite3.connect(android_settings_db_path)
            cursor = conn.cursor()

            cursor.execute("""
                SELECT setting_key, setting_value FROM settings
                WHERE platform = 'android'
                AND category = 'directory_path'
            """)

            results = cursor.fetchall()
            conn.close()

            # Convert Android-specific keys to standard keys
            paths_dict = {}
            for name, path in results:
                # Convert TEST_CASES_ANDROID to TEST_CASES, etc.
                if name.endswith('_ANDROID'):
                    standard_key = name[:-8]  # Remove '_ANDROID' suffix
                    paths_dict[standard_key] = path
                else:
                    paths_dict[name] = path

            logger.debug(f"Retrieved {len(paths_dict)} directory paths from Android settings database")
            return paths_dict

        except Exception as e:
            logger.error(f"Error getting all directory paths from Android settings database: {str(e)}")
            return {}

    def save_path(self, name, path):
        """
        Save a directory path to the Android settings database

        Args:
            name: The name/key of the directory (e.g., 'TEST_CASES', 'TEMP_FILES')
            path: The path to the directory

        Returns:
            bool: True if successful, False otherwise
        """
        try:
            from app_android.utils.database import get_db_path

            android_settings_db_path = get_db_path('settings')

            if not os.path.exists(android_settings_db_path):
                logger.error(f"Android settings database not found: {android_settings_db_path}")
                return False

            conn = sqlite3.connect(android_settings_db_path)
            cursor = conn.cursor()

            # Check if the setting already exists
            cursor.execute("""
                SELECT id FROM settings
                WHERE platform = 'android'
                AND category = 'directory_path'
                AND setting_key = ?
            """, (name,))

            existing = cursor.fetchone()

            if existing:
                # Update existing setting
                cursor.execute("""
                    UPDATE settings
                    SET setting_value = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE platform = 'android'
                    AND category = 'directory_path'
                    AND setting_key = ?
                """, (path, name))
                logger.info(f"Updated Android directory path: {name} = {path}")
            else:
                # Insert new setting
                cursor.execute("""
                    INSERT INTO settings (platform, category, setting_key, setting_value, description)
                    VALUES ('android', 'directory_path', ?, ?, ?)
                """, (name, path, f"Directory path for {name}"))
                logger.info(f"Inserted new Android directory path: {name} = {path}")

            conn.commit()
            conn.close()
            return True

        except Exception as e:
            logger.error(f"Error saving directory path to Android settings database: {str(e)}")
            return False

    def get_all_environments(self):
        """
        Get all environments from the Android-specific environments database.
        
        Returns:
            List of environment dictionaries with id, name, platform, etc.
        """
        try:
            from app_android.utils.database import get_db_path
            import sqlite3
            
            db_path = get_db_path('environments')
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Query environments for Android platform
            cursor.execute("""
                SELECT id, environment_id, name, description, is_active, created_at, updated_at
                FROM environments 
                WHERE platform = 'android'
                ORDER BY name
            """)
            
            environments = []
            for row in cursor.fetchall():
                environments.append({
                    'id': row[0],
                    'environment_id': row[1], 
                    'name': row[2],
                    'description': row[3],
                    'is_active': bool(row[4]),
                    'created_at': row[5],
                    'updated_at': row[6]
                })
            
            conn.close()
            logger.info(f"Retrieved {len(environments)} Android environments from database")
            return environments
            
        except Exception as e:
            logger.error(f"Error getting Android environments: {str(e)}")
            return []

# Create singleton instance
directory_paths_db = DirectoryPathsDB()