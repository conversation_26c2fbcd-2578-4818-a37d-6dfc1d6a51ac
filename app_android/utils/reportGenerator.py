import os
import glob
import re
import json
import shutil
import zipfile
from datetime import datetime
import logging
import subprocess
import tempfile
import traceback
import filecmp
import html

logger = logging.getLogger(__name__)

def getLatestReportUrl():
    """
    Get the URL path to the latest report

    Returns:
        str: URL path to the latest report or None if no reports found
    """
    logger.info("=== getLatestReportUrl() function called ===")
    try:
        # Use a simple approach to get the reports directory
        # First try to get from directory_paths_db
        reports_dir = None
        try:
            from .directory_paths_db import directory_paths_db
            reports_dir = directory_paths_db.get_path('REPORTS')
            if reports_dir:
                # Make it absolute
                if not os.path.isabs(reports_dir):
                    base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                    reports_dir = os.path.join(base_dir, reports_dir)
                logger.info(f"Using reports directory from database settings: {reports_dir}")
            else:
                logger.info("No reports directory found in database, using default")
        except Exception as e:
            logger.error(f"Error getting reports directory from database: {str(e)}")

        # Fallback to default path
        if not reports_dir:
            from .directory_utils import get_reports_directory
            reports_dir = get_reports_directory()
            logger.info(f"Using default reports directory: {reports_dir}")

        logger.info(f"Getting latest report URL from directory: {reports_dir}")

        # Check if the directory exists
        if not os.path.exists(reports_dir):
            logger.warning(f"Reports directory not found: {reports_dir}")
            return None

        # List all items in the directory for debugging
        try:
            all_items = os.listdir(reports_dir)
            logger.info(f"All items in reports directory: {all_items}")
        except Exception as e:
            logger.error(f"Error listing reports directory: {str(e)}")
            return None

        # Look for report directories with both old and new structure
        testsuite_dirs = [d for d in os.listdir(reports_dir) if d.startswith('testsuite_execution_') and os.path.isdir(os.path.join(reports_dir, d))]
        suite_dirs = [d for d in os.listdir(reports_dir) if d.startswith('suite_execution_') and os.path.isdir(os.path.join(reports_dir, d))]
        report_dirs = testsuite_dirs + suite_dirs
        logger.info(f"Found {len(testsuite_dirs)} testsuite_execution_ directories and {len(suite_dirs)} suite_execution_ directories: {report_dirs}")

        if not report_dirs:
            # Fall back to old structure if no new-style reports found
            import glob
            html_files = glob.glob(os.path.join(reports_dir, "*.html"))

            if not html_files:
                logger.info("No report files found")
                return None

            # Sort by creation time, newest first
            latest_file = max(html_files, key=os.path.getctime)

            # Get the filename
            filename = os.path.basename(latest_file)

            # Return URL path for legacy report
            return f"/reports/{filename}"

        # Sort directories by creation time, newest first, but only include those with mainreport.html
        valid_report_dirs = []
        for d in report_dirs:
            dir_path = os.path.join(reports_dir, d)
            report_path = os.path.join(dir_path, "mainreport.html")
            if os.path.exists(report_path):
                valid_report_dirs.append(dir_path)
                logger.info(f"Found valid report directory: {d}")
            else:
                logger.info(f"Skipping report directory without mainreport.html: {d}")

        if not valid_report_dirs:
            logger.warning("No report directories with mainreport.html found")
            return None

        # Get the latest valid report directory
        latest_dir = max(valid_report_dirs, key=os.path.getctime)
        dir_name = os.path.basename(latest_dir)

        # Return URL path for the new structure
        logger.info(f"Found latest valid report: {dir_name}")
        return f"/reports/{dir_name}/mainreport.html"
    except Exception as e:
        logger.error(f"Error getting latest report URL: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        return None

def ensure_report_directories(reports_dir, suite_id):
    """
    Ensures report directories exist with a UUID-based structure:
    reports/
        suite_execution_{suite_id}/
            screenshots/
            mainreport.html

    Args:
        reports_dir (str): Reports directory path
        suite_id (str): UUID-based suite identifier

    Returns:
        tuple: (report_dir, screenshots_dir) - Paths to the report and screenshots directories
    """
    # Create the main report directory with suite ID
    report_dir = os.path.join(reports_dir, f'suite_execution_{suite_id}')
    os.makedirs(report_dir, exist_ok=True)

    # Create the screenshots subdirectory
    screenshots_dir = os.path.join(report_dir, 'screenshots')
    os.makedirs(screenshots_dir, exist_ok=True)

    logger.info(f"Created report directory structure at: {report_dir}")
    return report_dir, screenshots_dir

def copy_screenshots_for_report(report_timestamp, test_data):
    """
    Copies screenshots to the report directory and updates the test data with the new paths.
    This simplified version only uses action_id-based naming for consistency.

    Args:
        report_timestamp (str): Timestamp for the report
        test_data (dict): Test data with steps that may contain screenshot paths

    Returns:
        tuple: (updated_test_data, report_dir) - Test data with updated screenshot paths and report directory path
    """
    try:
        # Import current_screenshots_dir from app
        try:
            import sys
            import os
            # Add the parent directory to sys.path to import from app
            current_dir = os.path.dirname(os.path.abspath(__file__))
            parent_dir = os.path.dirname(current_dir)
            if parent_dir not in sys.path:
                sys.path.insert(0, parent_dir)
            from app_android.app import current_screenshots_dir
        except ImportError:
            current_screenshots_dir = None

        # Get the reports directory path from the database
        try:
            from .directory_paths_db import directory_paths_db
            reports_dir = directory_paths_db.get_path('REPORTS')
            if reports_dir:
                # Make sure it's an absolute path
                if not os.path.isabs(reports_dir):
                    base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                    reports_dir = os.path.join(base_dir, reports_dir)
                logger.info(f"Using reports directory from database settings: {reports_dir}")
            else:
                # Fallback to default if not found in database
                from .directory_utils import get_reports_directory
                reports_dir = get_reports_directory()
                logger.info(f"Using default reports directory: {reports_dir}")
        except Exception as e:
            logger.error(f"Error getting reports directory from database: {str(e)}")
            # Fallback to default
            from .directory_utils import get_reports_directory
            reports_dir = get_reports_directory()
            logger.info(f"Using fallback reports directory: {reports_dir}")

        # Ensure the reports directory exists
        os.makedirs(reports_dir, exist_ok=True)

        logger.info(f"Using reports directory from settings: {reports_dir}")
        report_dir, screenshots_dir = ensure_report_directories(reports_dir, report_timestamp)

        # Get the app screenshots directory
        app_screenshots_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
                                          'app', 'static', 'screenshots')

        # Get a list of all available screenshots
        available_screenshots = []

        # Check current_screenshots_dir first (this is where the most recent screenshots should be)
        if current_screenshots_dir and os.path.exists(current_screenshots_dir):
            try:
                available_screenshots = [f for f in os.listdir(current_screenshots_dir) if f.endswith('.png') and f != 'latest.png']
                logger.info(f"Found {len(available_screenshots)} screenshots in current screenshots directory: {current_screenshots_dir}")
                for screenshot in available_screenshots:
                    logger.info(f"  - {screenshot}")
            except Exception as e:
                logger.error(f"Error listing current screenshots directory: {e}")

        # If no screenshots found in current_screenshots_dir, check app_screenshots_dir
        if not available_screenshots and os.path.exists(app_screenshots_dir):
            try:
                available_screenshots = [f for f in os.listdir(app_screenshots_dir) if f.endswith('.png') and f != 'latest.png']
                logger.info(f"Found {len(available_screenshots)} screenshots in app screenshots directory: {app_screenshots_dir}")
                for screenshot in available_screenshots:
                    logger.info(f"  - {screenshot}")
            except Exception as e:
                logger.error(f"Error listing app screenshots directory: {e}")

        # Create a mapping of step index to screenshot filename if we have the same number of steps as screenshots
        step_to_screenshot_map = {}
        total_steps = sum(len(test_case.get('steps', [])) for test_case in test_data.get('testCases', []))

        # If we have the same number of steps as screenshots, we can map them directly
        if total_steps == len(available_screenshots):
            logger.info("Number of steps matches number of screenshots - mapping directly")
            # Sort screenshots to ensure consistent ordering
            sorted_screenshots = sorted(available_screenshots)

            # Create a flat index for all steps across all test cases
            flat_index = 0
            for test_idx, test_case in enumerate(test_data.get('testCases', [])):
                for step_idx, step in enumerate(test_case.get('steps', [])):
                    if flat_index < len(sorted_screenshots):
                        step_to_screenshot_map[(test_idx, step_idx)] = sorted_screenshots[flat_index]
                        logger.info(f"Mapped test {test_idx}, step {step_idx} to screenshot {sorted_screenshots[flat_index]}")
                    flat_index += 1

        # Process each test case and step
        for test_idx, test_case in enumerate(test_data.get('testCases', [])):
            for step_idx, step in enumerate(test_case.get('steps', [])):
                try:
                    # Check if we have a direct mapping for this step
                    if (test_idx, step_idx) in step_to_screenshot_map:
                        # Use the mapped screenshot filename
                        step_screenshot = step_to_screenshot_map[(test_idx, step_idx)]
                        action_id = step_screenshot.replace('.png', '')
                        logger.info(f"Using mapped screenshot for test {test_idx}, step {step_idx}: {step_screenshot}")

                        # Set the action_id and screenshot filename
                        step['action_id'] = action_id
                        step['screenshot_filename'] = step_screenshot
                        step['report_screenshot'] = step_screenshot
                        step['resolved_screenshot'] = f"screenshots/{step_screenshot}"
                    # Check if we already have an action_id from the database or previous processing
                    elif 'action_id' in step and step['action_id']:
                        # Use the action_id for the filename
                        action_id = step['action_id']
                        step_screenshot = f"{action_id}.png"
                        logger.info(f"Using action_id for screenshot filename: {step_screenshot}")

                        # Add clean_action_id and prefixed_action_id for consistency
                        if action_id.startswith('al_'):
                            step['clean_action_id'] = action_id[3:]  # Remove 'al_' prefix
                            step['prefixed_action_id'] = action_id
                        else:
                            step['clean_action_id'] = action_id
                            step['prefixed_action_id'] = f"al_{action_id}"

                        # Check if this screenshot exists in available screenshots
                        if step_screenshot not in available_screenshots:
                            # Try to find a matching screenshot
                            matching_screenshots = [s for s in available_screenshots if s.startswith(action_id)]
                            if matching_screenshots:
                                step_screenshot = matching_screenshots[0]
                                action_id = step_screenshot.replace('.png', '')
                                step['action_id'] = action_id

                                # Update clean and prefixed action_id
                                if action_id.startswith('al_'):
                                    step['clean_action_id'] = action_id[3:]
                                    step['prefixed_action_id'] = action_id
                                else:
                                    step['clean_action_id'] = action_id
                                    step['prefixed_action_id'] = f"al_{action_id}"

                                logger.info(f"Found matching screenshot for action_id {action_id}: {step_screenshot}")
                    else:
                        # For new steps, generate a new action_id
                        import random
                        import string
                        chars = string.ascii_letters + string.digits
                        action_id = ''.join(random.choice(chars) for _ in range(10))
                        step['action_id'] = action_id

                        # Add clean_action_id and prefixed_action_id
                        step['clean_action_id'] = action_id
                        step['prefixed_action_id'] = f"al_{action_id}"

                        # Use the action_id for the filename
                        step_screenshot = f"{action_id}.png"
                        # Store the screenshot filename in the step data
                        step['screenshot_filename'] = step_screenshot
                        logger.info(f"Generated new action_id and screenshot filename: {step_screenshot}")

                    # Use the same filename for the report
                    report_screenshot = step_screenshot
                    # Store the raw filename without the path for the report
                    step['report_screenshot'] = report_screenshot
                    step['resolved_screenshot'] = f"screenshots/{report_screenshot}"
                    logger.info(f"Using report screenshot: {report_screenshot}")

                    # Set the target path in the report's screenshots directory
                    target_path = os.path.join(screenshots_dir, step_screenshot)
                    logger.info(f"Target screenshot path for report: {target_path}")

                    # Add timestamp to the step if not already present
                    if not step.get('timestamp'):
                        step['timestamp'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                    # Look for the screenshot in various locations
                    found_screenshot = False

                    # First check if the screenshot already exists in the report directory
                    if os.path.exists(target_path):
                        found_screenshot = True
                        logger.info(f"Screenshot already exists in report directory: {target_path}")
                    # Check if the screenshot exists in the current screenshots directory
                    elif current_screenshots_dir and os.path.exists(os.path.join(current_screenshots_dir, step_screenshot)):
                        source_path = os.path.join(current_screenshots_dir, step_screenshot)
                        shutil.copy2(source_path, target_path)
                        found_screenshot = True
                        logger.info(f"Copied screenshot from current screenshots directory: {source_path} -> {target_path}")
                    # Check if the screenshot exists in the app screenshots directory
                    elif os.path.exists(os.path.join(app_screenshots_dir, step_screenshot)):
                        source_path = os.path.join(app_screenshots_dir, step_screenshot)
                        shutil.copy2(source_path, target_path)
                        found_screenshot = True
                        logger.info(f"Copied screenshot from app screenshots directory: {source_path} -> {target_path}")

                    # Check for alternative format with 'al_' prefix
                    if not found_screenshot and action_id and not action_id.startswith('al_'):
                        # Try with 'al_' prefix
                        alt_screenshot = f"al_{action_id}.png"

                        # Check in current screenshots directory
                        if current_screenshots_dir and os.path.exists(os.path.join(current_screenshots_dir, alt_screenshot)):
                            source_path = os.path.join(current_screenshots_dir, alt_screenshot)
                            shutil.copy2(source_path, target_path)
                            found_screenshot = True
                            logger.info(f"Copied screenshot with 'al_' prefix from current screenshots directory: {source_path} -> {target_path}")
                        # Check in app screenshots directory
                        elif os.path.exists(os.path.join(app_screenshots_dir, alt_screenshot)):
                            source_path = os.path.join(app_screenshots_dir, alt_screenshot)
                            shutil.copy2(source_path, target_path)
                            found_screenshot = True
                            logger.info(f"Copied screenshot with 'al_' prefix from app screenshots directory: {source_path} -> {target_path}")

                    # If still not found, try to find an unused screenshot from available screenshots
                    if not found_screenshot and available_screenshots:
                        # Get list of screenshots already copied to the report directory
                        copied_screenshots = []
                        if os.path.exists(screenshots_dir):
                            copied_screenshots = os.listdir(screenshots_dir)

                        # Find screenshots that haven't been copied yet
                        unused_screenshots = [s for s in available_screenshots if s not in copied_screenshots]

                        if unused_screenshots:
                            # Use the first unused screenshot
                            unused_screenshot = unused_screenshots[0]
                            source_path = os.path.join(current_screenshots_dir or app_screenshots_dir, unused_screenshot)
                            shutil.copy2(source_path, target_path)
                            found_screenshot = True

                            # Update the step with the new action_id from the screenshot
                            action_id = unused_screenshot.replace('.png', '')
                            step['action_id'] = action_id
                            step['screenshot_filename'] = unused_screenshot
                            step['report_screenshot'] = unused_screenshot
                            step['resolved_screenshot'] = f"screenshots/{unused_screenshot}"

                            logger.info(f"Used unused screenshot for step: {source_path} -> {target_path}")

                            # Remove this screenshot from available_screenshots to avoid using it again
                            available_screenshots.remove(unused_screenshot)

                    # If not found, use latest.png as fallback
                    if not found_screenshot:
                        latest_path = os.path.join(app_screenshots_dir, 'latest.png')
                        if os.path.exists(latest_path):
                            shutil.copy2(latest_path, target_path)
                            found_screenshot = True
                            logger.info(f"Used latest.png as fallback: {latest_path} -> {target_path}")

                    # For takeScreenshot actions, also copy custom screenshots if they exist
                    step_name = step.get('name', '').lower()
                    if 'takescreenshot' in step_name and found_screenshot:
                        # Check if there are custom screenshot fields in the step data
                        custom_name = step.get('custom_screenshot_name')
                        custom_filename = step.get('custom_screenshot_filename')
                        custom_path = step.get('custom_screenshot_path')

                        if custom_name and custom_filename:
                            # Try to find and copy the custom screenshot
                            custom_source_paths = []

                            # Check in current screenshots directory
                            if current_screenshots_dir:
                                custom_source_paths.append(os.path.join(current_screenshots_dir, custom_filename))

                            # Check in app screenshots directory
                            custom_source_paths.append(os.path.join(app_screenshots_dir, custom_filename))

                            # Try to copy the custom screenshot
                            for custom_source_path in custom_source_paths:
                                if os.path.exists(custom_source_path):
                                    custom_target_path = os.path.join(screenshots_dir, custom_filename)
                                    try:
                                        shutil.copy2(custom_source_path, custom_target_path)
                                        logger.info(f"Copied custom screenshot for takeScreenshot action: {custom_source_path} -> {custom_target_path}")

                                        # Add custom screenshot info to step data
                                        step['custom_screenshot_name'] = custom_name
                                        step['custom_screenshot_filename'] = custom_filename
                                        step['custom_screenshot_path'] = f"screenshots/{custom_filename}"
                                        break
                                    except Exception as copy_error:
                                        logger.error(f"Error copying custom screenshot: {copy_error}")

                    # Update the step with the screenshot path
                    if found_screenshot:
                        # Update the path to point to the new screenshots directory structure
                        step['screenshot'] = f"screenshots/{step_screenshot}"
                        logger.info(f"Added screenshot to step {step_idx+1}: {step['screenshot']}")

                        # Verify the file exists in the target location
                        if os.path.exists(target_path):
                            file_size = os.path.getsize(target_path)
                            logger.info(f"Verified screenshot exists at {target_path} (size: {file_size} bytes)")
                        else:
                            logger.error(f"CRITICAL ERROR: Screenshot not found at expected location: {target_path}")

                except Exception as step_error:
                    logger.error(f"Error processing screenshot for step {step_idx+1}: {str(step_error)}")
                    logger.error(traceback.format_exc())

        # Log the files in the report screenshots directory
        try:
            if os.path.exists(screenshots_dir):
                report_files = os.listdir(screenshots_dir)
                logger.info(f"Files in report screenshots directory: {report_files}")
        except Exception as list_error:
            logger.error(f"Error listing report screenshots directory: {str(list_error)}")

        return test_data, report_dir
    except Exception as e:
        logger.error(f"Error copying screenshots for report: {str(e)}")
        traceback.print_exc()
        return test_data, None  # Return original data on error

def save_action_logs_to_file(logs, report_dir):
    """
    Save action logs to a text file in the report directory

    Args:
        logs (list): List of log entries
        report_dir (str): Path to the report directory

    Returns:
        str: Path to the log file
    """
    try:
        # Create the log file path
        log_file_path = os.path.join(report_dir, "action_log.txt")

        # Write the logs to the file
        with open(log_file_path, 'w') as f:
            f.write(f"Action Log - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 80 + "\n\n")

            for log_entry in logs:
                # Format: [timestamp] [type] message (action_id: ID)
                timestamp = log_entry.get('timestamp', datetime.now().strftime('%H:%M:%S'))
                log_type = log_entry.get('type', 'info')
                message = log_entry.get('message', '')
                action_id = log_entry.get('action_id', '')

                # Include action_id in the log message if available
                if action_id:
                    # For addLog actions, include the action_id in the message
                    if message.endswith('(with screenshot)'):
                        # This is likely an addLog action, so include the action_id in the message
                        f.write(f"[{timestamp}] [{log_type.upper()}] {message} (action_id: {action_id})\n")
                    else:
                        # For other actions, add (with screenshot) to make it easier to match
                        f.write(f"[{timestamp}] [{log_type.upper()}] {message} (with screenshot) (action_id: {action_id})\n")
                else:
                    f.write(f"[{timestamp}] [{log_type.upper()}] {message}\n")

        logger.info(f"Action logs saved to: {log_file_path}")
        return log_file_path
    except Exception as e:
        logger.error(f"Error saving action logs to file: {str(e)}")
        return None

def regenerate_report_from_data_json(report_dir):
    """
    Regenerate the HTML report from the data.json file in the report directory.
    This is useful for regenerating reports after the test execution has completed.

    Args:
        report_dir (str): Path to the report directory containing data.json

    Returns:
        bool: True if the report was successfully regenerated, False otherwise
    """
    try:
        # Check if the data.json file exists
        data_json_path = os.path.join(report_dir, "data.json")
        if not os.path.exists(data_json_path):
            logger.error(f"data.json not found in report directory: {report_dir}")
            return False

        # Load the data.json file
        with open(data_json_path, 'r') as f:
            test_data = json.load(f)
            logger.info(f"Loaded data.json from {data_json_path}")

        # Try to identify the test case file from the test data
        test_case_file = None
        for test_case in test_data.get('testCases', []):
            if 'id' in test_case:
                test_case_file = test_case.get('id')
                logger.info(f"Found test case ID in data.json: {test_case_file}")
                break

        # Try to load the original test case file to get the original action_ids
        original_action_ids = {}
        if test_case_file:
            try:
                # Try to load the test case file
                test_case_path = test_case_file
                if not os.path.exists(test_case_path):
                    test_case_path = os.path.join('test_cases', test_case_file)
                if not os.path.exists(test_case_path):
                    test_case_path = os.path.join('test_cases', f"{test_case_file}.json")

                if os.path.exists(test_case_path):
                    logger.info(f"Loading original test case file: {test_case_path}")
                    with open(test_case_path, 'r') as f:
                        original_test_case = json.load(f)

                    # Create a mapping of step index to original action_id
                    for idx, action in enumerate(original_test_case.get('actions', [])):
                        action_id = action.get('action_id', '')
                        if action_id:
                            original_action_ids[idx] = action_id
                            logger.info(f"Mapped step_idx {idx} to original action_id {action_id}")

                    logger.info(f"Loaded {len(original_action_ids)} original action_ids from test case file")
            except Exception as e:
                logger.error(f"Error loading original test case file: {str(e)}")

        # Scan the screenshots directory for existing action_id-based filenames
        screenshots_dir = os.path.join(report_dir, 'screenshots')
        if os.path.exists(screenshots_dir):
            # Get all PNG files in the screenshots directory
            all_screenshots = [f for f in os.listdir(screenshots_dir) if f.endswith('.png')]
            logger.info(f"Found {len(all_screenshots)} PNG files in {screenshots_dir}")

            # Filter for action_id-based screenshots
            action_id_files = {}
            action_id_screenshots = []
            for filename in all_screenshots:
                # Check if the filename matches the pattern of a 10-character alphanumeric string
                if re.match(r'^[a-zA-Z0-9]{10}\.png$', filename):
                    action_id = filename.replace('.png', '')
                    action_id_files[action_id] = filename
                    action_id_screenshots.append(filename)
                    logger.info(f"Found existing action_id screenshot: {filename}")

            # Log other screenshots
            other_screenshots = [f for f in all_screenshots if not re.match(r'^[a-zA-Z0-9]{10}\.png$', f)]
            logger.info(f"Found {len(other_screenshots)} other screenshots: {other_screenshots}")

            # Update the test data with the actual screenshot filenames
            if action_id_files:
                logger.info(f"Found {len(action_id_files)} action_id-based screenshots in {screenshots_dir}")

                # Process each test case and step
                for test_case in test_data.get('testCases', []):
                    for step_index, step in enumerate(test_case.get('steps', [])):
                        # Check if this step has a screenshot
                        if 'screenshot' in step or 'screenshot_filename' in step:
                            # Check if we have an existing action_id-based screenshot for this step
                            step_name = step.get('name', '')
                            logger.info(f"Processing step: {step_name}")

                            # IMPORTANT: First check if we have an original action_id from the test case file
                            if step_index in original_action_ids:
                                original_action_id = original_action_ids[step_index]
                                logger.info(f"Found original action_id for step {step_index}: {original_action_id}")

                                # Use the original action_id from the test case
                                step['action_id'] = original_action_id
                                step['screenshot_filename'] = f"{original_action_id}.png"
                                step['report_screenshot'] = f"{original_action_id}.png"
                                step['resolved_screenshot'] = f"screenshots/{original_action_id}.png"
                                step['screenshot'] = f"screenshots/{original_action_id}.png"

                                # Add clean_action_id and prefixed_action_id for consistency
                                if original_action_id.startswith('al_'):
                                    step['clean_action_id'] = original_action_id[3:]  # Remove 'al_' prefix
                                    step['prefixed_action_id'] = original_action_id
                                else:
                                    step['clean_action_id'] = original_action_id
                                    step['prefixed_action_id'] = f"al_{original_action_id}"

                                logger.info(f"Using original action_id from test case file: {original_action_id}")
                            # First check if the step already has an action_id
                            elif 'action_id' in step and step['action_id']:
                                action_id = step['action_id']
                                # Check if we have a file with this action_id
                                if action_id in action_id_files:
                                    # Use the existing action_id and filename
                                    step['screenshot_filename'] = f"{action_id}.png"
                                    step['report_screenshot'] = f"{action_id}.png"
                                    step['resolved_screenshot'] = f"screenshots/{action_id}.png"
                                    step['screenshot'] = f"screenshots/{action_id}.png"

                                    # Add clean_action_id and prefixed_action_id for consistency
                                    if action_id.startswith('al_'):
                                        step['clean_action_id'] = action_id[3:]  # Remove 'al_' prefix
                                        step['prefixed_action_id'] = action_id
                                    else:
                                        step['clean_action_id'] = action_id
                                        step['prefixed_action_id'] = f"al_{action_id}"

                                    logger.info(f"Updated step '{step_name}' with existing action_id: {action_id}")
                                else:
                                    # We have an action_id but no matching file, check if there's a file with 'al_' prefix
                                    if not action_id.startswith('al_') and f"al_{action_id}" in action_id_files:
                                        # Use the existing action_id with 'al_' prefix
                                        step['screenshot_filename'] = f"{action_id}.png"
                                        step['report_screenshot'] = f"{action_id}.png"
                                        step['resolved_screenshot'] = f"screenshots/{action_id}.png"
                                        step['screenshot'] = f"screenshots/{action_id}.png"

                                        # Add clean_action_id and prefixed_action_id for consistency
                                        step['clean_action_id'] = action_id
                                        step['prefixed_action_id'] = f"al_{action_id}"

                                        logger.info(f"Updated step '{step_name}' with action_id (using al_ prefix file): {action_id}")
                                    else:
                                        logger.info(f"Step has action_id {action_id} but no matching file found")

                            # Try to extract action_id from the step name (10 character alphanumeric)
                            elif not 'action_id' in step:
                                action_id_match = re.search(r'([a-zA-Z0-9]{10})', step_name)
                                if action_id_match:
                                    action_id = action_id_match.group(1)
                                    # Check if we have a file with this action_id
                                    if action_id in action_id_files:
                                        # Use the existing action_id and filename
                                        step['action_id'] = action_id
                                        step['screenshot_filename'] = f"{action_id}.png"
                                        step['report_screenshot'] = f"{action_id}.png"
                                        step['resolved_screenshot'] = f"screenshots/{action_id}.png"
                                        step['screenshot'] = f"screenshots/{action_id}.png"

                                        # Add clean_action_id and prefixed_action_id for consistency
                                        if action_id.startswith('al_'):
                                            step['clean_action_id'] = action_id[3:]  # Remove 'al_' prefix
                                            step['prefixed_action_id'] = action_id
                                        else:
                                            step['clean_action_id'] = action_id
                                            step['prefixed_action_id'] = f"al_{action_id}"

                                        logger.info(f"Updated step '{step_name}' with action_id from name: {action_id}")

                            # If we still don't have a match, try to assign an action_id by step index
                            if not 'action_id' in step or not step['action_id']:
                                if len(action_id_screenshots) > step_index:
                                    # Use the action_id from the screenshot filename
                                    screenshot_filename = action_id_screenshots[step_index]
                                    action_id = screenshot_filename.replace('.png', '')
                                    step['action_id'] = action_id
                                    step['screenshot_filename'] = f"{action_id}.png"
                                    step['report_screenshot'] = f"{action_id}.png"
                                    step['resolved_screenshot'] = f"screenshots/{action_id}.png"
                                    step['screenshot'] = f"screenshots/{action_id}.png"

                                    # Add clean_action_id and prefixed_action_id for consistency
                                    if action_id.startswith('al_'):
                                        step['clean_action_id'] = action_id[3:]  # Remove 'al_' prefix
                                        step['prefixed_action_id'] = action_id
                                    else:
                                        step['clean_action_id'] = action_id
                                        step['prefixed_action_id'] = f"al_{action_id}"

                                    logger.info(f"Assigned action_id {action_id} to step {step_index+1} by index")

                # Save the updated data.json file
                with open(data_json_path, 'w') as f:
                    json.dump(test_data, f, indent=2)
                    logger.info(f"Saved updated data.json with correct action_id values")

        # Get the path to the generate_report.js script
        from .directory_utils import get_reports_directory
        reports_dir = get_reports_directory()
        js_report_generator = os.path.join(reports_dir, 'generate_report.js')

        # Check if the script exists
        if not os.path.exists(js_report_generator):
            logger.error(f"generate_report.js not found at: {js_report_generator}")
            return False

        # Define the report file path
        report_path = os.path.join(report_dir, "mainreport.html")

        # Run the Node.js report generator
        cmd = ['node', js_report_generator, data_json_path, report_path]
        logger.info(f"Regenerating report with command: {' '.join(cmd)}")

        result = subprocess.run(cmd, capture_output=True, text=True)

        if result.returncode != 0:
            logger.error(f"Error regenerating report: {result.stderr}")
            return False

        # Verify the report was created
        if not os.path.exists(report_path):
            logger.error(f"Regenerated report file not found at: {report_path}")
            return False

        logger.info(f"Report successfully regenerated at: {report_path}")
        return True
    except Exception as e:
        logger.error(f"Error regenerating report: {str(e)}")
        traceback.print_exc()
        return False

def update_report_with_retry_results(report_dir, suite_id):
    """
    Update an existing report with retry results from the database

    Args:
        report_dir (str): Path to the report directory
        suite_id (str): Suite ID to get retry results for

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        # Find the data.json file in the report directory
        data_json_path = os.path.join(report_dir, "data.json")

        if not os.path.exists(data_json_path):
            logger.error(f"data.json not found in report directory: {report_dir}")
            return False

        # Load the existing data
        with open(data_json_path, 'r') as f:
            test_data = json.load(f)

        # Get updated execution tracking data from the database
        try:
            from app_android.utils.database import get_execution_tracking_for_suite
            execution_data = get_execution_tracking_for_suite(suite_id)

            if execution_data:
                # Update test data with latest execution results
                test_data = update_test_data_with_execution_results(test_data, execution_data)
                logger.info(f"Updated test data with {len(execution_data)} execution tracking records")
            else:
                logger.warning(f"No execution tracking data found for suite {suite_id}")
        except Exception as exec_error:
            logger.warning(f"Could not get execution tracking data: {str(exec_error)}")

        # Get test runs data to reflect retry results
        try:
            from app_android.utils.database import get_test_runs_summary, get_test_run_details
            test_runs = get_test_runs_summary()

            # Find matching test runs for this suite
            matching_runs = [run for run in test_runs if suite_id in run.get('execution_id', '')]

            if matching_runs:
                logger.info(f"Found {len(matching_runs)} matching test runs for suite {suite_id}")
                # Update test data with retry information from test runs
                for run in matching_runs:
                    if run.get('has_retries'):
                        # Mark test data as having retries
                        test_data['has_retries'] = True
                        test_data['retry_info'] = {
                            'execution_id': run.get('execution_id'),
                            'retry_count': run.get('failed_steps', 0)
                        }
        except Exception as test_runs_error:
            logger.warning(f"Could not get test runs data: {str(test_runs_error)}")

        # Save the updated data back to data.json
        with open(data_json_path, 'w') as f:
            json.dump(test_data, f, indent=2)

        # Regenerate the HTML report with updated data
        return regenerate_report_from_data_json(report_dir)

    except Exception as e:
        logger.error(f"Error updating report with retry results: {str(e)}")
        traceback.print_exc()
        return False

def update_test_data_with_execution_results(test_data, execution_data):
    """
    Update test data with the latest execution results from the database

    Args:
        test_data (dict): Original test data
        execution_data (list): Execution tracking data from database

    Returns:
        dict: Updated test data
    """
    try:
        # Create a mapping of action_id to execution result
        # Since execution_data is ordered by end_time DESC, the first occurrence of each action_id
        # will be the most recent execution status
        execution_map = {}
        for exec_item in execution_data:
            action_id = exec_item.get('action_id')
            if action_id and action_id not in execution_map:  # Only use the first (most recent) entry
                execution_map[action_id] = {
                    'status': exec_item.get('status', 'unknown'),
                    'retry_count': exec_item.get('retry_count', 0),
                    'max_retries': exec_item.get('max_retries', 0),
                    'error': exec_item.get('last_error', ''),
                    'end_time': exec_item.get('end_time', ''),
                    'in_progress': exec_item.get('in_progress', False)
                }

        # Update test cases with execution results using unique ID retry logic
        for test_case in test_data.get('testCases', []):
            # Group steps by action_id to handle retries properly
            action_groups = {}

            for step in test_case.get('steps', []):
                action_id = step.get('action_id')
                if action_id:
                    if action_id not in action_groups:
                        action_groups[action_id] = []
                    action_groups[action_id].append(step)

            # Update each step and determine final status for each action
            final_action_statuses = []

            for step in test_case.get('steps', []):
                action_id = step.get('action_id')
                if action_id and action_id in execution_map:
                    exec_result = execution_map[action_id]

                    # Update step status based on execution result
                    if exec_result['status'] == 'passed':
                        step['status'] = 'passed'
                    elif exec_result['status'] == 'failed':
                        step['status'] = 'failed'
                        if exec_result['error']:
                            step['error'] = exec_result['error']
                    elif exec_result['in_progress']:
                        step['status'] = 'running'

                    # Add retry information if available
                    if exec_result['retry_count'] > 0:
                        step['retry_count'] = exec_result['retry_count']
                        step['max_retries'] = exec_result['max_retries']
                        step['retried'] = True
                        test_case['has_retries'] = True

            # Determine final status for each unique action (considering retries)
            for action_id, steps in action_groups.items():
                if action_id in execution_map:
                    # Use the most recent execution result for this action
                    exec_result = execution_map[action_id]
                    final_action_statuses.append(exec_result['status'])

            # Determine overall test case status based on final action statuses
            if any(status == 'running' for status in final_action_statuses):
                test_case_status = 'running'
            elif any(status == 'failed' for status in final_action_statuses):
                test_case_status = 'failed'
            elif any(status == 'passed' for status in final_action_statuses):
                test_case_status = 'passed'
            else:
                test_case_status = 'passed'  # Default to passed if no clear status

            # Update test case status
            test_case['status'] = test_case_status

        # Update overall test suite status
        all_passed = all(tc.get('status') == 'passed' for tc in test_data.get('testCases', []))
        any_running = any(tc.get('status') == 'running' for tc in test_data.get('testCases', []))

        if any_running:
            test_data['status'] = 'running'
        elif all_passed:
            test_data['status'] = 'passed'
        else:
            test_data['status'] = 'failed'

        logger.info(f"Updated test data with execution results. Suite status: {test_data.get('status')}")
        return test_data

    except Exception as e:
        logger.error(f"Error updating test data with execution results: {str(e)}")
        return test_data

def create_zip_archive(report_dir):
    """
    Creates a ZIP archive of the report directory

    Args:
        report_dir (str): Path to the report directory

    Returns:
        str: Path to the created ZIP file
    """
    try:
        # Create a zip filename based on the directory name
        zip_filename = f"{os.path.basename(report_dir)}.zip"
        zip_path = os.path.join(os.path.dirname(report_dir), zip_filename)

        # Create the ZIP file
        with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
            # Walk through all files in the report directory
            for root, dirs, files in os.walk(report_dir):
                for file in files:
                    file_path = os.path.join(root, file)
                    # Calculate the relative path for the archive
                    rel_path = os.path.relpath(file_path, os.path.dirname(report_dir))
                    zipf.write(file_path, rel_path)
                    logger.info(f"Added file to ZIP archive: {rel_path}")

            # Make sure we have the assets directory and required files
            assets_dir = os.path.join(report_dir, 'assets')
            os.makedirs(assets_dir, exist_ok=True)

            # Copy report.css and report.js from the reports/assets directory if they don't exist
            from .directory_utils import get_reports_directory
            reports_dir = get_reports_directory()
            report_css_source = os.path.join(reports_dir, 'assets', 'report.css')
            report_css_target = os.path.join(assets_dir, 'report.css')
            if os.path.exists(report_css_source) and not os.path.exists(report_css_target):
                shutil.copy2(report_css_source, report_css_target)
                # Add the file to the ZIP archive
                rel_path = os.path.relpath(report_css_target, os.path.dirname(report_dir))
                zipf.write(report_css_target, rel_path)
                logger.info(f"Added report.css to ZIP archive: {rel_path}")

            report_js_source = os.path.join(reports_dir, 'assets', 'report.js')
            report_js_target = os.path.join(assets_dir, 'report.js')
            if os.path.exists(report_js_source) and not os.path.exists(report_js_target):
                shutil.copy2(report_js_source, report_js_target)
                # Add the file to the ZIP archive
                rel_path = os.path.relpath(report_js_target, os.path.dirname(report_dir))
                zipf.write(report_js_target, rel_path)
                logger.info(f"Added report.js to ZIP archive: {rel_path}")

            # Make sure we have the data.json file
            data_json_path = os.path.join(report_dir, 'data.json')
            if not os.path.exists(data_json_path):
                # Try to regenerate it from the mainreport.html file
                logger.info(f"data.json not found, trying to extract it from mainreport.html")
                report_path = os.path.join(report_dir, 'mainreport.html')
                if os.path.exists(report_path):
                    try:
                        with open(report_path, 'r') as f:
                            report_content = f.read()
                            # Extract the test data from the script content
                            test_data_match = re.search(r'const testData = (.*?);', report_content, re.DOTALL)
                            if test_data_match:
                                test_data_string = test_data_match.group(1)
                                # Save the test data to data.json
                                with open(data_json_path, 'w') as f:
                                    f.write(test_data_string)
                                    logger.info(f"Extracted test data from mainreport.html and saved to data.json")
                                # Add the file to the ZIP archive
                                rel_path = os.path.relpath(data_json_path, os.path.dirname(report_dir))
                                zipf.write(data_json_path, rel_path)
                                logger.info(f"Added data.json to ZIP archive: {rel_path}")
                    except Exception as extract_error:
                        logger.error(f"Error extracting test data from mainreport.html: {str(extract_error)}")

        logger.info(f"Created ZIP archive at: {zip_path}")
        return zip_path
    except Exception as e:
        logger.error(f"Error creating ZIP archive: {str(e)}")
        traceback.print_exc()
        return None

def prepare_steps_for_report(test_suite_data):
    """
    Prepare all steps for the report by ensuring they have action_ids and consistent screenshot naming.
    This function includes ALL steps in the report, not just addLog steps.

    Args:
        test_suite_data (dict): Test suite execution data

    Returns:
        dict: Prepared test suite data with all steps included
    """
    # Create a deep copy to avoid modifying the original
    import copy
    prepared_data = copy.deepcopy(test_suite_data)

    logger.info(f"Preparing steps for report. Test suite data has {len(prepared_data.get('testCases', []))} test cases")

    # Debug the structure of the test suite data
    logger.info(f"Test suite data structure: {prepared_data.keys()}")

    # Import database functions for getting action_id values
    from .database import get_action_id_for_step, get_screenshots_for_suite

    # Get the current screenshots directory
    try:
        import sys
        import os
        # Add the parent directory to sys.path to import from app
        current_dir = os.path.dirname(os.path.abspath(__file__))
        parent_dir = os.path.dirname(current_dir)
        if parent_dir not in sys.path:
            sys.path.insert(0, parent_dir)
        from app import current_screenshots_dir
    except ImportError:
        current_screenshots_dir = None

    # Get a list of actual screenshot files in the screenshots directory
    actual_screenshots = []
    if current_screenshots_dir and os.path.exists(current_screenshots_dir):
        try:
            actual_screenshots = [f for f in os.listdir(current_screenshots_dir) if f.endswith('.png') and f != 'latest.png']
            logger.info(f"Found {len(actual_screenshots)} actual screenshots in directory: {current_screenshots_dir}")
            for screenshot in actual_screenshots:
                logger.info(f"  - {screenshot}")
        except Exception as e:
            logger.error(f"Error listing screenshots directory: {e}")

    # Get all screenshots for this suite if we have a suite_id
    suite_id = prepared_data.get('suite_id')
    action_id_lookup = {}

    if suite_id:
        try:
            # Get all screenshots for this suite from the database
            screenshots = get_screenshots_for_suite(suite_id)
            logger.info(f"Found {len(screenshots)} screenshots in database for suite {suite_id}")

            # Create a lookup table by action_id
            for screenshot in screenshots:
                if screenshot.get('action_id'):
                    action_id_lookup[screenshot['action_id']] = screenshot
                    logger.info(f"Added screenshot to action_id lookup: {screenshot['action_id']} -> {screenshot.get('filename')}")
        except Exception as db_error:
            logger.error(f"Error getting screenshots from database: {str(db_error)}")

    # Try to load the original test case file to get the original action_ids
    original_action_ids = {}
    test_case_id = None

    # Check if we have a test case ID in the data
    for test_case in prepared_data.get('testCases', []):
        if 'id' in test_case:
            test_case_id = test_case.get('id')
            break

    if test_case_id:
        try:
            # Try to load the test case file
            test_case_path = test_case_id
            if not os.path.exists(test_case_path):
                test_case_path = os.path.join('test_cases', test_case_id)
            if not os.path.exists(test_case_path):
                test_case_path = os.path.join('test_cases', f"{test_case_id}.json")

            if os.path.exists(test_case_path):
                logger.info(f"Loading original test case file: {test_case_path}")
                with open(test_case_path, 'r') as f:
                    original_test_case = json.load(f)

                # Create a mapping of step index to original action_id
                for idx, action in enumerate(original_test_case.get('actions', [])):
                    action_id = action.get('action_id', '')
                    if action_id:
                        original_action_ids[idx] = action_id
                        logger.info(f"Mapped step_idx {idx} to original action_id {action_id}")

                logger.info(f"Loaded {len(original_action_ids)} original action_ids from test case file")
        except Exception as e:
            logger.error(f"Error loading original test case file: {str(e)}")

    for test_idx, test_case in enumerate(prepared_data.get('testCases', [])):
        # Process all steps in the test case
        original_steps = test_case.get('steps', [])

        logger.info(f"Test case #{test_idx+1}: '{test_case.get('name', 'Unnamed')}' has {len(original_steps)} steps")

        # Debug the structure of the test case
        logger.info(f"Test case structure: {test_case.keys()}")

        # Create a new list to store all processed steps
        processed_steps = []

        # Create a mapping of step index to screenshot filename if we have the same number of steps as screenshots
        step_to_screenshot_map = {}

        # If we have the same number of steps as screenshots, we can map them directly
        if len(original_steps) == len(actual_screenshots):
            logger.info("Number of steps matches number of screenshots - mapping directly")
            for i, screenshot in enumerate(sorted(actual_screenshots)):
                step_to_screenshot_map[i] = screenshot.replace('.png', '')
                logger.info(f"Mapped step {i} to screenshot {screenshot}")

        for step_idx, step in enumerate(original_steps):
            # Log the step structure for debugging
            logger.info(f"Step {step_idx+1} structure: {step}")

            # Make a copy of the step to avoid modifying the original
            processed_step = copy.deepcopy(step)

            # IMPORTANT: First check if we have an original action_id from the test case file
            if step_idx in original_action_ids:
                original_action_id = original_action_ids[step_idx]
                logger.info(f"Found original action_id for step {step_idx}: {original_action_id}")

                # Use the original action_id from the test case
                processed_step['action_id'] = original_action_id
                action_id_filename = f"{original_action_id}.png"
                processed_step['screenshot_filename'] = action_id_filename
                processed_step['report_screenshot'] = action_id_filename
                processed_step['resolved_screenshot'] = f"screenshots/{action_id_filename}"

                # Add clean_action_id and prefixed_action_id for consistency
                if original_action_id.startswith('al_'):
                    processed_step['clean_action_id'] = original_action_id[3:]  # Remove 'al_' prefix
                    processed_step['prefixed_action_id'] = original_action_id
                else:
                    processed_step['clean_action_id'] = original_action_id
                    processed_step['prefixed_action_id'] = f"al_{original_action_id}"

                logger.info(f"Using original action_id from test case file: {original_action_id}")
            # Check if we have a direct mapping for this step
            elif step_idx in step_to_screenshot_map:
                # Use the mapped screenshot filename
                action_id = step_to_screenshot_map[step_idx]
                logger.info(f"Using mapped screenshot for step {step_idx}: {action_id}")

                # Set the action_id and screenshot filename
                processed_step['action_id'] = action_id
                action_id_filename = f"{action_id}.png"
                processed_step['screenshot_filename'] = action_id_filename
                processed_step['report_screenshot'] = action_id_filename
                processed_step['resolved_screenshot'] = f"screenshots/{action_id_filename}"
            # Preserve action_id if available and it matches an actual screenshot
            elif 'action_id' in step and f"{step['action_id']}.png" in actual_screenshots:
                processed_step['action_id'] = step['action_id']
                logger.info(f"Preserved action_id in step: {step['action_id']} (matches actual screenshot)")

                # Use the action_id for the screenshot filename
                action_id_filename = f"{step['action_id']}.png"
                processed_step['screenshot_filename'] = action_id_filename
                processed_step['report_screenshot'] = action_id_filename
                processed_step['resolved_screenshot'] = f"screenshots/{action_id_filename}"
                logger.info(f"Using action_id for screenshot filename: {action_id_filename}")
            # Preserve action_id if available but check if we need to find a matching screenshot
            elif 'action_id' in step:
                action_id = step['action_id']
                processed_step['action_id'] = action_id
                logger.info(f"Preserved action_id in step: {action_id} (checking for matching screenshot)")

                # Check if we have any screenshots that start with this action_id
                matching_screenshots = [s for s in actual_screenshots if s.startswith(f"{action_id}")]
                if matching_screenshots:
                    # Use the first matching screenshot
                    screenshot_filename = matching_screenshots[0]
                    action_id = screenshot_filename.replace('.png', '')
                    processed_step['action_id'] = action_id
                    processed_step['screenshot_filename'] = screenshot_filename
                    processed_step['report_screenshot'] = screenshot_filename
                    processed_step['resolved_screenshot'] = f"screenshots/{screenshot_filename}"
                    logger.info(f"Found matching screenshot for action_id {action_id}: {screenshot_filename}")
                else:
                    # No matching screenshot, use the original action_id
                    action_id_filename = f"{action_id}.png"
                    processed_step['screenshot_filename'] = action_id_filename
                    processed_step['report_screenshot'] = action_id_filename
                    processed_step['resolved_screenshot'] = f"screenshots/{action_id_filename}"
                    logger.info(f"No matching screenshot found for action_id {action_id}, using original")
            else:
                # If no action_id is found, try to find it from the database
                if suite_id:
                    action_id = get_action_id_for_step(suite_id, test_idx, step_idx)
                    if action_id:
                        processed_step['action_id'] = action_id
                        logger.info(f"Found action_id from database: {action_id} for step {step_idx} in test {test_idx}")

                        # Use the action_id for the screenshot filename
                        action_id_filename = f"{action_id}.png"
                        processed_step['screenshot_filename'] = action_id_filename
                        processed_step['report_screenshot'] = action_id_filename
                        processed_step['resolved_screenshot'] = f"screenshots/{action_id_filename}"
                        logger.info(f"Using action_id from database for screenshot filename: {action_id_filename}")

                # If we still don't have an action_id, check if the step name contains a pattern like "XXXXXXXXXX"
                if 'action_id' not in processed_step and 'name' in step:
                    action_id_match = re.search(r'([a-zA-Z0-9]{10})', step.get('name', ''))
                    if action_id_match:
                        action_id = action_id_match.group(1)
                        processed_step['action_id'] = action_id
                        logger.info(f"Extracted action_id from step name: {action_id}")

                        # Use the action_id for the screenshot filename
                        action_id_filename = f"{action_id}.png"
                        processed_step['screenshot_filename'] = action_id_filename
                        processed_step['report_screenshot'] = action_id_filename
                        processed_step['resolved_screenshot'] = f"screenshots/{action_id_filename}"
                        logger.info(f"Using action_id from step name for screenshot filename: {action_id_filename}")

                # If we still don't have an action_id, try to find an unused screenshot
                if 'action_id' not in processed_step and actual_screenshots:
                    # Find screenshots that haven't been assigned to any step yet
                    used_screenshots = [processed_step.get('screenshot_filename') for processed_step in processed_steps]
                    unused_screenshots = [s for s in actual_screenshots if s not in used_screenshots]

                    if unused_screenshots:
                        # Use the first unused screenshot
                        screenshot_filename = unused_screenshots[0]
                        action_id = screenshot_filename.replace('.png', '')
                        processed_step['action_id'] = action_id
                        processed_step['screenshot_filename'] = screenshot_filename
                        processed_step['report_screenshot'] = screenshot_filename
                        processed_step['resolved_screenshot'] = f"screenshots/{screenshot_filename}"
                        logger.info(f"Using unused screenshot for step {step_idx}: {screenshot_filename}")
                    else:
                        # No unused screenshots left, use a placeholder
                        logger.warning(f"No unused screenshots left for step {step_idx}, using placeholder")
                        action_id = "placeholder_report"
                        processed_step['action_id'] = action_id
                        action_id_filename = f"{action_id}.png"
                        processed_step['screenshot_filename'] = action_id_filename
                        processed_step['report_screenshot'] = action_id_filename
                        processed_step['resolved_screenshot'] = f"screenshots/{action_id_filename}"
                        logger.warning(f"Using placeholder action_id for screenshot filename: {action_id_filename}")
                # If we still don't have an action_id, use a placeholder
                elif 'action_id' not in processed_step:
                    # We should never need to generate a new action_id here
                    # Log a warning and use a placeholder action_id
                    logger.warning("No action_id found for step - this should not happen")
                    # Use a placeholder that's clearly not a real action_id
                    action_id = "placeholder_report"
                    processed_step['action_id'] = action_id
                    logger.warning(f"Using placeholder action_id for step: {action_id}")

                    # Use the action_id for the screenshot filename
                    action_id_filename = f"{action_id}.png"
                    processed_step['screenshot_filename'] = action_id_filename
                    processed_step['report_screenshot'] = action_id_filename
                    processed_step['resolved_screenshot'] = f"screenshots/{action_id_filename}"
                    logger.warning(f"Using placeholder action_id for screenshot filename: {action_id_filename}")

            # Add the step to processed steps
            processed_steps.append(processed_step)
            logger.info(f"Added step to processed steps: {processed_step.get('name', 'Unnamed step')}")

        # Replace the original steps with processed steps
        test_case['steps'] = processed_steps
        logger.info(f"Test case #{test_idx+1} now has {len(processed_steps)} processed steps")

    return prepared_data

def generateReport(test_suite_data, reports_directory=None, test_case_file=None):
    """
    Generates an HTML report for a test suite execution by calling the JavaScript report generator.
    Uses a simplified directory structure:
    reports/
        testsuite_execution_timestamp/
            screenshots/
            mainreport.html
    And creates a ZIP archive of the entire report.

    This function can use screenshot paths from the database if they are available in the test_suite_data.
    The screenshots_map in test_suite_data is used to map test case and step IDs to screenshot filenames.

    Args:
        test_suite_data (dict): Test suite execution data
        reports_directory (str, optional): Reports directory path from Settings tab configuration
        test_case_file (str, optional): Path to the test case file, used to extract original action_ids
    """
    # Reset the image counter for a new report
    try:
        import sys
        import os
        # Add the parent directory to sys.path to import from app
        current_dir = os.path.dirname(os.path.abspath(__file__))
        parent_dir = os.path.dirname(current_dir)
        if parent_dir not in sys.path:
            sys.path.insert(0, parent_dir)
        from app import current_image_counter
        current_image_counter.value = 1
    except ImportError:
        pass

    """
    Returns:
        tuple: (report_path, zip_path) - Paths to the generated report file and ZIP archive
    """
    # Prepare all steps for the report, ensuring they have action_ids and consistent screenshot naming
    test_suite_data = prepare_steps_for_report(test_suite_data)

    # Check if we have a test case file to get original action_ids
    test_case_file = test_case_file
    original_action_ids = {}

    # Try to get the test case ID from the test suite data
    if test_case_file:
        try:
            # Try to load the test case file
            test_case_path = test_case_file
            if not os.path.exists(test_case_path):
                test_case_path = os.path.join('test_cases', test_case_file)
            if not os.path.exists(test_case_path):
                test_case_path = os.path.join('test_cases', f"{test_case_file}.json")

            if os.path.exists(test_case_path):
                logger.info(f"Loading original test case file for action_ids: {test_case_path}")
                with open(test_case_path, 'r') as f:
                    original_test_case = json.load(f)

                # Create a mapping of step index to original action_id
                for idx, action in enumerate(original_test_case.get('actions', [])):
                    action_id = action.get('action_id', '')
                    if action_id:
                        original_action_ids[idx] = action_id
                        logger.info(f"Mapped step_idx {idx} to original action_id {action_id}")

                logger.info(f"Loaded {len(original_action_ids)} original action_ids from test case file")
        except Exception as e:
            logger.error(f"Error loading original test case file: {str(e)}")

    # Ensure all steps have consistent screenshot naming
    for test_idx, test_case in enumerate(test_suite_data.get('testCases', [])):
        for step_idx, step in enumerate(test_case.get('steps', [])):
            # Use the standardized format for the filename with 1-based step indexing
            # to match the database and UI display
            adjusted_step_idx = step_idx + 1

            # IMPORTANT: First check if we have an original action_id from the test case file
            if step_idx in original_action_ids:
                original_action_id = original_action_ids[step_idx]
                logger.info(f"Found original action_id for step {step_idx}: {original_action_id}")

                # Use the original action_id from the test case
                step['action_id'] = original_action_id
                standardized_filename = f"{original_action_id}.png"
                logger.info(f"Using original action_id from test case file: {original_action_id}")
            # Always use action_id for the filename if it exists
            elif step.get('action_id'):
                action_id = step.get('action_id')
                # Use the action_id for the filename
                standardized_filename = f"{action_id}.png"
                logger.info(f"Using existing action_id for screenshot filename: {standardized_filename}")
            else:
                # If no action_id, generate one
                import random
                import string
                chars = string.ascii_letters + string.digits
                action_id = ''.join(random.choice(chars) for _ in range(10))
                step['action_id'] = action_id
                logger.info(f"Generated new action_id for step: {action_id}")

                # Use the action_id for the filename
                standardized_filename = f"{action_id}.png"
                logger.info(f"Using generated action_id for screenshot filename: {standardized_filename}")

            # Set the screenshot filename consistently
            step['screenshot_filename'] = standardized_filename

            # Set the report screenshot path consistently
            step['report_screenshot'] = step['screenshot_filename']

            # If there's a resolved_screenshot path, update it too
            if 'resolved_screenshot' in step:
                step['resolved_screenshot'] = f"screenshots/{step['screenshot_filename']}"
            else:
                step['resolved_screenshot'] = f"screenshots/{step['screenshot_filename']}"

            logger.info(f"Standardized screenshot for test {test_idx}, step {step_idx+1}: {standardized_filename}")
    try:
        # Get the paths
        current_dir = os.path.dirname(os.path.abspath(__file__))

        # Use provided reports directory or get from database
        if reports_directory:
            reports_dir = reports_directory
            logger.info(f"Using provided reports directory: {reports_dir}")
        else:
            # Get the reports directory path from the database
            try:
                from app_android.utils.directory_paths_db import directory_paths_db
                reports_dir = directory_paths_db.get_path('REPORTS')
                if reports_dir:
                    # Make sure it's an absolute path
                    if not os.path.isabs(reports_dir):
                        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                        reports_dir = os.path.join(base_dir, reports_dir)
                    logger.info(f"Using reports directory from database settings: {reports_dir}")
                else:
                    # Fallback to default if not found in database
                    from .directory_utils import get_reports_directory
                    reports_dir = get_reports_directory()
                    logger.info(f"Using default reports directory: {reports_dir}")
            except Exception as e:
                logger.error(f"Error getting reports directory from database: {str(e)}")
                # Fallback to default
                from .directory_utils import get_reports_directory
                reports_dir = get_reports_directory()
                logger.info(f"Using fallback reports directory: {reports_dir}")

        # Ensure the reports directory exists
        os.makedirs(reports_dir, exist_ok=True)

        logger.info(f"Using reports directory from settings: {reports_dir}")
        js_report_generator = os.path.join(reports_dir, 'generate_report.js')

        logger.info(f"Using report generator at: {js_report_generator}")

        # Check if reportGenerator.js exists
        if not os.path.exists(js_report_generator):
            logger.error(f"generate_report.js not found at: {js_report_generator}")
            raise FileNotFoundError(f"generate_report.js not found at: {js_report_generator}")

        # Check if we have a current report directory from the app
        try:
            import sys
            import os
            import threading

            # Add the parent directory to sys.path to import from app
            current_dir = os.path.dirname(os.path.abspath(__file__))
            parent_dir = os.path.dirname(current_dir)
            if parent_dir not in sys.path:
                sys.path.insert(0, parent_dir)

            # Thread-safe import to avoid signal handling issues
            if threading.current_thread() is threading.main_thread():
                from app_android.app import current_report_dir, current_report_timestamp, current_screenshots_dir
            else:
                # For non-main threads, try to get values without triggering signal handlers
                try:
                    # Import the module without executing signal registration
                    import app_android.app as app_module
                    current_report_dir = getattr(app_module, 'current_report_dir', None)
                    current_report_timestamp = getattr(app_module, 'current_report_timestamp', None)
                    current_screenshots_dir = getattr(app_module, 'current_screenshots_dir', None)
                except Exception:
                    current_report_dir = None
                    current_report_timestamp = None
                    current_screenshots_dir = None
        except (ImportError, Exception) as e:
            logger.warning(f"Could not import app variables safely: {e}")
            current_report_dir = None
            current_report_timestamp = None
            current_screenshots_dir = None

        # Check if we already have a report directory in the suite data
        if 'report_dir' in test_suite_data and test_suite_data['report_dir']:
            logger.info(f"Using report directory from suite data: {test_suite_data['report_dir']}")
            report_dir = test_suite_data['report_dir']
        elif current_report_dir and os.path.exists(current_report_dir):
            logger.info(f"Using existing report directory: {current_report_dir}")
            report_dir = current_report_dir
        else:
            # Get or generate suite ID for the report
            suite_id = test_suite_data.get('id')
            if not suite_id:
                import uuid
                suite_id = str(uuid.uuid4())
                test_suite_data['id'] = suite_id
                logger.info(f"Generated new suite ID: {suite_id}")

            # Generate timestamp for backward compatibility
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Check if the test suite data has screenshots from the database
            has_db_screenshots = False
            for test_case in test_suite_data.get('testCases', []):
                for step in test_case.get('steps', []):
                    if 'screenshot' in step and step['screenshot']:
                        has_db_screenshots = True
                        break
                if has_db_screenshots:
                    break

            # Create the report directory structure
            report_dir, screenshots_dir = ensure_report_directories(reports_dir, timestamp)

            # Always use copy_screenshots_for_report for consistency
            logger.info("Using screenshots from file system")
            test_suite_data, report_dir = copy_screenshots_for_report(timestamp, test_suite_data)

        if not report_dir:
            logger.error("Failed to create report directory")
            raise Exception("Failed to create report directory")

        # Create assets directory and copy custom CSS
        assets_dir = os.path.join(report_dir, 'assets')
        os.makedirs(assets_dir, exist_ok=True)
        logger.info(f"Created assets directory: {assets_dir}")

        # Copy custom CSS file if it exists
        custom_css_source = os.path.join(reports_dir, 'assets', 'custom.css')
        custom_css_target = os.path.join(assets_dir, 'custom.css')
        if os.path.exists(custom_css_source):
            shutil.copy2(custom_css_source, custom_css_target)
            logger.info(f"Copied custom CSS to report assets: {custom_css_target}")
        else:
            logger.warning(f"Custom CSS file not found at: {custom_css_source}")

        # Copy report.js if it exists
        report_js_source = os.path.join(reports_dir, 'assets', 'report.js')
        report_js_target = os.path.join(assets_dir, 'report.js')
        if os.path.exists(report_js_source):
            shutil.copy2(report_js_source, report_js_target)
            logger.info(f"Copied report.js to report assets: {report_js_target}")
        else:
            logger.warning(f"report.js file not found at: {report_js_source}")

        # Extra debug: List all screenshots in the directory
        screenshots_dir = os.path.join(report_dir, 'screenshots')
        if os.path.exists(screenshots_dir):
            screenshot_files = os.listdir(screenshots_dir)
            logger.info(f"Screenshots in directory {screenshots_dir}: {screenshot_files}")

            # Check for duplicate screenshots with the same content
            try:
                # Get all addlog_image files
                addlog_files = glob.glob(os.path.join(screenshots_dir, "addlog_image_*.png"))
                if len(addlog_files) > 1:
                    logger.info(f"Found {len(addlog_files)} addlog_image files, checking for duplicates")

                    # Sort by numeric index in the filename
                    def get_image_number(filename):
                        try:
                            # Extract the number from addlog_image_X.png
                            match = re.search(r'addlog_image_(\d+)\.png', os.path.basename(filename))
                            if match:
                                return int(match.group(1))
                            return 0
                        except:
                            return 0

                    # Sort by image number
                    addlog_files.sort(key=get_image_number)

                    # Check if files with different names have identical content
                    # We'll only remove files that are exact duplicates of another file
                    duplicates_to_remove = []

                    # Create a dictionary to track unique files by content hash
                    unique_files = {}

                    for file_path in addlog_files:
                        try:
                            # Check if this file is identical to any we've already seen
                            is_duplicate = False
                            for unique_path in unique_files.values():
                                if os.path.exists(file_path) and os.path.exists(unique_path):
                                    if filecmp.cmp(file_path, unique_path, shallow=False):
                                        # This is a duplicate of a file we've already seen
                                        duplicates_to_remove.append(file_path)
                                        is_duplicate = True
                                        logger.info(f"Found duplicate content: {file_path} is identical to {unique_path}")
                                        break

                            # If not a duplicate, add to our unique files
                            if not is_duplicate:
                                file_number = get_image_number(file_path)
                                unique_files[file_number] = file_path
                        except Exception as cmp_error:
                            logger.warning(f"Error comparing files: {str(cmp_error)}")

                    # Only remove files that are exact duplicates
                    for file_path in duplicates_to_remove:
                        try:
                            os.remove(file_path)
                            logger.info(f"Removed duplicate addlog_image file: {file_path}")
                        except Exception as rm_error:
                            logger.warning(f"Could not remove duplicate addlog_image file: {file_path}, error: {str(rm_error)}")

                    # List remaining screenshots after cleanup
                    screenshot_files = os.listdir(screenshots_dir)
                    logger.info(f"Screenshots in directory after cleanup: {screenshot_files}")
            except Exception as cleanup_error:
                logger.error(f"Error checking for duplicate screenshots: {str(cleanup_error)}")

        # Create a temporary JSON file with the test suite data
        temp_data_file = os.path.join(current_dir, 'temp_test_data.json')
        with open(temp_data_file, 'w') as f:
            json.dump(test_suite_data, f)
            logger.info(f"Created temporary JSON file at: {temp_data_file}")

        # Define the report file path in the new structure
        report_filename = "mainreport.html"
        report_path = os.path.join(report_dir, report_filename)

        # Save the test data as data.json in the report directory for future use
        data_json_path = os.path.join(report_dir, "data.json")
        with open(data_json_path, 'w') as f:
            json.dump(test_suite_data, f)
            logger.info(f"Saved test data to data.json at: {data_json_path}")

        # Modify the JavaScript command to output to our specific file
        cmd = ['node', js_report_generator, data_json_path, report_path]
        logger.info(f"Executing command: {' '.join(cmd)}")

        result = subprocess.run(cmd, capture_output=True, text=True)

        # We keep the data.json file in the report directory for future use
        # but clean up the temporary file
        if os.path.exists(temp_data_file):
            os.remove(temp_data_file)
            logger.info(f"Removed temporary JSON file: {temp_data_file}")

        if result.returncode != 0:
            logger.error(f"Error generating report: {result.stderr}")
            raise Exception(f"Report generation failed: {result.stderr}")

        # Verify the report was created
        if not os.path.exists(report_path):
            logger.error(f"Report file not found at: {report_path}")
            raise Exception(f"Report file was not created: {report_path}")

        # Save action logs if they exist
        try:
            import sys
            import os
            # Add the parent directory to sys.path to import from app
            current_dir = os.path.dirname(os.path.abspath(__file__))
            parent_dir = os.path.dirname(current_dir)
            if parent_dir not in sys.path:
                sys.path.insert(0, parent_dir)
            from app_android.app import current_action_logs
            if current_action_logs:
                logger.info(f"Saving {len(current_action_logs)} action logs to report directory")
                log_file_path = save_action_logs_to_file(current_action_logs, report_dir)
                if log_file_path:
                    logger.info(f"Action logs saved to: {log_file_path}")
        except Exception as log_error:
            logger.error(f"Error saving action logs: {str(log_error)}")

        # Make sure the action log file exists in the report directory
        action_log_path = os.path.join(report_dir, "action_log.txt")
        if not os.path.exists(action_log_path):
            logger.warning(f"Action log file not found at: {action_log_path}")
            # Create an empty action log file if it doesn't exist
            try:
                with open(action_log_path, 'w') as f:
                    f.write(f"Action Log - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write("=" * 80 + "\n\n")
                    f.write("No action logs were recorded for this test run.\n")
                logger.info(f"Created empty action log file at: {action_log_path}")
            except Exception as e:
                logger.error(f"Error creating empty action log file: {str(e)}")

        # Create a ZIP archive of the report directory
        zip_path = create_zip_archive(report_dir)

        logger.info(f"Report generated successfully: {report_path}")
        logger.info(f"ZIP archive created: {zip_path}")

        return report_path, zip_path

    except Exception as e:
        logger.error(f"Error generating report: {str(e)}")

        # Fallback: Generate a simple HTML report directly
        try:
            # Generate timestamp for the report
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Use provided reports directory or get from database
            if reports_directory:
                reports_dir = reports_directory
                logger.info(f"Using provided reports directory for fallback: {reports_dir}")
            else:
                # Get the reports directory path from the database
                try:
                    from app_android.utils.directory_paths_db import directory_paths_db
                    reports_dir = directory_paths_db.get_path('REPORTS')
                    if not reports_dir:
                        # Fallback to default if not found in database
                        reports_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'reports')
                except Exception as e:
                    logger.error(f"Error getting reports directory from database: {str(e)}")
                    # Fallback to default
                    reports_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'reports')

            logger.info(f"Using reports directory from settings: {reports_dir}")

            # Create the report directory structure
            report_dir, screenshots_dir = ensure_report_directories(reports_dir, timestamp)
            report_filename = "mainreport.html"
            report_path = os.path.join(report_dir, report_filename)

            # Create assets directory and copy custom CSS for fallback report
            assets_dir = os.path.join(report_dir, 'assets')
            os.makedirs(assets_dir, exist_ok=True)
            logger.info(f"Created assets directory for fallback report: {assets_dir}")

            # Copy custom CSS file if it exists
            custom_css_source = os.path.join(reports_dir, 'assets', 'custom.css')
            custom_css_target = os.path.join(assets_dir, 'custom.css')
            if os.path.exists(custom_css_source):
                shutil.copy2(custom_css_source, custom_css_target)
                logger.info(f"Copied custom CSS to fallback report assets: {custom_css_target}")
            else:
                # Create a minimal custom CSS file if the source doesn't exist
                with open(custom_css_target, 'w') as f:
                    f.write("/* Custom styles */\n.screenshot { max-width: 50% !important; max-height: 400px !important; }")
                logger.info(f"Created minimal custom CSS for fallback report: {custom_css_target}")

            logger.info(f"Attempting direct HTML generation at: {report_path}")

            # Create a simple HTML report
            html_content = f"""<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Suite Report - {timestamp}</title>
    <link rel="stylesheet" href="./assets/custom.css">
    <script>
        // Simple lightbox functionality for screenshots
        document.addEventListener('DOMContentLoaded', function() {{
            // Create lightbox elements
            const lightbox = document.createElement('div');
            lightbox.id = 'screenshot-lightbox';
            lightbox.style.display = 'none';
            lightbox.style.position = 'fixed';
            lightbox.style.top = '0';
            lightbox.style.left = '0';
            lightbox.style.width = '100%';
            lightbox.style.height = '100%';
            lightbox.style.backgroundColor = 'rgba(0,0,0,0.8)';
            lightbox.style.zIndex = '1000';
            lightbox.style.justifyContent = 'center';
            lightbox.style.alignItems = 'center';
            lightbox.style.cursor = 'pointer';

            const lightboxImg = document.createElement('img');
            lightboxImg.style.maxWidth = '90%';
            lightboxImg.style.maxHeight = '90%';
            lightboxImg.style.objectFit = 'contain';
            lightboxImg.style.border = '2px solid white';
            lightboxImg.style.borderRadius = '4px';

            lightbox.appendChild(lightboxImg);
            document.body.appendChild(lightbox);

            // Add click event to all screenshots
            document.querySelectorAll('.screenshot').forEach(img => {{
                img.addEventListener('click', function() {{
                    lightboxImg.src = this.src;
                    lightbox.style.display = 'flex';
                }});
            }});

            // Close lightbox when clicked
            lightbox.addEventListener('click', function() {{
                this.style.display = 'none';
            }});
        }});
    </script>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 0; padding: 20px; }}
        .header {{ background-color: #f5f5f5; padding: 20px; margin-bottom: 20px; }}
        .content {{ padding: 20px; }}
        .suite-summary {{ padding: 15px; margin-bottom: 20px; border-radius: 5px; }}
        .suite-error {{ padding: 15px; margin-bottom: 20px; background-color: #f8d7da; border: 1px solid #f5c6cb; border-radius: 5px; }}
        .suite-error pre {{ background-color: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }}
        .test-case {{ margin-bottom: 30px; padding: 15px; border-radius: 5px; }}
        .step {{ margin-bottom: 15px; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }}
        .step-header {{ display: flex; justify-content: space-between; margin-bottom: 10px; }}
        .step-name {{ font-weight: bold; }}
        .step-timestamp {{ color: #666; }}
        .screenshot-container {{ margin-top: 15px; text-align: center; }}
        .screenshot-label {{ font-weight: bold; margin-bottom: 5px; color: #333; }}
        .action-id {{ font-size: 13px; color: #333; margin-bottom: 8px; font-family: monospace; background-color: #f8f9fa; padding: 3px 6px; border-radius: 3px; display: inline-block; }}
        .screenshot {{ max-width: 80%; max-height: 400px; border: 1px solid #ddd; border-radius: 5px; object-fit: contain; cursor: pointer; }}
        .passed {{ background-color: #d4edda; border-color: #c3e6cb; }}
        .failed {{ background-color: #f8d7da; border-color: #f5c6cb; }}
        .step.passed {{ background-color: #d1f2d1; border-left: 4px solid #28a745; }}
        .step.failed {{ background-color: #f8d7da; border-left: 4px solid #dc3545; }}
        .error-details {{ margin-top: 10px; padding: 10px; background-color: #f8f9fa; border-radius: 5px; }}
        .error-details pre {{ margin: 0; overflow-x: auto; }}
    </style>
</head>
<body>
    <header class="header">
        <h1>Test Suite Report</h1>
        <div class="status-summary">
            <span>Generated at: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</span>
        </div>
    </header>
    <div class="content">
        <h2>{test_suite_data.get('name', 'Unknown Test Suite')}</h2>"""

            # Add overall test suite status
            suite_status = test_suite_data.get('status', 'unknown')
            suite_status_class = 'passed' if suite_status == 'passed' else 'failed'
            html_content += f"""
        <div class="suite-summary {suite_status_class}">
            <h2>Test Suite: {test_suite_data.get('name', 'Unknown Test Suite')}</h2>
            <p>Status: {suite_status}</p>
            <p>Passed: {test_suite_data.get('passed', 0)}</p>
            <p>Failed: {test_suite_data.get('failed', 0)}</p>
            <p>Skipped: {test_suite_data.get('skipped', 0)}</p>
        </div>"""

            # Add error message if present at the suite level
            if test_suite_data.get('error'):
                html_content += f"""
        <div class="suite-error">
            <h3>Suite Error:</h3>
            <pre>{test_suite_data.get('error')}</pre>
        </div>"""

            # Add test cases and steps
            for test_idx, test_case in enumerate(test_suite_data.get('testCases', [])):
                test_status_class = 'passed' if test_case.get('status') == 'passed' else 'failed'
                html_content += f"""
        <div class="test-case {test_status_class}">
            <h3>Test Case #{test_idx+1}: {test_case.get('name', 'Unnamed Test')}</h3>
            <p>Status: {test_case.get('status', 'unknown')}</p>
            <p>Duration: {test_case.get('duration', '0ms')}</p>
            <div class="steps">"""

                for step_idx, step in enumerate(test_case.get('steps', [])):
                    status_class = 'passed' if step.get('status') == 'passed' else 'failed'

                    # Extract action type from step name
                    raw_name = step.get('name', '')
                    action_type = raw_name.split(':')[0].strip() if ':' in raw_name else raw_name or 'Unknown'
                    safe_action_type = html.escape(action_type)

                    html_content += f"""
                <div class="step {status_class}">
                    <div class="step-header">
                        <span class="step-name">Step {step_idx+1}: {safe_action_type}</span>
                        <span class="step-timestamp">Time: {step.get('timestamp', 'Unknown')}</span>
                    </div>
                    <p>Status: {step.get('status', 'unknown')}</p>
                    <p>Duration: {step.get('duration', '0ms')}</p>

                    {f"<div class='error-details'><h4>Error:</h4><pre>{html.escape(step.get('error', ''))}</pre></div>" if step.get('error') else ""}"""

                    # Get the step ID
                    step_id = step.get('step_id', step_idx+1)

                    # Get screenshot info from the screenshots map if available
                    test_case_name = re.sub(r'[^a-zA-Z0-9]', '', test_case.get('name', f'test_{test_idx}'))
                    screenshot_key = f"{test_case_name}_{step_id}"
                    screenshot_info = test_data.get('screenshots_map', {}).get(screenshot_key)

                    if not screenshot_info:
                        # Try with _failed suffix
                        screenshot_key = f"{test_case_name}_{step_id}_failed"
                        screenshot_info = test_data.get('screenshots_map', {}).get(screenshot_key)

                    # Adjust step_index to match the 1-based indexing used in the database
                    adjusted_step_index = step_index + 1

                    # Check if we have a screenshot_info from action_id lookup
                    if step.get('screenshot_info'):
                        # Use the screenshot info from action_id lookup
                        screenshot_info = step.get('screenshot_info')
                        logger.info(f"Using screenshot info from action_id lookup for step {step_id}")

                    # Get the action_id for this step
                    action_id = step.get('action_id', '')

                    # If we have an action_id, use it for the filename
                    if action_id:
                        simple_screenshot_filename = f"{action_id}.png"
                        logger.info(f"Using action_id for screenshot filename: {simple_screenshot_filename}")

                        # Get the action type but escape any HTML special characters
                        raw_name = step.get('name', '')
                        # First split to get just the action type
                        action_type = raw_name.split(':')[0].strip() if ':' in raw_name else raw_name or 'Unknown'
                        # Then escape HTML special characters
                        safe_action_type = html.escape(action_type)

                        # Ensure the step has the correct screenshot paths
                        step['screenshot'] = f"screenshots/{simple_screenshot_filename}"
                        step['screenshot_filename'] = simple_screenshot_filename
                        step['report_screenshot'] = simple_screenshot_filename
                        step['resolved_screenshot'] = f"screenshots/{simple_screenshot_filename}"
                    # Check if we have a screenshot_filename directly in the step
                    elif step.get('screenshot_filename'):
                        simple_screenshot_filename = step.get('screenshot_filename')
                        logger.info(f"Using screenshot filename from step: {simple_screenshot_filename}")

                        # Get the action type but escape any HTML special characters
                        raw_name = step.get('name', '')
                        # First split to get just the action type
                        action_type = raw_name.split(':')[0].strip() if ':' in raw_name else raw_name or 'Unknown'
                        # Then escape HTML special characters
                        safe_action_type = html.escape(action_type)

                        # Ensure the step has the correct screenshot paths
                        step['screenshot'] = f"screenshots/{simple_screenshot_filename}"
                        step['report_screenshot'] = simple_screenshot_filename
                        step['resolved_screenshot'] = f"screenshots/{simple_screenshot_filename}"
                    elif screenshot_info:
                        # Use the information from the screenshots map or action_id lookup
                        simple_screenshot_filename = screenshot_info.get('filename')
                        safe_action_type = html.escape(screenshot_info.get('action_type', ''))
                        logger.info(f"Using screenshot filename from screenshot_info: {simple_screenshot_filename}")

                        # Ensure the step has the correct screenshot paths
                        step['screenshot'] = f"screenshots/{simple_screenshot_filename}"
                        step['screenshot_filename'] = simple_screenshot_filename
                        step['report_screenshot'] = simple_screenshot_filename
                        step['resolved_screenshot'] = f"screenshots/{simple_screenshot_filename}"
                    else:
                        # Fallback to standardized naming convention with 1-based step indexing
                        simple_screenshot_filename = f"step_{test_idx}_{adjusted_step_index}.png"

                        # Get the action type but escape any HTML special characters
                        raw_name = step.get('name', '')
                        # First split to get just the action type
                        action_type = raw_name.split(':')[0].strip() if ':' in raw_name else raw_name or 'Unknown'
                        # Then escape HTML special characters
                        safe_action_type = html.escape(action_type)
                        logger.info(f"Using fallback screenshot filename: {simple_screenshot_filename}")

                        # Ensure the step has the correct screenshot paths
                        step['screenshot'] = f"screenshots/{simple_screenshot_filename}"
                        step['screenshot_filename'] = simple_screenshot_filename
                        step['report_screenshot'] = simple_screenshot_filename
                        step['resolved_screenshot'] = f"screenshots/{simple_screenshot_filename}"

                    # Log the filename we're using for debugging
                    logger.info(f"Using screenshot filename in HTML: {simple_screenshot_filename}")
                    logger.info(f"Action ID for step: {action_id}")
                    logger.info(f"Fallback screenshot path: screenshots/step_{test_idx}_{adjusted_step_index}.png")

                    # Make the action_id more prominent in the UI
                    action_id_display = html.escape(step.get('action_id', ''))
                    action_id_html = f'<div class="action-id">Action ID: {action_id_display}</div>' if action_id_display else ''

                    # Only show screenshots for takeScreenshot actions
                    step_name = step.get('name', '').lower()
                    is_take_screenshot_action = 'takescreenshot' in step_name

                    if is_take_screenshot_action:
                        # Check if this takeScreenshot action has custom screenshot information
                        custom_name = step.get('custom_screenshot_name')
                        custom_filename = step.get('custom_screenshot_filename')
                        custom_path = step.get('custom_screenshot_path')

                        if custom_name and custom_filename:
                            # Show custom screenshot link for takeScreenshot actions
                            html_content += f"""
                            <div class="screenshot-container">
                                <div class="screenshot-label">Screenshot for Step {step_id}: {safe_action_type}</div>
                                {action_id_html}
                                <div class="custom-screenshot-info">
                                    <p><strong>Custom Screenshot:</strong> <a href="{custom_path}" target="_blank" title="Click to view {custom_name}">{custom_name}</a></p>
                                </div>
                                <img class="screenshot" src="{custom_path}" alt="Custom Screenshot: {html.escape(custom_name)}"
                                     onerror="this.onerror=null; this.src='screenshots/{action_id}.png'; this.alt='Step Screenshot'; this.onerror=function(){{this.onerror=null; this.src='screenshots/step_{test_idx}_{adjusted_step_index}.png'; this.alt='Step Screenshot'; this.onerror=function(){{this.style.display='none'; this.nextElementSibling.style.display='block';}}}}"
                                     onclick="window.open(this.src, '_blank')" title="Click to view full size" />
                                <p style="display: none; color: #6c757d;">No screenshot available for this step</p>
                            </div>"""
                        else:
                            # Show regular action_id screenshot for takeScreenshot actions without custom names
                            html_content += f"""
                            <div class="screenshot-container">
                                <div class="screenshot-label">Screenshot for Step {step_id}: {safe_action_type}</div>
                                {action_id_html}
                                <img class="screenshot" src="screenshots/{simple_screenshot_filename}" alt="Step {step_id}: {safe_action_type}"
                                     onerror="this.onerror=null; this.src='screenshots/{action_id}.png'; this.alt='Step Screenshot'; this.onerror=function(){{this.onerror=null; this.src='screenshots/step_{test_idx}_{adjusted_step_index}.png'; this.alt='Step Screenshot'; this.onerror=function(){{this.style.display='none'; this.nextElementSibling.style.display='block';}}}}"
                                     onclick="window.open(this.src, '_blank')" title="Click to view full size" />
                                <p style="display: none; color: #6c757d;">No screenshot available for this step</p>
                            </div>"""

                    html_content += """
                </div>"""

                html_content += """
            </div>
        </div>"""

            html_content += """
    </div>
</body>
</html>"""

            with open(report_path, 'w') as f:
                f.write(html_content)

            if os.path.exists(report_path):
                logger.info(f"Direct HTML report generated at: {report_path}")

                # Save action logs if they exist
                try:
                    import sys
                    import os
                    # Add the parent directory to sys.path to import from app
                    current_dir = os.path.dirname(os.path.abspath(__file__))
                    parent_dir = os.path.dirname(current_dir)
                    if parent_dir not in sys.path:
                        sys.path.insert(0, parent_dir)
                    from app import current_action_logs
                    if current_action_logs:
                        logger.info(f"Saving {len(current_action_logs)} action logs to fallback report directory")
                        log_file_path = save_action_logs_to_file(current_action_logs, report_dir)
                        if log_file_path:
                            logger.info(f"Action logs saved to fallback report: {log_file_path}")
                except Exception as log_error:
                    logger.error(f"Error saving action logs to fallback report: {str(log_error)}")

                # Make sure the action log file exists in the report directory
                action_log_path = os.path.join(report_dir, "action_log.txt")
                if not os.path.exists(action_log_path):
                    logger.warning(f"Action log file not found in fallback report: {action_log_path}")
                    # Create an empty action log file if it doesn't exist
                    try:
                        with open(action_log_path, 'w') as f:
                            f.write(f"Action Log - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                            f.write("=" * 80 + "\n\n")
                            f.write("No action logs were recorded for this test run.\n")
                        logger.info(f"Created empty action log file in fallback report: {action_log_path}")
                    except Exception as e:
                        logger.error(f"Error creating empty action log file in fallback report: {str(e)}")

                # Create a ZIP archive of the report directory
                zip_path = create_zip_archive(report_dir)

                return report_path, zip_path
            else:
                logger.error(f"Failed to create direct HTML report at: {report_path}")
        except Exception as direct_error:
            logger.error(f"Failed to generate direct HTML report: {str(direct_error)}")

        raise Exception(f"Failed to generate report: {str(e)}")

def copy_screenshots_from_database(report_dir, test_data):
    """
    Process screenshots for the report. Since screenshots are now saved directly to the report folder
    during test execution, this function mainly verifies that the screenshots exist and updates the step data.

    Args:
        report_dir (str): Path to the report directory
        test_data (dict): Test data with test cases and steps and screenshot paths

    Returns:
        dict: Updated test data with screenshot paths pointing to the report directory
    """
    try:
        # Create screenshots directory if it doesn't exist
        screenshots_dir = os.path.join(report_dir, 'screenshots')
        os.makedirs(screenshots_dir, exist_ok=True)

        # Get the app's static screenshots directory
        app_screenshots_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'app', 'static', 'screenshots')
        logger.info(f"App screenshots directory: {app_screenshots_dir}")

        # Get the current screenshots directory from app_android.py if available
        current_screenshots_dir = screenshots_dir
        try:
            import sys
            import os
            # Add the parent directory to sys.path to import from app
            current_dir = os.path.dirname(os.path.abspath(__file__))
            parent_dir = os.path.dirname(current_dir)
            if parent_dir not in sys.path:
                sys.path.insert(0, parent_dir)
            from app_android.app import current_screenshots_dir as app_current_screenshots_dir
            if app_current_screenshots_dir and os.path.exists(app_current_screenshots_dir):
                current_screenshots_dir = app_current_screenshots_dir
                logger.info(f"Using current screenshots directory from app: {current_screenshots_dir}")
        except ImportError:
            logger.warning("Could not import current_screenshots_dir from app_android.py, using report screenshots directory")

        # Process each test case and step
        for test_idx, test_case in enumerate(test_data.get('testCases', [])):
            for step_idx, step in enumerate(test_case.get('steps', [])):
                try:
                    # Check if we already have an action_id from the database or previous processing
                    if 'action_id' in step and step['action_id']:
                        # Use the action_id for the filename
                        action_id = step['action_id']
                        step_screenshot = f"{action_id}.png"
                        logger.info(f"Using action_id for screenshot filename: {step_screenshot}")
                    else:
                        # For new steps, generate a new action_id
                        import random
                        import string
                        chars = string.ascii_letters + string.digits
                        action_id = ''.join(random.choice(chars) for _ in range(10))
                        step['action_id'] = action_id
                        # Use the action_id for the filename
                        step_screenshot = f"{action_id}.png"
                        # Store the screenshot filename in the step data
                        step['screenshot_filename'] = step_screenshot
                        logger.info(f"Generated new action_id and screenshot filename: {step_screenshot}")

                    # Use the same filename for the report
                    report_screenshot = step_screenshot
                    # Store the raw filename without the path for the report
                    step['report_screenshot'] = report_screenshot
                    step['resolved_screenshot'] = f"screenshots/{report_screenshot}"
                    logger.info(f"Using report screenshot: {report_screenshot}")

                    # Set the target path in the report's screenshots directory
                    target_path = os.path.join(screenshots_dir, step_screenshot)
                    logger.info(f"Target screenshot path for report: {target_path}")

                    # Add timestamp to the step if not already present
                    if not step.get('timestamp'):
                        step['timestamp'] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

                    # Look for the screenshot in various locations
                    found_screenshot = False

                    # First check if the screenshot already exists in the report directory
                    if os.path.exists(target_path):
                        found_screenshot = True
                        logger.info(f"Screenshot already exists in report directory: {target_path}")

                    # Check if the screenshot exists in the app screenshots directory
                    elif os.path.exists(os.path.join(app_screenshots_dir, step_screenshot)):
                        source_path = os.path.join(app_screenshots_dir, step_screenshot)
                        shutil.copy2(source_path, target_path)
                        found_screenshot = True
                        logger.info(f"Copied screenshot from app screenshots directory: {source_path} -> {target_path}")

                    # Check if the screenshot exists in the current screenshots directory
                    elif current_screenshots_dir and os.path.exists(os.path.join(current_screenshots_dir, step_screenshot)):
                        source_path = os.path.join(current_screenshots_dir, step_screenshot)
                        shutil.copy2(source_path, target_path)
                        found_screenshot = True
                        logger.info(f"Copied screenshot from current screenshots directory: {source_path} -> {target_path}")

                    # Check for alternative format with 'al_' prefix
                    elif action_id and not action_id.startswith('al_'):
                        # Try with 'al_' prefix
                        alt_screenshot = f"al_{action_id}.png"

                        # Check in app screenshots directory
                        if os.path.exists(os.path.join(app_screenshots_dir, alt_screenshot)):
                            source_path = os.path.join(app_screenshots_dir, alt_screenshot)
                            shutil.copy2(source_path, target_path)
                            found_screenshot = True
                            logger.info(f"Copied screenshot with 'al_' prefix from app screenshots directory: {source_path} -> {target_path}")

                        # Check in current screenshots directory
                        elif current_screenshots_dir and os.path.exists(os.path.join(current_screenshots_dir, alt_screenshot)):
                            source_path = os.path.join(current_screenshots_dir, alt_screenshot)
                            shutil.copy2(source_path, target_path)
                            found_screenshot = True
                            logger.info(f"Copied screenshot with 'al_' prefix from current screenshots directory: {source_path} -> {target_path}")

                    # If not found, use latest.png as fallback
                    if not found_screenshot:
                        latest_path = os.path.join(app_screenshots_dir, 'latest.png')
                        if os.path.exists(latest_path):
                            shutil.copy2(latest_path, target_path)
                            found_screenshot = True
                            logger.info(f"Used latest.png as fallback: {latest_path} -> {target_path}")

                    # Update the step with the screenshot path
                    if found_screenshot:
                        # Update the path to point to the new screenshots directory structure
                        step['screenshot'] = f"screenshots/{step_screenshot}"
                        logger.info(f"Added screenshot to step {step_idx+1}: {step['screenshot']}")

                        # Verify the file exists in the target location
                        if os.path.exists(target_path):
                            file_size = os.path.getsize(target_path)
                            logger.info(f"Verified screenshot exists at {target_path} (size: {file_size} bytes)")
                        else:
                            logger.error(f"CRITICAL ERROR: Screenshot not found at expected location: {target_path}")

                except Exception as step_error:
                    logger.error(f"Error processing screenshot for step {step_idx+1}: {str(step_error)}")
                    logger.error(traceback.format_exc())

        return test_data
    except Exception as e:
        logger.error(f"Error processing screenshots for report: {str(e)}")
        traceback.print_exc()
        return test_data

def ensure_report_screenshots(report_dir, test_data):
    """
    Ensure all screenshots referenced in the test data are available in the report directory
    with the correct names. This simplified version uses action_id-based naming for consistency.

    Since screenshots are now saved directly to the report folder during test execution,
    this function mainly verifies that the screenshots exist and updates the step data.

    Args:
        report_dir (str): Path to the report directory
        test_data (dict): Test data with test cases and steps
    """
    try:
        screenshots_dir = os.path.join(report_dir, 'screenshots')
        os.makedirs(screenshots_dir, exist_ok=True)

        # Get the app's static screenshots directory (for fallback only)
        app_screenshots_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), 'app', 'static', 'screenshots')

        # Keep track of how many screenshots we verify
        verified_count = 0
        missing_count = 0

        # Process each test case and step
        for test_case in test_data.get('testCases', []):
            for step in test_case.get('steps', []):
                # Get the action_id for this step
                action_id = step.get('action_id')
                if not action_id:
                    logger.warning(f"Step has no action_id, skipping screenshot check")
                    continue

                # Use the action_id for the filename
                screenshot_filename = f"{action_id}.png"

                # Target path in report screenshots directory
                target_path = os.path.join(screenshots_dir, screenshot_filename)

                # Check if the screenshot exists in the report directory
                if os.path.exists(target_path):
                    logger.info(f"Screenshot verified in report directory: {target_path}")
                    verified_count += 1

                    # Update the step with the screenshot path
                    step['screenshot'] = f"screenshots/{screenshot_filename}"
                    step['report_screenshot'] = screenshot_filename
                    step['resolved_screenshot'] = f"screenshots/{screenshot_filename}"
                    continue

                # We no longer use 'al_' prefix, but keep this code for backward compatibility
                if not action_id.startswith('al_'):
                    alt_screenshot = f"al_{action_id}.png"
                    alt_target_path = os.path.join(screenshots_dir, alt_screenshot)

                    if os.path.exists(alt_target_path):
                        # Copy to the standard format for consistency
                        shutil.copy2(alt_target_path, target_path)
                        logger.info(f"Copied screenshot with 'al_' prefix to standard format: {alt_target_path} -> {target_path}")
                        verified_count += 1

                        # Update the step with the screenshot path
                        step['screenshot'] = f"screenshots/{screenshot_filename}"
                        step['report_screenshot'] = screenshot_filename
                        step['resolved_screenshot'] = f"screenshots/{screenshot_filename}"
                        continue

                # If still not found, check app screenshots directory as fallback
                source_path = os.path.join(app_screenshots_dir, screenshot_filename)
                if os.path.exists(source_path):
                    shutil.copy2(source_path, target_path)
                    logger.info(f"Copied screenshot from app screenshots directory as fallback: {source_path} -> {target_path}")
                    verified_count += 1

                    # Update the step with the screenshot path
                    step['screenshot'] = f"screenshots/{screenshot_filename}"
                    step['report_screenshot'] = screenshot_filename
                    step['resolved_screenshot'] = f"screenshots/{screenshot_filename}"
                    continue

                # If still not found, use latest.png as last resort fallback
                latest_path = os.path.join(app_screenshots_dir, 'latest.png')
                if os.path.exists(latest_path):
                    shutil.copy2(latest_path, target_path)
                    logger.info(f"Used latest.png as fallback: {latest_path} -> {target_path}")
                    verified_count += 1

                    # Update the step with the screenshot path
                    step['screenshot'] = f"screenshots/{screenshot_filename}"
                    step['report_screenshot'] = screenshot_filename
                    step['resolved_screenshot'] = f"screenshots/{screenshot_filename}"
                else:
                    logger.warning(f"Could not find screenshot for step with action_id: {action_id}")
                    missing_count += 1

        logger.info(f"Verified {verified_count} screenshots for the report, {missing_count} missing")
    except Exception as e:
        logger.error(f"Error ensuring report screenshots: {str(e)}")
        traceback.print_exc()

def generate_html_report(test_suite_data, html_report_path):
    """
    Generate a comprehensive HTML report from test suite data using the report template

    Args:
        test_suite_data (dict): Test suite data containing testCases, passed, failed, etc.
        html_report_path (str): Path where the HTML report should be saved

    Returns:
        bool: True if successful, False otherwise
    """
    try:
        from datetime import datetime
        from jinja2 import Template
        import os

        # Generate timestamp
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Load the report template
        template_path = "/Users/<USER>/Documents/automation-tool/reports/report_template.html"
        if not os.path.exists(template_path):
            logger.error(f"Report template not found at: {template_path}")
            return False

        with open(template_path, 'r', encoding='utf-8') as f:
            template_content = f.read()

        template = Template(template_content)

        # Process test suite data
        test_cases = test_suite_data.get('testCases', [])
        total_test_cases = len(test_cases)
        passed_test_cases = test_suite_data.get('passed', 0)
        failed_test_cases = test_suite_data.get('failed', 0)

        # Process test cases for the template
        processed_test_cases = []

        for test_case in test_cases:
            test_name = test_case.get('name', 'Unknown Test')
            test_status = test_case.get('status', 'unknown')
            test_steps = test_case.get('steps', [])

            # Map status to template expected format
            if test_status.lower() == 'passed':
                template_status = 'Passed'
            elif test_status.lower() == 'failed':
                template_status = 'Failed'
            else:
                template_status = 'Unknown'

            # Process actions/steps
            actions = []
            for i, step in enumerate(test_steps):
                step_name = step.get('name', f'Step {i+1}')
                action_id = step.get('action_id', '')

                # Extract action type from step name
                action_type = 'unknown'
                action_description = step_name

                if ':' in step_name:
                    parts = step_name.split(':', 1)
                    action_type = parts[0].strip().lower()
                    action_description = parts[1].strip() if len(parts) > 1 else step_name
                else:
                    # Try to infer action type from step name
                    step_lower = step_name.lower()
                    if 'tap' in step_lower or 'click' in step_lower:
                        action_type = 'tap'
                    elif 'swipe' in step_lower:
                        action_type = 'swipe'
                    elif 'input' in step_lower or 'text' in step_lower:
                        action_type = 'text'
                    elif 'wait' in step_lower:
                        action_type = 'wait'
                    elif 'log' in step_lower:
                        action_type = 'addLog'
                    elif 'launch' in step_lower:
                        action_type = 'launchApp'
                    elif 'terminate' in step_lower:
                        action_type = 'terminateApp'
                    elif 'element' in step_lower:
                        action_type = 'clickElement'
                    elif 'screenshot' in step_lower:
                        action_type = 'takeScreenshot'

                # Determine screenshot path
                screenshot_path = None
                if action_id and ('screenshot' in step_name.lower() or action_type == 'takeScreenshot'):
                    # Try different screenshot naming patterns
                    screenshot_patterns = [
                        f"screenshots/{action_id}.png",
                        f"screenshots/al_{action_id}.png",
                        f"screenshots/{action_id}.jpg"
                    ]

                    # Check if any of these files exist relative to the report directory
                    report_dir = os.path.dirname(html_report_path)
                    for pattern in screenshot_patterns:
                        full_path = os.path.join(report_dir, pattern)
                        if os.path.exists(full_path):
                            screenshot_path = pattern
                            break

                    # If no file found, still include the path for potential screenshots
                    if not screenshot_path:
                        screenshot_path = f"screenshots/{action_id}.png"

                action_data = {
                    'index': i + 1,
                    'type': action_type,
                    'description': action_description,
                    'screenshot': screenshot_path,
                    'action_id': action_id,
                    'status': step.get('status', 'unknown')
                }

                actions.append(action_data)

            processed_test_case = {
                'name': test_name,
                'status': template_status,
                'actions': actions,
                'total_actions': len(actions)
            }

            processed_test_cases.append(processed_test_case)

        # Prepare template context
        context = {
            'timestamp': timestamp,
            'total_test_cases': total_test_cases,
            'passed_test_cases': passed_test_cases,
            'failed_test_cases': failed_test_cases,
            'test_cases': processed_test_cases
        }

        # Render template
        html_content = template.render(**context)

        # Write to file
        with open(html_report_path, 'w', encoding='utf-8') as f:
            f.write(html_content)

        logger.info(f"Successfully generated comprehensive HTML report at: {html_report_path}")
        logger.info(f"Report includes {total_test_cases} test cases with full action details")
        return True

    except Exception as e:
        logger.error(f"Error generating HTML report: {str(e)}")
        logger.error(f"Traceback: {traceback.format_exc()}")
        return False

def generate_report_from_database_data(execution_id, test_cases_data, reports_dir):
    """
    Generate HTML report directly from database data

    Args:
        execution_id (str): Execution ID
        test_cases_data (dict): Test cases data from database
        reports_dir (str): Reports directory path

    Returns:
        str: Path to generated report or None if failed
    """
    try:
        import os
        from datetime import datetime

        logger.info(f"Generating report from database data for execution: {execution_id}")

        # Create report directory
        report_dir = os.path.join(reports_dir, f"db_report_{execution_id}")
        os.makedirs(report_dir, exist_ok=True)

        # Build test suite data structure
        test_suite_data = {
            'execution_id': execution_id,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'testCases': [],
            'passed': 0,
            'failed': 0,
            'total': len(test_cases_data)
        }

        # Convert test cases data to report format
        for filename, test_case_info in test_cases_data.items():
            test_case = {
                'name': test_case_info['name'],
                'filename': filename,
                'status': test_case_info['status'],
                'steps': [],
                'total_actions': test_case_info['total_actions']
            }

            # Convert actions to steps format
            for action_id, action_data in test_case_info.get('actions', {}).items():
                step = {
                    'action_id': action_id,
                    'status': action_data['status'],
                    'action_type': action_data.get('action_type', 'unknown'),
                    'retry_count': action_data.get('retry_count', 0),
                    'end_time': action_data.get('end_time', '')
                }
                test_case['steps'].append(step)

            test_suite_data['testCases'].append(test_case)

            # Update counters
            if test_case_info['status'] == 'passed':
                test_suite_data['passed'] += 1
            elif test_case_info['status'] == 'failed':
                test_suite_data['failed'] += 1

        # Save data.json for compatibility
        data_json_path = os.path.join(report_dir, 'data.json')
        with open(data_json_path, 'w') as f:
            json.dump(test_suite_data, f, indent=2)

        # Generate HTML report
        html_report_path = os.path.join(report_dir, 'test_execution_report.html')
        success = generate_html_report(test_suite_data, html_report_path)

        if success:
            logger.info(f"Successfully generated database report at: {html_report_path}")
            return html_report_path
        else:
            logger.error("Failed to generate HTML report from database data")
            return None

    except Exception as e:
        logger.error(f"Error generating report from database data: {str(e)}")
        return None


def get_temp_directory():
    """Get the configured temp directory for this platform"""
    try:
        import sqlite3
        import os

        # Determine platform based on file location
        platform = 'android' if 'app_android' in __file__ else 'ios'
        db_path = f'data/settings_{platform}.db'

        if not os.path.exists(db_path):
            # Fallback to default temp directory
            temp_dir = f'temp_{platform}'
            os.makedirs(temp_dir, exist_ok=True)
            return temp_dir

        conn = sqlite3.connect(db_path)
        cursor = conn.cursor()
        cursor.execute("SELECT path FROM directory_paths WHERE name = 'TEMP_FILES'")
        result = cursor.fetchone()
        conn.close()

        if result:
            temp_path = result[0]
            os.makedirs(temp_path, exist_ok=True)
            return temp_path
        else:
            # Fallback to default temp directory
            temp_dir = f'temp_{platform}'
            os.makedirs(temp_dir, exist_ok=True)
            return temp_dir

    except Exception as e:
        # Fallback to current directory temp folder
        platform = 'android' if 'app_android' in __file__ else 'ios'
        temp_dir = f'temp_{platform}'
        os.makedirs(temp_dir, exist_ok=True)
        return temp_dir
