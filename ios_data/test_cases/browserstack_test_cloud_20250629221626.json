{"name": "browserstack_test_cloud", "created": "2025-06-29 22:17:35", "device_id": "00008120-00186C801E13C01E", "actions": [{"action_id": "4DW5Pg01pk", "executionTime": "3556ms", "package_id": "com.saucelabs.SwagLabsMobileApp", "timestamp": 1750672694950, "type": "restartApp"}, {"action_id": "JmuCQbTn2a", "executionTime": "832ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"test-Username\"]", "method": "locator", "timeout": 10, "timestamp": 1750672726610, "type": "tap"}, {"action_id": "fifVZSCEwL", "executionTime": "1005ms", "text": "standard_user", "timestamp": 1750672754512, "type": "text"}, {"action_id": "ecGfLOHDJK", "executionTime": "1017ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"test-Password\"]", "method": "locator", "timeout": 10, "timestamp": 1750672782983, "type": "tap"}, {"action_id": "hEBr6m1eZL", "executionTime": "916ms", "text": "secret_sauce", "timestamp": 1750672789075, "type": "text"}, {"action_id": "YiQRCpuzmx", "executionTime": "1070ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "test-LOGIN", "method": "locator", "timeout": 10, "timestamp": 1750672860551, "type": "tap"}, {"action_id": "GbxajwINqL", "executionTime": "532ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"test-Cart\"]/XCUIElementTypeOther", "timeout": 10, "timestamp": 1750672897124, "type": "exists"}, {"action_id": "IrXfEIXgV6", "count": 1, "direction": "up", "duration": 500, "end_x": 50, "end_y": 30, "executionTime": "3005ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1751198793285, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "yhWwyaTsFh", "executionTime": "2557ms", "text_to_find": "<PERSON><PERSON>", "timeout": 30, "timestamp": 1751198850818, "type": "tapOnText"}, {"action_id": "LXNjRJbJKG", "count": 2, "direction": "up", "duration": 500, "end_x": 50, "end_y": 30, "executionTime": "3778ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"test-ADD TO CART\"]", "start_x": 50, "start_y": 70, "timestamp": 1751198918306, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "fNbZRqtFSc", "executionTime": "875ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"test-ADD TO CART\"]", "method": "locator", "timeout": 10, "timestamp": 1751198945166, "type": "tap"}, {"action_id": "utt7WRjBSB", "executionTime": "885ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"test-Cart\"]/XCUIElementTypeOther", "method": "locator", "timeout": 10, "timestamp": 1751199052933, "type": "tap"}], "labels": [], "updated": "2025-06-29 22:17:35"}