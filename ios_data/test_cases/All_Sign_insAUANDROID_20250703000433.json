{"name": "All Sign ins-AU-ANDROID", "device_id": "********-00186C801E13C01E", "actions": [{"action_id": "JXFxYCr98V", "executionTime": "3481ms", "package_id": "env[appid]", "timestamp": *************, "type": "restartApp"}, {"action_id": "qA1ap4n1m4", "executionTime": "5218ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "XuLgjNG74w", "executionTime": "1136ms", "function_name": "alert_accept", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "eJnHS9n9VL", "executionTime": "2114ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 30, "timestamp": *************, "type": "waitTill"}, {"action_id": "50Z2jrodNd", "executionTime": "2623ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "eOm1WExcrK", "enter": true, "executionTime": "3155ms", "function_name": "text", "text": "env[uname]", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "dpVaKL19uc", "executionTime": "2562ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "timeout": 10, "timestamp": 1746068218057, "type": "tap"}, {"action_id": "j8NXU87gV3", "enter": true, "executionTime": "2947ms", "function_name": "text", "text": "env[pwd]", "timestamp": 1749383470134, "type": "iosFunctions"}, {"action_id": "EDHl0X27Wi", "executionTime": "1834ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "timeout": 10, "timestamp": 1746068337374, "type": "waitTill"}, {"action_id": "F0gZF1jEnT", "executionTime": "2419ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746068378268, "type": "tap"}, {"action_id": "4WfPFN961S", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "5316ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1746068462307, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "bGo3feCwBQ", "executionTime": "2270ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1746068479716, "type": "tap"}, {"action_id": "IvqPpScAJa", "executionTime": "2406ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746068564144, "type": "tap"}, {"action_id": "WlISsMf9QA", "executionTime": "2164ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtLog in\"]", "method": "locator", "timeout": 10, "timestamp": 1746068627313, "type": "tap"}, {"action_id": "rJVGLpLWM3", "executionTime": "1165ms", "function_name": "alert_accept", "timestamp": 1746068653559, "type": "iosFunctions"}, {"action_id": "6mHVWI3j5e", "executionTime": "2053ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 20, "timestamp": 1746068667568, "type": "waitTill"}, {"action_id": "rLCI6NVxSc", "executionTime": "2583ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "method": "locator", "timeout": 10, "timestamp": 1746068757857, "type": "tap"}, {"action_id": "TGoXyeQtB7", "enter": true, "executionTime": "3208ms", "function_name": "text", "text": "env[uname]", "timestamp": 1749383517833, "type": "iosFunctions"}, {"action_id": "SHaIduBnay", "executionTime": "3412ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "timeout": 10, "timestamp": 1746583053506, "type": "tap"}, {"action_id": "8uojw2klHA", "enter": true, "executionTime": "2968ms", "function_name": "text", "text": "env[pwd]", "timestamp": 1749383548788, "type": "iosFunctions"}, {"action_id": "N9sXy9WltY", "executionTime": "1301ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtStart Shopping\"]", "timeout": 30, "timestamp": 1750378278176, "type": "exists"}, {"action_id": "FHRlQXe58T", "executionTime": "2188ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtStart Shopping\"]", "method": "locator", "timeout": 10, "timestamp": 1746068906050, "type": "tap"}, {"action_id": "FHRlQXe58T", "executionTime": "2443ms", "interval": 0.5, "locator_type": "xpath", "locator_value": " //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750378324505, "type": "tap"}, {"action_id": "quZwUwj3a8", "executionTime": "1870ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "timeout": 10, "timestamp": 1746068941511, "type": "waitTill"}, {"action_id": "ydRnBBO1vR", "executionTime": "2428ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746068987218, "type": "tap"}, {"action_id": "ehyLmdZWP2", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "5599ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1746069003463, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "A1Wz7p1iVG", "executionTime": "2262ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1746069019618, "type": "tap"}, {"action_id": "lCSewtjn1z", "executionTime": "3292ms", "package_id": "env[appid]", "timestamp": 1746943229460, "type": "restartApp"}, {"action_id": "4PZC1vVWJW", "double_tap": false, "executionTime": "4141ms", "image_filename": "homepage-search-se.png", "method": "image", "text_to_find": "Find", "threshold": 0.7, "timeout": 30, "timestamp": 1746069108697, "type": "tapOnText"}, {"action_id": "aRgHcQcLDP", "enter": true, "executionTime": "2543ms", "function_name": "text", "text": "uno card", "timestamp": 1746069162268, "type": "iosFunctions"}, {"action_id": "YC6bBrKQgq", "executionTime": "2800ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Filter\"]", "timeout": 30, "timestamp": 1746078698825, "type": "waitTill"}, {"action_id": "BTYxjEaZEk", "executionTime": "2580ms", "image_filename": "Unocard-product-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746069249053, "type": "tap"}, {"action_id": "6zUBxjSFym", "executionTime": "2270ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"SKU :\"]", "timeout": 30, "timestamp": 1746069314833, "type": "waitTill"}, {"action_id": "2YGctqXNED", "count": 4, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "17295ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"Already a member?\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1746069402843, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "4PZC1vVWJW", "double_tap": false, "executionTime": "3719ms", "image_filename": "homepage-search-se.png", "method": "image", "text_to_find": "Sign", "threshold": 0.7, "timeout": 30, "timestamp": 1750327000121, "type": "tapOnText"}, {"action_id": "q9ZiyYoE5B", "executionTime": "1178ms", "function_name": "alert_accept", "timestamp": 1746069474388, "type": "iosFunctions"}, {"action_id": "VK2oI6mXSB", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[contains(@name,\"Email\")]", "timeout": 10, "timestamp": 1750999390597, "type": "waitTill"}, {"action_id": "50Z2jrodNd", "executionTime": "2630ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[contains(@name,\"Email\")]", "method": "locator", "timeout": 10, "timestamp": 1750995710781, "type": "tap"}, {"action_id": "wuIMlAwYVA", "enter": true, "executionTime": "3261ms", "function_name": "text", "text": "env[uname1]", "timestamp": 1749383618709, "type": "iosFunctions"}, {"action_id": "SHaIduBnay", "executionTime": "3469ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[contains(@name,\"Password\")]", "method": "locator", "timeout": 10, "timestamp": 1750995734361, "type": "tap"}, {"action_id": "N2yjynioko", "enter": true, "executionTime": "3892ms", "function_name": "text", "text": "Wonderbaby@5", "timestamp": 1746073544849, "type": "iosFunctions"}, {"action_id": "AOcOOSuOsB", "executionTime": "2989ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 30, "timestamp": 1746073992632, "type": "waitTill"}, {"action_id": "AOcOOSuOsB", "executionTime": "2989ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1751000541013, "type": "tap"}, {"action_id": "4WfPFN961S", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "5896ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1747984778415, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "IJh702cxG0", "executionTime": "2245ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1746074208264, "type": "tap"}, {"action_id": "lCSewtjn1z", "executionTime": "3245ms", "package_id": "env[appid]", "timestamp": *************, "type": "restartApp"}, {"action_id": "XryN8qR1DX", "executionTime": "2923ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "L6wTorOX8B", "executionTime": "2181ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtMoreAccountCtaSignIn\"]", "method": "locator", "timeout": 15, "timestamp": *************, "type": "tap"}, {"action_id": "byEe7qbCpq", "executionTime": "1139ms", "function_name": "alert_accept", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "mWOCt0aAWW", "expanded": false, "test_case_id": "KmartProdSignin_Copy_20250501144222_20250501144222.json", "test_case_name": "Kmart-Signin", "test_case_steps_count": 5, "timestamp": *************, "type": "multiStep"}, {"action_id": "NurQsFoMkE", "executionTime": "2418ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "4WfPFN961S", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "5302ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1747984801858, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "7WYExJTqjp", "executionTime": "2267ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1746078255432, "type": "tap"}, {"action_id": "XryN8qR1DX", "executionTime": "2383ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750324302752, "type": "tap"}, {"action_id": "4PZC1vVWJW", "double_tap": false, "executionTime": "4090ms", "image_filename": "homepage-search-se.png", "method": "image", "text_to_find": "Find", "threshold": 0.7, "timeout": 30, "timestamp": 1750324373627, "type": "tapOnText"}, {"action_id": "aRgHcQcLDP", "enter": true, "executionTime": "2565ms", "function_name": "text", "text": "uno card", "timestamp": 1750324384312, "type": "iosFunctions"}, {"action_id": "YC6bBrKQgq", "executionTime": "2005ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Filter\"]", "timeout": 30, "timestamp": 1750324411955, "type": "waitTill"}, {"action_id": "BTYxjEaZEk", "executionTime": "2539ms", "image_filename": "Unocard-product-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1750324420234, "type": "tap"}, {"action_id": "K2w9XUGwnb", "executionTime": "5508ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Add to bag", "method": "locator", "timeout": 10, "timestamp": 1750324450331, "type": "tap"}, {"action_id": "XryN8qR1DX", "executionTime": "2943ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750324471190, "type": "tap"}, {"action_id": "g8u66qfKkX", "executionTime": "1838ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "timeout": 10, "timestamp": 1750324747501, "type": "waitTill"}, {"action_id": "tufIibCj03", "executionTime": "2361ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "method": "locator", "timeout": 10, "timestamp": 1750324767119, "type": "tap"}, {"action_id": "2YGctqXNED", "count": 1, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "5925ms", "interval": 0.5, "locator_type": "accessibilityid", "locator_value": "Continue to details", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1750324657451, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "2YGctqXNED", "count": 1, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "4313ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Continue to details", "method": "locator", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 10, "timestamp": 1750993261915, "type": "tap", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "6PL8P3rT57", "executionTime": "3270ms", "text_to_find": "Sign", "timeout": 30, "timestamp": 1750325569588, "type": "tapOnText"}, {"action_id": "q9ZiyYoE5B", "executionTime": "1279ms", "function_name": "alert_accept", "timestamp": 1750324808266, "type": "iosFunctions"}, {"action_id": "50Z2jrodNd", "executionTime": "2639ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "method": "locator", "timeout": 10, "timestamp": 1750995765323, "type": "tap"}, {"action_id": "e1RoZWCZJb", "enter": true, "executionTime": "3175ms", "function_name": "text", "text": "<EMAIL>", "timestamp": 1745667549306, "type": "iosFunctions"}, {"action_id": "DaVBARRwft", "executionTime": "2606ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[contains(@name,\"Password\")]", "method": "locator", "timeout": 10, "timestamp": 1745667657983, "type": "tap"}, {"action_id": "pCPTAtSZbf", "enter": true, "executionTime": "2923ms", "function_name": "text", "text": "Wonderbaby@5", "timestamp": 1745667677406, "type": "iosFunctions"}, {"action_id": "g8u66qfKkX", "executionTime": "4336ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "timeout": 10, "timestamp": 1750326852109, "type": "waitTill"}, {"action_id": "CkfAScJNq8", "executionTime": "3192ms", "image_filename": "env[closebtnimage]", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1750326048856, "type": "tap"}, {"action_id": "XryN8qR1DX", "executionTime": "2855ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750326082513, "type": "tap"}, {"action_id": "g8u66qfKkX", "executionTime": "1851ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "timeout": 10, "timestamp": 1750339093537, "type": "waitTill"}, {"action_id": "tufIibCj03", "executionTime": "2410ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "method": "locator", "timeout": 10, "timestamp": 1750326197819, "type": "tap"}, {"action_id": "1NWfFsDiTQ", "executionTime": "2391ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "method": "locator", "timeout": 10, "timestamp": 1750326180016, "type": "tap"}, {"action_id": "CkfAScJNq8", "executionTime": "2298ms", "image_filename": "env[closebtnimage]", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1750379336861, "type": "tap"}, {"action_id": "NurQsFoMkE", "executionTime": "3748ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750324860361, "type": "tap"}, {"action_id": "4WfPFN961S", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "5337ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1750324873622, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "7WYExJTqjp", "executionTime": "2288ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1750324883313, "type": "tap"}, {"action_id": "7QpmNS6hif", "executionTime": "3261ms", "method": "coordinates", "package_id": "env[appid]", "timestamp": 1750838345652, "type": "restartApp", "x": 0, "y": 0}, {"action_id": "NurQsFoMkE", "executionTime": "2331ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750379940962, "type": "tap"}, {"action_id": "8HTspxuvVG", "expanded": false, "test_case_id": "Search_and_Add_Notebooks_20250620103536.json", "test_case_name": "Search and Add (Notebooks)", "test_case_steps_count": 6, "timestamp": 1750379963554, "type": "multiStep"}, {"action_id": "VMzFZ2uTwl", "count": 4, "direction": "up", "duration": 500, "end_x": 50, "end_y": 30, "executionTime": "6996ms", "interval": 0.5, "locator_type": "accessibilityid", "locator_value": "Continue to details", "start_x": 50, "start_y": 70, "timestamp": 1750380019862, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "5Dk9h5bQWl", "executionTime": "4321ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Continue to details", "method": "locator", "timeout": 10, "timestamp": 1750380059683, "type": "tap"}, {"action_id": "LDH2hlTZT9", "duration": 5, "executionTime": "5021ms", "text_to_find": "in", "time": 5, "timeout": 30, "timestamp": 1750849209295, "type": "wait"}, {"action_id": "STEdg5jOU8", "double_tap": false, "executionTime": "3223ms", "text_to_find": "in", "timeout": 30, "timestamp": 1750380094246, "type": "tapOnText"}, {"action_id": "q9ZiyYoE5B", "executionTime": "1119ms", "function_name": "alert_accept", "timestamp": 1750380112396, "type": "iosFunctions"}, {"action_id": "mWOCt0aAWW", "expanded": false, "test_case_id": "KmartProdSignin_Copy_20250501144222_20250501144222.json", "test_case_name": "Kmart-Signin", "test_case_steps_count": 5, "timestamp": 1750380131565, "type": "multiStep"}, {"action_id": "CkfAScJNq8", "executionTime": "2586ms", "image_filename": "env[closebtnimage]", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1750380168150, "type": "tap"}, {"action_id": "XryN8qR1DX", "executionTime": "2545ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750380248434, "type": "tap"}, {"action_id": "tufIibCj03", "executionTime": "2376ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "method": "locator", "timeout": 10, "timestamp": 1750380273151, "type": "tap"}, {"action_id": "1NWfFsDiTQ", "executionTime": "2415ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "method": "locator", "timeout": 10, "timestamp": 1750380286665, "type": "tap"}, {"action_id": "CkfAScJNq8", "executionTime": "2365ms", "image_filename": "env[closebtnimage]", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1750380309586, "type": "tap"}, {"action_id": "NurQsFoMkE", "executionTime": "2558ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750380175905, "type": "tap"}, {"action_id": "4WfPFN961S", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "5396ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1750380181036, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "7WYExJTqjp", "executionTime": "2248ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1750380187636, "type": "tap"}, {"type": "cleanupSteps", "timestamp": 1751000602041, "test_case_id": "Kmart_AU_Cleanup_20250626204013.json", "test_case_name": "Kmart_AU_Cleanup", "test_case_steps_count": 0, "action_id": "IR7wnjW7C8"}], "labels": [], "test_case_id": "tc_dbd3cd1e33ca", "created": "2025-07-03T00:04:33.997445", "updated": "2025-07-03 00:04:58"}