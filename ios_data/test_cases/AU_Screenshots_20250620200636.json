{"name": "AU Screenshots", "created": "2025-06-20 21:03:52", "device_id": "********-00186C801E13C01E", "actions": [{"action_id": "HotUJOd6oB", "executionTime": "3501ms", "package_id": "env[appid]", "timestamp": *************, "type": "restartApp"}, {"action_id": "yXFY32kZhz", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "eLIrnn1bfa", "screenshot_name": "scrn_homepage_with_signin_btn", "timestamp": *************, "type": "takeScreenshot"}, {"action_id": "F1olhgKhUt", "executionTime": "8774ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "Zjpriq9nyQ", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtLog in\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "CVF7HKnFvX", "screenshot_name": "scrn_wishlist_guest", "timestamp": *************, "type": "takeScreenshot"}, {"action_id": "F1olhgKhUt", "executionTime": "8774ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "JryiXRExgt", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtMoreAccountCtaSignIn\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "fwvlxhq74o", "screenshot_name": "scrn_account_guest_user_page1", "timestamp": *************, "type": "takeScreenshot"}, {"action_id": "4cr7q4S2jQ", "count": 1, "direction": "up", "duration": 300, "end_x": 50, "end_y": 20, "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": *************, "type": "swipe", "vector_end": [0.5, 0.2], "vector_start": [0.5, 0.7]}, {"action_id": "VklJ4OUUaU", "screenshot_name": "scrn_account_guest_user_page2", "timestamp": *************, "type": "takeScreenshot"}, {"action_id": "F1olhgKhUt", "executionTime": "8774ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "rkL0oz4kiL", "executionTime": "9914ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "5tNqMSgptN", "screenshot_name": "scrn_homepage_signin_alert", "timestamp": *************, "type": "takeScreenshot"}, {"action_id": "yUJyVO5Wev", "executionTime": "1244ms", "function_name": "alert_accept", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "3caMBvQX7k", "executionTime": "3309ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 30, "timestamp": *************, "type": "waitTill"}, {"action_id": "kMdm759l0Z", "screenshot_name": "scrn_homepage_signin_page", "timestamp": *************, "type": "takeScreenshot"}, {"action_id": "LDkFLWks00", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "3FBGGKUMbh", "enter": true, "function_name": "text", "text": "env[uname-op]", "timestamp": 1749434445973, "type": "iosFunctions"}, {"action_id": "T3MmUw30SF", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "timeout": 10, "timestamp": 1749434481083, "type": "tap"}, {"action_id": "YqMEb5Jr6o", "enter": true, "function_name": "text", "text": "Wonderbaby@6", "timestamp": 1749435014969, "type": "iosFunctions"}, {"action_id": "yiKyF5FJwN", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "timeout": 20, "timestamp": 1749434558505, "type": "exists"}, {"action_id": "ScCpUClmjN", "screenshot_name": "scrn_homepage_loggedin_user", "timestamp": 1750414528672, "type": "takeScreenshot"}, {"action_id": "rqLJpAP0mA", "double_tap": false, "executionTime": "2302ms", "image_filename": "homepage-search-se.png", "method": "image", "text_to_find": "Find", "threshold": 0.7, "timeout": 30, "timestamp": 1745980305734, "type": "tapOnText"}, {"action_id": "xwCtOeDknu", "duration": 5, "time": 5, "timestamp": 1750415124049, "type": "wait"}, {"action_id": "RxQfyk4Wcf", "screenshot_name": "scrn_search_page", "timestamp": 1750415178865, "type": "takeScreenshot"}, {"action_id": "sc2KH9bG6H", "executionTime": "3697ms", "function_name": "text", "text": "Uno card", "timestamp": 1745484826180, "type": "iosFunctions"}, {"action_id": "nAB6Q8LAdv", "executionTime": "5476ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Filter\"]", "timeout": 30, "timestamp": 1745485041367, "type": "waitTill"}, {"action_id": "Jo0tWz9jxT", "screenshot_name": "scrn_search_listing_page", "timestamp": 1750415282098, "type": "takeScreenshot"}, {"action_id": "eLxHVWKeDQ", "executionTime": "4632ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "method": "locator", "timeout": 10, "timestamp": 1746836741255, "type": "tap"}, {"action_id": "ITHvSyXXmu", "executionTime": "6970ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"SKU :\"]", "timeout": 30, "timestamp": 1746837237441, "type": "waitTill"}, {"action_id": "HovxITljmo", "screenshot_name": "scrn_pdp_page1", "timestamp": 1750415857123, "type": "takeScreenshot"}, {"action_id": "WbxRVpWtjw", "executionTime": "5432ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"to wishlist\")]", "method": "locator", "timeout": 10, "timestamp": 1746837373321, "type": "tap"}, {"action_id": "WbxRVpWtjw", "executionTime": "5432ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Add to bag", "method": "locator", "timeout": 10, "timestamp": 1750416426328, "type": "tap"}, {"action_id": "phvCKGOwAn", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 10, "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1750415720784, "type": "swipe", "vector_end": [0.5, 0.1], "vector_start": [0.5, 0.7]}, {"action_id": "PEZcJmXHp6", "screenshot_name": "scrn_pdp_page2", "timestamp": 1750416037295, "type": "takeScreenshot"}, {"action_id": "phvCKGOwAn", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 10, "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1750416294864, "type": "swipe", "vector_end": [0.5, 0.1], "vector_start": [0.5, 0.7]}, {"action_id": "a9sGbp6JF8", "screenshot_name": "scrn_pdp_page2", "timestamp": 1750416368012, "type": "takeScreenshot"}, {"action_id": "phvCKGOwAn", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 10, "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1750416309286, "type": "swipe", "vector_end": [0.5, 0.1], "vector_start": [0.5, 0.7]}, {"action_id": "Kj6phatBjE", "screenshot_name": "scrn_pdp_page3", "timestamp": 1750416329231, "type": "takeScreenshot"}, {"action_id": "F1olhgKhUt", "executionTime": "6620ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1748258009231, "type": "tap"}, {"action_id": "xwCtOeDknu", "duration": 5, "time": 5, "timestamp": 1750416579318, "type": "wait"}, {"action_id": "o7Tr5KGBTC", "screenshot_name": "scrn_wishlist_with_product", "timestamp": 1750416606426, "type": "takeScreenshot"}, {"type": "tap", "timestamp": 1750416825661, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[contains(@name,\"txtPrice\")])[1]/following-sibling::XCUIElementTypeImage[2]", "timeout": 10, "interval": 0.5, "method": "locator", "action_id": "VDgdXv2m8t"}, {"type": "takeScreenshot", "timestamp": 1750416870915, "screenshot_name": "scrn_wishlist_options", "action_id": "jGZ9mVjTh1"}, {"action_id": "<PERSON><PERSON><PERSON>ynQyu", "double_tap": false, "executionTime": "3221ms", "text_to_find": "Remove", "timeout": 30, "timestamp": 1746838738528, "type": "tapOnText"}, {"action_id": "lWIRxRm6HE", "executionTime": "5127ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746840700159, "type": "tap"}, {"type": "waitTill", "timestamp": 1750417012774, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "timeout": 10, "interval": 0.5, "action_id": "ornIHsiLr6"}, {"type": "takeScreenshot", "timestamp": 1750417078972, "screenshot_name": "scrn_bag_delivery_page", "action_id": "PtBiDYrYaE"}, {"action_id": "fMzoZJg9I7", "double_tap": false, "image_filename": "homepage-search-se.png", "method": "image", "text_to_find": "Collect", "threshold": 0.7, "timeout": 30, "timestamp": 1748314463271, "type": "tapOnText"}, {"action_id": "xwCtOeDknu", "duration": 5, "time": 5, "timestamp": 1750417140530, "type": "wait"}, {"type": "takeScreenshot", "timestamp": 1750417169485, "screenshot_name": "scrn_bag_cnc_page", "action_id": "qIC2MJVHQ0"}, {"action_id": "Ob26qqcA0p", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "20455ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1748260434324, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "k3mu9Mt7Ec", "executionTime": "3977ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "method": "locator", "timeout": 10, "timestamp": 1746100354856, "type": "tap"}, {"action_id": "8umPSX0vrr", "executionTime": "3335ms", "image_filename": "banner-close-updated.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": *************, "type": "tap"}, {"action_id": "k3mu9Mt7Ec", "executionTime": "3977ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"type": "takeScreenshot", "timestamp": *************, "screenshot_name": "scrn_account_loggedin_user_page1", "action_id": "IpK3gIRaHF"}, {"action_id": "Ob26qqcA0p", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "8112ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": *************, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"type": "takeScreenshot", "timestamp": *************, "screenshot_name": "scrn_account_loggedin_user_page2", "action_id": "kAQXuSD2fC"}, {"action_id": "OyUowAaBzD", "executionTime": "3974ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}], "labels": [], "updated": "2025-06-20 21:03:52", "test_case_id": "tc_4038a37bc10a"}