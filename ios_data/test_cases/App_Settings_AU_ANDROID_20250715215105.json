{"name": "App Settings AU_ANDROID", "created": "2025-08-09 09:26:14", "device_id": "PJTCI7EMSSONYPU8", "actions": [{"action_id": "XEbZHdi0GT", "executionTime": "3369ms", "package_id": "au.com.kmart", "timestamp": *************, "type": "terminateApp"}, {"action_id": "XEbZHdi0GT", "executionTime": "3369ms", "package_id": "au.com.kmart", "timestamp": *************, "type": "launchApp"}, {"action_id": "veukWo4573", "executionTime": "2427ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtHomeAccountCtaSignIn\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "vkkWGFewke", "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "KmartSigninAUANDROID_20250709191752.json", "test_case_name": "Kmart-Signin-AU-ANDROID", "test_case_steps": [{"action_id": "HEBUNq0P6l", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "3963ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "AtwFJEKbDH", "enter": true, "function_name": "text", "text": "<EMAIL>", "timestamp": *************, "type": "text"}, {"action_id": "Rr6FnqfUnC", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Continue to password\"]", "method": "locator", "timeout": 10, "timestamp": 1754695396763, "type": "tap"}, {"action_id": "TkyyzFSlcu", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"password\"]", "method": "locator", "timeout": 10, "timestamp": 1751405157652, "type": "tap"}, {"action_id": "K7yV3GGsgr", "enter": true, "executionTime": "3905ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "text": "Wonderbaby@6", "timeout": 10, "timestamp": 1745666199850, "type": "text"}], "test_case_steps_count": 6, "timestamp": 1754695522735, "type": "multiStep"}, {"action_id": "mIKA85kXaW", "executionTime": "1233ms", "package_id": "com.android.settings", "timestamp": 1749444829578, "type": "terminateApp"}, {"action_id": "LfyQctrEJn", "executionTime": "1236ms", "package_id": "com.android.settings", "timestamp": 1749444810598, "type": "launchApp"}, {"action_id": "w1RV76df9x", "executionTime": "3298ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//androidx.recyclerview.widget.RecyclerView[@resource-id=\"com.android.settings:id/recycler_view\"]/android.widget.LinearLayout[3]", "method": "locator", "text_to_find": "Wi-Fi", "timeout": 10, "timestamp": 1749444898707, "type": "tap"}, {"action_id": "V42eHtTRYW", "duration": 5, "executionTime": "5018ms", "time": 5, "timestamp": 1749445273934, "type": "wait"}, {"action_id": "jUCAk6GJc4", "executionTime": "1020ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Switch[@resource-id=\"android:id/switch_widget\"]", "method": "locator", "timeout": 10, "timestamp": 1749445081254, "type": "tap"}, {"action_id": "V42eHtTRYW", "duration": 5, "executionTime": "5021ms", "time": 5, "timestamp": 1750843578808, "type": "wait"}, {"action_id": "oSQ8sPdVOJ", "executionTime": "3293ms", "package_id": "au.com.kmart", "timestamp": 1752581195187, "type": "terminateApp"}, {"action_id": "oSQ8sPdVOJ", "executionTime": "3293ms", "package_id": "au.com.kmart", "timestamp": 1749445437019, "type": "launchApp"}, {"action_id": "cokvFXhj4c", "executionTime": "283ms", "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtNo internet connection\"]", "timeout": 10, "timestamp": 1749445125565, "type": "exists"}, {"action_id": "3KNqlNy6Bj", "executionTime": "803ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 2 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749445177888, "type": "tap"}, {"action_id": "cokvFXhj4c", "executionTime": "283ms", "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtNo internet connection\"]", "timeout": 10, "timestamp": 1752581287764, "type": "exists"}, {"action_id": "3KNqlNy6Bj", "executionTime": "803ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 3 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752581297924, "type": "tap"}, {"action_id": "cokvFXhj4c", "executionTime": "283ms", "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtNo internet connection\"]", "timeout": 10, "timestamp": 1752581320274, "type": "exists"}, {"action_id": "3KNqlNy6Bj", "executionTime": "803ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752581338392, "type": "tap"}, {"action_id": "cokvFXhj4c", "executionTime": "283ms", "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtNo internet connection\"]", "timeout": 10, "timestamp": 1752581366187, "type": "exists"}, {"action_id": "mIKA85kXaW", "executionTime": "1233ms", "package_id": "com.android.settings", "timestamp": 1752581395092, "type": "terminateApp"}, {"action_id": "LfyQctrEJn", "executionTime": "1236ms", "package_id": "com.android.settings", "timestamp": 1752581426909, "type": "launchApp"}, {"action_id": "w1RV76df9x", "executionTime": "3298ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//androidx.recyclerview.widget.RecyclerView[@resource-id=\"com.android.settings:id/recycler_view\"]/android.widget.LinearLayout[3]", "method": "locator", "text_to_find": "Wi-Fi", "timeout": 10, "timestamp": 1752581453309, "type": "tap"}, {"action_id": "jUCAk6GJc4", "executionTime": "1020ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Switch[@resource-id=\"android:id/switch_widget\"]", "method": "locator", "timeout": 10, "timestamp": 1752581473143, "type": "tap"}, {"action_id": "V42eHtTRYW", "duration": 5, "executionTime": "5014ms", "time": 5, "timestamp": 1750843560629, "type": "wait"}, {"action_id": "hCCEvRtj1A", "executionTime": "3265ms", "package_id": "au.com.kmart", "timestamp": 1749445309230, "type": "terminateApp"}, {"action_id": "hCCEvRtj1A", "executionTime": "3265ms", "package_id": "au.com.kmart", "timestamp": 1752581522673, "type": "launchApp"}, {"action_id": "UpUSVInizv", "executionTime": "2680ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1749445687300, "type": "tap"}, {"action_id": "ZZPNqTJ65s", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 10, "executionTime": "2818ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1749445784426, "type": "swipe", "vector_end": [0.5, 0.1], "vector_start": [0.5, 0.7]}, {"action_id": "LcYLwUffqj", "executionTime": "2994ms", "text_to_find": "out", "timeout": 30, "timestamp": 1749445809311, "type": "tapOnText"}, {"action_id": "rmqVgsHPp8", "package_id": "com.android.chrome", "timestamp": 1753532546014, "type": "restartApp"}, {"action_id": "KAyXxO6c02", "locator_type": "xpath", "locator_value": "//android.widget.Button[@resource-id=\"com.android.chrome:id/signin_fre_dismiss_button\"]", "timeout": 10, "timestamp": 1753532616815, "type": "tapIfLocatorExists"}, {"action_id": "pOJrD4NK3F", "locator_type": "xpath", "locator_value": "//android.widget.Button[@resource-id=\"com.android.chrome:id/more_button\"]", "timeout": 10, "timestamp": 1753532718163, "type": "tapIfLocatorExists"}, {"action_id": "t6c0RSgUVq", "locator_type": "xpath", "locator_value": "//android.widget.Button[@resource-id=\"com.android.chrome:id/ack_button\"]", "timeout": 10, "timestamp": 1753532783771, "type": "tapIfLocatorExists"}, {"action_id": "Pj4FuA5oi5", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"com.android.chrome:id/url_bar\"]", "method": "locator", "timeout": 15, "timestamp": 1753532862179, "type": "tap"}, {"action_id": "fTdGMJ3NH3", "enter": true, "executionTime": "1322ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"https://www.kmart.com.au\"]", "method": "locator", "text": "kmart au", "timeout": 10, "timestamp": 1749446027317, "type": "text"}, {"action_id": "UpUSVInizv", "executionTime": "2532ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[@text=\"https://www.kmart.com.au\"]", "method": "locator", "timeout": 10, "timestamp": 1749472397515, "type": "tap"}, {"action_id": "QUeGIASAxV", "count": 1, "direction": "up", "duration": 300, "enabled": true, "end_x": 50, "end_y": 30, "executionTime": "2208ms", "interval": 0.5, "method": "coordinates", "start_x": 50, "start_y": 70, "timestamp": 1751711412051, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7], "x": 0, "y": 0}, {"action_id": "Cmvm82hiAa", "executionTime": "5524ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"Home & Living\"]", "method": "locator", "timeout": 10, "timestamp": 1749472379198, "type": "tap"}, {"action_id": "QUeGIASAxV", "count": 1, "direction": "up", "duration": 300, "enabled": true, "end_x": 50, "end_y": 30, "executionTime": "2208ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[@content-desc=\"Styled by You\"]/android.view.View[2]/android.view.View/android.widget.ImageView[1]", "method": "locator", "start_x": 50, "start_y": 70, "timeout": 10, "timestamp": 1752582182766, "type": "tap", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7], "x": 0, "y": 0}, {"action_id": "gkkQzTCmma", "executionTime": "3498ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"$\")]", "method": "locator", "text_to_find": "Catalogue", "timeout": 10, "timestamp": 1749470262060, "type": "tap"}, {"action_id": "ZZPNqTJ65s", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "3439ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1751711463086, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "gkkQzTCmma", "executionTime": "3498ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Add to bag\"]", "method": "locator", "text_to_find": "Catalogue", "timeout": 10, "timestamp": 1752582562441, "type": "tap"}, {"action_id": "gkkQzTCmma", "executionTime": "3498ms", "function_name": "send_key_event", "interval": 0.5, "key_event": "BACK", "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Add to bag\"]", "method": "locator", "text_to_find": "Catalogue", "timeout": 10, "timestamp": 1752582616397, "type": "androidFunctions"}, {"action_id": "gkkQzTCmma", "executionTime": "3498ms", "function_name": "send_key_event", "interval": 0.5, "key_event": "BACK", "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Add to bag\"]", "method": "locator", "text_to_find": "Catalogue", "timeout": 10, "timestamp": 1752582664264, "type": "androidFunctions"}, {"action_id": "UpUSVInizv", "executionTime": "2680ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752582676415, "type": "tap"}, {"action_id": "BTvTGpQ9IK", "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"Checkout\"]", "timeout": 10, "timestamp": 1753534062594, "type": "tapIfLocatorExists"}, {"action_id": "igReeDqips", "executionTime": "2833ms", "image_filename": "delivery-tab-android.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@text=\"Delivery\"]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1749471352255, "type": "tap"}, {"action_id": "ZZPNqTJ65s", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 50, "executionTime": "3439ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1753536378673, "type": "swipe", "vector_end": [0.5, 0.5], "vector_start": [0.5, 0.7]}, {"action_id": "Pd7cReoJM6", "executionTime": "4726ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Increase quantity\"]", "method": "locator", "text_to_find": "List", "timeout": 10, "timestamp": 1749472320276, "type": "tap"}, {"action_id": "Pd7cReoJM6", "executionTime": "4726ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Decrease quantity\"]", "method": "locator", "text_to_find": "List", "timeout": 10, "timestamp": 1752582906295, "type": "tap"}, {"action_id": "JcAR0JctQ6", "executionTime": "2419ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"Remove\")]", "method": "locator", "timeout": 10, "timestamp": 1749472290567, "type": "tap"}, {"action_id": "KqTcr10JDm", "image_filename": "bag-close-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1752583076309, "type": "tap"}, {"action_id": "UpUSVInizv", "executionTime": "2680ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 2 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752583125267, "type": "tap"}, {"action_id": "ZZPNqTJ65s", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "3439ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1752583159491, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "gkkQzTCmma", "executionTime": "3089ms", "text_to_find": "Catalogue", "timeout": 30, "timestamp": 1749472424498, "type": "tapOnText"}, {"action_id": "S7PVvWSmaK", "image_filename": "catalogue-menu-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1753536639512, "type": "tap"}, {"action_id": "YHaMIjULRf", "executionTime": "4713ms", "text_to_find": "List", "timeout": 30, "timestamp": 1749472769571, "type": "tapOnText"}, {"action_id": "Qy0Y0uJchm", "executionTime": "2407ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//android.widget.TextView[contains(@text,\"$\")])[1]", "method": "locator", "timeout": 20, "timestamp": 1749472775719, "type": "tap"}, {"action_id": "ZZPNqTJ65s", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 50, "executionTime": "3439ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1753534634063, "type": "swipe", "vector_end": [0.5, 0.5], "vector_start": [0.5, 0.7]}, {"action_id": "Iab9zCfpqO", "double_tap": false, "executionTime": "17678ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Add to bag", "method": "locator", "text_to_find": "Add", "timeout": 30, "timestamp": 1749472786795, "type": "tapOnText"}, {"action_id": "UpUSVInizv", "executionTime": "2680ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1753534156795, "type": "tap"}, {"action_id": "jW6OPorKBq", "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"Checkout\"]", "timeout": 10, "timestamp": 1753534201332, "type": "tapIfLocatorExists"}, {"action_id": "K0c1gL9UK1", "executionTime": "2543ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[contains(@text,\"Remove\")]", "method": "locator", "timeout": 10, "timestamp": 1749472862396, "type": "tap"}, {"action_id": "3NOS1fbxZs", "executionTime": "2296ms", "image_filename": "banner-close-updated.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1749473253040, "type": "tap"}, {"action_id": "Qb1AArnpCH", "duration": 5, "executionTime": "5011ms", "time": 5, "timestamp": 1750975463814, "type": "wait"}], "labels": [], "updated": "2025-08-09 09:26:14"}