{"name": "AU- MyAccount_Android", "created": "2025-08-09 09:27:38", "device_id": "PJTCI7EMSSONYPU8", "actions": [{"action_id": "FK0xWTx8zz", "executionTime": "335ms", "method": "coordinates", "package_id": "au.com.kmart", "timestamp": *************, "type": "terminateApp", "x": 0, "y": 0}, {"action_id": "pjFNt3w5Fr", "executionTime": "1549ms", "package_id": "au.com.kmart", "timestamp": *************, "type": "launchApp"}, {"action_id": "u6bRYZZFAv", "duration": 5, "executionTime": "5054ms", "time": 5, "timestamp": *************, "type": "wait"}, {"action_id": "xAPeBnVHrT", "executionTime": "2971ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "MGQkaPmMXD", "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "KmartSigninAUANDROID_20250709191752.json", "test_case_name": "Kmart-Signin-AU-ANDROID", "test_case_steps": [{"action_id": "HEBUNq0P6l", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "3963ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "AtwFJEKbDH", "enter": true, "function_name": "text", "text": "<EMAIL>", "timestamp": *************, "type": "text"}, {"action_id": "Rr6FnqfUnC", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Continue to password\"]", "method": "locator", "timeout": 10, "timestamp": 1754695396763, "type": "tap"}, {"action_id": "TkyyzFSlcu", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"password\"]", "method": "locator", "timeout": 10, "timestamp": 1751405157652, "type": "tap"}, {"action_id": "K7yV3GGsgr", "enter": true, "executionTime": "3905ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "text": "Wonderbaby@6", "timeout": 10, "timestamp": 1745666199850, "type": "text"}], "test_case_steps_count": 6, "timestamp": *************, "type": "multiStep"}, {"action_id": "sl3Wk1gK8X", "executionTime": "9898ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "V59u3l1wkM", "executionTime": "474ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[contains(@content-desc,\"Manage your account\")]", "timeout": 20, "timestamp": *************, "type": "waitTill"}, {"action_id": "pFlYwTS53v", "double_tap": false, "executionTime": "2235ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeLink[@name=\"My orders & receipts\"]", "method": "locator", "text_to_find": "receipts", "timeout": 30, "timestamp": *************, "type": "tapOnText"}, {"action_id": "Z6g3sGuHTp", "duration": 5, "executionTime": "5066ms", "time": 5, "timestamp": *************, "type": "wait"}, {"action_id": "7g6MFJSGIO", "executionTime": "2058ms", "image_filename": "order-link-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//android.widget.TextView[@text=\"ID\"]/following-sibling::android.view.View/android.view.View/android.widget.TextView)[1]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "ADHRFCY0LX", "duration": 10, "executionTime": "10055ms", "method": "coordinates", "time": 10, "timestamp": 1752302090675, "type": "wait", "x": 0, "y": 0}, {"action_id": "Rl6s389Qsd", "count": 4, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "4957ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1752297398436, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "GgQaBLWYkb", "double_tap": false, "executionTime": "1746ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.view.View[@text=\"Tracking ‌\"]", "method": "locator", "text_to_find": "invoice", "timeout": 30, "timestamp": 1746521169867, "type": "tapOnText"}, {"action_id": "zNwyPagPE1", "duration": 6, "executionTime": "6058ms", "time": 6, "timestamp": 1752303153123, "type": "wait"}, {"action_id": "g0PE7Mofye", "double_tap": false, "executionTime": "1733ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Print order details\"]", "method": "locator", "text_to_find": "Print", "timeout": 30, "timestamp": 1746521217340, "type": "tapOnText"}, {"action_id": "zNwyPagPE1", "duration": 6, "executionTime": "6055ms", "time": 6, "timestamp": 1752303186513, "type": "wait"}, {"action_id": "YuuQe2KupX", "executionTime": "160ms", "function_name": "send_key_event", "interval": 0.5, "key_event": "BACK", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Cancel\"]", "method": "locator", "timeout": 10, "timestamp": 1746521262417, "type": "androidFunctions"}, {"action_id": "YuuQe2KupX", "count": 3, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "2175ms", "function_name": "send_key_event", "interval": 0.5, "key_event": "BACK", "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Return to my orders\"]", "method": "locator", "start_x": 50, "start_y": 70, "timeout": 10, "timestamp": 1752297845892, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "YuuQe2KupX", "count": 3, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "885ms", "function_name": "send_key_event", "interval": 0.5, "key_event": "BACK", "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Return to my orders\"]", "method": "locator", "start_x": 50, "start_y": 70, "timeout": 10, "timestamp": 1752303096165, "type": "tap", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "zNwyPagPE1", "duration": 5, "executionTime": "5058ms", "time": 5, "timestamp": 1746575257741, "type": "wait"}, {"action_id": "7g6MFJSGIO", "executionTime": "1714ms", "image_filename": "order-link-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//android.widget.TextView[@text=\"ID\"]/following-sibling::android.view.View/android.view.View/android.widget.TextView)[1]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1752297942030, "type": "tap"}, {"action_id": "mRTYzOFRRw", "executionTime": "3306ms", "locator_type": "xpath", "locator_value": "//android.widget.TextView[@text=\"Learn more about refunds\"]", "timeout": 10, "timestamp": 1746521347391, "type": "exists"}, {"action_id": "9iOZGMqAZK", "executionTime": "877ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[@text=\"Learn more about refunds\"]", "method": "locator", "timeout": 10, "timestamp": 1746521360629, "type": "tap"}, {"action_id": "aAaTtUE92h", "executionTime": "450ms", "locator_type": "xpath", "locator_value": "//android.widget.TextView[@resource-id=\"exchanges-returns\"]", "timeout": 10, "timestamp": 1746521417323, "type": "exists"}, {"action_id": "YuuQe2KupX", "executionTime": "150ms", "function_name": "send_key_event", "interval": 0.5, "key_event": "BACK", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Cancel\"]", "method": "locator", "timeout": 10, "timestamp": 1752298050649, "type": "androidFunctions"}, {"action_id": "vmc01sHkbr", "duration": 5, "executionTime": "5053ms", "time": 5, "timestamp": 1746575241902, "type": "wait"}, {"action_id": "sl3Wk1gK8X", "executionTime": "1001ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1752298170954, "type": "tap"}, {"action_id": "3hOTINBVMf", "executionTime": "1932ms", "text_to_find": "details", "timeout": 30, "timestamp": 1746522842993, "type": "tapOnText"}, {"action_id": "YuuQe2KupX", "executionTime": "193ms", "function_name": "send_key_event", "interval": 0.5, "key_event": "BACK", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Cancel\"]", "method": "locator", "timeout": 10, "timestamp": 1752298239924, "type": "androidFunctions"}, {"action_id": "20qUCJgpE9", "double_tap": false, "executionTime": "1960ms", "text_to_find": "address", "timeout": 30, "timestamp": 1746523053141, "type": "tapOnText"}, {"action_id": "YuuQe2KupX", "executionTime": "190ms", "function_name": "send_key_event", "interval": 0.5, "key_event": "BACK", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Cancel\"]", "method": "locator", "timeout": 10, "timestamp": 1752298379652, "type": "androidFunctions"}, {"action_id": "napKDohf3Z", "double_tap": false, "executionTime": "1944ms", "text_to_find": "payment", "timeout": 30, "timestamp": 1746523090959, "type": "tapOnText"}, {"action_id": "YuuQe2KupX", "executionTime": "175ms", "function_name": "send_key_event", "interval": 0.5, "key_event": "BACK", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Cancel\"]", "method": "locator", "timeout": 10, "timestamp": 1752298409386, "type": "androidFunctions"}, {"action_id": "BracBsfa3Y", "executionTime": "1802ms", "text_to_find": "Flybuys", "timeout": 30, "timestamp": 1746573390611, "type": "tapOnText"}, {"action_id": "40hnWPsQ9P", "executionTime": "304ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btneditFlybuysCard", "method": "locator", "timeout": 15, "timestamp": 1746523229356, "type": "waitTill"}, {"action_id": "40hnWPsQ9P", "executionTime": "880ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btneditFlybuysCard", "method": "locator", "timeout": 10, "timestamp": 1746577799791, "type": "tap"}, {"action_id": "3ZFgwFaiXp", "executionTime": "1056ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Remove card", "method": "locator", "timeout": 15, "timestamp": 1746523274958, "type": "tap"}, {"action_id": "Ds5GfNVb3x", "executionTime": "963ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnRemove", "method": "locator", "timeout": 15, "timestamp": 1746523316209, "type": "tap"}, {"action_id": "BracBsfa3Y", "executionTime": "2046ms", "text_to_find": "Flybuys", "timeout": 30, "timestamp": 1746523184246, "type": "tapOnText"}, {"action_id": "Gxhf3XGc6e", "executionTime": "1030ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnLinkFlyBuys", "method": "locator", "timeout": 10, "timestamp": 1746573488051, "type": "tap"}, {"action_id": "Ey86YRVRzU", "executionTime": "1606ms", "interval": 0.5, "locator_type": "uiselector", "locator_value": "new UiSelector().className(\"android.widget.EditText\")", "method": "locator", "timeout": 10, "timestamp": 1746573530684, "type": "tap"}, {"action_id": "sLe0Wurhgm", "executionTime": "251ms", "locator_type": "accessibility_id", "locator_value": "Flybuys barcode number", "method": "locator", "text": "2791234567890", "timeout": 15, "timestamp": 1746573561688, "type": "text"}, {"action_id": "biRyWs3nSs", "executionTime": "393ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnSaveFlybuysCard", "method": "locator", "timeout": 10, "timestamp": 1746573806006, "type": "tap"}, {"action_id": "YuuQe2KupX", "executionTime": "277ms", "function_name": "send_key_event", "interval": 0.5, "key_event": "BACK", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Cancel\"]", "method": "locator", "timeout": 10, "timestamp": 1752298954715, "type": "androidFunctions"}, {"action_id": "2M0KHOVecv", "executionTime": "255ms", "locator_type": "accessibility_id", "locator_value": "txtMy Flybuys card", "timeout": 10, "timestamp": 1746573880079, "type": "exists"}, {"action_id": "s6tWdQ5URW", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "3597ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1748085981704, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "BracBsfa3Y", "double_tap": false, "executionTime": "1707ms", "text_to_find": "locator", "timeout": 30, "timestamp": 1746573912172, "type": "tapOnText"}, {"action_id": "BracBsfa3Y", "double_tap": false, "executionTime": "3855ms", "text_to_find": "Nearby", "timeout": 30, "timestamp": 1746573997116, "type": "tapOnText"}, {"action_id": "3Si0csRNaw", "enter": true, "executionTime": "308ms", "method": "coordinates", "text": "3000", "timeout": 15, "timestamp": 1749444122904, "type": "text", "x": "env[store-locator-x]", "y": "env[store-locator-y]"}, {"action_id": "PgjJCrKFYo", "double_tap": false, "executionTime": "1833ms", "image_filename": "storelocator-3000-se.png", "method": "image", "text_to_find": "VIC", "threshold": 0.7, "timeout": 30, "timestamp": 1746574179875, "type": "tapOnText"}, {"action_id": "2BfJyzwQFx", "executionTime": "1246ms", "locator_type": "text", "locator_value": "Cbd", "timeout": 10, "timestamp": 1752299100846, "type": "exists"}, {"action_id": "YuuQe2KupX", "executionTime": "190ms", "function_name": "send_key_event", "interval": 0.5, "key_event": "BACK", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Cancel\"]", "method": "locator", "timeout": 10, "timestamp": 1752299122236, "type": "androidFunctions"}, {"action_id": "YuuQe2KupX", "executionTime": "258ms", "function_name": "send_key_event", "interval": 0.5, "key_event": "BACK", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Cancel\"]", "method": "locator", "timeout": 10, "timestamp": 1752304028267, "type": "androidFunctions"}, {"action_id": "BracBsfa3Y", "double_tap": false, "executionTime": "1743ms", "text_to_find": "Invite", "timeout": 30, "timestamp": 1746574278949, "type": "tapOnText"}, {"action_id": "ePyaYpttQA", "executionTime": "330ms", "locator_type": "xpath", "locator_value": "//android.widget.TextView[@resource-id=\"android:id/content_preview_text\"]", "timeout": 10, "timestamp": 1746574369210, "type": "exists"}, {"action_id": "YuuQe2KupX", "executionTime": "220ms", "function_name": "send_key_event", "interval": 0.5, "key_event": "BACK", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Cancel\"]", "method": "locator", "timeout": 10, "timestamp": 1752299253777, "type": "androidFunctions"}, {"action_id": "BracBsfa3Y", "double_tap": false, "executionTime": "1769ms", "text_to_find": "Customer", "timeout": 30, "timestamp": 1746574406045, "type": "tapOnText"}, {"action_id": "YuuQe2KupX", "executionTime": "300ms", "function_name": "send_key_event", "interval": 0.5, "key_event": "BACK", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Cancel\"]", "method": "locator", "timeout": 10, "timestamp": 1752299271313, "type": "androidFunctions"}, {"action_id": "s6tWdQ5URW", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "3248ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1746575871287, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "BracBsfa3Y", "double_tap": false, "executionTime": "1734ms", "text_to_find": "out", "timeout": 30, "timestamp": 1746574489503, "type": "tapOnText"}], "labels": [], "updated": "2025-08-09 09:27:38"}