{"name": "Kmart-Prod Sign in NZ", "created": "2025-06-28 22:01:03", "device_id": "********-00186C801E13C01E", "actions": [{"action_id": "Vxt7QOYeDD", "executionTime": "3441ms", "package_id": "env[appid]", "timestamp": *************, "type": "restartApp"}, {"action_id": "2cTZvK1psn", "executionTime": "7743ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "q4hPXCBtx4", "executionTime": "1283ms", "function_name": "alert_accept", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "ImienLpJEN", "executionTime": "2871ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 30, "timestamp": *************, "type": "waitTill"}, {"action_id": "8OsQmoVYqW", "executionTime": "3598ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "rSxM47lUdy", "enter": true, "function_name": "text", "text": "env[uname]", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "I5bRbYY1hD", "executionTime": "3755ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "CtWhaVwbJC", "enter": true, "function_name": "text", "text": "env[pwd]", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "IsGWxLFpIn", "executionTime": "8873ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "timeout": 15, "timestamp": *************, "type": "exists"}, {"action_id": "08NzsvhQXK", "executionTime": "3104ms", "image_filename": "account-tab.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "xqHGFj3tDd", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "timestamp": *************, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "LlRfimKPrn", "executionTime": "14248ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "FlEukNkjlS", "executionTime": "2831ms", "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "timeout": 15, "timestamp": *************, "type": "exists"}, {"action_id": "cJDpd7aK3d", "executionTime": "5858ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "2kwu2VBmuZ", "executionTime": "1238ms", "function_name": "alert_accept", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "CJ88OgjKXp", "executionTime": "2923ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 30, "timestamp": *************, "type": "waitTill"}, {"action_id": "NL2gtj6qIu", "double_tap": false, "executionTime": "3126ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Apple Sign in with Apple\"]", "method": "locator", "text_to_find": "Apple", "timeout": 30, "timestamp": *************, "type": "tapOnText"}, {"action_id": "CzVeOTdAX9", "duration": 10, "executionTime": "10013ms", "locator_type": "image", "locator_value": "SigninwithPasscode.png", "time": 10, "timeout": 10, "timestamp": *************, "type": "wait"}, {"action_id": "hnH3ayslCh", "double_tap": false, "executionTime": "1884ms", "image_filename": "SigninwithPasscode.png", "method": "image", "text_to_find": "Passcode", "threshold": 0.7, "timeout": 30, "timestamp": 1745985394449, "type": "tapOnText"}, {"action_id": "soKM0KayFJ", "executionTime": "2058ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"5\"]", "method": "locator", "timeout": 10, "timestamp": 1745668718729, "type": "tap"}, {"action_id": "0pwZCYAtOv", "executionTime": "2015ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"9\"]", "method": "locator", "timeout": 10, "timestamp": 1745668723169, "type": "tap"}, {"action_id": "J7BPGVnRJI", "executionTime": "1971ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"1\"]", "method": "locator", "timeout": 10, "timestamp": 1745668728929, "type": "tap"}, {"action_id": "iSckENpXrN", "executionTime": "2014ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"2\"]", "method": "locator", "timeout": 10, "timestamp": 1745668733552, "type": "tap"}, {"action_id": "5nsUXQ5L7u", "executionTime": "2010ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"3\"]", "method": "locator", "timeout": 10, "timestamp": 1745668738152, "type": "tap"}, {"action_id": "zsVeGHiIgX", "executionTime": "2021ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"4\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "KfMHchi8cx", "executionTime": "6056ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "timeout": 15, "timestamp": *************, "type": "exists"}, {"action_id": "CWkqGp5ndO", "executionTime": "3218ms", "image_filename": "account-tab.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "uuatVQwQFW", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "timestamp": *************, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "ts3qyFxyMf", "executionTime": "14328ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "jQYHQIvQ8l", "executionTime": "2840ms", "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "timeout": 15, "timestamp": *************, "type": "exists"}, {"action_id": "quzlwPw42x", "executionTime": "5900ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "6HhScBaqQp", "executionTime": "1285ms", "function_name": "alert_accept", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "SDtskxyVpg", "executionTime": "3003ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 20, "timestamp": *************, "type": "waitTill"}, {"action_id": "xLGm9FefWE", "double_tap": false, "executionTime": "3145ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Google Sign in with Google\"]", "method": "locator", "text_to_find": "Google", "timeout": 30, "timestamp": *************, "type": "tapOnText"}, {"action_id": "vwFwkK6ydQ", "executionTime": "3840ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeLink[@name=\"<NAME_EMAIL>\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "bZCkx4U9Gk", "executionTime": "4030ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "timeout": 15, "timestamp": *************, "type": "exists"}, {"action_id": "FARWZvOj0x", "executionTime": "3226ms", "image_filename": "account-tab.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "ISfUFUnvFh", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "timestamp": *************, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "Bdhe5AoUlM", "executionTime": "2800ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "w0CWlknXmX", "duration": 5, "time": 5, "timestamp": *************, "type": "wait"}, {"type": "cleanupSteps", "timestamp": *************, "test_case_id": "Kmart_NZ_Cleanup_20250628211956.json", "test_case_name": "Kmart_NZ_Cleanup", "test_case_steps_count": 0, "loading_in_progress": false, "test_case_steps": [{"action_id": "OQ1fr8NUlV", "method": "coordinates", "package_id": "nz.com.kmart", "timestamp": 1750934498011, "type": "restartApp", "x": 0, "y": 0}, {"action_id": "IOzaCR1Euv", "duration": 5, "time": 5, "timestamp": 1750976955704, "type": "wait"}, {"action_id": "vAKpEDIzs7", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750975087425, "type": "tap"}, {"action_id": "hwdyCKFAUJ", "count": 1, "direction": "up", "duration": 500, "end_x": 50, "end_y": 30, "executionTime": "1814ms", "image_filename": "delivery-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "method": "locator", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 10, "timestamp": 1750975036314, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "SPgFRgq13M", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "timeout": 10, "timestamp": 1750978954486, "type": "tapIfLocatorExists"}, {"action_id": "ZhH80yndRU", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "package_id": "au.com.kmart", "timeout": 10, "timestamp": 1750975079444, "type": "terminateApp"}], "steps_loaded": true, "display_depth": 0, "action_id": "DcZCTgLVW9"}], "labels": [], "updated": "2025-06-28 22:01:03"}