{"name": "Postcode Flow_NZ", "created": "2025-06-28 21:41:37", "device_id": "********-00186C801E13C01E", "actions": [{"action_id": "H9fy9qcFbZ", "executionTime": "2462ms", "package_id": "env[appid]", "timestamp": *************, "type": "restartApp"}, {"action_id": "Y8vz7AJD1i", "executionTime": "4846ms", "fallback_locators": [{"locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtHomeAccountCtaSignIn\"]"}], "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "2xC5fLfLe8", "executionTime": "1158ms", "function_name": "alert_accept", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "Azb1flbIJJ", "executionTime": "1923ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 30, "timestamp": *************, "type": "waitTill"}, {"action_id": "kBjnmRXZVq", "expanded": false, "test_case_id": "KmartSignin_Copy_20250513181428_20250513181428.json", "test_case_name": "Kmart-NZ-Signin", "test_case_steps_count": 6, "timestamp": *************, "type": "multiStep", "loading_in_progress": false, "test_case_steps": [{"action_id": "EELcfo48Sh", "executionTime": "3169ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "5372ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "method": "locator", "timeout": 10, "timestamp": 1745665879530, "type": "tap"}, {"action_id": "2mhi7GOrlS", "enter": true, "function_name": "text", "text": "<EMAIL>", "timestamp": 1750035024895, "type": "iosFunctions"}, {"action_id": "K7yV3GGsgr", "executionTime": "5184ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "timeout": 10, "timestamp": 1745666199850, "type": "tap"}, {"action_id": "iExyA0ZirJ", "enter": true, "function_name": "text", "text": "Wonderbaby@5", "timestamp": 1750035059411, "type": "iosFunctions"}], "steps_loaded": true, "display_depth": 0}, {"action_id": "m0956RsrdM", "executionTime": "1736ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[contains(@name,\"Deliver\")]", "timeout": 20, "timestamp": 1746607636181, "type": "waitTill"}, {"action_id": "QMXBlswP6H", "double_tap": false, "executionTime": "2053ms", "image_filename": "homepage_editbtn-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[contains(@name,\"Deliver\")]", "method": "image", "text_to_find": "Edit", "threshold": 0.7, "timeout": 20, "timestamp": 1746143899898, "type": "tap"}, {"action_id": "8WCusTZ8q9", "executionTime": "3668ms", "fallback_locators": [{"locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Search suburb or postcode\"]"}], "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Search suburb or postcode", "method": "locator", "timeout": 10, "timestamp": 1746144003528, "type": "tap"}, {"action_id": "kbdEPCPYod", "delay": 500, "executionTime": "3240ms", "text": "CHRISTCHURCH", "timestamp": 1746144035427, "type": "textClear"}, {"action_id": "mw9GQ4mzRE", "double_tap": false, "executionTime": "3333ms", "image_filename": "christchurch-se.png", "method": "image", "text_to_find": "8053", "threshold": 0.7, "timeout": 30, "timestamp": 1746144235322, "type": "tapOnText"}, {"action_id": "Sl6eiqZkRm", "executionTime": "2794ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnSaveOrContinue", "timeout": 20, "timestamp": 1746608048465, "type": "waitTill"}, {"action_id": "E2jpN7BioW", "double_tap": false, "executionTime": "3260ms", "text_to_find": "Save", "timeout": 30, "timestamp": 1746144262142, "type": "tapOnText"}, {"action_id": "Xqj9EIVEfg", "condition": {"locator_type": "accessibility_id", "locator_value": "btnUpdate", "timeout": 10}, "condition_type": "exists", "executionTime": "4655ms", "then_action": {"locator_type": "accessibility_id", "locator_value": "btnUpdate", "timeout": 10, "type": "clickElement"}, "timestamp": 1746144409595, "type": "ifElseSteps"}, {"action_id": "b06OvcntcY", "executionTime": "10619ms", "locator_type": "text", "locator_value": "<PERSON><PERSON><PERSON>", "timeout": 30, "timestamp": 1746144713330, "type": "exists"}, {"action_id": "70iOOakiG7", "double_tap": false, "executionTime": "3999ms", "image_filename": "homepage-search-se.png", "method": "image", "text_to_find": "Find", "threshold": 0.7, "timeout": 30, "timestamp": 1745980305734, "type": "tapOnText"}, {"action_id": "IupxLP2Jsr", "executionTime": "2562ms", "function_name": "text", "text": "Uno card", "timestamp": 1745484826180, "type": "iosFunctions"}, {"action_id": "C6JHhLdWTv", "executionTime": "1682ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Filter\"]", "timeout": 30, "timestamp": 1745485041367, "type": "waitTill"}, {"action_id": "VkUKQbf1Qt", "double_tap": false, "executionTime": "3670ms", "image_filename": "homepage_editbtn-se.png", "method": "image", "text_to_find": "Edit", "threshold": 0.7, "timeout": 30, "timestamp": 1746144823467, "type": "tapOnText"}, {"action_id": "8WCusTZ8q9", "executionTime": "3673ms", "fallback_locators": [{"locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Search suburb or postcode\"]"}], "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Search suburb or postcode", "method": "locator", "timeout": 10, "timestamp": 1747124864993, "type": "tap"}, {"action_id": "kbdEPCPYod", "delay": 500, "executionTime": "3063ms", "text": "AUCKLAND", "timestamp": 1747124893809, "type": "textClear"}, {"action_id": "GYRHQr7TWx", "double_tap": false, "executionTime": "3114ms", "image_filename": "auckland-se.png", "method": "image", "text_to_find": "0616", "threshold": 0.7, "timeout": 30, "timestamp": 1746608514468, "type": "tapOnText"}, {"action_id": "M3dXqigqRv", "executionTime": "2790ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnSaveOrContinue", "timeout": 20, "timestamp": 1746608171409, "type": "waitTill"}, {"action_id": "pKjXoj4mNg", "double_tap": false, "executionTime": "3099ms", "text_to_find": "Save", "timeout": 30, "timestamp": 1746144919296, "type": "tapOnText"}, {"action_id": "73NABkfWyY", "executionTime": "30144ms", "locator_type": "text", "locator_value": "<PERSON>", "timeout": 30, "timestamp": 1746145022497, "type": "exists"}, {"action_id": "trBISwJ8eZ", "executionTime": "2195ms", "image_filename": "Unocard-product-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746145054848, "type": "tap"}, {"action_id": "letbbewlnA", "executionTime": "2041ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"SKU :\"]", "timeout": 20, "timestamp": 1746608939366, "type": "waitTill"}, {"action_id": "lnjoz8hHUU", "double_tap": false, "executionTime": "3783ms", "image_filename": "homepage_editbtn-se.png", "method": "image", "text_to_find": "Edit", "threshold": 0.7, "timeout": 30, "timestamp": 1746145090019, "type": "tapOnText"}, {"action_id": "WmNWcsWVHv", "executionTime": "3632ms", "fallback_locators": [{"locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Search suburb or postcode\"]"}], "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Search suburb or postcode", "method": "locator", "timeout": 10, "timestamp": 1746145246389, "type": "tap"}, {"action_id": "uZHvvAzVfx", "delay": 500, "executionTime": "3082ms", "text": "CHRISTCHURCH", "timestamp": 1746145223768, "type": "textClear"}, {"action_id": "H0ODFz7sWJ", "double_tap": false, "executionTime": "3142ms", "image_filename": "christchurch-se.png", "method": "image", "text_to_find": "8053", "threshold": 0.7, "timeout": 30, "timestamp": 1746145274227, "type": "tapOnText"}, {"action_id": "hr0IVckpYI", "executionTime": "2770ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnSaveOrContinue", "timeout": 20, "timestamp": 1746608116661, "type": "waitTill"}, {"action_id": "ORI6ZFMBK1", "double_tap": false, "executionTime": "3083ms", "text_to_find": "Save", "timeout": 30, "timestamp": 1746145295421, "type": "tapOnText"}, {"action_id": "b06OvcntcY", "executionTime": "11101ms", "locator_type": "text", "locator_value": "<PERSON><PERSON><PERSON>", "timeout": 30, "timestamp": 1747125262604, "type": "exists"}, {"action_id": "Jf2wJyOphY", "executionTime": "4688ms", "fallback_locators": [{"locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Add to bag\"]"}], "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Add to bag", "method": "locator", "timeout": 10, "timestamp": 1746145623307, "type": "tap"}, {"action_id": "q8oldD8uZt", "executionTime": "1545ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "timeout": 10, "timestamp": 1746145659635, "type": "exists"}, {"action_id": "94ikwhIEE2", "executionTime": "2441ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746145693144, "type": "tap"}, {"action_id": "dF3hpprg71", "executionTime": "1624ms", "image_filename": "cnc-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "threshold": 0.7, "timeout": 10, "timestamp": 1746170955302, "type": "waitTill"}, {"action_id": "3gJsiap2Ds", "double_tap": false, "executionTime": "3105ms", "image_filename": "cnc-tab-se.png", "method": "image", "text_to_find": "Collect", "threshold": 0.7, "timeout": 30, "timestamp": 1746169580270, "type": "tapOnText"}, {"action_id": "uArzgeZYf7", "executionTime": "1677ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[contains(@value,\"Nearby suburb or postcode\")]", "method": "locator", "timeout": 20, "timestamp": 1746609385619, "type": "waitTill"}, {"action_id": "G4A3KBlXHq", "executionTime": "3373ms", "text_to_find": "Nearby", "timeout": 30, "timestamp": 1746145831229, "type": "tapOnText"}, {"action_id": "QpBLC6BStn", "executionTime": "4152ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "delete", "method": "locator", "timeout": 10, "timestamp": 1746145953013, "type": "tap"}, {"action_id": "KOLK6CUomC", "executionTime": "5183ms", "method": "coordinates", "text": "0616", "timeout": 15, "timestamp": 1750037597910, "type": "tapAndType", "x": "env[delivery-addr-x]", "y": "env[delivery-addr-y]"}, {"action_id": "ZWpYNcpbFA", "double_tap": false, "executionTime": "3338ms", "image_filename": "auckland-se.png", "method": "image", "text_to_find": "AUCKLAND", "threshold": 0.7, "timeout": 30, "timestamp": 1746146079046, "type": "tapOnText"}, {"action_id": "bkU728TrRF", "executionTime": "4111ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Done", "method": "locator", "timeout": 10, "timestamp": 1746146113726, "type": "tap"}, {"action_id": "s8h8VDUIOC", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "2837ms", "interval": 0.5, "locator_type": "accessibilityid", "locator_value": "Remove UNO Card Game", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1746146260557, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "eVytJrry9x", "executionTime": "2113ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "method": "locator", "timeout": 10, "timestamp": 1746146287245, "type": "tap"}, {"action_id": "I4gwigwXSj", "executionTime": "1501ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"Continue shopping\"]", "timeout": 30, "timestamp": 1746592230567, "type": "waitTill"}, {"action_id": "Tebej51pT2", "executionTime": "1909ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"Continue shopping\"]", "method": "locator", "timeout": 10, "timestamp": 1746146348304, "type": "tap"}, {"action_id": "73NABkfWyY", "executionTime": "30448ms", "locator_type": "text", "locator_value": "<PERSON>", "timeout": 30, "timestamp": 1747125611929, "type": "exists"}, {"action_id": "F4NGh9HrLw", "executionTime": "2451ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746146617728, "type": "tap"}, {"action_id": "OmgvXicoWK", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "2606ms", "interval": 0.5, "start_x": 50, "start_y": 70, "timestamp": 1748319199766, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "xyHVihJMBi", "executionTime": "2052ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1746146665049, "type": "tap"}, {"type": "cleanupSteps", "timestamp": 1751110894640, "test_case_id": "Kmart_NZ_Cleanup_20250628211956.json", "test_case_name": "Kmart_NZ_Cleanup", "test_case_steps_count": 0, "loading_in_progress": false, "test_case_steps": [{"action_id": "OQ1fr8NUlV", "method": "coordinates", "package_id": "nz.com.kmart", "timestamp": 1750934498011, "type": "restartApp", "x": 0, "y": 0}, {"action_id": "IOzaCR1Euv", "duration": 5, "time": 5, "timestamp": 1750976955704, "type": "wait"}, {"action_id": "vAKpEDIzs7", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750975087425, "type": "tap"}, {"action_id": "hwdyCKFAUJ", "count": 1, "direction": "up", "duration": 500, "end_x": 50, "end_y": 30, "executionTime": "1814ms", "image_filename": "delivery-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "method": "locator", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 10, "timestamp": 1750975036314, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "SPgFRgq13M", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "timeout": 10, "timestamp": 1750978954486, "type": "tapIfLocatorExists"}, {"action_id": "ZhH80yndRU", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "package_id": "au.com.kmart", "timeout": 10, "timestamp": 1750975079444, "type": "terminateApp"}], "steps_loaded": true, "display_depth": 0, "action_id": "rt1xZqSe89"}], "labels": ["nztest"], "updated": "2025-06-29 21:29:04", "description": "", "notes": ""}