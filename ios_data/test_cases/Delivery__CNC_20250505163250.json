{"name": "Delivery & CNC", "created": "2025-08-25 09:59:57", "device_id": "00008120-00186C801E13C01E", "actions": [{"type": "cleanupSteps", "timestamp": *************, "x": 0, "y": 0, "method": "coordinates", "test_case_id": "Kmart_AU_Cleanup_20250626204013.json", "test_case_name": "Kmart_AU_Cleanup", "test_case_steps_count": 0}, {"action_id": "HotUJOd6oB", "executionTime": "2464ms", "package_id": "env[appid]", "timestamp": *************, "type": "restartApp"}, {"action_id": "rkL0oz4kiL", "executionTime": "6388ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "yUJyVO5Wev", "executionTime": "1286ms", "function_name": "alert_accept", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "3caMBvQX7k", "executionTime": "2244ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 30, "timestamp": *************, "type": "waitTill"}, {"action_id": "El6k4IPZly", "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "KmartProdSignin_Copy_20250501144222_20250501144222.json", "test_case_name": "Kmart-Signin", "test_case_steps": [{"action_id": "EELcfo48Sh", "executionTime": "3170ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "3963ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "method": "locator", "timeout": 10, "timestamp": 1745665879530, "type": "tap"}, {"action_id": "AtwFJEKbDH", "enter": true, "function_name": "text", "text": "env[uname]", "timestamp": 1749384094911, "type": "iosFunctions"}, {"action_id": "K7yV3GGsgr", "executionTime": "3905ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "timeout": 10, "timestamp": 1745666199850, "type": "tap"}, {"action_id": "qPv5C4K0a2", "enter": true, "executionTime": "4199ms", "function_name": "text", "text": "Wonderbaby@5", "timestamp": 1745666288726, "type": "iosFunctions"}], "test_case_steps_count": 8, "timestamp": 1749347414921, "type": "multiStep"}, {"action_id": "HYl6Z7Gvqz", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "timeout": 20, "timestamp": 1752532931953, "type": "waitTill"}, {"action_id": "ZBXCQNlT8z", "double_tap": false, "executionTime": "4350ms", "text_to_find": "Find", "timeout": 30, "timestamp": 1749347619914, "type": "tapOnText"}, {"action_id": "sc2KH9bG6H", "executionTime": "2866ms", "function_name": "text", "text": "Uno card", "timestamp": 1745484826180, "type": "iosFunctions"}, {"action_id": "nAB6Q8LAdv", "executionTime": "2676ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Filter\"]", "timeout": 30, "timestamp": 1745485041367, "type": "waitTill"}, {"action_id": "FnrbyHq7bU", "executionTime": "5817ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Add UNO Card Game - Red colour to bag Add", "method": "locator", "timeout": 10, "timestamp": 1745485077480, "type": "tap"}, {"action_id": "jY0oPjKbuS", "executionTime": "1666ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "timeout": 10, "timestamp": 1746102129685, "type": "exists"}, {"action_id": "F1olhgKhUt", "executionTime": "2643ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746100706297, "type": "tap"}, {"action_id": "QB2bKb0SsP", "executionTime": "3441ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Checkout\"]", "timeout": 10, "timestamp": 1751780739488, "type": "tapIfLocatorExists"}, {"action_id": "uM5FOSrU5U", "executionTime": "1587ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Collect\")]", "timeout": 10, "timestamp": 1746102183980, "type": "exists"}, {"action_id": "qjj0i3rcUh", "executionTime": "2580ms", "image_filename": "cnc-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Collect\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746100742115, "type": "tap"}, {"action_id": "Qbg9bipTGs", "count": 1, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "3816ms", "interval": 0.5, "locator_type": "accessibilityid", "locator_value": "Remove UNO Card Game", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1746102698568, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "FKZs2qCWoU", "double_tap": false, "executionTime": "3364ms", "text_to_find": "Tarneit", "timeout": 30, "timestamp": 1746099725928, "type": "tapOnText"}, {"action_id": "7SpDO20tS2", "duration": 10, "executionTime": "10017ms", "time": 10, "timestamp": 1746154851726, "type": "wait"}, {"action_id": "Q0fomJIDoQ", "executionTime": "2833ms", "image_filename": "banner-close-updated.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1746100313636, "type": "tap"}, {"action_id": "lWIRxRm6HE", "executionTime": "2384ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746102819296, "type": "tap"}, {"action_id": "42Jm6o7r1t", "executionTime": "3503ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Checkout\"]", "timeout": 10, "timestamp": 1751780763542, "type": "tapIfLocatorExists"}, {"action_id": "qjj0i3rcUh", "executionTime": "2707ms", "image_filename": "cnc-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Collect\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1749347993660, "type": "tap"}, {"action_id": "UnxZdeLmYu", "duration": 10, "method": "coordinates", "time": 10, "timestamp": 1752469783891, "type": "wait", "x": 0, "y": 0}, {"action_id": "XCynRs6gJ3", "count": 3, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "5004ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "start_x": 50, "start_y": 70, "timestamp": 1752468764429, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "pr9o8Zsm5p", "executionTime": "2570ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "method": "locator", "timeout": 10, "timestamp": 1746102873316, "type": "tap"}, {"action_id": "8umPSX0vrr", "executionTime": "2292ms", "image_filename": "banner-close-updated.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1746704751098, "type": "tap"}, {"action_id": "k3mu9Mt7Ec", "executionTime": "3095ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746100354856, "type": "tap"}, {"action_id": "Ob26qqcA0p", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "5576ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1746100402404, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "OyUowAaBzD", "executionTime": "2432ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1746100379226, "type": "tap"}, {"action_id": "cKNu2QoRC1", "executionTime": "2574ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "method": "locator", "timeout": 20, "timestamp": 1746426742221, "type": "tap"}, {"action_id": "8ZYdW2lMKv", "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "Delivery__Buy_20250505165058.json", "test_case_name": "Delivery  Buy", "test_case_steps": [{"action_id": "oubgsUKJBQ", "double_tap": false, "image_filename": "env[homepage-edit-link-img]", "method": "image", "text_to_find": "Edit", "threshold": 0.7, "timeout": 30, "timestamp": 1749348717236, "type": "tapOnText"}, {"action_id": "u9AfEcXf4f", "executionTime": "11455ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnCurrentLocationButton", "timeout": 10, "timestamp": 1746447155354, "type": "waitTill"}, {"action_id": "McMjFpUsEi", "executionTime": "5400ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "btnCurrentLocationButton", "method": "locator", "timeout": 20, "timestamp": 1745995736295, "type": "tap"}, {"action_id": "17eoVQWSoT", "executionTime": "3658ms", "text_to_find": "Save", "timeout": 30, "timestamp": 1745995853138, "type": "tapOnText"}, {"action_id": "sUREcYZkcJ", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Allow While Using App\"]", "timeout": 10, "timestamp": 1752468602257, "type": "tapIfLocatorExists"}, {"action_id": "qcZQIqosdj", "locator_type": "accessibility_id", "locator_value": "btnUpdate", "timeout": 10, "timestamp": 1752468671536, "type": "tapIfLocatorExists"}, {"action_id": "n1nO8aB15q", "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"txtLocationTitle\"]/preceding-sibling::XCUIElementTypeButton[1]", "timeout": 10, "timestamp": 1752468705620, "type": "tapIfLocatorExists"}, {"action_id": "64XwxxXdVM", "double_tap": false, "executionTime": "1830ms", "image_filename": "homepage-search-se.png", "method": "image", "text_to_find": "Find", "threshold": 0.7, "timeout": 30, "timestamp": 1745980305734, "type": "tapOnText"}, {"action_id": "Xve5h7yYnZ", "executionTime": "3088ms", "function_name": "text", "text": "Uno card", "timestamp": 1745484826180, "type": "iosFunctions"}, {"action_id": "zkVRnc7eha", "executionTime": "2767ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Filter\"]", "timeout": 30, "timestamp": 1745485041367, "type": "waitTill"}, {"action_id": "rAie1aNECc", "executionTime": "6791ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Add UNO Card Game - Red colour to bag Add", "method": "locator", "timeout": 10, "timestamp": 1745485077480, "type": "tap"}, {"action_id": "voY6NQoWRs", "executionTime": "3307ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1745486173359, "type": "tap"}, {"action_id": "NppgI69ZXu", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Checkout\"]", "timeout": 10, "timestamp": 1751780450679, "type": "tapIfLocatorExists"}, {"action_id": "aqBkqyVhrZ", "executionTime": "16354ms", "image_filename": "delivery-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "threshold": 0.7, "timeout": 15, "timestamp": 1746427811120, "type": "waitTill"}, {"action_id": "hwdyCKFAUJ", "executionTime": "1814ms", "image_filename": "delivery-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746427830202, "type": "tap"}, {"action_id": "xAa049Qgls", "count": 3, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "20082ms", "interval": 5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Continue to details\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1745486216977, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "Q5A0cNaJ24", "executionTime": "3031ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Continue to details\"]", "method": "locator", "timeout": 10, "timestamp": 1745486308596, "type": "tap"}, {"action_id": "h9trcMrvxt", "executionTime": "3038ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"First Name\"]", "method": "locator", "timeout": 10, "timestamp": 1745486361281, "type": "tap"}, {"action_id": "CLMmkV1OIM", "delay": 500, "executionTime": "3392ms", "function_name": "text", "text": "First Name", "timestamp": 1745486374043, "type": "textClear"}, {"action_id": "p8rfQL9ara", "executionTime": "3153ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Last Name\"]", "method": "locator", "timeout": 10, "timestamp": 1745486401162, "type": "tap"}, {"action_id": "QvuueoTR8W", "delay": 500, "executionTime": "3368ms", "function_name": "text", "text": "Last Name", "timestamp": 1745486416273, "type": "textClear"}, {"action_id": "9B5MQGTmpP", "executionTime": "3080ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "method": "locator", "timeout": 10, "timestamp": 1745486441044, "type": "tap"}, {"action_id": "lWJtKSqlPS", "delay": 500, "executionTime": "3483ms", "function_name": "text", "text": "<EMAIL>", "timestamp": 1745486452706, "type": "textClear"}, {"action_id": "yi5EsHEFvc", "executionTime": "3080ms", "function_name": "text", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Mobile number\"]", "method": "locator", "timeout": 10, "timestamp": 1745486486775, "type": "tap"}, {"action_id": "SFj4Aa7RHQ", "delay": 500, "executionTime": "3252ms", "function_name": "text", "text": "0400000000", "timestamp": 1745486504243, "type": "textClear"}, {"action_id": "kDpsm2D3xt", "enter": true, "executionTime": "2985ms", "function_name": "text", "text": " ", "timestamp": 1745570305956, "type": "iosFunctions"}, {"action_id": "5ZzW1VVSzy", "double_tap": false, "executionTime": "2068ms", "image_filename": "delivery_addreess_input.png", "method": "image", "text_to_find": "address", "threshold": 0.7, "timeout": 30, "timestamp": 1745562034217, "type": "tapOnText"}, {"action_id": "RmtvsSn1f9", "executionTime": "5707ms", "method": "coordinates", "text": "305 238 Flinders", "timeout": 60, "timestamp": 1745562450150, "type": "tapAndType", "x": 54, "y": 314}, {"action_id": "NcU6aex76k", "executionTime": "1807ms", "image_filename": "keyboard_done_iphoneSE.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"<PERSON>\"]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746616991897, "type": "tap"}, {"action_id": "R4dG3ONPQA", "image_filename": "env[delivery-address-img]", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1749349107703, "type": "tap"}, {"action_id": "W4WZVSkkUa", "count": 3, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "9682ms", "interval": 0.5, "locator_type": "xpath", "locator_value": " //XCUIElementTypeButton[@name=\"Continue to payment\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1745489862127, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "vYLhraWpQm", "executionTime": "1889ms", "image_filename": "banner-close-updated.png", "method": "image", "package_id": "au.com.kmart", "threshold": 0.7, "timeout": 20, "timestamp": 1745982487511, "type": "tap"}, {"action_id": "gPYNwJ0HKo", "executionTime": "3312ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1745490084698, "type": "tap"}, {"action_id": "SFG3Pz1wgC", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Checkout\"]", "timeout": 10, "timestamp": 1751780444579, "type": "tapIfLocatorExists"}, {"action_id": "a4pJa7EAyI", "executionTime": "5059ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "method": "locator", "timeout": 10, "timestamp": 1745490172397, "type": "tap"}, {"action_id": "q6kSH9e0MI", "executionTime": "2804ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"Continue shopping\"]", "method": "locator", "timeout": 10, "timestamp": 1745490217950, "type": "tap"}], "test_case_steps_count": 36, "timestamp": 1752468790238, "type": "multiStep"}, {"action_id": "hbIlJIWlVN", "display_depth": 0, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "Kmart_AU_Cleanup_20250626204013.json", "test_case_name": "Kmart_AU_Cleanup", "test_case_steps": [{"action_id": "OQ1fr8NUlV", "method": "coordinates", "package_id": "au.com.kmart", "timestamp": 1750934498011, "type": "restartApp", "x": 0, "y": 0}, {"action_id": "IOzaCR1Euv", "duration": 5, "time": 5, "timestamp": 1750976955704, "type": "wait"}, {"action_id": "vAKpEDIzs7", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750975087425, "type": "tap"}, {"action_id": "hwdyCKFAUJ", "count": 1, "direction": "up", "duration": 500, "end_x": 50, "end_y": 30, "executionTime": "1814ms", "image_filename": "delivery-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "method": "locator", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 10, "timestamp": 1750975036314, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "SPgFRgq13M", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "timeout": 10, "timestamp": 1750978954486, "type": "tapIfLocatorExists"}, {"action_id": "ZhH80yndRU", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "package_id": "au.com.kmart", "timeout": 10, "timestamp": 1750975079444, "type": "terminateApp"}], "test_case_steps_count": 0, "timestamp": 1751002935578, "type": "cleanupSteps"}], "labels": [], "created_at": "2025-08-25T09:59:57.064872", "modified_at": "2025-08-25T09:59:57.064872"}