{"name": "Search and Add (Notebooks)", "created": "2025-07-15 11:43:48", "device_id": "00008120-00186C801E13C01E", "actions": [{"action_id": "r9Ef3eOmlj", "text_to_find": "Find", "timeout": 30, "timestamp": 1750379595270, "type": "tapOnText"}, {"action_id": "8S8UskeUvp", "enter": true, "function_name": "text", "text": "Notebook", "timestamp": 1750379623062, "type": "iosFunctions"}, {"action_id": "qSYJYPHhYJ", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Filter\"]", "timeout": 30, "timestamp": 1750379714689, "type": "waitTill"}, {"action_id": "u7kJ2m8mFs", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]", "method": "locator", "timeout": 10, "timestamp": 1750379679764, "type": "tap"}, {"action_id": "fpJSA5zFxF", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750379828258, "type": "tap"}, {"type": "wait", "timestamp": 1752543824026, "time": 5, "duration": 5, "action_id": "u8qdoG1u37"}, {"action_id": "pby60yUdzg", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Checkout\"]", "timeout": 10, "timestamp": 1751780505614, "type": "tapIfLocatorExists"}, {"action_id": "bCXOxC2J7X", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "timeout": 30, "timestamp": 1750379913061, "type": "waitTill"}], "labels": [], "updated": "2025-07-15 11:43:48"}