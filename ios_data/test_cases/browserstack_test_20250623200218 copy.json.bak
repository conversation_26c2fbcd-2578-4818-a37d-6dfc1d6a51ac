{"name": "browserstack_test", "created": "2025-06-29 22:10:58", "device_id": "00008120-00186C801E13C01E", "actions": [{"action_id": "4DW5Pg01pk", "package_id": "com.saucelabs.SwagLabsMobileApp.7RJ42ANHA9", "timestamp": 1750672694950, "type": "restartApp", "executionTime": "3461ms"}, {"action_id": "JmuCQbTn2a", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"test-Username\"]", "method": "locator", "timeout": 10, "timestamp": 1750672726610, "type": "tap", "executionTime": "845ms"}, {"action_id": "fifVZSCEwL", "text": "standard_user", "timestamp": 1750672754512, "type": "text", "executionTime": "1027ms"}, {"action_id": "ecGfLOHDJK", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"test-Password\"]", "method": "locator", "timeout": 10, "timestamp": 1750672782983, "type": "tap", "executionTime": "1028ms"}, {"action_id": "hEBr6m1eZL", "text": "secret_sauce", "timestamp": 1750672789075, "type": "text", "executionTime": "948ms"}, {"action_id": "YiQRCpuzmx", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "test-LOGIN", "method": "locator", "timeout": 10, "timestamp": 1750672860551, "type": "tap", "executionTime": "1096ms"}, {"action_id": "GbxajwINqL", "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"test-Cart\"]/XCUIElementTypeOther", "timeout": 10, "timestamp": 1750672897124, "type": "exists", "executionTime": "542ms"}, {"type": "swipe", "timestamp": 1751198793285, "start_x": 50, "start_y": 70, "end_x": 50, "end_y": 30, "vector_start": [0.5, 0.7], "vector_end": [0.5, 0.3], "duration": 500, "count": 1, "interval": 0.5, "direction": "up", "action_id": "9BlLU8CA77"}, {"type": "tapOnText", "timestamp": 1751198850818, "text_to_find": "<PERSON><PERSON>", "timeout": 30, "action_id": "Wi2i93tCdi"}, {"type": "swipeTillVisible", "timestamp": 1751198918306, "locator_value": "//XCUIElementTypeOther[@name=\"test-ADD TO CART\"]", "locator_type": "xpath", "start_x": 50, "start_y": 70, "end_x": 50, "end_y": 30, "vector_start": [0.5, 0.7], "vector_end": [0.5, 0.3], "duration": 500, "count": 2, "interval": 0.5, "direction": "up", "action_id": "6HW5T8koqs"}, {"type": "tap", "timestamp": 1751198945166, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"test-ADD TO CART\"]", "timeout": 10, "interval": 0.5, "method": "locator", "action_id": "I2iL9EzFrW"}, {"type": "tap", "timestamp": 1751199052933, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"test-Cart\"]/XCUIElementTypeOther", "timeout": 10, "interval": 0.5, "method": "locator", "action_id": "2UiO7bKLvk"}], "labels": [], "updated": "2025-06-29 22:10:58"}