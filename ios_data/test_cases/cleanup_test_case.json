{"test_case_name": "iOS Cleanup Steps Verification Test", "description": "Test case to verify that cleanup steps are properly detected, separated, and executed on failure in iOS implementation", "platform": "ios", "test_case_steps": [{"action_id": "step_1", "executionTime": "2024-01-15T10:00:00Z", "type": "wait", "timestamp": "2024-01-15T10:00:00Z", "duration": 1.0, "time": 1000}, {"action_id": "step_2", "executionTime": "2024-01-15T10:00:01Z", "type": "tap", "timestamp": "2024-01-15T10:00:01Z", "x": 200, "y": 300}, {"action_id": "step_3_fail", "executionTime": "2024-01-15T10:00:02Z", "type": "tap", "timestamp": "2024-01-15T10:00:02Z", "locator_type": "xpath", "locator_value": "//button[@id='nonexistent_button_that_will_fail']", "timeout": 5}, {"action_id": "step_4", "executionTime": "2024-01-15T10:00:03Z", "type": "wait", "timestamp": "2024-01-15T10:00:03Z", "duration": 1.0, "time": 1000}, {"action_id": "cleanup_step_1", "executionTime": "2024-01-15T10:00:04Z", "type": "cleanupSteps", "timestamp": "2024-01-15T10:00:04Z", "steps": [{"action_id": "cleanup_action_1", "type": "wait", "duration": 0.5, "time": 500}, {"action_id": "cleanup_action_2", "type": "tap", "x": 100, "y": 100}]}, {"action_id": "multistep_with_cleanup", "executionTime": "2024-01-15T10:00:05Z", "type": "multiStep", "timestamp": "2024-01-15T10:00:05Z", "cleanup": true, "steps": [{"action_id": "multistep_cleanup_1", "type": "wait", "duration": 0.3, "time": 300}, {"action_id": "multistep_cleanup_2", "type": "tap", "x": 50, "y": 50}]}], "metadata": {"created_by": "automation_test", "created_at": "2024-01-15T10:00:00Z", "version": "1.0", "tags": ["cleanup", "verification", "ios"]}}