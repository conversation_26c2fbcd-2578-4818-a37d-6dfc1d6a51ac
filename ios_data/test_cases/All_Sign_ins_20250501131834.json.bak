{"name": "All Sign ins", "created": "2025-07-15 11:44:56", "device_id": "********-00186C801E13C01E", "actions": [{"action_id": "JXFxYCr98V", "executionTime": "3400ms", "package_id": "env[appid]", "timestamp": *************, "type": "restartApp"}, {"action_id": "qA1ap4n1m4", "executionTime": "5578ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "XuLgjNG74w", "executionTime": "1152ms", "function_name": "alert_accept", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "eJnHS9n9VL", "executionTime": "2695ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 30, "timestamp": *************, "type": "waitTill"}, {"action_id": "50Z2jrodNd", "executionTime": "2747ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "eOm1WExcrK", "enter": true, "executionTime": "3333ms", "function_name": "text", "text": "env[uname]", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "dpVaKL19uc", "executionTime": "2785ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "timeout": 10, "timestamp": 1746068218057, "type": "tap"}, {"action_id": "j8NXU87gV3", "enter": true, "executionTime": "3107ms", "function_name": "text", "text": "env[pwd]", "timestamp": 1749383470134, "type": "iosFunctions"}, {"action_id": "EDHl0X27Wi", "executionTime": "3354ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "timeout": 10, "timestamp": 1746068337374, "type": "waitTill"}, {"action_id": "F0gZF1jEnT", "executionTime": "2589ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746068378268, "type": "tap"}, {"action_id": "4WfPFN961S", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "5518ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1746068462307, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "bGo3feCwBQ", "executionTime": "2439ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1746068479716, "type": "tap"}, {"action_id": "IvqPpScAJa", "executionTime": "2575ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 3 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746068564144, "type": "tap"}, {"action_id": "WlISsMf9QA", "executionTime": "2369ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtLog in\"]", "method": "locator", "timeout": 10, "timestamp": 1746068627313, "type": "tap"}, {"action_id": "rJVGLpLWM3", "executionTime": "1208ms", "function_name": "alert_accept", "timestamp": 1746068653559, "type": "iosFunctions"}, {"action_id": "6mHVWI3j5e", "executionTime": "2182ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 20, "timestamp": 1746068667568, "type": "waitTill"}, {"action_id": "rLCI6NVxSc", "executionTime": "2797ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "method": "locator", "timeout": 10, "timestamp": 1746068757857, "type": "tap"}, {"action_id": "TGoXyeQtB7", "enter": true, "executionTime": "4196ms", "function_name": "text", "text": "env[uname]", "timestamp": 1749383517833, "type": "iosFunctions"}, {"action_id": "SHaIduBnay", "executionTime": "2786ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "timeout": 10, "timestamp": 1746583053506, "type": "tap"}, {"action_id": "8uojw2klHA", "enter": true, "executionTime": "3081ms", "function_name": "text", "text": "env[pwd]", "timestamp": 1749383548788, "type": "iosFunctions"}, {"action_id": "FHRlQXe58T", "executionTime": "2600ms", "interval": 0.5, "locator_type": "xpath", "locator_value": " //XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750378324505, "type": "tap"}, {"action_id": "quZwUwj3a8", "executionTime": "2362ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"txtHomeGreetingText\"]", "timeout": 10, "timestamp": 1746068941511, "type": "waitTill"}, {"action_id": "ydRnBBO1vR", "executionTime": "2589ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746068987218, "type": "tap"}, {"action_id": "ehyLmdZWP2", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "5648ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1746069003463, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "A1Wz7p1iVG", "executionTime": "2408ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1746069019618, "type": "tap"}, {"action_id": "lCSewtjn1z", "executionTime": "3220ms", "package_id": "env[appid]", "timestamp": 1746943229460, "type": "restartApp"}, {"action_id": "4PZC1vVWJW", "double_tap": false, "executionTime": "4389ms", "image_filename": "homepage-search-se.png", "method": "image", "text_to_find": "Find", "threshold": 0.7, "timeout": 30, "timestamp": 1746069108697, "type": "tapOnText"}, {"action_id": "aRgHcQcLDP", "enter": true, "executionTime": "2737ms", "function_name": "text", "text": "uno card", "timestamp": 1746069162268, "type": "iosFunctions"}, {"action_id": "YC6bBrKQgq", "executionTime": "2169ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Filter\"]", "timeout": 30, "timestamp": 1746078698825, "type": "waitTill"}, {"action_id": "BTYxjEaZEk", "executionTime": "3579ms", "image_filename": "Unocard-product-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746069249053, "type": "tap"}, {"action_id": "6zUBxjSFym", "executionTime": "2434ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"SKU :\"]", "timeout": 30, "timestamp": 1746069314833, "type": "waitTill"}, {"action_id": "mcscWdhpn2", "count": 3, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "15860ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"Already a member?\"]", "start_x": 50, "start_y": 70, "timestamp": 1751713871587, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "4PZC1vVWJW", "double_tap": false, "executionTime": "3843ms", "image_filename": "homepage-search-se.png", "method": "image", "text_to_find": "Sign", "threshold": 0.7, "timeout": 30, "timestamp": 1750327000121, "type": "tapOnText"}, {"action_id": "q9ZiyYoE5B", "executionTime": "1173ms", "function_name": "alert_accept", "timestamp": 1746069474388, "type": "iosFunctions"}, {"action_id": "VK2oI6mXSB", "executionTime": "2346ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[contains(@name,\"Email\")]", "timeout": 10, "timestamp": 1750999390597, "type": "waitTill"}, {"action_id": "50Z2jrodNd", "executionTime": "3766ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[contains(@name,\"Email\")]", "method": "locator", "timeout": 10, "timestamp": 1750995710781, "type": "tap"}, {"action_id": "wuIMlAwYVA", "enter": true, "executionTime": "3464ms", "function_name": "text", "text": "env[uname1]", "timestamp": 1749383618709, "type": "iosFunctions"}, {"action_id": "SHaIduBnay", "executionTime": "3007ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[contains(@name,\"Password\")]", "method": "locator", "timeout": 10, "timestamp": 1750995734361, "type": "tap"}, {"action_id": "N2yjynioko", "enter": true, "executionTime": "3199ms", "function_name": "text", "text": "Wonderbaby@5", "timestamp": 1746073544849, "type": "iosFunctions"}, {"action_id": "AOcOOSuOsB", "executionTime": "4067ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 30, "timestamp": 1746073992632, "type": "waitTill"}, {"action_id": "AOcOOSuOsB", "executionTime": "3261ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1751000541013, "type": "tap"}, {"action_id": "4WfPFN961S", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "5592ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1747984778415, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "IJh702cxG0", "executionTime": "2473ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1746074208264, "type": "tap"}, {"action_id": "lCSewtjn1z", "executionTime": "3235ms", "package_id": "env[appid]", "timestamp": *************, "type": "restartApp"}, {"action_id": "XryN8qR1DX", "executionTime": "2503ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "L6wTorOX8B", "executionTime": "2402ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtMoreAccountCtaSignIn\"]", "method": "locator", "timeout": 15, "timestamp": *************, "type": "tap"}, {"action_id": "byEe7qbCpq", "executionTime": "1176ms", "function_name": "alert_accept", "timestamp": *************, "type": "iosFunctions"}, {"action_id": "APqAlKbucp", "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "KmartProdSignin_Copy_20250501144222_20250501144222.json", "test_case_name": "Kmart-Signin", "test_case_steps": [{"action_id": "EELcfo48Sh", "executionTime": "2240ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "3011ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "6vtTsVjGmr", "enter": true, "executionTime": "3311ms", "function_name": "text", "text": "<EMAIL>", "timestamp": 1752446259233, "type": "iosFunctions"}, {"action_id": "K7yV3GGsgr", "executionTime": "3759ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "timeout": 10, "timestamp": 1745666199850, "type": "tap"}, {"action_id": "ImKkcRCyRi", "enter": true, "executionTime": "3103ms", "function_name": "text", "text": "Wonderbaby@5", "timestamp": 1752446273568, "type": "iosFunctions"}], "test_case_steps_count": 5, "timestamp": 1752540502021, "type": "multiStep"}, {"action_id": "NurQsFoMkE", "executionTime": "2445ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1746078231073, "type": "tap"}, {"action_id": "4WfPFN961S", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "5484ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1747984801858, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "7WYExJTqjp", "executionTime": "2483ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1746078255432, "type": "tap"}, {"action_id": "XryN8qR1DX", "executionTime": "2575ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750324302752, "type": "tap"}, {"action_id": "4PZC1vVWJW", "double_tap": false, "executionTime": "4388ms", "image_filename": "homepage-search-se.png", "method": "image", "text_to_find": "Find", "threshold": 0.7, "timeout": 30, "timestamp": 1750324373627, "type": "tapOnText"}, {"action_id": "aRgHcQcLDP", "enter": true, "executionTime": "2841ms", "function_name": "text", "text": "uno card", "timestamp": 1750324384312, "type": "iosFunctions"}, {"action_id": "YC6bBrKQgq", "executionTime": "2123ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Filter\"]", "timeout": 30, "timestamp": 1750324411955, "type": "waitTill"}, {"action_id": "BTYxjEaZEk", "executionTime": "2723ms", "image_filename": "Unocard-product-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::XCUIElementTypeOther[1]//XCUIElementTypeLink)[1]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1750324420234, "type": "tap"}, {"action_id": "K2w9XUGwnb", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "7542ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Add to bag", "method": "locator", "start_x": 50, "start_y": 70, "timeout": 10, "timestamp": 1750324450331, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "S1cQQxksEj", "executionTime": "5754ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Add to bag", "method": "locator", "timeout": 10, "timestamp": 1752204287597, "type": "tap", "x": 0, "y": 0}, {"action_id": "XcWXIMtv1E", "duration": 5, "executionTime": "5014ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Add to bag", "method": "locator", "time": 5, "timeout": 10, "timestamp": 1752204317021, "type": "wait"}, {"action_id": "XryN8qR1DX", "executionTime": "2966ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750324471190, "type": "tap"}, {"action_id": "ZBXuV4sJUR", "executionTime": "3471ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Checkout\"]", "timeout": 10, "timestamp": 1751781184169, "type": "tapIfLocatorExists"}, {"action_id": "g8u66qfKkX", "executionTime": "1985ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "timeout": 10, "timestamp": 1750324747501, "type": "waitTill"}, {"action_id": "tufIibCj03", "executionTime": "2585ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "method": "locator", "timeout": 10, "timestamp": 1750324767119, "type": "tap"}, {"action_id": "2YGctqXNED", "count": 1, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "7275ms", "interval": 0.5, "locator_type": "accessibilityid", "locator_value": "Continue to details", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1750324657451, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "2YGctqXNED", "count": 1, "direction": "up", "duration": 300, "end_x": 50, "end_y": 30, "executionTime": "4653ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Continue to details", "method": "locator", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 10, "timestamp": 1750993261915, "type": "tap", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "6PL8P3rT57", "executionTime": "3387ms", "text_to_find": "Sign", "timeout": 30, "timestamp": 1750325569588, "type": "tapOnText"}, {"action_id": "q9ZiyYoE5B", "executionTime": "1183ms", "function_name": "alert_accept", "timestamp": 1750324808266, "type": "iosFunctions"}, {"action_id": "50Z2jrodNd", "executionTime": "2781ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "method": "locator", "timeout": 10, "timestamp": 1750995765323, "type": "tap"}, {"action_id": "e1RoZWCZJb", "enter": true, "executionTime": "3318ms", "function_name": "text", "text": "<EMAIL>", "timestamp": 1745667549306, "type": "iosFunctions"}, {"action_id": "DaVBARRwft", "executionTime": "2760ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[contains(@name,\"Password\")]", "method": "locator", "timeout": 10, "timestamp": 1745667657983, "type": "tap"}, {"action_id": "pCPTAtSZbf", "enter": true, "executionTime": "3112ms", "function_name": "text", "text": "Wonderbaby@5", "timestamp": 1745667677406, "type": "iosFunctions"}, {"action_id": "mg4S62Rdtq", "executionTime": "10133ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Checkout\"]", "timeout": 10, "timestamp": 1751781241754, "type": "tapIfLocatorExists"}, {"action_id": "g8u66qfKkX", "executionTime": "1945ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "timeout": 10, "timestamp": 1750339093537, "type": "waitTill"}, {"action_id": "tufIibCj03", "executionTime": "2538ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "method": "locator", "timeout": 10, "timestamp": 1750326197819, "type": "tap"}, {"action_id": "1NWfFsDiTQ", "executionTime": "2556ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "method": "locator", "timeout": 10, "timestamp": 1750326180016, "type": "tap"}, {"action_id": "CkfAScJNq8", "executionTime": "2329ms", "image_filename": "env[closebtnimage]", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1750379336861, "type": "tap"}, {"action_id": "NurQsFoMkE", "executionTime": "3323ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750324860361, "type": "tap"}, {"action_id": "4WfPFN961S", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "6256ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1750324873622, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "7WYExJTqjp", "executionTime": "2459ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1750324883313, "type": "tap"}, {"action_id": "7QpmNS6hif", "executionTime": "3982ms", "method": "coordinates", "package_id": "env[appid]", "timestamp": 1750838345652, "type": "restartApp", "x": 0, "y": 0}, {"action_id": "NurQsFoMkE", "executionTime": "2552ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 1 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750379940962, "type": "tap"}, {"type": "multiStep", "timestamp": 1752543862997, "test_case_id": "Search_and_Add_Notebooks_20250620103536.json", "test_case_name": "Search and Add (Notebooks)", "test_case_steps_count": 8, "expanded": false, "loading_in_progress": false, "test_case_steps": [{"action_id": "r9Ef3eOmlj", "text_to_find": "Find", "timeout": 30, "timestamp": 1750379595270, "type": "tapOnText"}, {"action_id": "8S8UskeUvp", "enter": true, "function_name": "text", "text": "Notebook", "timestamp": 1750379623062, "type": "iosFunctions"}, {"action_id": "qSYJYPHhYJ", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Filter\"]", "timeout": 30, "timestamp": 1750379714689, "type": "waitTill"}, {"action_id": "u7kJ2m8mFs", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//XCUIElementTypeOther[@name=\"Sort by: Relevance\"]/following-sibling::*[1]//XCUIElementTypeButton)[1]", "method": "locator", "timeout": 10, "timestamp": 1750379679764, "type": "tap"}, {"action_id": "fpJSA5zFxF", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750379828258, "type": "tap"}, {"action_id": "u8qdoG1u37", "duration": 5, "time": 5, "timestamp": 1752543824026, "type": "wait"}, {"action_id": "pby60yUdzg", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Checkout\"]", "timeout": 10, "timestamp": 1751780505614, "type": "tapIfLocatorExists"}, {"action_id": "bCXOxC2J7X", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "timeout": 30, "timestamp": 1750379913061, "type": "waitTill"}], "steps_loaded": true, "display_depth": 0, "action_id": "1XUKKmBanM"}, {"action_id": "VMzFZ2uTwl", "count": 4, "direction": "up", "duration": 500, "end_x": 50, "end_y": 30, "executionTime": "7034ms", "interval": 0.5, "locator_type": "accessibilityid", "locator_value": "Continue to details", "start_x": 50, "start_y": 70, "timestamp": 1750380019862, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "5Dk9h5bQWl", "executionTime": "5374ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "Continue to details", "method": "locator", "timeout": 10, "timestamp": 1750380059683, "type": "tap"}, {"action_id": "LDH2hlTZT9", "duration": 5, "executionTime": "5016ms", "text_to_find": "in", "time": 5, "timeout": 30, "timestamp": 1750849209295, "type": "wait"}, {"action_id": "STEdg5jOU8", "double_tap": false, "executionTime": "3402ms", "text_to_find": "in", "timeout": 30, "timestamp": 1750380094246, "type": "tapOnText"}, {"action_id": "q9ZiyYoE5B", "executionTime": "1185ms", "function_name": "alert_accept", "timestamp": 1750380112396, "type": "iosFunctions"}, {"action_id": "L5CIZqzpQK", "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "KmartProdSignin_Copy_20250501144222_20250501144222.json", "test_case_name": "Kmart-Signin", "test_case_steps": [{"action_id": "EELcfo48Sh", "executionTime": "2240ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "3011ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeTextField[@name=\"Email\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "6vtTsVjGmr", "enter": true, "executionTime": "3311ms", "function_name": "text", "text": "<EMAIL>", "timestamp": 1752446259233, "type": "iosFunctions"}, {"action_id": "K7yV3GGsgr", "executionTime": "3759ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "timeout": 10, "timestamp": 1745666199850, "type": "tap"}, {"action_id": "ImKkcRCyRi", "enter": true, "executionTime": "3103ms", "function_name": "text", "text": "Wonderbaby@5", "timestamp": 1752446273568, "type": "iosFunctions"}], "test_case_steps_count": 5, "timestamp": 1752540462094, "type": "multiStep"}, {"action_id": "uNbKV4slh0", "executionTime": "3479ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Checkout\"]", "timeout": 10, "timestamp": 1751781228542, "type": "tapIfLocatorExists"}, {"action_id": "tufIibCj03", "executionTime": "2474ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "method": "locator", "timeout": 10, "timestamp": 1750380273151, "type": "tap"}, {"action_id": "1NWfFsDiTQ", "executionTime": "3317ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"<PERSON>move\")]", "method": "locator", "timeout": 10, "timestamp": 1750380286665, "type": "tap"}, {"action_id": "CkfAScJNq8", "executionTime": "2250ms", "image_filename": "env[closebtnimage]", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1750380309586, "type": "tap"}, {"action_id": "NurQsFoMkE", "executionTime": "2337ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750380175905, "type": "tap"}, {"action_id": "4WfPFN961S", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "6224ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1750380181036, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "7WYExJTqjp", "executionTime": "2439ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1750380187636, "type": "tap"}, {"action_id": "IR7wnjW7C8", "display_depth": 0, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "Kmart_AU_Cleanup_20250626204013.json", "test_case_name": "Kmart_AU_Cleanup", "test_case_steps": [{"action_id": "OQ1fr8NUlV", "method": "coordinates", "package_id": "au.com.kmart", "timestamp": 1750934498011, "type": "restartApp", "x": 0, "y": 0}, {"action_id": "IOzaCR1Euv", "duration": 5, "time": 5, "timestamp": 1750976955704, "type": "wait"}, {"action_id": "vAKpEDIzs7", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750975087425, "type": "tap"}, {"action_id": "hwdyCKFAUJ", "count": 1, "direction": "up", "duration": 500, "end_x": 50, "end_y": 30, "executionTime": "1814ms", "image_filename": "delivery-tab-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Delivery\"]", "method": "locator", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 10, "timestamp": 1750975036314, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "SPgFRgq13M", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "timeout": 10, "timestamp": 1750978954486, "type": "tapIfLocatorExists"}, {"action_id": "ZhH80yndRU", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "package_id": "au.com.kmart", "timeout": 10, "timestamp": 1750975079444, "type": "terminateApp"}], "test_case_steps_count": 0, "timestamp": 1751000602041, "type": "cleanupSteps"}], "labels": [], "updated": "2025-07-15 11:44:56"}