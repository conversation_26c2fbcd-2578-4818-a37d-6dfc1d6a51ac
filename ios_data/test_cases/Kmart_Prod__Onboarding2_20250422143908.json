{"name": "Kmart Prod - Onboarding2", "created": "2025-06-07 21:28:49", "device_id": "00008030-00020C123E60402E", "actions": [{"action_id": "UaInjZvYnF", "package_id": "env[tfappid]", "timestamp": 1745984719978, "type": "terminateApp"}, {"action_id": "3IFuXit8Zu", "package_id": "env[appid]", "timestamp": 1745292775600, "type": "uninstallApp"}, {"action_id": "Qt0hAlg3mH", "package_id": "env[tfappid]", "timestamp": 1745292807279, "type": "launchApp"}, {"action_id": "YmiPg9cEeJ", "image_filename": "kmartau-install-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"1611129681\"]/XCUIElementTypeButton/XCUIElementTypeButton", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1745672870365, "type": "tap"}, {"action_id": "ktysmyM2xx", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"1611129681\"]/XCUIElementTypeButton/XCUIElementTypeButton[@name=\"Open\"]", "method": "locator", "timeout": 30, "timestamp": 1745292852008, "type": "waitTill"}, {"action_id": "CIa1yzRquQ", "duration": 15, "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"1611129681\"]/XCUIElementTypeButton/XCUIElementTypeButton[@name=\"Open\"]", "method": "locator", "time": 15, "timeout": 10, "timestamp": 1745292879370, "type": "tap"}, {"action_id": "PzdOGpha4Y", "package_id": "env[appid]", "timestamp": 1745292910720, "type": "launchApp"}, {"action_id": "6BlY5jNebw", "duration": 5, "package_id": "env[appid]", "time": 5, "timestamp": 1745293018633, "type": "restartApp"}, {"action_id": "YLdCGXoOgw", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"btnOnboardingScreenActionButton\"]", "method": "locator", "timeout": 10, "timestamp": 1745293083992, "type": "tap"}, {"action_id": "dRt8f23ykA", "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Allow\"]", "timeout": 10, "timestamp": 1745293134273, "type": "exists"}, {"action_id": "rTEfpjy5eR", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Allow\"]", "method": "locator", "timeout": 10, "timestamp": 1745293198430, "type": "tap"}, {"action_id": "w6Zy5PcOTF", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"btnOnboardingScreenActionButton\"]", "method": "locator", "timeout": 10, "timestamp": 1745293243234, "type": "tap"}, {"action_id": "Y4WhqPx4XF", "duration": 5, "time": 5, "timestamp": 1747542816178, "type": "wait"}, {"action_id": "of6jjK3pde", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"Allow While Using App\"]", "method": "locator", "timeout": 10, "timestamp": 1745293305110, "type": "tap"}, {"action_id": "Cao4NMFQi9", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"btnMayBeLater\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "ntM776u9ty", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtOnePassOnboardingSkipForNow\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "ChqObopqO6", "duration": 15, "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "time": 15, "timeout": 60, "timestamp": *************, "type": "waitTill"}, {"action_id": "LcOZtL3ShH", "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"Shop Broadway Deliver 2000 Edit\"]", "timeout": 10, "timestamp": *************, "type": "exists"}, {"action_id": "wE3h8Kb2Nr", "image_filename": "account-tab.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[contains(@name,\"Tab 5 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": *************, "type": "tap", "x": 355, "y": 788}, {"action_id": "JVqYBLEyG7", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtPush Notifications\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "UvgW3dYtsG", "locator_type": "xpath", "locator_value": "//XCUIElementTypeOther[@name=\"Allow notifications\"]", "timeout": 10, "timestamp": *************, "type": "exists"}, {"action_id": "rTg9UIrU8o", "hook_data": {"image_filename": "banner-close-updated.png", "log_details": "Image: banner-close-updated.png", "method": "image", "threshold": 0.7, "timeout": 20}, "hook_type": "tap", "timestamp": *************, "type": "hookAction"}], "labels": [], "updated": "2025-06-07 21:28:49", "test_case_id": "tc_233079acd5ff"}