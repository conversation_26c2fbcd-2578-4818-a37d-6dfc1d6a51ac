{"name": "Search and Add (Notebooks)_ANDROID", "created": "2025-07-27 08:19:10", "device_id": "PJTCI7EMSSONYPU8", "actions": [{"action_id": "r9Ef3eOmlj", "text_to_find": "Find", "timeout": 30, "timestamp": 1750379595270, "type": "tapOnText"}, {"action_id": "8S8UskeUvp", "enter": true, "function_name": "text", "text": "Notebooks", "timestamp": 1750379623062, "type": "text"}, {"action_id": "qSYJYPHhYJ", "interval": 0.5, "locator_type": "text", "locator_value": "Filter", "timeout": 30, "timestamp": 1750379714689, "type": "waitTill"}, {"action_id": "u7kJ2m8mFs", "interval": 0.5, "locator_type": "xpath", "locator_value": "(//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView/following-sibling::android.view.View/android.widget.Button", "method": "locator", "timeout": 10, "timestamp": 1750379679764, "type": "tap"}, {"action_id": "fpJSA5zFxF", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "method": "locator", "timeout": 10, "timestamp": 1750379828258, "type": "tap"}, {"type": "tapIfLocatorExists", "timestamp": 1753568134160, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"Checkout\"]", "timeout": 10, "action_id": "8ZdLHXBbvY"}, {"type": "waitTill", "timestamp": 1753568341343, "locator_type": "xpath", "locator_value": "//android.view.View[@text=\"Delivery\"]", "timeout": 10, "interval": 0.5, "action_id": "Ko32VlFCRX"}], "labels": [], "updated": "2025-07-27 08:19:10"}