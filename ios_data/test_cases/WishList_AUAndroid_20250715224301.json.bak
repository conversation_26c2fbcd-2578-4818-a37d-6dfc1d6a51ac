{"name": "WishList_AU-Android", "created": "2025-08-09 09:39:35", "device_id": null, "actions": [{"action_id": "HotUJOd6oB", "executionTime": "289ms", "package_id": "au.com.kmart", "timestamp": *************, "type": "terminateApp"}, {"action_id": "HotUJOd6oB", "executionTime": "1235ms", "package_id": "au.com.kmart", "timestamp": *************, "type": "launchApp"}, {"action_id": "rkL0oz4kiL", "executionTime": "5931ms", "interval": 0.5, "locator_type": "accessibility_id", "locator_value": "txtHomeAccountCtaSignIn", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "rUH3kvaEH9", "display_depth": 0, "expanded": false, "loading_in_progress": false, "steps_loaded": true, "test_case_id": "KmartSigninAUANDROID_20250709191752.json", "test_case_name": "Kmart-Signin-AU-ANDROID", "test_case_steps": [{"action_id": "HEBUNq0P6l", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "3963ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "AtwFJEKbDH", "enter": false, "function_name": "text", "text": "<EMAIL>", "timestamp": *************, "type": "text"}, {"action_id": "TkyyzFSlcu", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"password\"]", "method": "locator", "timeout": 10, "timestamp": 1751405157652, "type": "tap"}, {"action_id": "K7yV3GGsgr", "enter": true, "executionTime": "3905ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "text": "Wonderbaby@6", "timeout": 10, "timestamp": 1745666199850, "type": "text"}], "test_case_steps_count": 5, "timestamp": 1752618403524, "type": "multiStep"}, {"action_id": "yiKyF5FJwN", "executionTime": "2851ms", "locator_type": "xpath", "locator_value": "//android.view.View[@content-desc=\"txtHomeGreetingText\"]", "timeout": 30, "timestamp": 1749434558505, "type": "exists"}, {"action_id": "rqLJpAP0mA", "double_tap": false, "executionTime": "2201ms", "image_filename": "homepage-search-se.png", "method": "image", "text_to_find": "Find", "threshold": 0.7, "timeout": 30, "timestamp": 1745980305734, "type": "tapOnText"}, {"action_id": "sc2KH9bG6H", "enter": true, "executionTime": "266ms", "function_name": "text", "text": "Uno card", "timestamp": 1745484826180, "type": "text"}, {"action_id": "nAB6Q8LAdv", "executionTime": "4487ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Filter\"]", "timeout": 30, "timestamp": 1745485041367, "type": "waitTill"}, {"action_id": "eLxHVWKeDQ", "executionTime": "798ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "((//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView[1])[1]", "method": "locator", "timeout": 10, "timestamp": 1746836741255, "type": "tap"}, {"action_id": "ITHvSyXXmu", "executionTime": "30076ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[contains(@text,\"SKU\")]", "timeout": 30, "timestamp": 1746837237441, "type": "waitTill"}, {"action_id": "H3IAmq3r3i", "count": 3, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "2265ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@resource-id=\"wishlist-button\"]", "start_x": 50, "start_y": 70, "timestamp": 1746837343917, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "WbxRVpWtjw", "executionTime": "511ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@resource-id=\"wishlist-button\"]", "method": "locator", "timeout": 10, "timestamp": 1753264299817, "type": "tap"}, {"action_id": "Ob26qqcA0p", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "3303ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1752622696952, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "H3IAmq3r3i", "count": 2, "direction": "up", "duration": 500, "end_x": 50, "end_y": 30, "executionTime": "1996ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ListView[contains(@resource-id,\"swiper-wrapper\")]/android.view.View[1]//android.widget.TextView", "start_x": 50, "start_y": 70, "timestamp": 1748259858069, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "WbxRVpWtjw", "executionTime": "40269ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ListView[contains(@resource-id,\"swiper-wrapper\")]/android.view.View[1]//android.widget.TextView", "method": "locator", "timeout": 10, "timestamp": 1748259866784, "type": "tap"}, {"action_id": "ITHvSyXXmu", "executionTime": "3367ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[contains(@text,\"SKU\")]", "timeout": 30, "timestamp": 1752717154893, "type": "waitTill"}, {"action_id": "H3IAmq3r3i", "count": 3, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "11911ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@resource-id=\"wishlist-button\"]", "start_x": 50, "start_y": 70, "timestamp": 1752620014209, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "WbxRVpWtjw", "executionTime": "480ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@resource-id=\"wishlist-button\"]", "method": "locator", "timeout": 10, "timestamp": 1752620025895, "type": "tap"}, {"action_id": "SxKlojIFRq", "executionTime": "177ms", "function_name": "send_key_event", "key_event": "BACK", "method": "coordinates", "timestamp": 1753265911414, "type": "androidFunctions", "x": 0, "y": 0}, {"action_id": "F1olhgKhUt", "executionTime": "31076ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 2 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1753268599128, "type": "tap"}, {"action_id": "wzxrm7WwXv", "executionTime": "13991ms", "image_filename": "search-glassimage-android.png", "method": "image", "threshold": 0.7, "timeout": 20, "timestamp": 1753267660276, "type": "tap"}, {"action_id": "H3IAmq3r3i", "count": 2, "direction": "up", "duration": 500, "end_x": 50, "end_y": 30, "enter": true, "executionTime": "723ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ListView[contains(@resource-id,\"swiper-wrapper\")]/android.view.View[1]//android.widget.TextView", "start_x": 50, "start_y": 70, "text": "P_42999157", "timestamp": 1753261206509, "type": "text", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "nAB6Q8LAdv", "executionTime": "1386ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Filter\"]", "timeout": 30, "timestamp": 1753264251861, "type": "waitTill"}, {"action_id": "eLxHVWKeDQ", "executionTime": "33529ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "((//android.view.View[contains(@content-desc,\"to bag\")])[1]/android.view.View/android.widget.TextView[1])[1]", "method": "locator", "timeout": 10, "timestamp": 1753264273882, "type": "tap"}, {"action_id": "H3IAmq3r3i", "count": 3, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "6173ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@resource-id=\"wishlist-button\"]", "start_x": 50, "start_y": 70, "timestamp": 1753264283001, "type": "swipeTillVisible", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "WbxRVpWtjw", "executionTime": "9586ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@resource-id=\"wishlist-button\"]", "method": "locator", "timeout": 10, "timestamp": 1746837373321, "type": "tap"}, {"action_id": "F1olhgKhUt", "executionTime": "438ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 3 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1748258009231, "type": "tap"}, {"action_id": "Q0fomJIDoQ", "executionTime": "349ms", "image_filename": "banner-close-updated.png", "interval": 0.5, "locator_type": "uiselector", "locator_value": "new UiSelector().className(\"android.widget.ImageView\").instance(1)", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746842287810, "type": "tap"}, {"action_id": "y4i304JeJj", "executionTime": "31354ms", "text_to_find": "Move", "timeout": 30, "timestamp": 1746838675751, "type": "tapOnText"}, {"action_id": "Q0fomJIDoQ", "executionTime": "880ms", "image_filename": "banner-close-updated.png", "interval": 0.5, "locator_type": "uiselector", "locator_value": "new UiSelector().className(\"android.widget.ImageView\").instance(1)", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1752620368500, "type": "tap"}, {"action_id": "<PERSON><PERSON><PERSON>ynQyu", "double_tap": false, "executionTime": "1658ms", "text_to_find": "Remove", "timeout": 30, "timestamp": 1746838738528, "type": "tapOnText"}, {"action_id": "F1olhgKhUt", "executionTime": "5134ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 4 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1752620415165, "type": "tap"}, {"action_id": "bGqhW1Kciz", "executionTime": "60127ms", "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"Checkout\"]", "timeout": 10, "timestamp": 1752717021234, "type": "tapIfLocatorExists"}, {"action_id": "uOt2cFGhGr", "executionTime": "675ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Move to wishlist\"]", "method": "locator", "timeout": 10, "timestamp": 1746838817503, "type": "tap"}, {"action_id": "lWIRxRm6HE", "executionTime": "427ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.TextView[@text=\"Continue shopping\"]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1746840700159, "type": "tap"}, {"action_id": "F1olhgKhUt", "executionTime": "1261ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 3 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1752620584233, "type": "tap"}, {"action_id": "LCxISjrRBu", "condition": {"locator_type": "uiselector", "locator_value": "new UiSelector().className(\"android.widget.ImageView\").instance(1)", "timeout": 10}, "condition_type": "exists", "executionTime": "1333ms", "then_action": {"locator_type": "uiselector", "locator_value": "new UiSelector().className(\"android.widget.ImageView\").instance(1)", "method": "locator", "timeout": 10, "type": "tap"}, "timestamp": 1752620705314, "type": "ifThenSteps"}, {"action_id": "<PERSON><PERSON><PERSON>ynQyu", "double_tap": false, "executionTime": "1804ms", "text_to_find": "Remove", "timeout": 30, "timestamp": 1746842300771, "type": "tapOnText"}, {"action_id": "AJXVWhoBUt", "condition": {"locator_type": "uiselector", "locator_value": "new UiSelector().className(\"android.widget.ImageView\").instance(1)", "timeout": 10}, "condition_type": "exists", "executionTime": "1212ms", "then_action": {"locator_type": "uiselector", "locator_value": "new UiSelector().className(\"android.widget.ImageView\").instance(1)", "method": "locator", "timeout": 10, "type": "tap"}, "timestamp": 1752620730816, "type": "ifThenSteps"}, {"action_id": "<PERSON><PERSON><PERSON>ynQyu", "double_tap": false, "executionTime": "1647ms", "text_to_find": "Remove", "timeout": 30, "timestamp": 1752620739170, "type": "tapOnText"}, {"action_id": "F1olhgKhUt", "executionTime": "904ms", "image_filename": "bag1-se.png", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.ImageView[contains(@content-desc,\"Tab 5 of 5\")]", "method": "locator", "threshold": 0.7, "timeout": 10, "timestamp": 1752620774047, "type": "tap"}, {"action_id": "Ob26qqcA0p", "count": 2, "direction": "up", "duration": 1000, "end_x": 50, "end_y": 30, "executionTime": "3676ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeButton[@name=\"txtSign out\"]", "start_x": 50, "start_y": 70, "threshold": 0.7, "timeout": 20, "timestamp": 1746100402404, "type": "swipe", "vector_end": [0.5, 0.3], "vector_start": [0.5, 0.7]}, {"action_id": "OyUowAaBzD", "executionTime": "1791ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@content-desc=\"txtSign out\"]", "method": "locator", "timeout": 10, "timestamp": 1746100379226, "type": "tap"}, {"type": "multiStep", "timestamp": 1754696370201, "test_case_id": "KmartSigninAUANDROID_20250709191752.json", "test_case_name": "Kmart-Signin-AU-ANDROID", "test_case_steps_count": 6, "expanded": false, "loading_in_progress": false, "test_case_steps": [{"action_id": "HEBUNq0P6l", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "timeout": 10, "timestamp": *************, "type": "waitTill"}, {"action_id": "RCYxT9YD8u", "executionTime": "3963ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"username\"]", "method": "locator", "timeout": 10, "timestamp": *************, "type": "tap"}, {"action_id": "AtwFJEKbDH", "enter": true, "function_name": "text", "text": "<EMAIL>", "timestamp": *************, "type": "text"}, {"action_id": "Rr6FnqfUnC", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.Button[@text=\"Continue to password\"]", "method": "locator", "timeout": 10, "timestamp": 1754695396763, "type": "tap"}, {"action_id": "TkyyzFSlcu", "interval": 0.5, "locator_type": "xpath", "locator_value": "//android.widget.EditText[@resource-id=\"password\"]", "method": "locator", "timeout": 10, "timestamp": 1751405157652, "type": "tap"}, {"action_id": "K7yV3GGsgr", "enter": true, "executionTime": "3905ms", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeSecureTextField[@name=\"Password\"]", "method": "locator", "text": "Wonderbaby@6", "timeout": 10, "timestamp": 1745666199850, "type": "text"}], "steps_loaded": true, "display_depth": 0, "action_id": "gd5TFQ6ryl"}], "labels": [], "updated": "2025-08-09 09:39:35"}