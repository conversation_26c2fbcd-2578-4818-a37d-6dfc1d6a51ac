{"name": "health2", "created": "2025-06-26 19:02:41", "device_id": null, "actions": [{"action_id": "ee5KkVz90e", "executionTime": "2374ms", "package": "com.apple.Health", "package_id": "com.apple.Health", "type": "restartApp"}, {"type": "info", "timestamp": 1750928548752, "text": "The app id is env[appid]", "action_id": "gtoR0P4jYL"}, {"action_id": "E5An5BbVuK", "executionTime": "1193ms", "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"Edit\"]", "timeout": 10, "type": "clickElement"}, {"action_id": "KfOSdvcOkk", "executionTime": "1644ms", "locator_type": "xpath", "locator_value": " //XCUIElementTypeButton[@name=\"<PERSON>\"]", "timeout": 10, "type": "clickElement"}, {"action_id": "4kBvNvFi5i", "duration": 1, "executionTime": "1013ms", "time": 1, "timestamp": 1747996417607, "type": "wait"}, {"action_id": "mP1IGA6a3b", "condition": {"locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"Edit\"]", "timeout": 10}, "condition_type": "exists", "executionTime": "3635ms", "then_action": {"test_case_id": "testingcleanup_20250626150016.json", "test_case_name": "testing-cleanup", "test_case_steps_count": 0, "type": "multiStep"}, "timestamp": 1750914164910, "type": "ifElseSteps"}, {"action_id": "RekH7t5GQF", "executionTime": "1212ms", "package_id": "com.apple.Health", "timestamp": 1750915548608, "type": "launchApp"}, {"action_id": "cEZOsTdDcx", "executionTime": "3542ms", "test_case_id": "testingcleanup_20250626150016.json", "test_case_name": "testing-cleanup", "test_case_steps_count": 0, "timestamp": 1750914081900, "type": "cleanupSteps"}], "labels": [], "updated": "2025-06-26 19:02:41"}