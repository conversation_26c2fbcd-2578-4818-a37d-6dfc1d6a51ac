{"name": "apple health (Copy)", "created": "2025-06-05 17:21:04", "device_id": "00008030-00020C123E60402E", "actions": [{"action_id": "wp1dY1wJ58", "package": "com.apple.Health", "package_id": "com.apple.Health", "type": "launchApp"}, {"action_id": "HphRLWPfSD", "interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"Edit\"]", "method": "locator", "timeout": 10, "type": "tap"}, {"action_id": "AoLct5ZYWj", "locator_type": "xpath", "locator_value": " //XCUIElementTypeButton[@name=\"<PERSON>\"]", "timeout": 10, "type": "clickElement"}, {"action_id": "mOoxO3pBlm", "package": "com.apple.Health", "package_id": "com.apple.Health", "type": "terminateApp"}, {"action_id": "OqYf9xF3oX", "message": "Closed App Successfully", "take_screenshot": true, "timestamp": 1745821901235, "type": "addLog"}, {"type": "multiStep", "timestamp": 1749108060084, "test_case_id": "health2_20250408214926.json", "test_case_name": "health2", "test_case_steps_count": 9, "expanded": false, "action_id": "0YrxfRHJvd"}, {"action_id": "8kn1BCy2ZE", "hook_data": {"interval": 0.5, "locator_type": "xpath", "locator_value": "//XCUIElementTypeStaticText[@name=\"Edit\"]", "log_details": "Locator: xpath=\"//XCUIElementTypeStaticText[@name=\"Edit\"]\"", "method": "locator", "timeout": 10}, "hook_type": "tap", "timestamp": 1746323261594, "type": "hookAction"}], "labels": [], "updated": "2025-06-05 17:21:04"}